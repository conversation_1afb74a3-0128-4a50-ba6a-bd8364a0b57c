#!/usr/bin/env node

/**
 * Setup Script for HRMS Database Provisioning System
 * Helps with initial configuration and database setup
 */

const fs = require('fs').promises;
const path = require('path');
const { Pool } = require('pg');
const readline = require('readline');

class SetupWizard {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.config = {};
  }

  async run() {
    console.log('🚀 HRMS Database Provisioning Setup Wizard');
    console.log('==========================================\n');

    try {
      await this.checkPrerequisites();
      await this.gatherConfiguration();
      await this.createEnvironmentFile();
      await this.setupDatabase();
      await this.validateSetup();
      
      console.log('\n✅ Setup completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Review the generated .env file');
      console.log('2. Start the application: npm start');
      console.log('3. Test the provisioning: npm run provision -- --test');
      
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  async checkPrerequisites() {
    console.log('📋 Checking prerequisites...\n');

    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
      throw new Error(`Node.js 16+ required, found ${nodeVersion}`);
    }
    console.log(`✓ Node.js version: ${nodeVersion}`);

    // Check if package.json exists
    try {
      await fs.access('package.json');
      console.log('✓ package.json found');
    } catch {
      throw new Error('package.json not found. Run this script from the project root.');
    }

    // Check if config directory exists
    try {
      await fs.access('config');
      console.log('✓ Config directory found');
    } catch {
      await fs.mkdir('config', { recursive: true });
      console.log('✓ Config directory created');
    }

    // Check if migrations directory exists
    try {
      await fs.access('migrations');
      console.log('✓ Migrations directory found');
    } catch {
      throw new Error('Migrations directory not found. Please ensure all migration files are present.');
    }

    console.log('\n✅ All prerequisites met!\n');
  }

  async gatherConfiguration() {
    console.log('⚙️  Configuration Setup\n');

    // Environment
    this.config.environment = await this.prompt('Environment (development/staging/production)', 'development');
    
    // Database configuration
    console.log('\n📊 Database Configuration:');
    this.config.db = {
      host: await this.prompt('Database host', 'localhost'),
      port: await this.prompt('Database port', '5432'),
      database: await this.prompt('Master database name', 'hrms_master_dev'),
      username: await this.prompt('Database username', 'hrms_dev_user'),
      password: await this.prompt('Database password', '', true)
    };

    // Provisioning user
    console.log('\n🔧 Provisioning User (needs CREATE privileges):');
    this.config.provisioner = {
      username: await this.prompt('Provisioning username', 'hrms_provisioner'),
      password: await this.prompt('Provisioning password', '', true)
    };

    // Application settings
    console.log('\n🌐 Application Settings:');
    this.config.app = {
      port: await this.prompt('Application port', '3000'),
      jwtSecret: await this.prompt('JWT secret', this.generateSecret()),
      logLevel: await this.prompt('Log level (debug/info/warn/error)', 'info')
    };

    // Email configuration (optional)
    const enableEmail = await this.prompt('Enable email notifications? (y/n)', 'n');
    if (enableEmail.toLowerCase() === 'y') {
      console.log('\n📧 Email Configuration:');
      this.config.email = {
        host: await this.prompt('SMTP host', 'smtp.gmail.com'),
        port: await this.prompt('SMTP port', '587'),
        user: await this.prompt('SMTP username', ''),
        password: await this.prompt('SMTP password', '', true),
        adminEmails: await this.prompt('Admin emails (comma-separated)', '<EMAIL>')
      };
    }

    console.log('\n✅ Configuration gathered!\n');
  }

  async createEnvironmentFile() {
    console.log('📝 Creating environment file...');

    const envContent = this.generateEnvContent();
    await fs.writeFile('.env', envContent);
    
    console.log('✓ .env file created');
  }

  async setupDatabase() {
    console.log('\n🗄️  Setting up database...');

    const pool = new Pool({
      host: this.config.db.host,
      port: parseInt(this.config.db.port),
      database: this.config.db.database,
      user: this.config.db.username,
      password: this.config.db.password
    });

    try {
      // Test connection
      const client = await pool.connect();
      console.log('✓ Database connection successful');
      
      // Create master tables if they don't exist
      await this.createMasterTables(client);
      
      // Create provisioning audit table
      await this.createAuditTable(client);
      
      client.release();
      console.log('✓ Master database setup complete');
      
    } catch (error) {
      throw new Error(`Database setup failed: ${error.message}`);
    } finally {
      await pool.end();
    }
  }

  async createMasterTables(client) {
    const createTablesSQL = `
      -- Master organizations table
      CREATE TABLE IF NOT EXISTS organizations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        domain VARCHAR(255) UNIQUE NOT NULL,
        industry VARCHAR(100) NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        schema_name VARCHAR(63),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Master users table
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) NOT NULL,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_organizations_domain ON organizations(domain);
      CREATE INDEX IF NOT EXISTS idx_organizations_status ON organizations(status);
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_users_organization ON users(organization_id);
    `;

    await client.query(createTablesSQL);
    console.log('✓ Master tables created');
  }

  async createAuditTable(client) {
    const createAuditSQL = `
      CREATE TABLE IF NOT EXISTS provisioning_audit_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        organization_id UUID,
        organization_name VARCHAR(255),
        schema_name VARCHAR(63),
        status VARCHAR(20) NOT NULL,
        duration_ms INTEGER,
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_audit_organization ON provisioning_audit_log(organization_id);
      CREATE INDEX IF NOT EXISTS idx_audit_status ON provisioning_audit_log(status);
      CREATE INDEX IF NOT EXISTS idx_audit_created_at ON provisioning_audit_log(created_at);
    `;

    await client.query(createAuditSQL);
    console.log('✓ Audit table created');
  }

  async validateSetup() {
    console.log('\n🔍 Validating setup...');

    // Check if configuration files exist
    try {
      await fs.access('.env');
      console.log('✓ Environment file exists');
    } catch {
      throw new Error('Environment file not created');
    }

    // Validate database configuration
    const DatabaseConfigLoader = require('../src/config/database-config-loader');
    try {
      const configLoader = new DatabaseConfigLoader();
      configLoader.loadConfig();
      console.log('✓ Database configuration valid');
    } catch (error) {
      throw new Error(`Configuration validation failed: ${error.message}`);
    }

    console.log('✓ Setup validation passed');
  }

  generateEnvContent() {
    const env = this.config.environment.toUpperCase();
    
    return `# HRMS Database Provisioning Environment Configuration
# Generated by setup wizard on ${new Date().toISOString()}

# Application Settings
NODE_ENV=${this.config.environment}
PORT=${this.config.app.port}
LOG_LEVEL=${this.config.app.logLevel}
LOG_DIR=./logs

# Database Configuration
DB_${env}_HOST=${this.config.db.host}
DB_${env}_PORT=${this.config.db.port}
DB_${env}_DATABASE=${this.config.db.database}
DB_${env}_USERNAME=${this.config.db.username}
DB_${env}_PASSWORD=${this.config.db.password}

# Provisioning User
DB_PROVISIONER_USERNAME=${this.config.provisioner.username}
DB_PROVISIONER_PASSWORD=${this.config.provisioner.password}

# Security
JWT_SECRET=${this.config.app.jwtSecret}
JWT_EXPIRES_IN=24h

# Provisioning Settings
AUTO_PROVISION_ENABLED=true
PROVISIONING_TIMEOUT=300
PROVISIONING_MAX_RETRIES=3
PROVISIONING_ROLLBACK_ENABLED=true
SCHEMA_VALIDATION_ENABLED=true

# Monitoring
METRICS_ENABLED=true
HEALTH_MONITORING_ENABLED=true

# Backup
BACKUP_ENABLED=true
BACKUP_DIR=./backups
BACKUP_RETENTION_DAYS=30

${this.config.email ? `
# Email Configuration
EMAIL_NOTIFICATIONS_ENABLED=true
SMTP_HOST=${this.config.email.host}
SMTP_PORT=${this.config.email.port}
SMTP_USER=${this.config.email.user}
SMTP_PASS=${this.config.email.password}
ADMIN_EMAILS=${this.config.email.adminEmails}
` : '# Email notifications disabled'}

# Feature Flags
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_CUSTOM_REPORTS=true
GDPR_COMPLIANCE_ENABLED=true
AUDIT_LOGGING_ENABLED=true
`;
  }

  generateSecret() {
    return require('crypto').randomBytes(32).toString('hex');
  }

  async prompt(question, defaultValue = '', isPassword = false) {
    return new Promise((resolve) => {
      const displayDefault = defaultValue ? ` (${isPassword ? '***' : defaultValue})` : '';
      this.rl.question(`${question}${displayDefault}: `, (answer) => {
        resolve(answer.trim() || defaultValue);
      });
      
      if (isPassword) {
        this.rl.stdoutMuted = true;
        this.rl._writeToOutput = function _writeToOutput(stringToWrite) {
          if (stringToWrite.charCodeAt(0) === 13) {
            this.output.write('\n');
            this.stdoutMuted = false;
          } else {
            this.output.write('*');
          }
        };
      }
    });
  }
}

// Run setup wizard
if (require.main === module) {
  const wizard = new SetupWizard();
  wizard.run().catch(console.error);
}

module.exports = SetupWizard;
