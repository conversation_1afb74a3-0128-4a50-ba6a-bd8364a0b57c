#!/bin/bash

# HRMS Leave Balance Removal Migration Script
# This script safely removes leave balance functionality from the database
# while preserving core leave management features

set -e  # Exit on any error

# Database connection details
SERVER="**************,2829"
DATABASE="dbHRMS"
USERNAME="userHRMS"
PASSWORD="P@ssw0rd123"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL and capture output
execute_sql() {
    local sql_command="$1"
    local description="$2"

    print_status "$description"

    sqlcmd -S "$SERVER" -d "$DATABASE" -U "$USERNAME" -P "$PASSWORD" -Q "$sql_command" -h -1 -W -C

    if [ $? -eq 0 ]; then
        print_success "$description completed"
    else
        print_error "$description failed"
        exit 1
    fi
}

# Function to execute SQL file
execute_sql_file() {
    local sql_file="$1"
    local description="$2"

    print_status "$description"

    sqlcmd -S "$SERVER" -d "$DATABASE" -U "$USERNAME" -P "$PASSWORD" -i "$sql_file" -C

    if [ $? -eq 0 ]; then
        print_success "$description completed"
    else
        print_error "$description failed"
        exit 1
    fi
}

echo "=========================================="
echo "HRMS Leave Balance Removal Migration"
echo "=========================================="
echo ""

# Step 1: Test database connection
print_status "Testing database connection..."
execute_sql "SELECT 'Connection successful' as Status" "Database connection test"
echo ""

# Step 2: Check current table status
print_status "Checking current database schema..."
execute_sql "SELECT name as TableName FROM sys.tables WHERE name IN ('LeaveRequests', 'LeaveTypes', 'LeaveBalances', 'MonthlyLeaveAllowances', 'MonthlyLeaveUsages') ORDER BY name" "Current table status check"
echo ""

# Step 3: Create backup (optional but recommended)
print_warning "BACKUP RECOMMENDATION:"
echo "Before proceeding, consider creating a database backup:"
echo "BACKUP DATABASE [dbHRMS] TO DISK = 'C:\\backup\\dbHRMS_before_leave_migration.bak'"
echo ""
read -p "Do you want to continue with the migration? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Migration cancelled by user"
    exit 0
fi

# Step 4: Execute the migration
print_status "Executing leave balance removal migration..."
execute_sql_file "backend/HRMS.Infrastructure/Migrations/RemoveLeaveBalanceTables.sql" "Leave balance tables removal"
echo ""

# Step 5: Verify migration results
print_status "Verifying migration results..."
execute_sql "SELECT name as RemainingTables FROM sys.tables WHERE name IN ('LeaveRequests', 'LeaveTypes', 'LeaveBalances', 'MonthlyLeaveAllowances', 'MonthlyLeaveUsages') ORDER BY name" "Post-migration table verification"
echo ""

# Step 6: Check core functionality
print_status "Verifying core leave management tables..."
execute_sql "SELECT COUNT(*) as LeaveRequestsCount FROM LeaveRequests" "LeaveRequests table check"
execute_sql "SELECT COUNT(*) as LeaveTypesCount FROM LeaveTypes" "LeaveTypes table check"
echo ""

# Step 7: Final status
print_success "Migration completed successfully!"
echo ""
echo "=========================================="
echo "MIGRATION SUMMARY"
echo "=========================================="
echo "✅ Core tables preserved:"
echo "   - LeaveRequests (leave applications)"
echo "   - LeaveTypes (leave type definitions)"
echo ""
echo "❌ Balance tables removed:"
echo "   - LeaveBalances"
echo "   - MonthlyLeaveAllowances"
echo "   - MonthlyLeaveUsages"
echo ""
echo "🎯 RESULT: Leave management now works without balance restrictions"
echo ""
print_warning "NEXT STEPS:"
echo "1. Restart your HRMS application"
echo "2. Test leave application functionality"
echo "3. Test leave approval workflow"
echo "4. Verify dashboard displays correctly"
echo ""
print_success "Migration completed! Your HRMS system now allows unrestricted leave applications."
