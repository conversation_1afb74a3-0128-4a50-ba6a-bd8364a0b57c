// Final verification of PlanSquare database cleanup
const API_BASE = 'http://localhost:5020/api/v1';

async function finalVerification() {
    console.log('✅ Final Verification of PlanSquare Database Cleanup...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as PlanSquare Admin
        console.log('\n1️⃣ PLANSQUARE ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`PlanSquare admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`✅ PlanSquare Admin login successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);
        console.log(`   🏢 Organization: ${planSquareData.data.user.organization?.name}`);
        console.log(`   🆔 Organization ID: ${planSquareOrgId}`);

        // Step 2: Get all employees with detailed information
        console.log('\n2️⃣ FETCHING ALL EMPLOYEES');
        console.log('-'.repeat(30));
        
        const employeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!employeesResponse.ok) {
            throw new Error(`Failed to fetch employees: ${employeesResponse.status}`);
        }

        const employeesData = await employeesResponse.json();
        const employees = employeesData.data.items;
        
        console.log(`📊 Total employees in PlanSquare: ${employees.length}`);
        console.log('\n📋 Employee Details:');
        
        employees.forEach((emp, index) => {
            console.log(`\n   ${index + 1}. ${emp.name} (${emp.email})`);
            console.log(`      Role: ${emp.role}`);
            console.log(`      User ID: ${emp.id}`);
            console.log(`      Active: ${emp.isActive ? 'Yes' : 'No'}`);
            console.log(`      Created: ${emp.createdAt ? new Date(emp.createdAt).toLocaleDateString() : 'N/A'}`);
            
            if (emp.employeeDetail) {
                console.log(`      Employee ID: ${emp.employeeDetail.employeeId || 'N/A'}`);
                console.log(`      Job Title: ${emp.employeeDetail.jobTitle || 'N/A'}`);
                console.log(`      Department: ${emp.employeeDetail.department || 'N/A'}`);
                console.log(`      Join Date: ${emp.employeeDetail.joinDate ? new Date(emp.employeeDetail.joinDate).toLocaleDateString() : 'N/A'}`);
                console.log(`      Employment Status: ${emp.employeeDetail.employmentStatus || 'N/A'}`);
            } else {
                console.log(`      Employee Details: Not available`);
            }
        });

        // Step 3: Verify expected employees
        console.log('\n3️⃣ VERIFYING EXPECTED EMPLOYEES');
        console.log('-'.repeat(30));
        
        const expectedEmployees = [
            { email: '<EMAIL>', name: 'Akash Jadhav', role: 'orgadmin' },
            { email: '<EMAIL>', name: 'Abhishek', role: 'employee' },
            { email: '<EMAIL>', name: 'Priya Sharma', role: 'employee' }
        ];

        let allExpectedFound = true;
        
        expectedEmployees.forEach((expected, index) => {
            const found = employees.find(emp => emp.email.toLowerCase() === expected.email.toLowerCase());
            if (found) {
                console.log(`   ✅ ${index + 1}. ${expected.name} (${expected.email}) - Found as ${found.role}`);
            } else {
                console.log(`   ❌ ${index + 1}. ${expected.name} (${expected.email}) - NOT FOUND`);
                allExpectedFound = false;
            }
        });

        // Step 4: Check for unexpected employees
        console.log('\n4️⃣ CHECKING FOR UNEXPECTED EMPLOYEES');
        console.log('-'.repeat(30));
        
        const expectedEmails = expectedEmployees.map(e => e.email.toLowerCase());
        const unexpectedEmployees = employees.filter(emp => 
            !expectedEmails.includes(emp.email.toLowerCase())
        );

        if (unexpectedEmployees.length === 0) {
            console.log('✅ No unexpected employees found');
        } else {
            console.log(`❌ Found ${unexpectedEmployees.length} unexpected employee(s):`);
            unexpectedEmployees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role}`);
            });
        }

        // Step 5: Final assessment
        console.log('\n5️⃣ FINAL ASSESSMENT');
        console.log('-'.repeat(30));
        
        const isCleanupSuccessful = 
            employees.length === 3 && 
            allExpectedFound && 
            unexpectedEmployees.length === 0;

        if (isCleanupSuccessful) {
            console.log('🎉 CLEANUP SUCCESSFUL!');
            console.log('✅ PlanSquare organization has exactly 3 legitimate employees');
            console.log('✅ All expected employees are present');
            console.log('✅ No unexpected employees found');
            console.log('✅ Database is clean and ready for development');
            
            console.log('\n📋 FINAL EMPLOYEE SUMMARY:');
            console.log('1. Akash Jadhav (<EMAIL>) - Organization Admin');
            console.log('2. Abhishek (<EMAIL>) - Employee (Engineering)');
            console.log('3. Priya Sharma (<EMAIL>) - Employee (Design)');
            
            console.log('\n🚀 READY FOR DEVELOPMENT!');
            console.log('The PlanSquare organization database is now in a clean state.');
            console.log('You can proceed with further HRMS development work.');
            
        } else {
            console.log('⚠️  CLEANUP INCOMPLETE');
            console.log(`   Employee count: ${employees.length} (expected: 3)`);
            console.log(`   All expected found: ${allExpectedFound}`);
            console.log(`   Unexpected employees: ${unexpectedEmployees.length}`);
        }

        console.log('\n' + '='.repeat(60));
        console.log('🏁 FINAL VERIFICATION COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ VERIFICATION FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the API connection and try again.');
    }
}

// Run the verification
finalVerification();
