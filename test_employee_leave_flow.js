// Test the complete employee leave flow
async function testEmployeeLeaveFlow() {
    const baseUrl = 'http://localhost:5020/api/v1';
    
    try {
        // Step 1: Login as super admin
        console.log('🔐 Step 1: Logging in as super admin...');
        const superAdminResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'SuperAdmin123!'
            })
        });

        if (!superAdminResponse.ok) {
            console.error('❌ Super admin login failed');
            return;
        }

        const superAdminData = await superAdminResponse.json();
        const superAdminToken = superAdminData.data.token;
        
        console.log('✅ Super admin login successful!');

        // Step 2: Get organizations
        console.log('\n🏢 Step 2: Getting organizations...');
        const orgsResponse = await fetch(`${baseUrl}/super-admin/organizations`, {
            headers: { 'Authorization': `Bearer ${superAdminToken}` }
        });

        if (!orgsResponse.ok) {
            console.error('❌ Failed to get organizations:', orgsResponse.status);
            return;
        }

        const orgsData = await orgsResponse.json();
        const organizations = orgsData.data?.organizations || [];
        
        console.log(`Found ${organizations.length} organizations:`);
        organizations.forEach((org, index) => {
            console.log(`  ${index + 1}. ${org.name} (${org.id}) - ${org.domain}`);
        });

        if (organizations.length === 0) {
            console.error('❌ No organizations found');
            return;
        }

        // Use the first organization
        const targetOrg = organizations[0];
        console.log(`\n🎯 Using organization: ${targetOrg.name} (${targetOrg.id})`);

        // Step 3: Get organization details to find admin credentials
        console.log('\n👤 Step 3: Getting organization admin credentials...');
        const adminCredsResponse = await fetch(`${baseUrl}/super-admin/organizations/${targetOrg.id}/admin-credentials`, {
            headers: { 'Authorization': `Bearer ${superAdminToken}` }
        });

        if (!adminCredsResponse.ok) {
            console.error('❌ Failed to get admin credentials:', adminCredsResponse.status);
            return;
        }

        const adminCredsData = await adminCredsResponse.json();
        const adminEmail = adminCredsData.data?.email;
        
        console.log(`✅ Found admin email: ${adminEmail}`);

        // Step 4: Reset admin password to get a new one
        console.log('\n🔑 Step 4: Resetting admin password...');
        const resetPasswordResponse = await fetch(`${baseUrl}/super-admin/organizations/${targetOrg.id}/reset-admin-password`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${superAdminToken}` }
        });

        if (!resetPasswordResponse.ok) {
            console.error('❌ Failed to reset admin password:', resetPasswordResponse.status);
            return;
        }

        const resetPasswordData = await resetPasswordResponse.json();
        const newAdminPassword = resetPasswordData.data?.temporaryPassword;
        
        console.log(`✅ New admin password: ${newAdminPassword}`);

        // Step 5: Login as organization admin
        console.log('\n🔐 Step 5: Logging in as organization admin...');
        const adminLoginResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: adminEmail,
                password: newAdminPassword
            })
        });

        if (!adminLoginResponse.ok) {
            console.error('❌ Admin login failed:', adminLoginResponse.status);
            return;
        }

        const adminLoginData = await adminLoginResponse.json();
        const adminToken = adminLoginData.data.token;
        const adminOrgId = adminLoginData.data.user.organization?.id;
        
        console.log(`✅ Admin login successful! Organization ID: ${adminOrgId}`);

        // Step 6: Create a test employee (or use existing one)
        console.log('\n👤 Step 6: Creating test employee...');
        const testEmail = `test.employee.${Date.now()}@example.com`;
        const createEmployeeResponse = await fetch(`${baseUrl}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'X-Organization-ID': adminOrgId,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'Test Employee',
                email: testEmail,
                role: 'Employee',
                employeeDetails: {
                    employeeId: `EMP_TEST_${Date.now()}`,
                    jobTitle: 'Software Developer',
                    department: 'Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 75000,
                    annualCTC: 90000
                }
            })
        });

        let employeeCredentials;
        if (!createEmployeeResponse.ok) {
            console.log('❌ Employee creation failed, trying to use existing employee...');
            // Try to use existing employee - let's try common test credentials
            employeeCredentials = {
                email: '<EMAIL>',
                password: 'TempPass123!' // Common generated password pattern
            };
            console.log(`   Using existing employee: ${employeeCredentials.email}`);
        } else {
            const createEmployeeData = await createEmployeeResponse.json();
            console.log('   Raw response:', JSON.stringify(createEmployeeData, null, 2));

            // Extract credentials from the response
            const responseData = createEmployeeData.data;
            employeeCredentials = {
                email: responseData.loginUsername || responseData.email,
                password: responseData.loginTemporaryPassword || responseData.tempPassword
            };

            if (!employeeCredentials.email || !employeeCredentials.password) {
                console.log('❌ Could not find credentials in response, using fallback');
                employeeCredentials = {
                    email: testEmail,
                    password: 'TempPass123!' // Fallback password
                };
            }

            console.log(`✅ Employee created successfully!`);
            console.log(`   Email: ${employeeCredentials.email}`);
            console.log(`   Password: ${employeeCredentials.password || 'Not provided'}`);
        }

        // Step 7: Login as employee
        console.log('\n🔐 Step 7: Logging in as employee...');
        const employeeLoginResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: employeeCredentials.email,
                password: employeeCredentials.password
            })
        });

        if (!employeeLoginResponse.ok) {
            console.error('❌ Employee login failed:', employeeLoginResponse.status);
            return;
        }

        const employeeLoginData = await employeeLoginResponse.json();
        const employeeToken = employeeLoginData.data.token;
        const employeeUserId = employeeLoginData.data.user.id;
        const employeeOrgId = employeeLoginData.data.user.organization?.id;
        
        console.log(`✅ Employee login successful!`);
        console.log(`   User ID: ${employeeUserId}`);
        console.log(`   Organization ID: ${employeeOrgId}`);

        // Step 8: Test leave requests (should be empty initially)
        console.log('\n📋 Step 8: Testing employee leave requests (should be empty)...');
        const initialLeaveResponse = await fetch(`${baseUrl}/leave/requests`, {
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId
            }
        });

        if (!initialLeaveResponse.ok) {
            console.error('❌ Failed to get initial leave requests:', initialLeaveResponse.status);
            const errorText = await initialLeaveResponse.text();
            console.error('   Error details:', errorText);
            return;
        }

        const initialLeaveData = await initialLeaveResponse.json();
        console.log(`✅ Initial leave requests response received`);
        console.log(`   Success: ${initialLeaveData.success}`);
        console.log(`   Number of requests: ${initialLeaveData.data?.requests?.length || 0}`);

        // Step 9: Apply for leave (just 1 day to avoid balance issues)
        console.log('\n📝 Step 9: Applying for leave (1 day)...');
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dayAfter = new Date(tomorrow);
        dayAfter.setDate(dayAfter.getDate()); // Same day for 1-day leave

        const applyLeaveResponse = await fetch(`${baseUrl}/leave/apply`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                fromDate: tomorrow.toISOString(),
                toDate: tomorrow.toISOString(), // Same day for 1-day leave
                durationType: 'full-day',
                reason: 'Test leave application for debugging (1 day)'
            })
        });

        if (!applyLeaveResponse.ok) {
            console.error('❌ Leave application failed:', applyLeaveResponse.status);
            const errorText = await applyLeaveResponse.text();
            console.error('   Error details:', errorText);
            return;
        }

        const applyLeaveData = await applyLeaveResponse.json();
        console.log(`✅ Leave application successful!`);
        console.log(`   Leave ID: ${applyLeaveData.data?.id}`);
        console.log(`   Status: ${applyLeaveData.data?.status}`);

        // Step 10: Test leave requests again (should now have 1 request)
        console.log('\n📋 Step 10: Testing employee leave requests after applying...');
        const afterLeaveResponse = await fetch(`${baseUrl}/leave/requests`, {
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId
            }
        });

        if (!afterLeaveResponse.ok) {
            console.error('❌ Failed to get leave requests after applying:', afterLeaveResponse.status);
            const errorText = await afterLeaveResponse.text();
            console.error('   Error details:', errorText);
            return;
        }

        const afterLeaveData = await afterLeaveResponse.json();
        console.log(`✅ After leave application response received`);
        console.log(`   Success: ${afterLeaveData.success}`);
        console.log(`   Raw response:`, JSON.stringify(afterLeaveData, null, 2));
        console.log(`   Number of requests: ${afterLeaveData.data?.requests?.length || 0}`);

        if (afterLeaveData.data?.requests?.length > 0) {
            console.log('   Leave requests found:');
            afterLeaveData.data.requests.forEach((request, index) => {
                console.log(`     ${index + 1}. ID: ${request.id}`);
                console.log(`        Status: ${request.status}`);
                console.log(`        From: ${request.fromDate} To: ${request.toDate}`);
                console.log(`        Reason: ${request.reason}`);
            });
            console.log('\n🎉 SUCCESS: Employee can see their leave history!');
        } else {
            console.log('\n❌ ISSUE CONFIRMED: Employee cannot see their leave history!');
        }

    } catch (error) {
        console.error('❌ Error during test:', error.message);
    }
}

testEmployeeLeaveFlow();
