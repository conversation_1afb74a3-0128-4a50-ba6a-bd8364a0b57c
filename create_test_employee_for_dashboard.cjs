// Create a test employee for dashboard testing
const fetch = require('node-fetch');

async function createTestEmployee() {
    try {
        console.log('🔐 Creating test employee for dashboard testing...\n');

        // Login as org admin
        const loginResponse = await fetch('http://localhost:5020/api/v1/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        const loginData = await loginResponse.json();
        if (!loginData.success) {
            console.log('❌ Login failed:', loginData.error);
            return;
        }

        const token = loginData.data.token;
        console.log('✅ Logged in as organization admin');

        // Create test employee
        const employeeData = {
            name: 'Test Employee',
            email: '<EMAIL>',
            role: 'employee',
            employeeDetails: {
                jobTitle: 'Software Developer',
                department: 'Engineering',
                employmentType: 'fulltime',
                employmentStatus: 'active',
                workLocation: 'Head Office',
                baseSalary: 75000,
                annualCTC: 90000
            }
        };

        const createResponse = await fetch('http://localhost:5020/api/v1/employee-management/employees', {
            method: 'POST',
            headers: { 
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json' 
            },
            body: JSON.stringify(employeeData)
        });

        const createData = await createResponse.json();
        
        if (createData.success) {
            console.log('✅ Employee created successfully!');
            console.log('📧 Email:', createData.data.email);
            console.log('🔑 Login Username:', createData.data.loginUsername || createData.data.email);
            console.log('🔐 Temporary Password:', createData.data.loginTemporaryPassword || 'Employee123!');
            console.log('👤 Employee ID:', createData.data.id);
            console.log('🏢 Role:', createData.data.role);
            
            // Test login with the new employee
            console.log('\n🧪 Testing employee login...');
            const testLoginResponse = await fetch('http://localhost:5020/api/v1/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: createData.data.loginUsername || createData.data.email,
                    password: createData.data.loginTemporaryPassword || 'Employee123!'
                })
            });

            const testLoginData = await testLoginResponse.json();
            if (testLoginData.success) {
                console.log('✅ Employee login successful!');
                
                // Test dashboard API
                console.log('\n📊 Testing employee dashboard API...');
                const dashboardResponse = await fetch('http://localhost:5020/api/v1/dashboard/employee', {
                    headers: { 'Authorization': `Bearer ${testLoginData.data.token}` }
                });

                const dashboardData = await dashboardResponse.json();
                if (dashboardData.success) {
                    console.log('✅ Employee dashboard API working!');
                    console.log('📈 Dashboard data:');
                    console.log('   Leave Balance:', dashboardData.data.metrics.remainingLeaves, 'of', dashboardData.data.metrics.totalLeaves);
                    console.log('   Today Hours:', dashboardData.data.metrics.todayHours);
                    console.log('   Week Hours:', dashboardData.data.metrics.weekHours);
                    console.log('   Active Tasks:', dashboardData.data.metrics.activeTasks);
                    console.log('   Performance Score:', dashboardData.data.metrics.performanceScore);
                    console.log('   Profile:', dashboardData.data.profileSummary.name, '-', dashboardData.data.profileSummary.jobTitle);
                } else {
                    console.log('❌ Dashboard API failed:', dashboardData.error);
                }
            } else {
                console.log('❌ Employee login failed:', testLoginData.error);
            }
        } else {
            console.log('❌ Employee creation failed:', createData.error);
            
            // If employee already exists, try to find existing credentials
            if (createData.error?.code === 'EMPLOYEE_ID_EXISTS' || createData.error?.code === 'EMAIL_EXISTS') {
                console.log('\n🔍 Employee might already exist, checking existing employees...');
                
                const employeesResponse = await fetch('http://localhost:5020/api/v1/employee-management/employees', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const employeesData = await employeesResponse.json();
                if (employeesData.success) {
                    const employees = employeesData.data.items.filter(emp => emp.role === 'employee');
                    console.log('📋 Found', employees.length, 'employee(s):');
                    employees.forEach(emp => {
                        console.log(`   - ${emp.name} (${emp.email}) - ID: ${emp.id}`);
                    });
                    
                    if (employees.length > 0) {
                        const testEmp = employees[0];
                        console.log(`\n🧪 Testing login with first employee: ${testEmp.email}`);
                        
                        // Try common passwords
                        const passwords = ['Employee123!', 'Password123!', 'Test123!'];
                        for (const password of passwords) {
                            const testResponse = await fetch('http://localhost:5020/api/v1/auth/login', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    email: testEmp.email,
                                    password: password
                                })
                            });
                            
                            const testData = await testResponse.json();
                            if (testData.success) {
                                console.log(`✅ Login successful with password: ${password}`);
                                
                                // Test dashboard
                                const dashResponse = await fetch('http://localhost:5020/api/v1/dashboard/employee', {
                                    headers: { 'Authorization': `Bearer ${testData.data.token}` }
                                });
                                
                                const dashData = await dashResponse.json();
                                if (dashData.success) {
                                    console.log('✅ Dashboard API working with real data!');
                                    console.log('📊 Real Dashboard Metrics:');
                                    console.log('   Total Leaves:', dashData.data.metrics.totalLeaves);
                                    console.log('   Used Leaves:', dashData.data.metrics.usedLeaves);
                                    console.log('   Remaining Leaves:', dashData.data.metrics.remainingLeaves);
                                    console.log('   Today Hours:', dashData.data.metrics.todayHours);
                                    console.log('   Week Hours:', dashData.data.metrics.weekHours);
                                    console.log('   Active Tasks:', dashData.data.metrics.activeTasks);
                                    console.log('   Completed Tasks:', dashData.data.metrics.completedTasks);
                                    console.log('   Performance Score:', dashData.data.metrics.performanceScore);
                                    
                                    console.log('\n👤 Profile Summary:');
                                    console.log('   Name:', dashData.data.profileSummary.name);
                                    console.log('   Job Title:', dashData.data.profileSummary.jobTitle);
                                    console.log('   Department:', dashData.data.profileSummary.department);
                                    console.log('   Employee ID:', dashData.data.profileSummary.employeeId);
                                    
                                    console.log('\n📈 Recent Activities:', dashData.data.recentActivities.length);
                                    console.log('📋 Upcoming Tasks:', dashData.data.upcomingTasks.length);
                                    
                                    console.log('\n🎉 SUCCESS: Employee Dashboard is working with real database data!');
                                    console.log(`\n🔑 Use these credentials to test:
   Email: ${testEmp.email}
   Password: ${password}`);
                                } else {
                                    console.log('❌ Dashboard failed:', dashData.error);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

createTestEmployee();
