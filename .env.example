# HRMS Database Provisioning Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
NODE_ENV=development

# Application Port
PORT=3000

# Application Name
APP_NAME=HRMS Database Provisioning

# Application Version
APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Development Database (SQL Server)
DB_DEV_HOST=your-sql-server-host
DB_DEV_PORT=1433
DB_DEV_DATABASE=hrms_master_dev
DB_DEV_USERNAME=your_sql_server_username
DB_DEV_PASSWORD=your_sql_server_password

# Staging Database (SQL Server)
DB_STAGING_HOST=your-staging-sql-server-host
DB_STAGING_PORT=1433
DB_STAGING_DATABASE=hrms_master_staging
DB_STAGING_USERNAME=your_staging_sql_server_username
DB_STAGING_PASSWORD=your_staging_sql_server_password

# Production Database (SQL Server)
DB_PROD_HOST=your-production-sql-server-host
DB_PROD_PORT=1433
DB_PROD_DATABASE=hrms_master_prod
DB_PROD_USERNAME=your_production_sql_server_username
DB_PROD_PASSWORD=your_production_sql_server_password

# Database Provisioning User (with elevated privileges for schema creation)
DB_PROVISIONER_USERNAME=your_sql_server_admin_username
DB_PROVISIONER_PASSWORD=your_sql_server_admin_password

# Database Connection Pool Settings
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=300000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level (debug, info, warn, error)
LOG_LEVEL=info

# Log Directory
LOG_DIR=./logs

# Log File Max Size (in bytes)
LOG_MAX_SIZE=10485760

# Log File Max Files
LOG_MAX_FILES=10

# Enable Console Logging
LOG_CONSOLE=true

# Enable File Logging
LOG_FILE=true

# =============================================================================
# MONITORING & METRICS
# =============================================================================

# Enable Prometheus Metrics
METRICS_ENABLED=true

# Metrics Endpoint Path
METRICS_PATH=/metrics

# Health Check Endpoint
HEALTH_CHECK_PATH=/health

# Monitoring Port (if different from main app)
MONITORING_PORT=3001

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# JWT Secret for API authentication
JWT_SECRET=your_jwt_secret_key_here

# JWT Expiration Time
JWT_EXPIRES_IN=24h

# API Rate Limiting (requests per hour)
RATE_LIMIT_REQUESTS=1000

# API Rate Limit Window (in milliseconds)
RATE_LIMIT_WINDOW=3600000

# Enable CORS
CORS_ENABLED=true

# CORS Origin (comma-separated for multiple origins)
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# =============================================================================
# PROVISIONING SETTINGS
# =============================================================================

# Enable Auto Provisioning
AUTO_PROVISION_ENABLED=true

# Provisioning Timeout (in seconds)
PROVISIONING_TIMEOUT=300

# Max Retry Attempts
PROVISIONING_MAX_RETRIES=3

# Retry Delay (in seconds)
PROVISIONING_RETRY_DELAY=5

# Enable Rollback on Failure
PROVISIONING_ROLLBACK_ENABLED=true

# Enable Schema Validation
SCHEMA_VALIDATION_ENABLED=true

# Max Concurrent Provisioning Operations
MAX_CONCURRENT_PROVISIONS=3

# =============================================================================
# BACKUP SETTINGS
# =============================================================================

# Enable Automatic Backups
BACKUP_ENABLED=true

# Backup Directory
BACKUP_DIR=/var/backups/hrms/schemas

# Backup Retention (in days)
BACKUP_RETENTION_DAYS=30

# Backup Compression
BACKUP_COMPRESS=true

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Enable Email Notifications
EMAIL_NOTIFICATIONS_ENABLED=true

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Admin Email Addresses (comma-separated)
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Webhook URL for Notifications
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Enable Slack Notifications
SLACK_NOTIFICATIONS_ENABLED=false

# Slack Webhook URL
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================

# Enable API Access
API_ACCESS_ENABLED=true

# API Base URL
API_BASE_URL=https://api.hrms.com/v1

# External Service URLs
EXTERNAL_AUTH_SERVICE=https://auth.hrms.com
EXTERNAL_BILLING_SERVICE=https://billing.hrms.com

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable Debug Mode
DEBUG_MODE=false

# Enable SQL Logging
SQL_LOGGING=false

# Enable Performance Profiling
PERFORMANCE_PROFILING=false

# Generate Test Data
GENERATE_TEST_DATA=false

# Test Data Size (small, medium, large)
TEST_DATA_SIZE=small

# Auto Cleanup Test Schemas
AUTO_CLEANUP_TEST_SCHEMAS=true

# Test Schema Prefix
TEST_SCHEMA_PREFIX=test_

# Cleanup After Hours
CLEANUP_AFTER_HOURS=24

# =============================================================================
# REDIS CONFIGURATION (Optional - for caching and sessions)
# =============================================================================

# Redis Host (Optional - for caching and sessions)
REDIS_HOST=your-redis-host-or-localhost

# Redis Port
REDIS_PORT=6379

# Redis Password (leave empty if no password)
REDIS_PASSWORD=

# Redis Database Number
REDIS_DB=0

# Redis Connection Timeout
REDIS_TIMEOUT=5000

# =============================================================================
# FILE STORAGE SETTINGS
# =============================================================================

# File Storage Type (local, s3, gcs)
FILE_STORAGE_TYPE=local

# Local Storage Path
LOCAL_STORAGE_PATH=./uploads

# AWS S3 Configuration (if using S3)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=hrms-file-storage

# Google Cloud Storage (if using GCS)
GCS_PROJECT_ID=your_gcs_project_id
GCS_BUCKET_NAME=hrms-file-storage
GCS_KEY_FILE=./gcs-key.json

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable Schema Templates
FEATURE_SCHEMA_TEMPLATES=false

# Enable Dynamic Scaling
FEATURE_DYNAMIC_SCALING=false

# Enable Cross Schema Queries
FEATURE_CROSS_SCHEMA_QUERIES=false

# Enable Advanced Analytics
FEATURE_ADVANCED_ANALYTICS=true

# Enable Custom Reports
FEATURE_CUSTOM_REPORTS=true

# Enable Mobile API
FEATURE_MOBILE_API=true

# =============================================================================
# COMPLIANCE & AUDIT
# =============================================================================

# Enable GDPR Compliance
GDPR_COMPLIANCE_ENABLED=true

# Data Retention Period (in years)
DATA_RETENTION_YEARS=7

# Enable Audit Logging
AUDIT_LOGGING_ENABLED=true

# Audit Log Retention (in days)
AUDIT_LOG_RETENTION_DAYS=2555

# Enable Data Export
DATA_EXPORT_ENABLED=true

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Enable Query Optimization
QUERY_OPTIMIZATION_ENABLED=true

# Enable Connection Pooling
CONNECTION_POOLING_ENABLED=true

# Enable Caching
CACHING_ENABLED=true

# Cache TTL (in seconds)
CACHE_TTL=3600

# Enable Compression
COMPRESSION_ENABLED=true

# =============================================================================
# MAINTENANCE SETTINGS
# =============================================================================

# Maintenance Mode
MAINTENANCE_MODE=false

# Maintenance Message
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# Maintenance Window Start (24-hour format)
MAINTENANCE_WINDOW_START=02:00

# Maintenance Window End (24-hour format)
MAINTENANCE_WINDOW_END=04:00

# Enable System Health Monitoring
HEALTH_MONITORING_ENABLED=true

# Health Check Interval (in seconds)
HEALTH_CHECK_INTERVAL=60
