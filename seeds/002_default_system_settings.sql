-- Seed 002: Default System Settings
-- Inserts default organization settings and configurations
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Insert default organization settings
INSERT INTO organization_settings (organization_id, setting_key, setting_value, setting_type) VALUES
-- General Settings
('{ORG_ID}', 'company_name', 'Organization Name', 'string'),
('{ORG_ID}', 'company_address', '123 Business Street, City, State, Country', 'string'),
('{ORG_ID}', 'company_phone', '******-123-4567', 'string'),
('{ORG_ID}', 'company_email', '<EMAIL>', 'string'),
('{ORG_ID}', 'company_website', 'https://www.organization.com', 'string'),
('{ORG_ID}', 'company_logo_url', '', 'string'),
('{ORG_ID}', 'timezone', 'UTC', 'string'),
('{ORG_ID}', 'date_format', 'YYYY-MM-DD', 'string'),
('{ORG_ID}', 'currency', 'USD', 'string'),
('{ORG_ID}', 'language', 'en', 'string'),

-- Working Hours Settings
('{ORG_ID}', 'default_working_hours_start', '09:00', 'string'),
('{ORG_ID}', 'default_working_hours_end', '18:00', 'string'),
('{ORG_ID}', 'working_days_per_week', '5', 'number'),
('{ORG_ID}', 'weekend_days', '["Saturday", "Sunday"]', 'json'),
('{ORG_ID}', 'lunch_break_duration', '60', 'number'),
('{ORG_ID}', 'minimum_break_duration', '15', 'number'),
('{ORG_ID}', 'overtime_threshold_hours', '8', 'number'),
('{ORG_ID}', 'overtime_rate_multiplier', '1.5', 'number'),

-- Attendance Settings
('{ORG_ID}', 'attendance_tracking_enabled', 'true', 'boolean'),
('{ORG_ID}', 'late_arrival_threshold_minutes', '15', 'number'),
('{ORG_ID}', 'early_departure_threshold_minutes', '30', 'number'),
('{ORG_ID}', 'auto_checkout_enabled', 'false', 'boolean'),
('{ORG_ID}', 'auto_checkout_time', '20:00', 'string'),
('{ORG_ID}', 'attendance_grace_period_minutes', '10', 'number'),
('{ORG_ID}', 'biometric_attendance_enabled', 'false', 'boolean'),
('{ORG_ID}', 'mobile_attendance_enabled', 'true', 'boolean'),
('{ORG_ID}', 'location_based_attendance', 'false', 'boolean'),

-- Leave Management Settings
('{ORG_ID}', 'leave_approval_required', 'true', 'boolean'),
('{ORG_ID}', 'leave_auto_approval_threshold_days', '1', 'number'),
('{ORG_ID}', 'leave_advance_booking_days', '30', 'number'),
('{ORG_ID}', 'leave_carry_forward_enabled', 'true', 'boolean'),
('{ORG_ID}', 'leave_encashment_enabled', 'true', 'boolean'),
('{ORG_ID}', 'leave_proration_enabled', 'true', 'boolean'),
('{ORG_ID}', 'leave_weekend_inclusion', 'false', 'boolean'),
('{ORG_ID}', 'leave_holiday_inclusion', 'false', 'boolean'),
('{ORG_ID}', 'sick_leave_medical_certificate_days', '3', 'number'),

-- Payroll Settings
('{ORG_ID}', 'payroll_frequency', 'monthly', 'string'),
('{ORG_ID}', 'payroll_cutoff_date', '25', 'number'),
('{ORG_ID}', 'payroll_payment_date', '1', 'number'),
('{ORG_ID}', 'payroll_auto_calculation', 'true', 'boolean'),
('{ORG_ID}', 'tax_calculation_enabled', 'true', 'boolean'),
('{ORG_ID}', 'provident_fund_enabled', 'true', 'boolean'),
('{ORG_ID}', 'provident_fund_employee_percentage', '12', 'number'),
('{ORG_ID}', 'provident_fund_employer_percentage', '12', 'number'),
('{ORG_ID}', 'professional_tax_enabled', 'true', 'boolean'),
('{ORG_ID}', 'gratuity_enabled', 'true', 'boolean'),

-- Performance Management Settings
('{ORG_ID}', 'performance_review_frequency', 'quarterly', 'string'),
('{ORG_ID}', 'performance_rating_scale', '5', 'number'),
('{ORG_ID}', 'goal_setting_enabled', 'true', 'boolean'),
('{ORG_ID}', 'peer_review_enabled', 'false', 'boolean'),
('{ORG_ID}', 'self_assessment_enabled', 'true', 'boolean'),
('{ORG_ID}', 'performance_improvement_plan_enabled', 'true', 'boolean'),
('{ORG_ID}', 'performance_bonus_enabled', 'true', 'boolean'),

-- Task Management Settings
('{ORG_ID}', 'task_management_enabled', 'true', 'boolean'),
('{ORG_ID}', 'task_time_tracking_enabled', 'true', 'boolean'),
('{ORG_ID}', 'task_auto_assignment_enabled', 'false', 'boolean'),
('{ORG_ID}', 'task_notification_enabled', 'true', 'boolean'),
('{ORG_ID}', 'task_deadline_reminder_days', '3', 'number'),
('{ORG_ID}', 'task_overdue_escalation_enabled', 'true', 'boolean'),

-- Recruitment Settings
('{ORG_ID}', 'recruitment_enabled', 'true', 'boolean'),
('{ORG_ID}', 'job_posting_approval_required', 'true', 'boolean'),
('{ORG_ID}', 'candidate_assessment_enabled', 'true', 'boolean'),
('{ORG_ID}', 'interview_scheduling_enabled', 'true', 'boolean'),
('{ORG_ID}', 'background_check_enabled', 'false', 'boolean'),
('{ORG_ID}', 'offer_letter_template_enabled', 'true', 'boolean'),

-- Notification Settings
('{ORG_ID}', 'email_notifications_enabled', 'true', 'boolean'),
('{ORG_ID}', 'sms_notifications_enabled', 'false', 'boolean'),
('{ORG_ID}', 'push_notifications_enabled', 'true', 'boolean'),
('{ORG_ID}', 'notification_digest_frequency', 'daily', 'string'),
('{ORG_ID}', 'leave_approval_notification', 'true', 'boolean'),
('{ORG_ID}', 'payroll_notification', 'true', 'boolean'),
('{ORG_ID}', 'birthday_notification', 'true', 'boolean'),
('{ORG_ID}', 'work_anniversary_notification', 'true', 'boolean'),

-- Security Settings
('{ORG_ID}', 'password_min_length', '8', 'number'),
('{ORG_ID}', 'password_require_uppercase', 'true', 'boolean'),
('{ORG_ID}', 'password_require_lowercase', 'true', 'boolean'),
('{ORG_ID}', 'password_require_numbers', 'true', 'boolean'),
('{ORG_ID}', 'password_require_special_chars', 'true', 'boolean'),
('{ORG_ID}', 'password_expiry_days', '90', 'number'),
('{ORG_ID}', 'session_timeout_minutes', '30', 'number'),
('{ORG_ID}', 'two_factor_authentication_enabled', 'false', 'boolean'),
('{ORG_ID}', 'login_attempt_limit', '5', 'number'),
('{ORG_ID}', 'account_lockout_duration_minutes', '30', 'number'),

-- Integration Settings
('{ORG_ID}', 'slack_integration_enabled', 'false', 'boolean'),
('{ORG_ID}', 'slack_webhook_url', '', 'string'),
('{ORG_ID}', 'google_calendar_integration', 'false', 'boolean'),
('{ORG_ID}', 'microsoft_teams_integration', 'false', 'boolean'),
('{ORG_ID}', 'zoom_integration_enabled', 'false', 'boolean'),
('{ORG_ID}', 'api_access_enabled', 'true', 'boolean'),
('{ORG_ID}', 'webhook_notifications_enabled', 'false', 'boolean'),

-- Compliance Settings
('{ORG_ID}', 'data_retention_period_years', '7', 'number'),
('{ORG_ID}', 'audit_log_enabled', 'true', 'boolean'),
('{ORG_ID}', 'gdpr_compliance_enabled', 'true', 'boolean'),
('{ORG_ID}', 'data_export_enabled', 'true', 'boolean'),
('{ORG_ID}', 'employee_data_access_enabled', 'true', 'boolean'),

-- Mobile App Settings
('{ORG_ID}', 'mobile_app_enabled', 'true', 'boolean'),
('{ORG_ID}', 'mobile_attendance_enabled', 'true', 'boolean'),
('{ORG_ID}', 'mobile_leave_request_enabled', 'true', 'boolean'),
('{ORG_ID}', 'mobile_payslip_access_enabled', 'true', 'boolean'),
('{ORG_ID}', 'mobile_task_management_enabled', 'true', 'boolean'),
('{ORG_ID}', 'mobile_offline_mode_enabled', 'false', 'boolean'),

-- Reporting Settings
('{ORG_ID}', 'custom_reports_enabled', 'true', 'boolean'),
('{ORG_ID}', 'automated_reports_enabled', 'true', 'boolean'),
('{ORG_ID}', 'report_export_formats', '["PDF", "Excel", "CSV"]', 'json'),
('{ORG_ID}', 'dashboard_refresh_interval_minutes', '15', 'number'),
('{ORG_ID}', 'analytics_enabled', 'true', 'boolean'),

-- Backup and Maintenance
('{ORG_ID}', 'auto_backup_enabled', 'true', 'boolean'),
('{ORG_ID}', 'backup_frequency', 'daily', 'string'),
('{ORG_ID}', 'backup_retention_days', '30', 'number'),
('{ORG_ID}', 'maintenance_window_start', '02:00', 'string'),
('{ORG_ID}', 'maintenance_window_end', '04:00', 'string'),
('{ORG_ID}', 'system_health_monitoring', 'true', 'boolean');

-- Insert default performance metrics for the organization
INSERT INTO performance_metrics (organization_id, metric_name, metric_description, metric_type, measurement_scale, is_active) VALUES
('{ORG_ID}', 'Quality of Work', 'Assessment of work quality and attention to detail', 'qualitative', '1-5', true),
('{ORG_ID}', 'Productivity', 'Measurement of output and efficiency', 'quantitative', '1-5', true),
('{ORG_ID}', 'Communication Skills', 'Effectiveness in verbal and written communication', 'qualitative', '1-5', true),
('{ORG_ID}', 'Teamwork', 'Collaboration and team contribution', 'behavioral', '1-5', true),
('{ORG_ID}', 'Leadership', 'Leadership qualities and mentoring abilities', 'behavioral', '1-5', true),
('{ORG_ID}', 'Innovation', 'Creative thinking and problem-solving', 'qualitative', '1-5', true),
('{ORG_ID}', 'Punctuality', 'Timeliness and reliability', 'behavioral', '1-5', true),
('{ORG_ID}', 'Goal Achievement', 'Success in meeting set objectives', 'quantitative', 'percentage', true),
('{ORG_ID}', 'Customer Satisfaction', 'Client/customer feedback and satisfaction', 'quantitative', '1-10', true),
('{ORG_ID}', 'Technical Skills', 'Proficiency in required technical competencies', 'quantitative', '1-5', true);

-- Insert default task categories
INSERT INTO task_categories (organization_id, name, description, color_code, is_active) VALUES
('{ORG_ID}', 'Development', 'Software development and coding tasks', '#3498db', true),
('{ORG_ID}', 'Testing', 'Quality assurance and testing activities', '#e74c3c', true),
('{ORG_ID}', 'Documentation', 'Documentation and knowledge management', '#f39c12', true),
('{ORG_ID}', 'Meeting', 'Meetings and collaborative sessions', '#9b59b6', true),
('{ORG_ID}', 'Research', 'Research and analysis tasks', '#1abc9c', true),
('{ORG_ID}', 'Training', 'Learning and skill development', '#2ecc71', true),
('{ORG_ID}', 'Support', 'Customer support and maintenance', '#34495e', true),
('{ORG_ID}', 'Planning', 'Project planning and strategy', '#e67e22', true),
('{ORG_ID}', 'Review', 'Code review and quality checks', '#8e44ad', true),
('{ORG_ID}', 'Administrative', 'Administrative and operational tasks', '#95a5a6', true);

-- Insert default salary components
INSERT INTO salary_components (organization_id, component_name, component_type, calculation_type, calculation_value, is_taxable, is_active) VALUES
-- Earnings
('{ORG_ID}', 'Basic Salary', 'earning', 'fixed', 0, true, true),
('{ORG_ID}', 'House Rent Allowance', 'earning', 'percentage', 40, true, true),
('{ORG_ID}', 'Transport Allowance', 'earning', 'fixed', 1600, false, true),
('{ORG_ID}', 'Medical Allowance', 'earning', 'fixed', 1250, false, true),
('{ORG_ID}', 'Special Allowance', 'earning', 'percentage', 20, true, true),
('{ORG_ID}', 'Performance Bonus', 'earning', 'fixed', 0, true, true),
('{ORG_ID}', 'Overtime Pay', 'earning', 'formula', 0, true, true),

-- Deductions
('{ORG_ID}', 'Provident Fund', 'deduction', 'percentage', 12, false, true),
('{ORG_ID}', 'Professional Tax', 'deduction', 'fixed', 200, false, true),
('{ORG_ID}', 'Income Tax', 'deduction', 'formula', 0, false, true),
('{ORG_ID}', 'Health Insurance', 'deduction', 'fixed', 500, false, true),
('{ORG_ID}', 'Life Insurance', 'deduction', 'fixed', 300, false, true),

-- Employer Contributions
('{ORG_ID}', 'Employer PF Contribution', 'employer_contribution', 'percentage', 12, false, true),
('{ORG_ID}', 'Employer ESI Contribution', 'employer_contribution', 'percentage', 3.25, false, true),
('{ORG_ID}', 'Gratuity', 'employer_contribution', 'percentage', 4.81, false, true);

COMMIT;
