-- Seed 003: Default Subscription Plans
-- Inserts default subscription plans and billing information
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price_monthly, price_yearly, employee_limit, features, is_active) VALUES
('Starter', 
 'Perfect for small teams getting started with HR management', 
 29.99, 
 299.99, 
 25, 
 '{
   "features": [
     "Employee Management",
     "Basic Attendance Tracking",
     "Leave Management",
     "Basic Reporting",
     "Email Support"
   ],
   "limits": {
     "employees": 25,
     "storage_gb": 5,
     "api_calls_per_month": 1000,
     "custom_fields": 10
   },
   "integrations": [
     "Google Calendar",
     "Basic Email"
   ]
 }'::jsonb, 
 true),

('Professional', 
 'Comprehensive HR solution for growing businesses', 
 79.99, 
 799.99, 
 100, 
 '{
   "features": [
     "Employee Management",
     "Advanced Attendance Tracking",
     "Leave Management",
     "Payroll Management",
     "Performance Reviews",
     "Task Management",
     "Advanced Reporting",
     "Custom Workflows",
     "Priority Support"
   ],
   "limits": {
     "employees": 100,
     "storage_gb": 25,
     "api_calls_per_month": 10000,
     "custom_fields": 50
   },
   "integrations": [
     "Google Calendar",
     "Slack",
     "Microsoft Teams",
     "Zoom",
     "Advanced Email"
   ]
 }'::jsonb, 
 true),

('Enterprise', 
 'Full-featured solution for large organizations', 
 149.99, 
 1499.99, 
 500, 
 '{
   "features": [
     "Employee Management",
     "Advanced Attendance Tracking",
     "Leave Management",
     "Payroll Management",
     "Performance Reviews",
     "Task Management",
     "Recruitment Management",
     "Advanced Analytics",
     "Custom Reporting",
     "Workflow Automation",
     "API Access",
     "SSO Integration",
     "Dedicated Support"
   ],
   "limits": {
     "employees": 500,
     "storage_gb": 100,
     "api_calls_per_month": 100000,
     "custom_fields": "unlimited"
   },
   "integrations": [
     "Google Workspace",
     "Microsoft 365",
     "Slack",
     "Microsoft Teams",
     "Zoom",
     "Salesforce",
     "Custom Integrations"
   ]
 }'::jsonb, 
 true),

('Ultimate', 
 'Premium solution with unlimited features for enterprise clients', 
 299.99, 
 2999.99, 
 null, 
 '{
   "features": [
     "All Professional Features",
     "Unlimited Employees",
     "Advanced Security",
     "Custom Branding",
     "White Label Solution",
     "Advanced Analytics",
     "Machine Learning Insights",
     "Custom Development",
     "24/7 Premium Support",
     "Dedicated Account Manager"
   ],
   "limits": {
     "employees": "unlimited",
     "storage_gb": "unlimited",
     "api_calls_per_month": "unlimited",
     "custom_fields": "unlimited"
   },
   "integrations": [
     "All Available Integrations",
     "Custom API Development",
     "Enterprise SSO",
     "Advanced Security Integrations"
   ]
 }'::jsonb, 
 true);

-- Create organization subscription based on the plan (default to Professional)
INSERT INTO organization_subscriptions (
    organization_id, 
    plan_id, 
    status, 
    billing_cycle, 
    current_period_start, 
    current_period_end, 
    next_billing_date
) 
SELECT 
    '{ORG_ID}',
    sp.id,
    'active',
    'monthly',
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '1 month',
    CURRENT_DATE + INTERVAL '1 month'
FROM subscription_plans sp
WHERE sp.name = 'Professional';

-- Create initial invoice for the subscription
INSERT INTO billing_invoices (
    organization_id,
    subscription_id,
    invoice_number,
    amount,
    tax_amount,
    total_amount,
    status,
    issue_date,
    due_date,
    paid_date,
    payment_method
)
SELECT 
    '{ORG_ID}',
    os.id,
    'INV-' || TO_CHAR(CURRENT_DATE, 'YYYY') || '-001',
    sp.price_monthly,
    ROUND(sp.price_monthly * 0.18, 2), -- 18% tax
    ROUND(sp.price_monthly * 1.18, 2),
    'paid',
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '30 days',
    CURRENT_DATE,
    'credit_card'
FROM organization_subscriptions os
JOIN subscription_plans sp ON os.plan_id = sp.id
WHERE os.organization_id = '{ORG_ID}';

COMMIT;
