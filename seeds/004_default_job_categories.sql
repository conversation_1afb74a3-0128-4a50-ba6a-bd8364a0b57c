-- Seed 004: Default Job Categories and Templates
-- Inserts default job categories, templates, and recruitment data
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Insert default attendance policies
INSERT INTO attendance_policies (organization_id, policy_name, policy_type, policy_value, is_active, effective_from) VALUES
('{ORG_ID}', 'Standard Working Hours', 'working_hours', 
 '{
   "start_time": "09:00",
   "end_time": "18:00",
   "break_duration": 60,
   "flexible_hours": false,
   "core_hours_start": "10:00",
   "core_hours_end": "16:00"
 }'::jsonb, true, CURRENT_DATE),

('{ORG_ID}', 'Overtime Policy', 'overtime', 
 '{
   "threshold_hours": 8,
   "rate_multiplier": 1.5,
   "weekend_multiplier": 2.0,
   "holiday_multiplier": 2.5,
   "approval_required": true,
   "max_overtime_per_day": 4,
   "max_overtime_per_week": 12
 }'::jsonb, true, CURRENT_DATE),

('{ORG_ID}', 'Break Time Policy', 'break_time', 
 '{
   "lunch_break_duration": 60,
   "short_break_duration": 15,
   "max_short_breaks": 2,
   "break_tracking_required": false
 }'::jsonb, true, CURRENT_DATE),

('{ORG_ID}', 'Late Arrival Policy', 'late_policy', 
 '{
   "grace_period_minutes": 10,
   "late_threshold_minutes": 15,
   "deduction_per_minute": 0.5,
   "warning_after_instances": 3,
   "disciplinary_action_after": 5
 }'::jsonb, true, CURRENT_DATE);

-- Insert default work schedules
INSERT INTO work_schedules (user_id, schedule_name, monday_start, monday_end, tuesday_start, tuesday_end, 
                           wednesday_start, wednesday_end, thursday_start, thursday_end, friday_start, friday_end,
                           saturday_start, saturday_end, sunday_start, sunday_end, is_active, effective_from)
SELECT 
    u.id,
    'Standard Schedule',
    '09:00'::time, '18:00'::time,
    '09:00'::time, '18:00'::time,
    '09:00'::time, '18:00'::time,
    '09:00'::time, '18:00'::time,
    '09:00'::time, '18:00'::time,
    null, null,
    null, null,
    true,
    CURRENT_DATE
FROM users u
WHERE u.organization_id = '{ORG_ID}' AND u.role IN ('employee', 'org_admin');

-- Insert sample task templates
INSERT INTO task_templates (organization_id, name, description, category, estimated_hours, priority, template_data, is_active, created_by) 
SELECT 
    '{ORG_ID}',
    template_name,
    template_description,
    template_category,
    template_hours,
    template_priority,
    template_json,
    true,
    u.id
FROM (VALUES
    ('Employee Onboarding', 'Complete onboarding process for new employee', 'Administrative', 8, 'high',
     '{"checklist": ["Create user account", "Setup workspace", "Provide equipment", "Conduct orientation", "Assign mentor"], "documents": ["Employee handbook", "IT policy", "Safety guidelines"]}'::jsonb),
    
    ('Monthly Report Generation', 'Generate and review monthly departmental reports', 'Documentation', 4, 'medium',
     '{"checklist": ["Collect data", "Analyze metrics", "Create visualizations", "Write summary", "Review with manager"], "deadline_type": "monthly"}'::jsonb),
    
    ('Code Review', 'Review code changes and provide feedback', 'Review', 2, 'high',
     '{"checklist": ["Check code quality", "Verify functionality", "Test edge cases", "Provide feedback", "Approve/Request changes"], "tools": ["GitHub", "SonarQube"]}'::jsonb),
    
    ('Training Session', 'Conduct training session for team members', 'Training', 6, 'medium',
     '{"checklist": ["Prepare materials", "Setup environment", "Conduct session", "Collect feedback", "Update documentation"], "resources": ["Presentation", "Hands-on exercises"]}'::jsonb),
    
    ('Bug Investigation', 'Investigate and resolve reported bug', 'Development', 4, 'high',
     '{"checklist": ["Reproduce issue", "Analyze root cause", "Develop fix", "Test solution", "Deploy fix"], "priority": "high"}'::jsonb),
    
    ('Performance Review Preparation', 'Prepare for employee performance review', 'Planning', 3, 'medium',
     '{"checklist": ["Review goals", "Collect feedback", "Analyze performance data", "Prepare discussion points", "Schedule meeting"], "frequency": "quarterly"}'::jsonb),
    
    ('Client Meeting', 'Conduct meeting with client stakeholders', 'Meeting', 2, 'high',
     '{"checklist": ["Prepare agenda", "Review requirements", "Conduct meeting", "Document decisions", "Follow up"], "participants": ["Client", "Project team"]}'::jsonb),
    
    ('System Maintenance', 'Perform routine system maintenance tasks', 'Support', 3, 'low',
     '{"checklist": ["Backup data", "Update software", "Check system health", "Monitor performance", "Document changes"], "frequency": "weekly"}'::jsonb)
) AS templates(template_name, template_description, template_category, template_hours, template_priority, template_json)
CROSS JOIN (
    SELECT id FROM users WHERE organization_id = '{ORG_ID}' AND role = 'org_admin' LIMIT 1
) u;

-- Insert sample notifications for the organization admin
INSERT INTO notifications (user_id, title, message, type, is_read, action_url)
SELECT 
    u.id,
    notification_title,
    notification_message,
    notification_type,
    false,
    notification_url
FROM (VALUES
    ('Welcome to HRMS!', 'Your organization has been successfully set up. Start by adding employees and configuring your settings.', 'success', '/dashboard'),
    ('Complete Your Profile', 'Please complete your organization profile to get the most out of the HRMS system.', 'info', '/settings/organization'),
    ('Set Up Leave Policies', 'Configure leave policies and types according to your organization''s requirements.', 'info', '/leave/policies'),
    ('Configure Payroll', 'Set up payroll components and salary structures for your employees.', 'info', '/payroll/setup'),
    ('Invite Team Members', 'Start inviting your team members to join the HRMS platform.', 'info', '/employees/invite')
) AS notifications(notification_title, notification_message, notification_type, notification_url)
CROSS JOIN (
    SELECT id FROM users WHERE organization_id = '{ORG_ID}' AND role = 'org_admin' LIMIT 1
) u;

-- Create initial audit log entries
INSERT INTO audit_logs (user_id, organization_id, action, resource_type, resource_id, new_values)
SELECT 
    u.id,
    '{ORG_ID}',
    'CREATE',
    'organization',
    '{ORG_ID}',
    '{"status": "active", "setup_completed": true, "initial_data_seeded": true}'::jsonb
FROM users u
WHERE u.organization_id = '{ORG_ID}' AND u.role = 'org_admin' LIMIT 1;

-- Insert sample file upload records (placeholders for system files)
INSERT INTO file_uploads (organization_id, uploaded_by, file_name, file_path, file_size, file_type, mime_type, purpose, is_public)
SELECT 
    '{ORG_ID}',
    u.id,
    file_name,
    file_path,
    file_size,
    file_type,
    mime_type,
    purpose,
    is_public
FROM (VALUES
    ('employee_handbook.pdf', '/uploads/documents/employee_handbook.pdf', 2048576, 'pdf', 'application/pdf', 'document', true),
    ('company_logo.png', '/uploads/branding/company_logo.png', 51200, 'png', 'image/png', 'logo', true),
    ('leave_policy_template.docx', '/uploads/templates/leave_policy_template.docx', 1024000, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'template', false),
    ('payroll_template.xlsx', '/uploads/templates/payroll_template.xlsx', 512000, 'xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'template', false)
) AS files(file_name, file_path, file_size, file_type, mime_type, purpose, is_public)
CROSS JOIN (
    SELECT id FROM users WHERE organization_id = '{ORG_ID}' AND role = 'org_admin' LIMIT 1
) u;

COMMIT;
