-- Seed 001: Default Leave Types
-- Inserts default leave types for the organization
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Insert default leave types for the organization
INSERT INTO leave_types (organization_id, name, code, annual_allocation, carry_forward_allowed, max_carry_forward, requires_approval, is_active) VALUES
('{ORG_ID}', 'Annual Leave', 'AL', 25, true, 5, true, true),
('{ORG_ID}', 'Sick Leave', 'SL', 12, false, 0, false, true),
('{ORG_ID}', 'Personal Leave', 'PL', 5, false, 0, true, true),
('{ORG_ID}', 'Maternity Leave', 'ML', 180, false, 0, true, true),
('{ORG_ID}', 'Paternity Leave', 'PTL', 15, false, 0, true, true),
('{ORG_ID}', 'Bereavement Leave', 'BL', 5, false, 0, true, true),
('{ORG_ID}', 'Emergency Leave', 'EL', 3, false, 0, true, true),
('{ORG_ID}', 'Study Leave', 'STL', 10, false, 0, true, true),
('{ORG_ID}', 'Compensatory Off', 'CO', 0, false, 0, true, true),
('{ORG_ID}', 'Work From Home', 'WFH', 0, false, 0, false, true);

-- Insert default leave policies for each leave type
INSERT INTO leave_policies (organization_id, leave_type_id, policy_name, policy_rules, is_active, effective_from) 
SELECT 
    '{ORG_ID}',
    lt.id,
    'Default Policy for ' || lt.name,
    CASE 
        WHEN lt.code = 'AL' THEN '{"min_advance_days": 7, "max_consecutive_days": 30, "blackout_periods": ["Dec 25-31"], "approval_required": true}'::jsonb
        WHEN lt.code = 'SL' THEN '{"min_advance_days": 0, "max_consecutive_days": 7, "medical_certificate_required_after": 3, "approval_required": false}'::jsonb
        WHEN lt.code = 'PL' THEN '{"min_advance_days": 3, "max_consecutive_days": 5, "approval_required": true}'::jsonb
        WHEN lt.code = 'ML' THEN '{"min_advance_days": 30, "medical_certificate_required": true, "approval_required": true}'::jsonb
        WHEN lt.code = 'PTL' THEN '{"min_advance_days": 15, "approval_required": true}'::jsonb
        WHEN lt.code = 'BL' THEN '{"min_advance_days": 0, "documentation_required": true, "approval_required": true}'::jsonb
        WHEN lt.code = 'EL' THEN '{"min_advance_days": 0, "approval_required": true, "justification_required": true}'::jsonb
        WHEN lt.code = 'STL' THEN '{"min_advance_days": 30, "approval_required": true, "documentation_required": true}'::jsonb
        WHEN lt.code = 'CO' THEN '{"min_advance_days": 1, "approval_required": true, "earned_basis": true}'::jsonb
        WHEN lt.code = 'WFH' THEN '{"min_advance_days": 1, "approval_required": false, "max_per_month": 10}'::jsonb
        ELSE '{}'::jsonb
    END,
    true,
    CURRENT_DATE
FROM leave_types lt
WHERE lt.organization_id = '{ORG_ID}';

-- Insert default company holidays (common holidays - can be customized per organization)
INSERT INTO leave_calendar (organization_id, date, event_type, name, description, is_working_day) VALUES
('{ORG_ID}', '2024-01-01', 'holiday', 'New Year''s Day', 'New Year celebration', false),
('{ORG_ID}', '2024-01-26', 'holiday', 'Republic Day', 'Indian Republic Day', false),
('{ORG_ID}', '2024-03-08', 'holiday', 'Holi', 'Festival of Colors', false),
('{ORG_ID}', '2024-03-29', 'holiday', 'Good Friday', 'Christian holiday', false),
('{ORG_ID}', '2024-04-14', 'holiday', 'Baisakhi', 'Harvest festival', false),
('{ORG_ID}', '2024-08-15', 'holiday', 'Independence Day', 'Indian Independence Day', false),
('{ORG_ID}', '2024-08-26', 'holiday', 'Janmashtami', 'Krishna''s birthday', false),
('{ORG_ID}', '2024-10-02', 'holiday', 'Gandhi Jayanti', 'Mahatma Gandhi''s birthday', false),
('{ORG_ID}', '2024-10-24', 'holiday', 'Dussehra', 'Victory of good over evil', false),
('{ORG_ID}', '2024-11-01', 'holiday', 'Diwali', 'Festival of Lights', false),
('{ORG_ID}', '2024-11-15', 'holiday', 'Guru Nanak Jayanti', 'Guru Nanak''s birthday', false),
('{ORG_ID}', '2024-12-25', 'holiday', 'Christmas Day', 'Christian holiday', false);

-- Insert blackout periods (periods when leave is restricted)
INSERT INTO leave_calendar (organization_id, date, event_type, name, description, is_working_day) VALUES
('{ORG_ID}', '2024-03-31', 'blackout', 'Year End Closing', 'Financial year end - critical period', true),
('{ORG_ID}', '2024-12-31', 'blackout', 'Year End Closing', 'Calendar year end - critical period', true);

-- Insert optional holidays (employees can choose to take these)
INSERT INTO leave_calendar (organization_id, date, event_type, name, description, is_working_day) VALUES
('{ORG_ID}', '2024-01-14', 'optional_holiday', 'Makar Sankranti', 'Harvest festival - optional', true),
('{ORG_ID}', '2024-02-14', 'optional_holiday', 'Valentine''s Day', 'Optional celebration day', true),
('{ORG_ID}', '2024-03-27', 'optional_holiday', 'Holi (Second Day)', 'Festival of Colors - second day', true),
('{ORG_ID}', '2024-04-09', 'optional_holiday', 'Ram Navami', 'Lord Rama''s birthday - optional', true),
('{ORG_ID}', '2024-05-01', 'optional_holiday', 'Labour Day', 'International Workers'' Day', true),
('{ORG_ID}', '2024-07-17', 'optional_holiday', 'Muharram', 'Islamic New Year - optional', true),
('{ORG_ID}', '2024-08-19', 'optional_holiday', 'Raksha Bandhan', 'Brother-sister festival - optional', true),
('{ORG_ID}', '2024-09-07', 'optional_holiday', 'Ganesh Chaturthi', 'Lord Ganesha festival - optional', true);

-- Insert company events
INSERT INTO leave_calendar (organization_id, date, event_type, name, description, is_working_day) VALUES
('{ORG_ID}', '2024-02-29', 'company_event', 'Annual Company Meeting', 'All-hands company meeting', false),
('{ORG_ID}', '2024-06-15', 'company_event', 'Team Building Day', 'Company-wide team building activities', false),
('{ORG_ID}', '2024-09-15', 'company_event', 'Innovation Day', 'Company innovation and hackathon day', false),
('{ORG_ID}', '2024-12-15', 'company_event', 'Annual Party', 'Year-end celebration party', false);

COMMIT;
