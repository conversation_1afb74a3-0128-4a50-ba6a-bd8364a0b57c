// Comprehensive error handling test for HRMS authentication
const API_BASE = 'http://localhost:5020/api/v1';

async function testErrorHandling() {
    console.log('🚨 HRMS Error Handling Test Suite\n');
    console.log('=' .repeat(60));

    const testResults = {
        invalidCredentials: false,
        emptyCredentials: false,
        malformedRequest: false,
        networkError: false,
        serverError: false,
        tokenExpiry: false,
        unauthorizedAccess: false
    };

    try {
        // Test 1: Invalid Credentials
        console.log('\n1️⃣ INVALID CREDENTIALS TEST');
        console.log('-'.repeat(30));
        
        const invalidResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'wrongpassword123'
            })
        });

        if (invalidResponse.status === 401) {
            const errorData = await invalidResponse.json();
            testResults.invalidCredentials = true;
            console.log('✅ Invalid Credentials: PROPERLY HANDLED');
            console.log(`   📊 Status: ${invalidResponse.status}`);
            console.log(`   💬 Message: ${errorData.error?.message || errorData.message}`);
        } else {
            console.log('❌ Invalid Credentials: IMPROPERLY HANDLED');
            console.log(`   📊 Unexpected Status: ${invalidResponse.status}`);
        }

        // Test 2: Empty Credentials
        console.log('\n2️⃣ EMPTY CREDENTIALS TEST');
        console.log('-'.repeat(30));
        
        const emptyResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '',
                password: ''
            })
        });

        if (emptyResponse.status === 400 || emptyResponse.status === 401) {
            const errorData = await emptyResponse.json();
            testResults.emptyCredentials = true;
            console.log('✅ Empty Credentials: PROPERLY HANDLED');
            console.log(`   📊 Status: ${emptyResponse.status}`);
            console.log(`   💬 Message: ${errorData.error?.message || errorData.message}`);
        } else {
            console.log('❌ Empty Credentials: IMPROPERLY HANDLED');
            console.log(`   📊 Unexpected Status: ${emptyResponse.status}`);
        }

        // Test 3: Malformed Request
        console.log('\n3️⃣ MALFORMED REQUEST TEST');
        console.log('-'.repeat(30));
        
        const malformedResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: 'invalid json'
        });

        if (malformedResponse.status === 400) {
            testResults.malformedRequest = true;
            console.log('✅ Malformed Request: PROPERLY HANDLED');
            console.log(`   📊 Status: ${malformedResponse.status}`);
        } else {
            console.log('❌ Malformed Request: IMPROPERLY HANDLED');
            console.log(`   📊 Unexpected Status: ${malformedResponse.status}`);
        }

        // Test 4: Network Error Simulation (wrong port)
        console.log('\n4️⃣ NETWORK ERROR TEST');
        console.log('-'.repeat(30));
        
        try {
            const networkResponse = await fetch('http://localhost:9999/api/v1/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'password'
                })
            });
        } catch (networkError) {
            testResults.networkError = true;
            console.log('✅ Network Error: PROPERLY HANDLED');
            console.log(`   💬 Error: ${networkError.message}`);
        }

        // Test 5: Unauthorized Access (invalid token)
        console.log('\n5️⃣ UNAUTHORIZED ACCESS TEST');
        console.log('-'.repeat(30));
        
        const unauthorizedResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: { 
                'Authorization': 'Bearer invalid_token_12345',
                'Content-Type': 'application/json'
            }
        });

        if (unauthorizedResponse.status === 401) {
            testResults.unauthorizedAccess = true;
            console.log('✅ Unauthorized Access: PROPERLY HANDLED');
            console.log(`   📊 Status: ${unauthorizedResponse.status}`);
        } else {
            console.log('❌ Unauthorized Access: IMPROPERLY HANDLED');
            console.log(`   📊 Unexpected Status: ${unauthorizedResponse.status}`);
        }

        // Test 6: SQL Injection Attempt
        console.log('\n6️⃣ SQL INJECTION PROTECTION TEST');
        console.log('-'.repeat(30));
        
        const sqlInjectionResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: "<EMAIL>'; DROP TABLE users; --",
                password: "password"
            })
        });

        if (sqlInjectionResponse.status === 401 || sqlInjectionResponse.status === 400) {
            console.log('✅ SQL Injection: PROPERLY PROTECTED');
            console.log(`   📊 Status: ${sqlInjectionResponse.status}`);
        } else {
            console.log('❌ SQL Injection: POTENTIAL VULNERABILITY');
            console.log(`   📊 Status: ${sqlInjectionResponse.status}`);
        }

        // Test 7: Rate Limiting (multiple rapid requests)
        console.log('\n7️⃣ RATE LIMITING TEST');
        console.log('-'.repeat(30));
        
        const rapidRequests = [];
        for (let i = 0; i < 10; i++) {
            rapidRequests.push(
                fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                })
            );
        }

        const rapidResponses = await Promise.all(rapidRequests);
        const rateLimited = rapidResponses.some(response => response.status === 429);
        
        if (rateLimited) {
            console.log('✅ Rate Limiting: ACTIVE');
        } else {
            console.log('⚠️  Rate Limiting: NOT DETECTED (may not be implemented)');
        }

        // Summary
        console.log('\n' + '=' .repeat(60));
        console.log('📊 ERROR HANDLING TEST RESULTS');
        console.log('=' .repeat(60));
        
        const totalTests = Object.keys(testResults).length;
        const passedTests = Object.values(testResults).filter(result => result).length;
        
        console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed\n`);
        
        Object.entries(testResults).forEach(([test, passed]) => {
            const status = passed ? '✅ PASS' : '❌ FAIL';
            const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            console.log(`   ${status} - ${testName}`);
        });

        console.log('\n🔒 SECURITY RECOMMENDATIONS:');
        if (!testResults.invalidCredentials) {
            console.log('   ⚠️  Implement proper invalid credential handling');
        }
        if (!testResults.unauthorizedAccess) {
            console.log('   ⚠️  Strengthen token validation');
        }
        if (!rateLimited) {
            console.log('   ⚠️  Consider implementing rate limiting for login attempts');
        }

        console.log('\n✨ ERROR HANDLING VERIFICATION COMPLETE');

    } catch (error) {
        console.error('\n❌ Test suite failed with error:', error.message);
    }
}

// Run the error handling tests
testErrorHandling();
