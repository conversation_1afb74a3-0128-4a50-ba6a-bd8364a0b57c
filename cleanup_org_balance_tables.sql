-- Cleanup Leave Balance Tables from All Organization Schemas
-- This script removes balance-related tables from all org_* schemas
-- while preserving core leave management functionality

BEGIN TRANSACTION;

PRINT 'Starting cleanup of leave balance tables from all organization schemas...';

-- Declare variables for dynamic SQL
DECLARE @sql NVARCHAR(MAX) = '';
DECLARE @schema_name NVARCHAR(128);
DECLARE @table_name NVARCHAR(128);

-- Cursor to iterate through all organization schemas
DECLARE schema_cursor CURSOR FOR
SELECT DISTINCT SCHEMA_NAME(schema_id) as SchemaName
FROM sys.tables 
WHERE SCHEMA_NAME(schema_id) LIKE 'org_%'
AND name IN ('LeaveBalances', 'MonthlyLeaveAllowances', 'MonthlyLeaveUsages');

OPEN schema_cursor;
FETCH NEXT FROM schema_cursor INTO @schema_name;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Processing schema: ' + @schema_name;
    
    -- Check and drop MonthlyLeaveUsages table
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MonthlyLeaveUsages' AND schema_id = SCHEMA_ID(@schema_name))
    BEGIN
        SET @sql = 'DROP TABLE [' + @schema_name + '].[MonthlyLeaveUsages]';
        EXEC sp_executesql @sql;
        PRINT '  - Dropped MonthlyLeaveUsages from ' + @schema_name;
    END
    
    -- Check and drop MonthlyLeaveAllowances table
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MonthlyLeaveAllowances' AND schema_id = SCHEMA_ID(@schema_name))
    BEGIN
        SET @sql = 'DROP TABLE [' + @schema_name + '].[MonthlyLeaveAllowances]';
        EXEC sp_executesql @sql;
        PRINT '  - Dropped MonthlyLeaveAllowances from ' + @schema_name;
    END
    
    -- Check and drop LeaveBalances table
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'LeaveBalances' AND schema_id = SCHEMA_ID(@schema_name))
    BEGIN
        SET @sql = 'DROP TABLE [' + @schema_name + '].[LeaveBalances]';
        EXEC sp_executesql @sql;
        PRINT '  - Dropped LeaveBalances from ' + @schema_name;
    END
    
    -- Verify core tables still exist
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'LeaveRequests' AND schema_id = SCHEMA_ID(@schema_name))
    BEGIN
        PRINT '  - WARNING: LeaveRequests table missing in ' + @schema_name;
    END
    ELSE
    BEGIN
        PRINT '  - ✓ LeaveRequests table preserved in ' + @schema_name;
    END
    
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'LeaveTypes' AND schema_id = SCHEMA_ID(@schema_name))
    BEGIN
        PRINT '  - WARNING: LeaveTypes table missing in ' + @schema_name;
    END
    ELSE
    BEGIN
        PRINT '  - ✓ LeaveTypes table preserved in ' + @schema_name;
    END
    
    FETCH NEXT FROM schema_cursor INTO @schema_name;
END

CLOSE schema_cursor;
DEALLOCATE schema_cursor;

-- Final verification
PRINT '';
PRINT 'Final verification - Remaining balance tables:';
SELECT SCHEMA_NAME(schema_id) as SchemaName, name as TableName 
FROM sys.tables 
WHERE name IN ('LeaveBalances', 'MonthlyLeaveAllowances', 'MonthlyLeaveUsages')
ORDER BY SchemaName, TableName;

PRINT '';
PRINT 'Core leave management tables:';
SELECT SCHEMA_NAME(schema_id) as SchemaName, name as TableName, 
       (SELECT COUNT(*) FROM sys.columns WHERE object_id = sys.tables.object_id) as ColumnCount
FROM sys.tables 
WHERE name IN ('LeaveRequests', 'LeaveTypes')
ORDER BY SchemaName, TableName;

PRINT '';
PRINT 'Organization schema cleanup completed successfully!';
PRINT 'Leave management now works without balance restrictions across all organizations.';

COMMIT TRANSACTION;
