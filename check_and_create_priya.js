// Check existing employee IDs and create <PERSON><PERSON> with a unique ID
const API_BASE = 'http://localhost:5020/api/v1';

async function checkAndCreatePriya() {
    console.log('🔍 Checking Employee IDs and Creating Priya...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as PlanSquare Admin
        console.log('\n1️⃣ PLANSQUARE ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`PlanSquare admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        
        console.log(`✅ PlanSquare Admin login successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);

        // Step 2: Check current employees and their IDs
        console.log('\n2️⃣ CHECKING CURRENT EMPLOYEE IDs');
        console.log('-'.repeat(30));
        
        const employeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!employeesResponse.ok) {
            throw new Error(`Failed to fetch employees: ${employeesResponse.status}`);
        }

        const employeesData = await employeesResponse.json();
        const employees = employeesData.data.items;
        
        console.log(`📊 Current employees (${employees.length}):`);
        const usedEmployeeIds = [];
        employees.forEach((emp, index) => {
            const empId = emp.employeeDetail?.employeeId || 'N/A';
            usedEmployeeIds.push(empId);
            console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - Employee ID: ${empId}`);
        });

        console.log(`\n📋 Used Employee IDs: ${usedEmployeeIds.join(', ')}`);

        // Step 3: Find a unique employee ID
        console.log('\n3️⃣ FINDING UNIQUE EMPLOYEE ID');
        console.log('-'.repeat(30));
        
        let uniqueEmployeeId = null;
        for (let i = 1; i <= 10; i++) {
            const testId = `PS${i.toString().padStart(3, '0')}`;
            if (!usedEmployeeIds.includes(testId)) {
                uniqueEmployeeId = testId;
                break;
            }
        }

        if (!uniqueEmployeeId) {
            // Try with different format
            uniqueEmployeeId = `PLAN${Date.now().toString().slice(-3)}`;
        }

        console.log(`✅ Found unique Employee ID: ${uniqueEmployeeId}`);

        // Step 4: Create Priya employee with unique ID
        console.log('\n4️⃣ CREATING PRIYA EMPLOYEE');
        console.log('-'.repeat(30));
        
        const priyaEmployeeData = {
            name: "Priya Sharma",
            email: "<EMAIL>",
            employeeId: uniqueEmployeeId,
            jobTitle: "UI/UX Designer",
            department: "Design",
            joinDate: "2024-01-15",
            employmentType: "full-time",
            workLocation: "Mumbai Office",
            phone: "+91-9876543212",
            address: "456 Design Street, Mumbai, Maharashtra 400001",
            dateOfBirth: "1995-08-20",
            baseSalary: 650000,
            annualCtc: 780000
        };

        console.log(`📝 Creating employee: ${priyaEmployeeData.name} (${priyaEmployeeData.email})`);
        console.log(`   Employee ID: ${priyaEmployeeData.employeeId}`);
        console.log(`   Job Title: ${priyaEmployeeData.jobTitle}`);
        console.log(`   Department: ${priyaEmployeeData.department}`);

        const createEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(priyaEmployeeData)
        });

        if (createEmployeeResponse.ok) {
            const createdEmployee = await createEmployeeResponse.json();
            console.log(`✅ Successfully created Priya employee`);
            console.log(`   User ID: ${createdEmployee.data.id}`);
            console.log(`   Login Username: ${createdEmployee.data.loginUsername || 'Not generated'}`);
            console.log(`   Temporary Password: ${createdEmployee.data.temporaryPassword || 'Not generated'}`);
        } else {
            const errorData = await createEmployeeResponse.json();
            console.log(`❌ Failed to create Priya employee: ${errorData.message || 'Unknown error'}`);
            console.log('Error details:', JSON.stringify(errorData, null, 2));
            
            // If still failing, try with a timestamp-based ID
            if (errorData.error?.code === 'EMPLOYEE_ID_EXISTS') {
                console.log('\n🔄 Trying with timestamp-based Employee ID...');
                priyaEmployeeData.employeeId = `PRIYA${Date.now().toString().slice(-4)}`;
                console.log(`   New Employee ID: ${priyaEmployeeData.employeeId}`);
                
                const retryResponse = await fetch(`${API_BASE}/employee-management/employees`, {
                    method: 'POST',
                    headers: { 
                        'Authorization': `Bearer ${planSquareToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(priyaEmployeeData)
                });
                
                if (retryResponse.ok) {
                    const retryEmployee = await retryResponse.json();
                    console.log(`✅ Successfully created Priya employee with retry`);
                    console.log(`   User ID: ${retryEmployee.data.id}`);
                } else {
                    const retryError = await retryResponse.json();
                    console.log(`❌ Retry also failed: ${retryError.message || 'Unknown error'}`);
                }
            }
        }

        // Step 5: Final verification
        console.log('\n5️⃣ FINAL VERIFICATION');
        console.log('-'.repeat(30));
        
        const finalEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (finalEmployeesResponse.ok) {
            const finalEmployeesData = await finalEmployeesResponse.json();
            const finalEmployees = finalEmployeesData.data.items;
            
            console.log(`📊 Final employee count: ${finalEmployees.length}`);
            console.log('📋 All employees:');
            finalEmployees.forEach((emp, index) => {
                const empId = emp.employeeDetail?.employeeId || 'N/A';
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - Employee ID: ${empId} - Role: ${emp.role}`);
            });

            if (finalEmployees.length === 3) {
                console.log('\n🎉 SUCCESS: PlanSquare now has exactly 3 employees!');
                console.log('✅ Database cleanup completed successfully');
                console.log('✅ Ready for clean development work');
            } else {
                console.log(`\n⚠️  Current count: ${finalEmployees.length} employees (expected 3)`);
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🏁 PROCESS COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ PROCESS FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the API connection and try again.');
    }
}

// Run the process
checkAndCreatePriya();
