// Check which organization employees actually belong to
const API_BASE = 'http://localhost:5020/api/v1';

async function checkEmployeeOrganizations() {
    console.log('🔍 Checking Employee Organization Assignments...\n');

    try {
        // Step 1: Login as Plan Square Admin and get detailed employee info
        console.log('1️⃣ Plan Square Admin - Detailed Employee Check');
        console.log('-'.repeat(50));
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`Plan Square Organization ID: ${planSquareOrgId}`);

        const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': planSquareOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (planSquareEmployeesResponse.ok) {
            const planSquareEmployeesData = await planSquareEmployeesResponse.json();
            const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
            console.log(`Plan Square sees ${employees.length} employees:`);
            
            for (let i = 0; i < employees.length; i++) {
                const emp = employees[i];
                console.log(`\n   Employee ${i + 1}: ${emp.name} (${emp.email})`);
                console.log(`   - User ID: ${emp.id}`);
                console.log(`   - Employee ID: ${emp.employeeDetails?.employeeId || 'N/A'}`);
                console.log(`   - Organization in Response: ${emp.organization?.name || 'N/A'} (${emp.organization?.id || 'N/A'})`);
                console.log(`   - Role: ${emp.role}`);
                console.log(`   - Active: ${emp.isActive}`);
                
                // Get individual employee details
                try {
                    const empDetailResponse = await fetch(`${API_BASE}/employee-management/employees/${emp.id}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${planSquareToken}`,
                            'X-Organization-ID': planSquareOrgId,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (empDetailResponse.ok) {
                        const empDetailData = await empDetailResponse.json();
                        console.log(`   - Detailed Org: ${empDetailData.data?.organization?.name || 'N/A'} (${empDetailData.data?.organization?.id || 'N/A'})`);
                    }
                } catch (error) {
                    console.log(`   - Error getting details: ${error.message}`);
                }
            }
        }

        // Step 2: Login as Test Company Admin and get detailed employee info
        console.log('\n\n2️⃣ Test Company Admin - Detailed Employee Check');
        console.log('-'.repeat(50));
        const testCompanyLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        const testCompanyData = await testCompanyLoginResponse.json();
        const testCompanyToken = testCompanyData.data.token;
        const testCompanyOrgId = testCompanyData.data.user.organization?.id;
        
        console.log(`Test Company Organization ID: ${testCompanyOrgId}`);

        const testCompanyEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${testCompanyToken}`,
                'X-Organization-ID': testCompanyOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (testCompanyEmployeesResponse.ok) {
            const testCompanyEmployeesData = await testCompanyEmployeesResponse.json();
            const employees = testCompanyEmployeesData.data?.items || testCompanyEmployeesData.data || [];
            console.log(`Test Company sees ${employees.length} employees:`);
            
            for (let i = 0; i < employees.length; i++) {
                const emp = employees[i];
                console.log(`\n   Employee ${i + 1}: ${emp.name} (${emp.email})`);
                console.log(`   - User ID: ${emp.id}`);
                console.log(`   - Employee ID: ${emp.employeeDetails?.employeeId || 'N/A'}`);
                console.log(`   - Organization in Response: ${emp.organization?.name || 'N/A'} (${emp.organization?.id || 'N/A'})`);
                console.log(`   - Role: ${emp.role}`);
                console.log(`   - Active: ${emp.isActive}`);
                
                // Get individual employee details
                try {
                    const empDetailResponse = await fetch(`${API_BASE}/employee-management/employees/${emp.id}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${testCompanyToken}`,
                            'X-Organization-ID': testCompanyOrgId,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (empDetailResponse.ok) {
                        const empDetailData = await empDetailResponse.json();
                        console.log(`   - Detailed Org: ${empDetailData.data?.organization?.name || 'N/A'} (${empDetailData.data?.organization?.id || 'N/A'})`);
                    }
                } catch (error) {
                    console.log(`   - Error getting details: ${error.message}`);
                }
            }
        }

        console.log('\n\n3️⃣ Analysis');
        console.log('-'.repeat(50));
        console.log('🚨 ISSUE IDENTIFIED:');
        console.log('   Both organizations are seeing the same employees');
        console.log('   This indicates a multi-tenant isolation failure');
        console.log('   The tenant-specific database contexts are not working properly');

    } catch (error) {
        console.error('❌ Check failed:', error.message);
    }
}

// Run the check
checkEmployeeOrganizations();
