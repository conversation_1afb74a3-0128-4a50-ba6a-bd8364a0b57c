// Check where users are stored in the database
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function checkUserOrganizations() {
    console.log('🔍 Checking User Organization Assignments...\n');

    let pool;
    try {
        // Connect to database
        console.log('1️⃣ Connecting to database...');
        pool = await sql.connect(config);
        console.log('✅ Connected successfully');

        // Check all organizations
        console.log('\n2️⃣ Checking all organizations...');
        const orgQuery = `SELECT Id, Name, Domain FROM Organizations ORDER BY Name`;
        const orgResult = await pool.request().query(orgQuery);
        
        console.log(`Found ${orgResult.recordset.length} organizations:`);
        orgResult.recordset.forEach((org, index) => {
            console.log(`   ${index + 1}. ${org.Name} (${org.Id})`);
            console.log(`      Domain: ${org.Domain}`);
        });

        // Check all users and their organizations
        console.log('\n3️⃣ Checking all users...');
        const userQuery = `
            SELECT u.Id, u.Name, u.Email, u.Role, u.OrganizationId, u.IsActive, u.IsDeleted,
                   o.Name as OrgName, o.Domain as OrgDomain
            FROM Users u
            LEFT JOIN Organizations o ON u.OrganizationId = o.Id
            ORDER BY o.Name, u.Name
        `;
        
        const userResult = await pool.request().query(userQuery);
        console.log(`Found ${userResult.recordset.length} users:`);
        
        let planSquareUsers = [];
        let orphanedUsers = [];
        
        userResult.recordset.forEach((user, index) => {
            console.log(`\n   ${index + 1}. ${user.Name} (${user.Email})`);
            console.log(`      - User ID: ${user.Id}`);
            console.log(`      - Role: ${user.Role}`);
            console.log(`      - Organization ID: ${user.OrganizationId}`);
            console.log(`      - Organization: ${user.OrgName || 'NULL'}`);
            console.log(`      - Domain: ${user.OrgDomain || 'NULL'}`);
            console.log(`      - Active: ${user.IsActive}`);
            console.log(`      - Deleted: ${user.IsDeleted}`);
            
            if (user.OrgName === 'PlanSquare ') {
                planSquareUsers.push(user);
            } else if (!user.OrgName) {
                orphanedUsers.push(user);
            }
        });

        console.log('\n4️⃣ User Analysis...');
        console.log(`   - PlanSquare users: ${planSquareUsers.length}`);
        console.log(`   - Orphaned users (no organization): ${orphanedUsers.length}`);
        
        if (orphanedUsers.length > 0) {
            console.log('\n   🚨 Orphaned Users Found:');
            orphanedUsers.forEach(user => {
                console.log(`      - ${user.Name} (${user.Email}) - ID: ${user.Id}`);
            });
        }

        // Check for specific users we're looking for
        console.log('\n5️⃣ Checking for specific users...');
        const targetEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        
        targetEmails.forEach(email => {
            const user = userResult.recordset.find(u => u.Email === email);
            if (user) {
                console.log(`   ✅ ${email}: Found in ${user.OrgName || 'NO ORGANIZATION'} (${user.OrganizationId})`);
                console.log(`      Active: ${user.IsActive}, Deleted: ${user.IsDeleted}`);
            } else {
                console.log(`   ❌ ${email}: Not found`);
            }
        });

        // Fix orphaned users if any
        if (orphanedUsers.length > 0) {
            console.log('\n6️⃣ Fixing orphaned users...');
            const planSquareOrgId = orgResult.recordset.find(org => org.Name === 'PlanSquare ')?.Id;
            
            if (planSquareOrgId) {
                for (const user of orphanedUsers) {
                    if (user.Email.includes('plan2intl.com')) {
                        console.log(`   🔧 Moving ${user.Email} to PlanSquare organization...`);
                        const updateQuery = `
                            UPDATE Users 
                            SET OrganizationId = '${planSquareOrgId}' 
                            WHERE Id = '${user.Id}'
                        `;
                        await pool.request().query(updateQuery);
                        console.log(`   ✅ Updated ${user.Email}`);
                    }
                }
            }
        }

        console.log('\n7️⃣ Final verification...');
        const finalUserQuery = `
            SELECT u.Name, u.Email, u.Role, o.Name as OrgName
            FROM Users u
            LEFT JOIN Organizations o ON u.OrganizationId = o.Id
            WHERE u.Email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
            ORDER BY u.Name
        `;
        
        const finalResult = await pool.request().query(finalUserQuery);
        console.log(`Target users status:`);
        finalResult.recordset.forEach(user => {
            console.log(`   ${user.Email}: ${user.OrgName || 'NO ORGANIZATION'} (${user.Role})`);
        });

    } catch (error) {
        console.error('❌ Check failed:', error.message);
    } finally {
        if (pool) {
            await pool.close();
            console.log('\n🔌 Database connection closed');
        }
    }
}

// Run the check
checkUserOrganizations();
