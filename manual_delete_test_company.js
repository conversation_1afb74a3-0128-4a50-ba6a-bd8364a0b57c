// Manual deletion of Test Company data while preserving PlanSquare
const API_BASE = 'http://localhost:5020/api/v1';

async function manualDeleteTestCompany() {
    console.log('🗑️ Manual Test Company Data Cleanup...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Verify current state - Login as both admins
        console.log('\n1️⃣ CURRENT STATE VERIFICATION');
        console.log('-'.repeat(30));
        
        // Login as PlanSquare admin
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        let planSquareToken = null;
        let planSquareOrgId = null;
        
        if (planSquareLoginResponse.ok) {
            const planSquareData = await planSquareLoginResponse.json();
            planSquareToken = planSquareData.data.token;
            planSquareOrgId = planSquareData.data.user.organization?.id;
            console.log(`✅ PlanSquare Admin Login: SUCCESS`);
            console.log(`   Organization ID: ${planSquareOrgId}`);
        } else {
            console.log(`❌ PlanSquare Admin Login: FAILED`);
        }

        // Try to login as Test Company admin
        const testCompanyLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        let testCompanyToken = null;
        let testCompanyOrgId = null;
        
        if (testCompanyLoginResponse.ok) {
            const testCompanyData = await testCompanyLoginResponse.json();
            testCompanyToken = testCompanyData.data.token;
            testCompanyOrgId = testCompanyData.data.user.organization?.id;
            console.log(`✅ Test Company Admin Login: SUCCESS (needs to be removed)`);
            console.log(`   Organization ID: ${testCompanyOrgId}`);
        } else {
            console.log(`✅ Test Company Admin Login: ALREADY BLOCKED`);
        }

        // Step 2: Check current employee data
        console.log('\n2️⃣ CURRENT EMPLOYEE DATA');
        console.log('-'.repeat(30));
        
        if (planSquareToken) {
            const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${planSquareToken}`,
                    'X-Organization-ID': planSquareOrgId,
                    'Content-Type': 'application/json'
                }
            });

            if (planSquareEmployeesResponse.ok) {
                const planSquareEmployeesData = await planSquareEmployeesResponse.json();
                const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
                console.log(`PlanSquare currently sees ${employees.length} employees:`);
                
                employees.forEach((emp, index) => {
                    console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                    console.log(`      - User ID: ${emp.id}`);
                    console.log(`      - Employee ID: ${emp.employeeDetails?.employeeId}`);
                    console.log(`      - Department: ${emp.employeeDetails?.department}`);
                });
            }
        }

        // Step 3: Since we can't use super admin, let's focus on data cleanup approach
        console.log('\n3️⃣ DATA CLEANUP STRATEGY');
        console.log('-'.repeat(30));
        console.log('Since super admin is not available, we will:');
        console.log('1. Stop the backend application');
        console.log('2. Manually clean the database');
        console.log('3. Restart with only PlanSquare data');
        console.log('');
        console.log('🔧 MANUAL DATABASE CLEANUP REQUIRED:');
        console.log('');
        console.log('Execute these SQL commands on the SQL Server database:');
        console.log('');
        console.log('-- 1. Delete Test Company organization and users');
        console.log(`DELETE FROM Users WHERE OrganizationId = '9e15d63c-f3ef-494a-adf3-bd4e82b50f2b';`);
        console.log(`DELETE FROM Organizations WHERE Id = '9e15d63c-f3ef-494a-adf3-bd4e82b50f2b';`);
        console.log('');
        console.log('-- 2. Drop Test Company schema if it exists');
        console.log(`DROP SCHEMA IF EXISTS org_9e15d63cf3ef494aadf3bd4e82b50f2b;`);
        console.log('');
        console.log('-- 3. Verify only PlanSquare data remains');
        console.log(`SELECT * FROM Organizations WHERE Name = 'PlanSquare';`);
        console.log(`SELECT * FROM Users WHERE OrganizationId = '${planSquareOrgId}';`);

        // Step 4: Provide verification script
        console.log('\n4️⃣ POST-CLEANUP VERIFICATION');
        console.log('-'.repeat(30));
        console.log('After manual database cleanup, run this verification:');
        console.log('');
        console.log('1. Restart the backend application');
        console.log('2. Test PlanSquare admin login: <EMAIL> / PlanSquare@123');
        console.log('3. Test Test Company admin login should fail: <EMAIL> / Admin123!');
        console.log('4. Verify PlanSquare admin only sees PlanSquare employees');

        // Step 5: Create verification script
        console.log('\n5️⃣ CREATING VERIFICATION SCRIPT');
        console.log('-'.repeat(30));
        
        const verificationScript = `
// Post-cleanup verification script
const API_BASE = 'http://localhost:5020/api/v1';

async function verifyCleanup() {
    console.log('🔍 Verifying Test Company Cleanup...');
    
    // Test 1: PlanSquare admin should work
    const planSquareLogin = await fetch(\`\${API_BASE}/auth/login\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'PlanSquare@123'
        })
    });
    
    console.log(\`PlanSquare admin login: \${planSquareLogin.ok ? '✅ SUCCESS' : '❌ FAILED'}\`);
    
    // Test 2: Test Company admin should fail
    const testCompanyLogin = await fetch(\`\${API_BASE}/auth/login\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'Admin123!'
        })
    });
    
    console.log(\`Test Company admin login: \${testCompanyLogin.ok ? '❌ STILL WORKS (CLEANUP INCOMPLETE)' : '✅ BLOCKED (CLEANUP SUCCESS)'}\`);
    
    // Test 3: Check employee isolation
    if (planSquareLogin.ok) {
        const planSquareData = await planSquareLogin.json();
        const token = planSquareData.data.token;
        const orgId = planSquareData.data.user.organization?.id;
        
        const employeesResponse = await fetch(\`\${API_BASE}/employee-management/employees\`, {
            method: 'GET',
            headers: {
                'Authorization': \`Bearer \${token}\`,
                'X-Organization-ID': orgId,
                'Content-Type': 'application/json'
            }
        });
        
        if (employeesResponse.ok) {
            const employeesData = await employeesResponse.json();
            const employees = employeesData.data?.items || employeesData.data || [];
            console.log(\`PlanSquare sees \${employees.length} employees:\`);
            
            let hasTestCompanyEmployees = false;
            employees.forEach(emp => {
                console.log(\`   - \${emp.name} (\${emp.email})\`);
                if (emp.email.includes('testcompany.com')) {
                    hasTestCompanyEmployees = true;
                }
            });
            
            console.log(\`Contains Test Company employees: \${hasTestCompanyEmployees ? '❌ YES (CLEANUP INCOMPLETE)' : '✅ NO (CLEANUP SUCCESS)'}\`);
        }
    }
    
    console.log('\\n🎯 Cleanup verification completed!');
}

verifyCleanup();
`;

        // Write verification script to file
        require('fs').writeFileSync('verify_cleanup.js', verificationScript);
        console.log('✅ Created verify_cleanup.js for post-cleanup verification');

        console.log('\n6️⃣ SUMMARY');
        console.log('-'.repeat(30));
        console.log('🔧 Manual cleanup required due to missing super admin credentials');
        console.log('📋 SQL commands provided above for database cleanup');
        console.log('✅ Verification script created: verify_cleanup.js');
        console.log('');
        console.log('🎯 Next Steps:');
        console.log('1. Stop the backend application');
        console.log('2. Execute the SQL commands on the database');
        console.log('3. Restart the backend application');
        console.log('4. Run: node verify_cleanup.js');

    } catch (error) {
        console.error('❌ Manual cleanup process failed:', error.message);
    }
}

// Run the manual cleanup
manualDeleteTestCompany();
