// Debug tenant context and database schema isolation
const API_BASE = 'http://localhost:5020/api/v1';

async function debugTenantContext() {
    console.log('🔍 Debugging Tenant Context and Database Schema Isolation...\n');

    try {
        // Step 1: Test Plan Square Admin with detailed logging
        console.log('1️⃣ Plan Square Admin - Detailed Context Check');
        console.log('-'.repeat(50));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`Plan Square Organization ID: ${planSquareOrgId}`);
        console.log(`Plan Square User ID: ${planSquareData.data.user.id}`);
        console.log(`Plan Square User Role: ${planSquareData.data.user.role}`);

        // Test with explicit organization header
        console.log('\n📋 Testing Plan Square Employee Endpoint with Headers:');
        console.log(`   Authorization: Bearer ${planSquareToken.substring(0, 20)}...`);
        console.log(`   X-Organization-ID: ${planSquareOrgId}`);

        const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': planSquareOrgId,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Response Status: ${planSquareEmployeesResponse.status}`);
        console.log(`   Response Headers: ${JSON.stringify(Object.fromEntries(planSquareEmployeesResponse.headers.entries()))}`);

        if (planSquareEmployeesResponse.ok) {
            const planSquareEmployeesData = await planSquareEmployeesResponse.json();
            const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
            console.log(`   Employees Count: ${employees.length}`);
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                console.log(`      - User ID: ${emp.id}`);
                console.log(`      - Organization ID in User: ${emp.organizationId || 'N/A'}`);
                console.log(`      - Organization Object: ${JSON.stringify(emp.organization)}`);
            });
        } else {
            const errorText = await planSquareEmployeesResponse.text();
            console.log(`   Error: ${errorText}`);
        }

        // Step 2: Test Test Company Admin with detailed logging
        console.log('\n\n2️⃣ Test Company Admin - Detailed Context Check');
        console.log('-'.repeat(50));
        
        const testCompanyLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        const testCompanyData = await testCompanyLoginResponse.json();
        const testCompanyToken = testCompanyData.data.token;
        const testCompanyOrgId = testCompanyData.data.user.organization?.id;
        
        console.log(`Test Company Organization ID: ${testCompanyOrgId}`);
        console.log(`Test Company User ID: ${testCompanyData.data.user.id}`);
        console.log(`Test Company User Role: ${testCompanyData.data.user.role}`);

        // Test with explicit organization header
        console.log('\n📋 Testing Test Company Employee Endpoint with Headers:');
        console.log(`   Authorization: Bearer ${testCompanyToken.substring(0, 20)}...`);
        console.log(`   X-Organization-ID: ${testCompanyOrgId}`);

        const testCompanyEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${testCompanyToken}`,
                'X-Organization-ID': testCompanyOrgId,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Response Status: ${testCompanyEmployeesResponse.status}`);
        console.log(`   Response Headers: ${JSON.stringify(Object.fromEntries(testCompanyEmployeesResponse.headers.entries()))}`);

        if (testCompanyEmployeesResponse.ok) {
            const testCompanyEmployeesData = await testCompanyEmployeesResponse.json();
            const employees = testCompanyEmployeesData.data?.items || testCompanyEmployeesData.data || [];
            console.log(`   Employees Count: ${employees.length}`);
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                console.log(`      - User ID: ${emp.id}`);
                console.log(`      - Organization ID in User: ${emp.organizationId || 'N/A'}`);
                console.log(`      - Organization Object: ${JSON.stringify(emp.organization)}`);
            });
        } else {
            const errorText = await testCompanyEmployeesResponse.text();
            console.log(`   Error: ${errorText}`);
        }

        // Step 3: Cross-organization test (should fail)
        console.log('\n\n3️⃣ Cross-Organization Access Test');
        console.log('-'.repeat(50));
        console.log('Testing Plan Square token with Test Company organization ID...');
        
        const crossAccessResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': testCompanyOrgId, // Wrong org ID
                'Content-Type': 'application/json'
            }
        });

        console.log(`Cross-access Response Status: ${crossAccessResponse.status}`);
        
        if (crossAccessResponse.ok) {
            const crossAccessData = await crossAccessResponse.json();
            const employees = crossAccessData.data?.items || crossAccessData.data || [];
            console.log(`❌ SECURITY ISSUE: Cross-access allowed! Employees: ${employees.length}`);
        } else {
            console.log(`✅ Cross-access properly blocked`);
            const errorText = await crossAccessResponse.text();
            console.log(`   Error: ${errorText.substring(0, 200)}...`);
        }

        console.log('\n\n4️⃣ Analysis Summary');
        console.log('-'.repeat(50));
        console.log('🔍 Key Findings:');
        console.log(`   - Plan Square Org ID: ${planSquareOrgId}`);
        console.log(`   - Test Company Org ID: ${testCompanyOrgId}`);
        console.log(`   - Organizations Different: ${planSquareOrgId !== testCompanyOrgId ? 'YES' : 'NO'}`);
        console.log('   - Both seeing same employees: This indicates tenant isolation failure');
        console.log('   - Organization data in responses: Missing (N/A)');
        console.log('\n💡 Likely Issues:');
        console.log('   1. Employees stored in wrong organization schema');
        console.log('   2. Tenant context not properly applied to database queries');
        console.log('   3. Database schema isolation not working');

    } catch (error) {
        console.error('❌ Debug test failed:', error.message);
    }
}

// Run the debug test
debugTenantContext();
