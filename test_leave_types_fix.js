// Test script to verify the leave types 400 error fix
const API_BASE = 'http://localhost:5020/api/v1';

async function testLeaveTypesFix() {
    console.log('🧪 Testing Leave Types 400 Error Fix...\n');

    try {
        // Step 1: Login as Employee
        console.log('1️⃣ Logging in as Employee...');
        const employeeLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'TempPass123!'
            })
        });

        if (!employeeLoginResponse.ok) {
            throw new Error(`Employee login failed: ${employeeLoginResponse.status} - ${await employeeLoginResponse.text()}`);
        }

        const employeeLoginData = await employeeLoginResponse.json();
        const employeeToken = employeeLoginData.data.token;
        const employeeOrgId = employeeLoginData.data.user.organization?.id;
        
        console.log(`✅ Employee login successful. Organization ID: ${employeeOrgId}`);

        // Step 2: Test Leave Types endpoint specifically
        console.log('\n2️⃣ Testing Leave Types endpoint...');
        
        const leaveTypesResponse = await fetch(`${API_BASE}/leave/types`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Response Status: ${leaveTypesResponse.status}`);
        
        if (leaveTypesResponse.ok) {
            const data = await leaveTypesResponse.json();
            console.log(`   ✅ Leave Types: SUCCESS (${leaveTypesResponse.status})`);
            console.log(`   📊 Response: ${JSON.stringify(data, null, 2)}`);
        } else {
            const errorText = await leaveTypesResponse.text();
            console.log(`   ❌ Leave Types: FAILED (${leaveTypesResponse.status})`);
            console.log(`   🚨 Error: ${errorText}`);
        }

        // Step 3: Test other employee endpoints to ensure they still work
        console.log('\n3️⃣ Testing other employee endpoints...');
        
        const testEndpoints = [
            { name: 'Leave Balance', url: '/leave/balance' },
            { name: 'Leave Requests', url: '/leave/requests' },
            { name: 'Attendance Records', url: '/attendance/records' }
        ];

        for (const endpoint of testEndpoints) {
            console.log(`\n   Testing ${endpoint.name}...`);
            try {
                const response = await fetch(`${API_BASE}${endpoint.url}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${employeeToken}`,
                        'X-Organization-ID': employeeOrgId,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    console.log(`   ✅ ${endpoint.name}: SUCCESS (${response.status})`);
                } else {
                    const errorText = await response.text();
                    console.log(`   ❌ ${endpoint.name}: FAILED (${response.status})`);
                    console.log(`   🚨 Error: ${errorText.substring(0, 200)}...`);
                }
            } catch (error) {
                console.log(`   ❌ ${endpoint.name}: ERROR - ${error.message}`);
            }
        }

        console.log('\n🎉 Leave Types Fix Test Completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testLeaveTypesFix();
