// Test database schema isolation by creating employees in specific organizations
const API_BASE = 'http://localhost:5020/api/v1';

async function testDatabaseSchemaIsolation() {
    console.log('🔍 Testing Database Schema Isolation...\n');

    try {
        // Step 1: Create a new employee in Plan Square
        console.log('1️⃣ Creating New Employee in Plan Square');
        console.log('-'.repeat(50));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`Plan Square Organization ID: ${planSquareOrgId}`);

        // Create a unique employee in Plan Square
        const timestamp = Date.now();
        const createPlanSquareEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': planSquareOrgId
            },
            body: JSON.stringify({
                name: 'Plan Square Employee',
                email: `plansquare.employee.${timestamp}@plan2intl.com`,
                role: 'Employee',
                employeeDetails: {
                    employeeId: `PS_EMP_${timestamp}`,
                    jobTitle: 'Plan Square Developer',
                    department: 'Plan Square Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 80000,
                    annualCTC: 95000
                }
            })
        });

        let planSquareEmployeeId = null;
        if (createPlanSquareEmployeeResponse.ok) {
            const planSquareEmployeeData = await createPlanSquareEmployeeResponse.json();
            planSquareEmployeeId = planSquareEmployeeData.data.id;
            console.log(`✅ Plan Square Employee Created`);
            console.log(`   Name: ${planSquareEmployeeData.data.name}`);
            console.log(`   Email: ${planSquareEmployeeData.data.email}`);
            console.log(`   User ID: ${planSquareEmployeeId}`);
            console.log(`   Password: ${planSquareEmployeeData.data.loginTemporaryPassword}`);
        } else {
            const errorText = await createPlanSquareEmployeeResponse.text();
            console.log(`❌ Failed to create Plan Square employee: ${errorText}`);
        }

        // Step 2: Create a new employee in Test Company
        console.log('\n2️⃣ Creating New Employee in Test Company');
        console.log('-'.repeat(50));
        
        const testCompanyLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        const testCompanyData = await testCompanyLoginResponse.json();
        const testCompanyToken = testCompanyData.data.token;
        const testCompanyOrgId = testCompanyData.data.user.organization?.id;
        
        console.log(`Test Company Organization ID: ${testCompanyOrgId}`);

        // Create a unique employee in Test Company
        const createTestCompanyEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${testCompanyToken}`,
                'X-Organization-ID': testCompanyOrgId
            },
            body: JSON.stringify({
                name: 'Test Company Employee',
                email: `testcompany.employee.${timestamp}@testcompany.com`,
                role: 'Employee',
                employeeDetails: {
                    employeeId: `TC_EMP_${timestamp}`,
                    jobTitle: 'Test Company Developer',
                    department: 'Test Company Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 75000,
                    annualCTC: 90000
                }
            })
        });

        let testCompanyEmployeeId = null;
        if (createTestCompanyEmployeeResponse.ok) {
            const testCompanyEmployeeData = await createTestCompanyEmployeeResponse.json();
            testCompanyEmployeeId = testCompanyEmployeeData.data.id;
            console.log(`✅ Test Company Employee Created`);
            console.log(`   Name: ${testCompanyEmployeeData.data.name}`);
            console.log(`   Email: ${testCompanyEmployeeData.data.email}`);
            console.log(`   User ID: ${testCompanyEmployeeId}`);
            console.log(`   Password: ${testCompanyEmployeeData.data.loginTemporaryPassword}`);
        } else {
            const errorText = await createTestCompanyEmployeeResponse.text();
            console.log(`❌ Failed to create Test Company employee: ${errorText}`);
        }

        // Step 3: Verify Plan Square only sees its employees
        console.log('\n3️⃣ Verifying Plan Square Employee Isolation');
        console.log('-'.repeat(50));
        
        const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': planSquareOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (planSquareEmployeesResponse.ok) {
            const planSquareEmployeesData = await planSquareEmployeesResponse.json();
            const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
            console.log(`Plan Square sees ${employees.length} employees:`);
            
            let foundPlanSquareEmployee = false;
            let foundTestCompanyEmployee = false;
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                console.log(`      - User ID: ${emp.id}`);
                console.log(`      - Employee ID: ${emp.employeeDetails?.employeeId}`);
                console.log(`      - Department: ${emp.employeeDetails?.department}`);
                
                if (emp.id === planSquareEmployeeId) foundPlanSquareEmployee = true;
                if (emp.id === testCompanyEmployeeId) foundTestCompanyEmployee = true;
            });
            
            console.log(`\n   📊 Plan Square Employee Visibility:`);
            console.log(`      - Can see own employee: ${foundPlanSquareEmployee ? '✅ YES' : '❌ NO'}`);
            console.log(`      - Can see Test Company employee: ${foundTestCompanyEmployee ? '❌ YES (SECURITY ISSUE)' : '✅ NO (CORRECT)'}`);
        }

        // Step 4: Verify Test Company only sees its employees
        console.log('\n4️⃣ Verifying Test Company Employee Isolation');
        console.log('-'.repeat(50));
        
        const testCompanyEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${testCompanyToken}`,
                'X-Organization-ID': testCompanyOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (testCompanyEmployeesResponse.ok) {
            const testCompanyEmployeesData = await testCompanyEmployeesResponse.json();
            const employees = testCompanyEmployeesData.data?.items || testCompanyEmployeesData.data || [];
            console.log(`Test Company sees ${employees.length} employees:`);
            
            let foundPlanSquareEmployee = false;
            let foundTestCompanyEmployee = false;
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                console.log(`      - User ID: ${emp.id}`);
                console.log(`      - Employee ID: ${emp.employeeDetails?.employeeId}`);
                console.log(`      - Department: ${emp.employeeDetails?.department}`);
                
                if (emp.id === planSquareEmployeeId) foundPlanSquareEmployee = true;
                if (emp.id === testCompanyEmployeeId) foundTestCompanyEmployee = true;
            });
            
            console.log(`\n   📊 Test Company Employee Visibility:`);
            console.log(`      - Can see own employee: ${foundTestCompanyEmployee ? '✅ YES' : '❌ NO'}`);
            console.log(`      - Can see Plan Square employee: ${foundPlanSquareEmployee ? '❌ YES (SECURITY ISSUE)' : '✅ NO (CORRECT)'}`);
        }

        console.log('\n5️⃣ Final Analysis');
        console.log('-'.repeat(50));
        console.log('🔍 Schema Isolation Test Results:');
        console.log(`   - Plan Square Org ID: ${planSquareOrgId}`);
        console.log(`   - Test Company Org ID: ${testCompanyOrgId}`);
        console.log(`   - Organizations Different: ${planSquareOrgId !== testCompanyOrgId ? '✅ YES' : '❌ NO'}`);
        console.log('\n💡 If both organizations can see each other\'s employees,');
        console.log('   the database schema isolation is not working correctly.');

    } catch (error) {
        console.error('❌ Schema isolation test failed:', error.message);
    }
}

// Run the test
testDatabaseSchemaIsolation();
