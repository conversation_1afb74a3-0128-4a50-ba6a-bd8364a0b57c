// <PERSON>ript to run the monthly leave allowance migration
import sql from 'mssql';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const config = {
    server: '**************',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function runMigration() {
    try {
        console.log('🔗 Connecting to SQL Server...');
        const pool = await sql.connect(config);
        
        console.log('📄 Reading migration scripts...');
        const addTablesScript = fs.readFileSync(
            path.join(__dirname, 'backend/HRMS.Infrastructure/Migrations/AddMonthlyLeaveAllowanceTables.sql'),
            'utf8'
        );

        const makeNullableScript = fs.readFileSync(
            path.join(__dirname, 'backend/HRMS.Infrastructure/Migrations/MakeLeaveTypeIdNullable.sql'),
            'utf8'
        );

        console.log('🚀 Executing migrations...');
        console.log('  1. Adding monthly leave allowance tables...');
        const result1 = await pool.request().query(addTablesScript);

        console.log('  2. Making LeaveTypeId nullable...');
        const result2 = await pool.request().query(makeNullableScript);

        console.log('✅ All migrations completed successfully!');
        console.log('📊 Results:', { addTables: result1, makeNullable: result2 });
        
        await pool.close();
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

runMigration();
