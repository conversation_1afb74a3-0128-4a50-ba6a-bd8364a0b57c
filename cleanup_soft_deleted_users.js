// Clean up soft-deleted users from PlanSquare organization
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function cleanupSoftDeletedUsers() {
    console.log('🧹 Cleaning up Soft-Deleted Users from PlanSquare...\n');
    console.log('=' .repeat(60));

    try {
        // Connect to database
        console.log('\n1️⃣ CONNECTING TO DATABASE');
        console.log('-'.repeat(30));
        
        const pool = await sql.connect(config);
        console.log('✅ Connected to SQL Server database');

        // Step 1: Find PlanSquare organization
        const orgQuery = `
            SELECT Id, Name, Domain
            FROM Organizations 
            WHERE Name LIKE '%Plan%' OR Domain LIKE '%plan%'
        `;
        
        const orgResult = await pool.request().query(orgQuery);
        const planSquareOrgId = orgResult.recordset[0]?.Id;
        
        if (!planSquareOrgId) {
            console.log('❌ PlanSquare organization not found');
            return;
        }

        console.log(`✅ Found PlanSquare Organization ID: ${planSquareOrgId}`);

        // Step 2: Check all users in PlanSquare organization
        console.log('\n2️⃣ CHECKING ALL PLANSQUARE USERS');
        console.log('-'.repeat(30));
        
        const allUsersQuery = `
            SELECT Id, Name, Email, Role, IsActive, IsDeleted, CreatedAt, DeletedAt
            FROM Users 
            WHERE OrganizationId = '${planSquareOrgId}'
            ORDER BY CreatedAt DESC
        `;
        
        const allUsersResult = await pool.request().query(allUsersQuery);
        console.log(`📊 Found ${allUsersResult.recordset.length} users in PlanSquare organization:`);
        
        let activeUsers = [];
        let softDeletedUsers = [];
        
        allUsersResult.recordset.forEach((user, index) => {
            const status = user.IsDeleted ? 'DELETED' : (user.IsActive ? 'ACTIVE' : 'INACTIVE');
            console.log(`   ${index + 1}. ${user.Name} (${user.Email}) - ${user.Role} - ${status}`);
            console.log(`      Created: ${user.CreatedAt}, Deleted: ${user.DeletedAt || 'N/A'}`);
            
            if (user.IsDeleted) {
                softDeletedUsers.push(user);
            } else {
                activeUsers.push(user);
            }
        });

        console.log(`\n📊 Active users: ${activeUsers.length}`);
        console.log(`📊 Soft-deleted users: ${softDeletedUsers.length}`);

        // Step 3: Hard delete soft-deleted users
        console.log('\n3️⃣ HARD DELETING SOFT-DELETED USERS');
        console.log('-'.repeat(30));
        
        if (softDeletedUsers.length > 0) {
            for (const user of softDeletedUsers) {
                try {
                    console.log(`   Deleting user: ${user.Name} (${user.Email})`);
                    
                    const deleteUserQuery = `DELETE FROM Users WHERE Id = '${user.Id}'`;
                    await pool.request().query(deleteUserQuery);
                    console.log(`   ✅ Deleted user record`);
                } catch (error) {
                    console.log(`   ❌ Failed to delete user: ${error.message}`);
                }
            }
        } else {
            console.log('✅ No soft-deleted users found');
        }

        // Step 4: Verify final state
        console.log('\n4️⃣ VERIFYING FINAL STATE');
        console.log('-'.repeat(30));
        
        const finalUsersQuery = `
            SELECT Id, Name, Email, Role, IsActive, IsDeleted
            FROM Users 
            WHERE OrganizationId = '${planSquareOrgId}'
            ORDER BY CreatedAt
        `;
        
        const finalUsersResult = await pool.request().query(finalUsersQuery);
        console.log(`📊 Final PlanSquare users: ${finalUsersResult.recordset.length}`);
        
        finalUsersResult.recordset.forEach((user, index) => {
            const status = user.IsDeleted ? 'DELETED' : (user.IsActive ? 'ACTIVE' : 'INACTIVE');
            console.log(`   ${index + 1}. ${user.Name} (${user.Email}) - ${user.Role} - ${status}`);
        });

        // Step 5: Check total users count across all organizations
        const totalUsersQuery = `
            SELECT COUNT(*) as TotalCount,
                   SUM(CASE WHEN IsDeleted = 1 THEN 1 ELSE 0 END) as SoftDeletedCount,
                   SUM(CASE WHEN IsDeleted = 0 AND IsActive = 1 THEN 1 ELSE 0 END) as ActiveCount
            FROM Users
        `;
        
        const totalUsersResult = await pool.request().query(totalUsersQuery);
        const stats = totalUsersResult.recordset[0];
        
        console.log(`\n📊 Database-wide user statistics:`);
        console.log(`   Total users: ${stats.TotalCount}`);
        console.log(`   Active users: ${stats.ActiveCount}`);
        console.log(`   Soft-deleted users: ${stats.SoftDeletedCount}`);

        await pool.close();
        console.log('\n✅ Database connection closed');

        console.log('\n' + '='.repeat(60));
        console.log('🏁 USER CLEANUP COMPLETED');
        console.log('✅ Soft-deleted users removed successfully');
        console.log('🚀 Dashboard should now show correct employee counts');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ CLEANUP FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the database connection and try again.');
    }
}

// Run the cleanup
cleanupSoftDeletedUsers();
