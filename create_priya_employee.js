// Create <PERSON><PERSON> as the third legitimate employee for PlanSquare
const API_BASE = 'http://localhost:5020/api/v1';

async function createPriyaEmployee() {
    console.log('👤 Creating Priya Employee for PlanSquare...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as PlanSquare Admin
        console.log('\n1️⃣ PLANSQUARE ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`PlanSquare admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`✅ PlanSquare Admin login successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);
        console.log(`   🏢 Organization: ${planSquareData.data.user.organization?.name}`);
        console.log(`   🆔 Organization ID: ${planSquareOrgId}`);

        // Step 2: Create Priya employee
        console.log('\n2️⃣ CREATING PRIYA EMPLOYEE');
        console.log('-'.repeat(30));
        
        const priyaEmployeeData = {
            name: "Priya Sharma",
            email: "<EMAIL>",
            employeeId: "PS004",
            jobTitle: "UI/UX Designer",
            department: "Design",
            joinDate: "2024-01-15",
            employmentType: "full-time",
            workLocation: "Mumbai Office",
            phone: "+91-9876543212",
            address: "456 Design Street, Mumbai, Maharashtra 400001",
            dateOfBirth: "1995-08-20",
            baseSalary: 650000,
            annualCtc: 780000
        };

        console.log(`📝 Creating employee: ${priyaEmployeeData.name} (${priyaEmployeeData.email})`);
        console.log(`   Employee ID: ${priyaEmployeeData.employeeId}`);
        console.log(`   Job Title: ${priyaEmployeeData.jobTitle}`);
        console.log(`   Department: ${priyaEmployeeData.department}`);

        const createEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(priyaEmployeeData)
        });

        if (createEmployeeResponse.ok) {
            const createdEmployee = await createEmployeeResponse.json();
            console.log(`✅ Successfully created Priya employee`);
            console.log(`   User ID: ${createdEmployee.data.id}`);
            console.log(`   Login Username: ${createdEmployee.data.loginUsername || 'Not generated'}`);
            console.log(`   Temporary Password: ${createdEmployee.data.temporaryPassword || 'Not generated'}`);
        } else {
            const errorData = await createEmployeeResponse.json();
            console.log(`❌ Failed to create Priya employee: ${errorData.message || 'Unknown error'}`);
            console.log('Error details:', JSON.stringify(errorData, null, 2));
        }

        // Step 3: Verify final employee count
        console.log('\n3️⃣ VERIFYING FINAL EMPLOYEE COUNT');
        console.log('-'.repeat(30));
        
        const employeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (employeesResponse.ok) {
            const employeesData = await employeesResponse.json();
            const employees = employeesData.data.items;
            
            console.log(`📊 Total employees in PlanSquare: ${employees.length}`);
            console.log('📋 All employees:');
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role}`);
                if (emp.employeeDetail) {
                    console.log(`      Employee ID: ${emp.employeeDetail.employeeId}, Department: ${emp.employeeDetail.department}`);
                }
            });

            if (employees.length === 3) {
                console.log('\n🎉 SUCCESS: PlanSquare now has exactly 3 employees!');
                console.log('✅ Database is clean and ready for development');
            } else {
                console.log(`\n⚠️  WARNING: Expected 3 employees but found ${employees.length}`);
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🏁 EMPLOYEE CREATION COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ EMPLOYEE CREATION FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the API connection and try again.');
    }
}

// Run the employee creation
createPriyaEmployee();
