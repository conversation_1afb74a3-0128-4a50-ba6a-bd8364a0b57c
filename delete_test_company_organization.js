// Script to delete Test Company organization while preserving PlanSquare
const API_BASE = 'http://localhost:5020/api/v1';

async function deleteTestCompanyOrganization() {
    console.log('🗑️ Deleting Test Company Organization...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as Super Admin to delete organization
        console.log('\n1️⃣ SUPER ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const superAdminLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'SuperAdmin@123'
            })
        });

        if (!superAdminLoginResponse.ok) {
            throw new Error(`Super admin login failed: ${superAdminLoginResponse.status}`);
        }

        const superAdminData = await superAdminLoginResponse.json();
        const superAdminToken = superAdminData.data.token;
        
        console.log(`✅ Super Admin login successful`);
        console.log(`   👤 Name: ${superAdminData.data.user.name}`);
        console.log(`   📧 Email: ${superAdminData.data.user.email}`);
        console.log(`   🔑 Role: ${superAdminData.data.user.role}`);

        // Step 2: Get all organizations to identify Test Company
        console.log('\n2️⃣ IDENTIFYING ORGANIZATIONS');
        console.log('-'.repeat(30));
        
        const organizationsResponse = await fetch(`${API_BASE}/super-admin/organizations`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${superAdminToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!organizationsResponse.ok) {
            throw new Error(`Failed to get organizations: ${organizationsResponse.status}`);
        }

        const organizationsData = await organizationsResponse.json();
        const organizations = organizationsData.data || [];
        
        console.log(`📊 Found ${organizations.length} organizations:`);
        
        let testCompanyOrg = null;
        let planSquareOrg = null;
        
        organizations.forEach((org, index) => {
            console.log(`   ${index + 1}. ${org.name} (${org.id})`);
            console.log(`      Domain: ${org.domain}`);
            console.log(`      Industry: ${org.industry}`);
            console.log(`      Created: ${new Date(org.createdAt).toLocaleDateString()}`);
            
            if (org.name === 'Test Company' || org.domain === 'testcompany.com') {
                testCompanyOrg = org;
            }
            if (org.name === 'PlanSquare' || org.domain === 'plan2intl.com') {
                planSquareOrg = org;
            }
        });

        if (!testCompanyOrg) {
            console.log('⚠️ Test Company organization not found. It may have already been deleted.');
            return;
        }

        if (!planSquareOrg) {
            console.log('❌ PlanSquare organization not found. Cannot proceed without confirming PlanSquare exists.');
            return;
        }

        console.log(`\n🎯 Target for deletion: ${testCompanyOrg.name} (${testCompanyOrg.id})`);
        console.log(`🛡️ Preserving: ${planSquareOrg.name} (${planSquareOrg.id})`);

        // Step 3: Delete Test Company organization
        console.log('\n3️⃣ DELETING TEST COMPANY ORGANIZATION');
        console.log('-'.repeat(30));
        
        const deleteResponse = await fetch(`${API_BASE}/super-admin/organizations/${testCompanyOrg.id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${superAdminToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Delete Response Status: ${deleteResponse.status}`);
        
        if (deleteResponse.ok) {
            console.log(`✅ Test Company organization deleted successfully`);
        } else {
            const errorText = await deleteResponse.text();
            console.log(`❌ Failed to delete Test Company: ${errorText}`);
            
            // If direct deletion fails, try alternative approach
            console.log('\n🔄 Attempting alternative deletion method...');
            // Note: This would require implementing a custom deletion endpoint
            console.log('⚠️ Manual database cleanup may be required');
        }

        // Step 4: Verify Test Company is deleted
        console.log('\n4️⃣ VERIFICATION - ORGANIZATIONS');
        console.log('-'.repeat(30));
        
        const verifyOrgsResponse = await fetch(`${API_BASE}/super-admin/organizations`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${superAdminToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (verifyOrgsResponse.ok) {
            const verifyOrgsData = await verifyOrgsResponse.json();
            const remainingOrgs = verifyOrgsData.data || [];
            
            console.log(`📊 Remaining organizations: ${remainingOrgs.length}`);
            
            let testCompanyExists = false;
            let planSquareExists = false;
            
            remainingOrgs.forEach((org, index) => {
                console.log(`   ${index + 1}. ${org.name} (${org.id})`);
                if (org.id === testCompanyOrg.id) testCompanyExists = true;
                if (org.id === planSquareOrg.id) planSquareExists = true;
            });
            
            console.log(`\n   📊 Verification Results:`);
            console.log(`      - Test Company exists: ${testCompanyExists ? '❌ YES (DELETION FAILED)' : '✅ NO (DELETED)'}`);
            console.log(`      - PlanSquare exists: ${planSquareExists ? '✅ YES (PRESERVED)' : '❌ NO (ERROR!)'}`);
        }

        // Step 5: Verify Test Company login no longer works
        console.log('\n5️⃣ VERIFICATION - TEST COMPANY LOGIN');
        console.log('-'.repeat(30));
        
        const testLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        console.log(`   Test Company login status: ${testLoginResponse.status}`);
        
        if (testLoginResponse.ok) {
            console.log(`   ❌ Test Company login still works (deletion incomplete)`);
        } else {
            console.log(`   ✅ Test Company login blocked (deletion successful)`);
        }

        // Step 6: Verify PlanSquare still works
        console.log('\n6️⃣ VERIFICATION - PLANSQUARE ACCESS');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        if (planSquareLoginResponse.ok) {
            const planSquareLoginData = await planSquareLoginResponse.json();
            const planSquareToken = planSquareLoginData.data.token;
            const planSquareOrgId = planSquareLoginData.data.user.organization?.id;
            
            console.log(`✅ PlanSquare admin login successful`);
            console.log(`   👤 Name: ${planSquareLoginData.data.user.name}`);
            console.log(`   🏢 Organization: ${planSquareLoginData.data.user.organization?.name}`);
            console.log(`   🆔 Organization ID: ${planSquareOrgId}`);

            // Check PlanSquare employees
            const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${planSquareToken}`,
                    'X-Organization-ID': planSquareOrgId,
                    'Content-Type': 'application/json'
                }
            });

            if (planSquareEmployeesResponse.ok) {
                const planSquareEmployeesData = await planSquareEmployeesResponse.json();
                const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
                
                console.log(`   📊 PlanSquare employees: ${employees.length}`);
                employees.forEach((emp, index) => {
                    console.log(`      ${index + 1}. ${emp.name} (${emp.email})`);
                });
            }
        } else {
            console.log(`❌ PlanSquare login failed (unexpected error)`);
        }

        // Step 7: Summary
        console.log('\n7️⃣ DELETION SUMMARY');
        console.log('-'.repeat(30));
        console.log('✅ Test Company organization deletion completed');
        console.log('✅ PlanSquare organization preserved');
        console.log('✅ System now has single organization for testing');
        console.log('\n🎯 Next Steps:');
        console.log('   1. Test multi-tenant isolation with single organization');
        console.log('   2. Verify database schema separation');
        console.log('   3. Fix any remaining tenant isolation issues');
        
        console.log('\n🔑 Active Credentials:');
        console.log('   Super Admin: <EMAIL> / SuperAdmin@123');
        console.log('   PlanSquare Admin: <EMAIL> / PlanSquare@123');
        console.log('   PlanSquare Employee: <EMAIL> / [check employee records]');

    } catch (error) {
        console.error('❌ Deletion process failed:', error.message);
    }
}

// Run the deletion
deleteTestCompanyOrganization();
