const sql = require('mssql');

const config = {
  server: '103.145.50.203',
  port: 2829,
  database: 'dbHRMS',
  user: 'userHRMS',
  password: 'P@ssw0rd123',
  options: {
    encrypt: true,
    trustServerCertificate: true,
    connectTimeout: 30000,
    requestTimeout: 30000
  }
};

async function testTaskUpdatesWithEmployeeNames() {
  try {
    console.log('🔍 Testing Task Updates Employee Name Resolution...\n');
    
    // Connect to database
    await sql.connect(config);
    console.log('✅ Connected to SQL Server database');

    // Check if there are any task updates
    const taskUpdatesQuery = `
      SELECT TOP 10
        tu.Id,
        tu.TaskId,
        tu.UpdatedById,
        tu.UpdateNotes,
        tu.CreatedAt,
        t.Title as TaskTitle,
        u.Name as EmployeeName,
        u.Email as EmployeeEmail,
        ed.EmployeeId as EmployeeNumber
      FROM TaskUpdates tu
      INNER JOIN Tasks t ON tu.TaskId = t.Id
      LEFT JOIN Users u ON tu.UpdatedById = u.Id
      LEFT JOIN EmployeeDetails ed ON u.Id = ed.UserId
      ORDER BY tu.CreatedAt DESC
    `;

    const result = await sql.query(taskUpdatesQuery);
    
    if (result.recordset.length === 0) {
      console.log('⚠️  No task updates found in the database');
      console.log('   This might be because no employees have submitted daily updates yet.');
      console.log('   To test the fix:');
      console.log('   1. Log in as an employee');
      console.log('   2. Go to Task Management');
      console.log('   3. Submit a daily update for a task');
      console.log('   4. Then log in as Organization Admin to see the updates');
    } else {
      console.log(`📊 Found ${result.recordset.length} task updates:\n`);
      
      result.recordset.forEach((update, index) => {
        console.log(`${index + 1}. Task Update:`);
        console.log(`   Task: ${update.TaskTitle}`);
        console.log(`   Employee: ${update.EmployeeName || 'NULL'} (${update.EmployeeEmail || 'NULL'})`);
        console.log(`   Employee ID: ${update.EmployeeNumber || 'NULL'}`);
        console.log(`   Update: ${update.UpdateNotes.substring(0, 50)}...`);
        console.log(`   Date: ${update.CreatedAt}`);
        console.log(`   UpdatedById: ${update.UpdatedById}`);
        console.log('');
      });

      // Check for any updates with missing employee names
      const updatesWithoutNames = result.recordset.filter(u => !u.EmployeeName);
      if (updatesWithoutNames.length > 0) {
        console.log(`⚠️  Found ${updatesWithoutNames.length} task updates with missing employee names`);
        console.log('   This indicates the UpdatedById field might be NULL or pointing to non-existent users');
      } else {
        console.log('✅ All task updates have proper employee names!');
      }
    }

    // Check the organization admin API endpoint simulation
    console.log('\n🔧 Testing API Response Structure...');
    
    const apiTestQuery = `
      SELECT TOP 5
        tu.Id,
        tu.TaskId,
        t.Title as TaskTitle,
        u.Id as EmployeeId,
        u.Name as EmployeeName,
        ed.EmployeeId as EmployeeNumber,
        tu.ProgressPercentage,
        tu.HoursSpent,
        tu.UpdateNotes,
        tu.Blockers,
        tu.NextSteps,
        tu.UpdateDate,
        tu.UpdateType,
        tu.CreatedAt
      FROM TaskUpdates tu
      INNER JOIN Tasks t ON tu.TaskId = t.Id
      INNER JOIN Users u ON tu.UpdatedById = u.Id
      LEFT JOIN EmployeeDetails ed ON u.Id = ed.UserId
      WHERE t.AssignedToId IN (
        SELECT Id FROM Users WHERE OrganizationId = (
          SELECT TOP 1 OrganizationId FROM Users WHERE Email = '<EMAIL>'
        )
      )
      ORDER BY tu.CreatedAt DESC
    `;

    const apiResult = await sql.query(apiTestQuery);
    
    if (apiResult.recordset.length > 0) {
      console.log('✅ API simulation successful - sample response:');
      const sample = apiResult.recordset[0];
      console.log(JSON.stringify({
        id: sample.Id,
        taskId: sample.TaskId,
        taskTitle: sample.TaskTitle,
        employee: {
          id: sample.EmployeeId,
          name: sample.EmployeeName,
          employeeId: sample.EmployeeNumber
        },
        progressPercentage: sample.ProgressPercentage,
        hoursSpent: sample.HoursSpent,
        updateNotes: sample.UpdateNotes,
        blockers: sample.Blockers,
        nextSteps: sample.NextSteps,
        updateDate: sample.UpdateDate,
        updateType: sample.UpdateType,
        createdAt: sample.CreatedAt
      }, null, 2));
    } else {
      console.log('⚠️  No task updates found for the organization');
    }

  } catch (error) {
    console.error('❌ Error testing task updates:', error.message);
  } finally {
    await sql.close();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the test
testTaskUpdatesWithEmployeeNames();
