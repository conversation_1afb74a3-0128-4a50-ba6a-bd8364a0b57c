// Check if admin user has EmployeeDetail record
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function checkAdminEmployeeDetails() {
    console.log('🔍 Checking Admin User EmployeeDetail Records...\n');

    let pool;
    try {
        // Connect to database
        console.log('1️⃣ Connecting to database...');
        pool = await sql.connect(config);
        console.log('✅ Connected successfully');

        // Check Akash admin user
        console.log('\n2️⃣ Checking Akash admin user...');
        const akashQuery = `
            SELECT u.Id, u.Name, u.Email, u.Role, u.OrganizationId, u.IsActive, u.IsDeleted,
                   o.Name as OrgName
            FROM Users u
            LEFT JOIN Organizations o ON u.OrganizationId = o.Id
            WHERE u.Email = '<EMAIL>'
        `;
        
        const akashResult = await pool.request().query(akashQuery);
        
        if (akashResult.recordset.length === 0) {
            console.log('❌ Akash admin user not found');
            return;
        }
        
        const akashUser = akashResult.recordset[0];
        console.log(`✅ Found Akash admin user:`);
        console.log(`   - User ID: ${akashUser.Id}`);
        console.log(`   - Name: ${akashUser.Name}`);
        console.log(`   - Email: ${akashUser.Email}`);
        console.log(`   - Role: ${akashUser.Role}`);
        console.log(`   - Organization ID: ${akashUser.OrganizationId}`);
        console.log(`   - Organization: ${akashUser.OrgName}`);
        console.log(`   - Active: ${akashUser.IsActive}`);
        console.log(`   - Deleted: ${akashUser.IsDeleted}`);

        // Check EmployeeDetail for Akash
        console.log('\n3️⃣ Checking EmployeeDetail for Akash...');
        const employeeDetailQuery = `
            SELECT ed.Id, ed.UserId, ed.EmployeeId, ed.JobTitle, ed.Department, 
                   ed.JoinDate, ed.EmploymentType, ed.EmploymentStatus, ed.WorkLocation,
                   ed.BaseSalary, ed.AnnualCTC, ed.CreatedAt, ed.UpdatedAt
            FROM EmployeeDetails ed
            WHERE ed.UserId = '${akashUser.Id}'
        `;
        
        const employeeDetailResult = await pool.request().query(employeeDetailQuery);
        
        if (employeeDetailResult.recordset.length === 0) {
            console.log('❌ No EmployeeDetail found for Akash admin user');
            console.log('   This explains why admin is not appearing in employee list');
        } else {
            const employeeDetail = employeeDetailResult.recordset[0];
            console.log(`✅ Found EmployeeDetail for Akash:`);
            console.log(`   - Detail ID: ${employeeDetail.Id}`);
            console.log(`   - Employee ID: ${employeeDetail.EmployeeId}`);
            console.log(`   - Job Title: ${employeeDetail.JobTitle}`);
            console.log(`   - Department: ${employeeDetail.Department}`);
            console.log(`   - Join Date: ${employeeDetail.JoinDate}`);
            console.log(`   - Employment Type: ${employeeDetail.EmploymentType}`);
            console.log(`   - Employment Status: ${employeeDetail.EmploymentStatus}`);
            console.log(`   - Work Location: ${employeeDetail.WorkLocation}`);
            console.log(`   - Base Salary: ${employeeDetail.BaseSalary}`);
            console.log(`   - Annual CTC: ${employeeDetail.AnnualCTC}`);
            console.log(`   - Created: ${employeeDetail.CreatedAt}`);
            console.log(`   - Updated: ${employeeDetail.UpdatedAt}`);
        }

        // Check all users with EmployeeDetails in PlanSquare org
        console.log('\n4️⃣ Checking all PlanSquare users with EmployeeDetails...');
        const allEmployeesQuery = `
            SELECT u.Id, u.Name, u.Email, u.Role, u.IsActive, u.IsDeleted,
                   ed.EmployeeId, ed.JobTitle, ed.Department, ed.EmploymentStatus
            FROM Users u
            LEFT JOIN EmployeeDetails ed ON u.Id = ed.UserId
            LEFT JOIN Organizations o ON u.OrganizationId = o.Id
            WHERE o.Name = 'PlanSquare '
            ORDER BY u.Role DESC, u.Name
        `;
        
        const allEmployeesResult = await pool.request().query(allEmployeesQuery);
        console.log(`Found ${allEmployeesResult.recordset.length} PlanSquare users:`);
        
        allEmployeesResult.recordset.forEach((user, index) => {
            console.log(`\n   ${index + 1}. ${user.Name} (${user.Email})`);
            console.log(`      - Role: ${user.Role}`);
            console.log(`      - Active: ${user.IsActive}, Deleted: ${user.IsDeleted}`);
            console.log(`      - Has EmployeeDetail: ${user.EmployeeId ? 'YES' : 'NO'}`);
            if (user.EmployeeId) {
                console.log(`      - Employee ID: ${user.EmployeeId}`);
                console.log(`      - Job Title: ${user.JobTitle}`);
                console.log(`      - Department: ${user.Department}`);
                console.log(`      - Status: ${user.EmploymentStatus}`);
            }
        });

        // Create EmployeeDetail for Akash if missing
        if (employeeDetailResult.recordset.length === 0) {
            console.log('\n5️⃣ Creating EmployeeDetail for Akash admin...');
            const createEmployeeDetailQuery = `
                INSERT INTO EmployeeDetails (
                    Id, UserId, EmployeeId, JobTitle, Department, JoinDate, 
                    EmploymentType, EmploymentStatus, WorkLocation, BaseSalary, AnnualCTC,
                    CreatedAt, UpdatedAt
                ) VALUES (
                    NEWID(), 
                    '${akashUser.Id}', 
                    'PS_ADMIN_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}',
                    'Organization Administrator',
                    'Administration',
                    GETUTCDATE(),
                    1, -- FullTime
                    1, -- Active
                    'Head Office',
                    120000,
                    150000,
                    GETUTCDATE(),
                    GETUTCDATE()
                )
            `;
            
            await pool.request().query(createEmployeeDetailQuery);
            console.log('✅ Created EmployeeDetail for Akash admin');
            
            // Verify creation
            const verifyResult = await pool.request().query(employeeDetailQuery);
            if (verifyResult.recordset.length > 0) {
                const newDetail = verifyResult.recordset[0];
                console.log(`✅ Verification successful:`);
                console.log(`   - Employee ID: ${newDetail.EmployeeId}`);
                console.log(`   - Job Title: ${newDetail.JobTitle}`);
                console.log(`   - Department: ${newDetail.Department}`);
            }
        }

        console.log('\n6️⃣ SUMMARY');
        console.log('-'.repeat(30));
        console.log(`✅ Akash admin user exists in Users table`);
        console.log(`${employeeDetailResult.recordset.length > 0 ? '✅' : '❌'} EmployeeDetail exists for admin`);
        console.log(`✅ Admin should now appear in employee list`);

    } catch (error) {
        console.error('❌ Check failed:', error.message);
    } finally {
        if (pool) {
            await pool.close();
            console.log('\n🔌 Database connection closed');
        }
    }
}

// Run the check
checkAdminEmployeeDetails();
