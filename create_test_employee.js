// Script to create employee "Abhishek" and test attendance functionality
const API_BASE = 'http://localhost:5020/api/v1';

async function createAbhishekEmployee() {
    console.log('🧪 Creating Employee "Abhishek" and Testing Attendance...\n');

    try {
        // Step 1: Login as Organization Admin
        console.log('1️⃣ Logging in as Organization Admin...');
        const adminLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!adminLoginResponse.ok) {
            throw new Error(`Admin login failed: ${adminLoginResponse.status}`);
        }

        const adminLoginData = await adminLoginResponse.json();
        const adminToken = adminLoginData.data.token;
        const organizationId = adminLoginData.data.user.organization?.id;
        
        console.log(`✅ Admin login successful. Organization ID: ${organizationId}`);

        // Step 2: Create employee "Abhishek"
        const testEmail = '<EMAIL>';

        console.log(`\n2️⃣ Creating employee "Abhishek" with email: ${testEmail}...`);
        const createEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`,
                'X-Organization-ID': organizationId
            },
            body: JSON.stringify({
                name: 'Abhishek',
                email: testEmail,
                role: 'Employee',
                employeeDetails: {
                    employeeId: 'EMP_ABHISHEK_001',
                    jobTitle: 'Software Developer',
                    department: 'Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 75000,
                    annualCTC: 90000
                }
            })
        });

        if (!createEmployeeResponse.ok) {
            const errorText = await createEmployeeResponse.text();
            throw new Error(`Employee creation failed: ${createEmployeeResponse.status} - ${errorText}`);
        }

        const createEmployeeData = await createEmployeeResponse.json();
        const employeeCredentials = {
            email: createEmployeeData.data.email,
            password: createEmployeeData.data.loginTemporaryPassword
        };
        
        console.log(`✅ Employee "Abhishek" created successfully!`);
        console.log(`   📧 Email: ${employeeCredentials.email}`);
        console.log(`   🔑 Password: ${employeeCredentials.password}`);

        // Step 3: Test login with Abhishek
        console.log(`\n3️⃣ Testing login with Abhishek...`);
        const employeeLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(employeeCredentials)
        });

        if (!employeeLoginResponse.ok) {
            const errorText = await employeeLoginResponse.text();
            throw new Error(`Employee login failed: ${employeeLoginResponse.status} - ${errorText}`);
        }

        const employeeLoginData = await employeeLoginResponse.json();
        const employeeToken = employeeLoginData.data.token;
        const employeeOrgId = employeeLoginData.data.user.organization?.id;
        
        console.log(`✅ Abhishek login successful. Organization ID: ${employeeOrgId}`);

        // Step 4: Test Attendance Check-in
        console.log(`\n4️⃣ Testing Attendance Check-in...`);
        const checkInResponse = await fetch(`${API_BASE}/attendance/checkin`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                timestamp: new Date().toISOString(),
                location: 'Office',
                notes: 'Starting work for the day'
            })
        });

        console.log(`   Check-in Response Status: ${checkInResponse.status}`);

        if (checkInResponse.ok) {
            const checkInData = await checkInResponse.json();
            console.log(`   ✅ Check-in: SUCCESS (${checkInResponse.status})`);
            console.log(`   📊 Response: ${JSON.stringify(checkInData, null, 2)}`);
        } else {
            const errorText = await checkInResponse.text();
            console.log(`   ❌ Check-in: FAILED (${checkInResponse.status})`);
            console.log(`   🚨 Error: ${errorText}`);
        }

        // Step 5: Test Attendance Records endpoint
        console.log(`\n5️⃣ Testing Attendance Records endpoint...`);
        const attendanceRecordsResponse = await fetch(`${API_BASE}/attendance/records`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Attendance Records Response Status: ${attendanceRecordsResponse.status}`);

        if (attendanceRecordsResponse.ok) {
            const data = await attendanceRecordsResponse.json();
            console.log(`   ✅ Attendance Records: SUCCESS (${attendanceRecordsResponse.status})`);
            console.log(`   📊 Response: ${JSON.stringify(data, null, 2)}`);
        } else {
            const errorText = await attendanceRecordsResponse.text();
            console.log(`   ❌ Attendance Records: FAILED (${attendanceRecordsResponse.status})`);
            console.log(`   🚨 Error: ${errorText}`);
        }

        console.log('\n🎉 Employee "Abhishek" Creation and Attendance Test Completed!');
        console.log(`\n📝 Use these credentials for Abhishek:`);
        console.log(`   Email: ${employeeCredentials.email}`);
        console.log(`   Password: ${employeeCredentials.password}`);

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
createAbhishekEmployee();
