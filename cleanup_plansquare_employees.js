// Clean up PlanSquare organization employees to keep only 3 legitimate employees
const API_BASE = 'http://localhost:5020/api/v1';

async function cleanupPlanSquareEmployees() {
    console.log('🧹 Cleaning up PlanSquare Employee Database...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as PlanSquare Admin
        console.log('\n1️⃣ PLANSQUARE ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`PlanSquare admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`✅ PlanSquare Admin login successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);
        console.log(`   🏢 Organization: ${planSquareData.data.user.organization?.name}`);
        console.log(`   🆔 Organization ID: ${planSquareOrgId}`);

        // Step 2: Get all current employees
        console.log('\n2️⃣ FETCHING CURRENT EMPLOYEES');
        console.log('-'.repeat(30));
        
        const employeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!employeesResponse.ok) {
            throw new Error(`Failed to fetch employees: ${employeesResponse.status}`);
        }

        const employeesData = await employeesResponse.json();
        const employees = employeesData.data.items;
        
        console.log(`📊 Found ${employees.length} total employees in PlanSquare organization:`);
        employees.forEach((emp, index) => {
            console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role} - ID: ${emp.id}`);
            if (emp.employeeDetail) {
                console.log(`      Employee ID: ${emp.employeeDetail.employeeId}, Department: ${emp.employeeDetail.department}`);
            }
        });

        // Step 3: Identify legitimate employees (keep these 3)
        console.log('\n3️⃣ IDENTIFYING LEGITIMATE EMPLOYEES');
        console.log('-'.repeat(30));
        
        const legitimateEmails = [
            '<EMAIL>',      // Organization Admin
            '<EMAIL>',   // Employee 1
            '<EMAIL>'       // Employee 2
        ];

        const legitimateEmployees = employees.filter(emp => 
            legitimateEmails.includes(emp.email.toLowerCase())
        );

        const unwantedEmployees = employees.filter(emp => 
            !legitimateEmails.includes(emp.email.toLowerCase())
        );

        console.log(`✅ Legitimate employees to keep (${legitimateEmployees.length}):`);
        legitimateEmployees.forEach((emp, index) => {
            console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role}`);
        });

        console.log(`❌ Unwanted employees to remove (${unwantedEmployees.length}):`);
        unwantedEmployees.forEach((emp, index) => {
            console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role} - ID: ${emp.id}`);
        });

        // Step 4: Delete unwanted employees
        if (unwantedEmployees.length > 0) {
            console.log('\n4️⃣ DELETING UNWANTED EMPLOYEES');
            console.log('-'.repeat(30));
            
            for (const employee of unwantedEmployees) {
                try {
                    console.log(`🗑️  Deleting: ${employee.name} (${employee.email})`);
                    
                    const deleteResponse = await fetch(`${API_BASE}/employee-management/employees/${employee.id}`, {
                        method: 'DELETE',
                        headers: { 
                            'Authorization': `Bearer ${planSquareToken}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (deleteResponse.ok) {
                        console.log(`   ✅ Successfully deleted ${employee.name}`);
                    } else {
                        const errorData = await deleteResponse.json();
                        console.log(`   ❌ Failed to delete ${employee.name}: ${errorData.message || 'Unknown error'}`);
                    }
                } catch (error) {
                    console.log(`   ❌ Error deleting ${employee.name}: ${error.message}`);
                }
            }
        } else {
            console.log('\n4️⃣ NO UNWANTED EMPLOYEES FOUND');
            console.log('-'.repeat(30));
            console.log('✅ Database is already clean - only legitimate employees exist');
        }

        // Step 5: Verify final state
        console.log('\n5️⃣ VERIFYING FINAL STATE');
        console.log('-'.repeat(30));
        
        const finalEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (finalEmployeesResponse.ok) {
            const finalEmployeesData = await finalEmployeesResponse.json();
            const finalEmployees = finalEmployeesData.data.items;
            
            console.log(`📊 Final employee count: ${finalEmployees.length}`);
            console.log('📋 Remaining employees:');
            finalEmployees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role}`);
            });

            if (finalEmployees.length === 3) {
                console.log('\n🎉 SUCCESS: PlanSquare database cleaned successfully!');
                console.log('✅ Only 3 legitimate employees remain in the database');
            } else {
                console.log(`\n⚠️  WARNING: Expected 3 employees but found ${finalEmployees.length}`);
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🏁 CLEANUP COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ CLEANUP FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the API connection and try again.');
    }
}

// Run the cleanup
cleanupPlanSquareEmployees();
