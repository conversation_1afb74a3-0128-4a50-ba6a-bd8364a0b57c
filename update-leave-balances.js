/**
 * <PERSON><PERSON><PERSON> to update existing leave balance records with dynamic calculations
 * This script calls the API to sync all leave balances
 */

const API_BASE_URL = 'http://localhost:5020/api/v1';

// Test credentials - you'll need to use actual credentials
const CREDENTIALS = {
    superAdmin: {
        email: '<EMAIL>',
        password: 'SuperAdmin@123'
    },
    orgAdmin: {
        email: '<EMAIL>',
        password: 'OrgAdmin@123'
    }
};

/**
 * <PERSON><PERSON> and get JWT token
 */
async function login(email, password) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();
        
        if (data.success && data.data?.token) {
            console.log(`✅ Login successful for ${email}`);
            return data.data.token;
        } else {
            console.error(`❌ Login failed for ${email}:`, data.error?.message || 'Unknown error');
            return null;
        }
    } catch (error) {
        console.error(`❌ Login error for ${email}:`, error.message);
        return null;
    }
}

/**
 * Sync leave balances for organization
 */
async function syncOrganizationLeaveBalances(token) {
    try {
        const response = await fetch(`${API_BASE_URL}/leave-balance-sync/sync-organization`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Organization leave balances synced successfully:', data.message);
            return true;
        } else {
            console.error('❌ Failed to sync organization leave balances:', data.error?.message || 'Unknown error');
            return false;
        }
    } catch (error) {
        console.error('❌ Error syncing organization leave balances:', error.message);
        return false;
    }
}

/**
 * Sync all leave balances (SuperAdmin only)
 */
async function syncAllLeaveBalances(token) {
    try {
        const response = await fetch(`${API_BASE_URL}/leave-balance-sync/sync-all`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();
        
        if (data.success) {
            console.log('✅ All leave balances synced successfully:', data.message);
            return true;
        } else {
            console.error('❌ Failed to sync all leave balances:', data.error?.message || 'Unknown error');
            return false;
        }
    } catch (error) {
        console.error('❌ Error syncing all leave balances:', error.message);
        return false;
    }
}

/**
 * Get sync status
 */
async function getSyncStatus(token) {
    try {
        const response = await fetch(`${API_BASE_URL}/leave-balance-sync/status`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();
        
        if (data.success) {
            console.log('📊 Sync Status:', data.data);
            return data.data;
        } else {
            console.error('❌ Failed to get sync status:', data.error?.message || 'Unknown error');
            return null;
        }
    } catch (error) {
        console.error('❌ Error getting sync status:', error.message);
        return null;
    }
}

/**
 * Main function to update leave balances
 */
async function main() {
    console.log('🚀 Starting Leave Balance Update Process...\n');

    // Try to login as SuperAdmin first
    console.log('🔐 Attempting SuperAdmin login...');
    let token = await login(CREDENTIALS.superAdmin.email, CREDENTIALS.superAdmin.password);
    
    if (token) {
        console.log('👑 SuperAdmin login successful, syncing all leave balances...\n');
        
        // Get sync status
        await getSyncStatus(token);
        
        // Sync all leave balances
        const success = await syncAllLeaveBalances(token);
        
        if (success) {
            console.log('\n🎉 All leave balances have been updated successfully!');
            console.log('📝 The database now contains dynamically calculated leave balances.');
            console.log('🔄 Frontend should now display correct leave balance values.');
        } else {
            console.log('\n⚠️  Some issues occurred during sync. Check the logs above.');
        }
    } else {
        // Fallback to OrgAdmin
        console.log('🔐 SuperAdmin login failed, trying OrgAdmin...');
        token = await login(CREDENTIALS.orgAdmin.email, CREDENTIALS.orgAdmin.password);
        
        if (token) {
            console.log('👤 OrgAdmin login successful, syncing organization leave balances...\n');
            
            // Get sync status
            await getSyncStatus(token);
            
            // Sync organization leave balances
            const success = await syncOrganizationLeaveBalances(token);
            
            if (success) {
                console.log('\n🎉 Organization leave balances have been updated successfully!');
                console.log('📝 The database now contains dynamically calculated leave balances for this organization.');
                console.log('🔄 Frontend should now display correct leave balance values.');
            } else {
                console.log('\n⚠️  Some issues occurred during sync. Check the logs above.');
            }
        } else {
            console.log('\n❌ Both SuperAdmin and OrgAdmin login failed.');
            console.log('📋 Please check the credentials and try again.');
            console.log('🔧 You can also manually call the API endpoints:');
            console.log('   - POST /api/v1/leave-balance-sync/sync-organization (OrgAdmin)');
            console.log('   - POST /api/v1/leave-balance-sync/sync-all (SuperAdmin)');
        }
    }
}

// Run the script
if (typeof window === 'undefined') {
    // Node.js environment
    main().catch(console.error);
} else {
    // Browser environment
    console.log('This script is designed to run in Node.js environment.');
    console.log('Please run: node update-leave-balances.js');
}
