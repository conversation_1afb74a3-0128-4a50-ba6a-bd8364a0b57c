# External SQL Server Setup Guide

This guide explains how to configure the HRMS application to connect to an external SQL Server database.

## Prerequisites

1. **SQL Server Instance**: Access to a SQL Server instance (on-premises or cloud-based)
2. **Database Credentials**: Username and password with appropriate permissions
3. **Network Access**: Ensure the application server can reach the SQL Server instance
4. **SQL Server Permissions**: The provided user should have:
   - `db_owner` permissions on the target database
   - Permission to create schemas
   - Permission to create tables and indexes

## Configuration Steps

### 1. Set Environment Variables

The application uses environment variables to configure the SQL Server connection. Set the following variables:

```bash
# Required SQL Server connection details
export SQL_SERVER_HOST="your-sql-server-host"
export SQL_SERVER_DATABASE="hrms_master"
export SQL_SERVER_USERNAME="your-username"
export SQL_SERVER_PASSWORD="your-password"
```

**Examples:**
- **Azure SQL Database**: `SQL_SERVER_HOST="your-server.database.windows.net"`
- **AWS RDS SQL Server**: `SQL_SERVER_HOST="your-instance.region.rds.amazonaws.com"`
- **On-premises**: `SQL_SERVER_HOST="*************"` or `SQL_SERVER_HOST="sql-server.company.com"`

### 2. Database Setup

The application will automatically:
- Create the master database if it doesn't exist
- Apply Entity Framework migrations
- Set up the multi-tenant schema architecture

**Manual Database Creation (Optional):**
If you prefer to create the database manually:

```sql
-- Create the master database
CREATE DATABASE hrms_master;

-- Create a user for the application (if using SQL Server authentication)
CREATE LOGIN hrms_app_user WITH PASSWORD = 'YourSecurePassword123!';
USE hrms_master;
CREATE USER hrms_app_user FOR LOGIN hrms_app_user;
ALTER ROLE db_owner ADD MEMBER hrms_app_user;
```

### 3. Connection String Format

The application uses this connection string template:
```
Server={SQL_SERVER_HOST};Database={SQL_SERVER_DATABASE};User Id={SQL_SERVER_USERNAME};Password={SQL_SERVER_PASSWORD};TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;
```

### 4. Multi-Tenant Schema Architecture

The application automatically creates organization-specific schemas:
- **Master Schema**: `dbo` (contains organization metadata and user accounts)
- **Organization Schemas**: `org_<organization_id>` (contains organization-specific data)

Example schemas:
- `dbo` - Master data
- `org_123e4567e89b12d3a456426614174000` - Organization 1 data
- `org_987fcdeb51a2b3c4d5e6f78901234567` - Organization 2 data

## Deployment Options

### Option 1: Environment Variables (Recommended)

Set environment variables on your deployment platform:

**Docker:**
```bash
docker run -e SQL_SERVER_HOST="your-host" \
           -e SQL_SERVER_DATABASE="hrms_master" \
           -e SQL_SERVER_USERNAME="your-user" \
           -e SQL_SERVER_PASSWORD="your-password" \
           your-hrms-app
```

**Kubernetes:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hrms-api
spec:
  template:
    spec:
      containers:
      - name: hrms-api
        env:
        - name: SQL_SERVER_HOST
          value: "your-sql-server-host"
        - name: SQL_SERVER_DATABASE
          value: "hrms_master"
        - name: SQL_SERVER_USERNAME
          valueFrom:
            secretKeyRef:
              name: sql-credentials
              key: username
        - name: SQL_SERVER_PASSWORD
          valueFrom:
            secretKeyRef:
              name: sql-credentials
              key: password
```

### Option 2: Direct Configuration File Edit

If you cannot use environment variables, directly edit the connection string in:
- `backend/HRMS.API/appsettings.json`
- `backend/HRMS.API/appsettings.Development.json`

Replace the placeholders with actual values:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-actual-host;Database=hrms_master;User Id=your-username;Password=your-password;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;"
  }
}
```

## Security Considerations

1. **Use Strong Passwords**: Ensure SQL Server passwords are complex and secure
2. **Network Security**: Use VPN or private networks when possible
3. **SSL/TLS**: Enable encryption for SQL Server connections in production
4. **Firewall Rules**: Restrict SQL Server access to application servers only
5. **Regular Updates**: Keep SQL Server updated with security patches

## Troubleshooting

### Connection Issues

1. **Check Network Connectivity:**
   ```bash
   telnet your-sql-server-host 1433
   ```

2. **Verify Credentials:**
   - Test connection using SQL Server Management Studio
   - Ensure user has necessary permissions

3. **Check Application Logs:**
   - Look for connection string validation errors
   - Check for SQL Server authentication failures

### Common Error Messages

- **"SQL Server connection string contains unresolved placeholders"**
  - Solution: Set all required environment variables

- **"Login failed for user"**
  - Solution: Verify username/password and user permissions

- **"A network-related or instance-specific error occurred"**
  - Solution: Check network connectivity and SQL Server configuration

## Testing the Connection

After configuration, the application will:
1. Validate the connection string on startup
2. Create the master database if needed
3. Apply Entity Framework migrations
4. Log successful database initialization

Check the application logs for:
```
[Information] Database initialization completed successfully
[Information] Master database created successfully
[Information] Master database migrations applied successfully
```

## Next Steps

Once the external SQL Server is configured:
1. Start the HRMS API application
2. Verify successful database connection in logs
3. Test organization registration (which creates tenant schemas)
4. Monitor database for proper schema creation

For additional support, check the application logs and ensure all environment variables are properly set.
