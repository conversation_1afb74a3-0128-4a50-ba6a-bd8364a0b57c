// Create Priya with a guaranteed unique employee ID based on database analysis
const API_BASE = 'http://localhost:5020/api/v1';

async function createPriyaGuaranteedUnique() {
    console.log('👤 Creating P<PERSON> with Guaranteed Unique Employee ID...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as PlanSquare Admin
        console.log('\n1️⃣ PLANSQUARE ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`PlanSquare admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        
        console.log(`✅ PlanSquare Admin login successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);

        // Step 2: Create Priya with a unique employee ID that doesn't exist in database
        console.log('\n2️⃣ CREATING PRIYA EMPLOYEE');
        console.log('-'.repeat(30));
        
        // Based on database analysis, these IDs are NOT in use:
        // Let's use a completely different pattern that won't conflict
        const uniqueEmployeeId = `PRIYA_DESIGN_2025`;
        
        const priyaEmployeeData = {
            name: "Priya Sharma",
            email: "<EMAIL>",
            employeeId: uniqueEmployeeId,
            jobTitle: "UI/UX Designer",
            department: "Design",
            joinDate: "2024-01-15",
            employmentType: "full-time",
            workLocation: "Mumbai Office",
            phone: "+91-9876543212",
            address: "456 Design Street, Mumbai, Maharashtra 400001",
            dateOfBirth: "1995-08-20",
            baseSalary: 650000,
            annualCtc: 780000
        };

        console.log(`📝 Creating employee: ${priyaEmployeeData.name} (${priyaEmployeeData.email})`);
        console.log(`   Employee ID: ${priyaEmployeeData.employeeId}`);
        console.log(`   Job Title: ${priyaEmployeeData.jobTitle}`);
        console.log(`   Department: ${priyaEmployeeData.department}`);

        const createEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(priyaEmployeeData)
        });

        if (createEmployeeResponse.ok) {
            const createdEmployee = await createEmployeeResponse.json();
            console.log(`✅ Successfully created Priya employee`);
            console.log(`   User ID: ${createdEmployee.data.id}`);
            console.log(`   Login Username: ${createdEmployee.data.loginUsername || 'Not generated'}`);
            console.log(`   Temporary Password: ${createdEmployee.data.temporaryPassword || 'Not generated'}`);
            
            if (createdEmployee.data.loginUsername && createdEmployee.data.temporaryPassword) {
                console.log('\n🔑 LOGIN CREDENTIALS FOR PRIYA:');
                console.log(`   Username: ${createdEmployee.data.loginUsername}`);
                console.log(`   Password: ${createdEmployee.data.temporaryPassword}`);
                console.log('   ⚠️  Please save these credentials - they will not be shown again!');
            }
        } else {
            const errorData = await createEmployeeResponse.json();
            console.log(`❌ Failed to create Priya employee: ${errorData.message || 'Unknown error'}`);
            console.log('Error details:', JSON.stringify(errorData, null, 2));
            
            // Try with different variations if it still fails
            const alternativeIds = [
                `PRIYA_UI_UX_2025`,
                `PS_PRIYA_SHARMA_2025`,
                `DESIGN_PRIYA_001`,
                `PLANSQUARE_PRIYA_${Date.now()}`,
                `EMPLOYEE_PRIYA_${Math.floor(Math.random() * 10000)}`
            ];
            
            for (const altId of alternativeIds) {
                console.log(`\n🔄 Trying alternative Employee ID: ${altId}`);
                priyaEmployeeData.employeeId = altId;
                
                const retryResponse = await fetch(`${API_BASE}/employee-management/employees`, {
                    method: 'POST',
                    headers: { 
                        'Authorization': `Bearer ${planSquareToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(priyaEmployeeData)
                });
                
                if (retryResponse.ok) {
                    const retryEmployee = await retryResponse.json();
                    console.log(`✅ Successfully created Priya employee with ID: ${altId}`);
                    console.log(`   User ID: ${retryEmployee.data.id}`);
                    
                    if (retryEmployee.data.loginUsername && retryEmployee.data.temporaryPassword) {
                        console.log('\n🔑 LOGIN CREDENTIALS FOR PRIYA:');
                        console.log(`   Username: ${retryEmployee.data.loginUsername}`);
                        console.log(`   Password: ${retryEmployee.data.temporaryPassword}`);
                        console.log('   ⚠️  Please save these credentials - they will not be shown again!');
                    }
                    break;
                } else {
                    const retryError = await retryResponse.json();
                    console.log(`   ❌ Failed with ${altId}: ${retryError.error?.message || 'Unknown error'}`);
                }
            }
        }

        // Step 3: Final verification
        console.log('\n3️⃣ FINAL VERIFICATION');
        console.log('-'.repeat(30));
        
        const finalEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (finalEmployeesResponse.ok) {
            const finalEmployeesData = await finalEmployeesResponse.json();
            const finalEmployees = finalEmployeesData.data.items;
            
            console.log(`📊 Final employee count: ${finalEmployees.length}`);
            console.log('📋 All PlanSquare employees:');
            finalEmployees.forEach((emp, index) => {
                const empId = emp.employeeDetail?.employeeId || 'N/A';
                const dept = emp.employeeDetail?.department || 'N/A';
                const jobTitle = emp.employeeDetail?.jobTitle || 'N/A';
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role}`);
                console.log(`      Employee ID: ${empId}, Job: ${jobTitle}, Dept: ${dept}`);
            });

            if (finalEmployees.length === 3) {
                console.log('\n🎉 SUCCESS: PlanSquare now has exactly 3 employees!');
                console.log('✅ Database cleanup completed successfully');
                console.log('✅ Ready for clean development work');
                
                console.log('\n📋 SUMMARY OF PLANSQUARE EMPLOYEES:');
                console.log('1. Akash Jadhav (<EMAIL>) - Organization Admin');
                console.log('2. Abhishek (<EMAIL>) - Employee (Engineering)');
                console.log('3. Priya Sharma (<EMAIL>) - Employee (Design)');
                
                console.log('\n✅ CLEANUP COMPLETED SUCCESSFULLY!');
                console.log('🚀 The PlanSquare organization database is now clean and ready for development.');
            } else {
                console.log(`\n⚠️  Current count: ${finalEmployees.length} employees (expected 3)`);
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🏁 PROCESS COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ PROCESS FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the API connection and try again.');
    }
}

// Run the process
createPriyaGuaranteedUnique();
