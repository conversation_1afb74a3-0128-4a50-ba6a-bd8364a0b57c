# HRMS Database Provisioning System

## Overview

This automated database provisioning system creates isolated database schemas for each new organization in the HRMS application. It provides a complete multi-tenant architecture with automated schema creation, data seeding, and monitoring.

## Features

- **Automated Schema Provisioning**: Creates complete database schemas for new organizations
- **Multi-tenant Architecture**: Isolated schemas for each organization
- **Configuration Management**: Flexible YAML/JSON configuration system
- **Error Handling & Rollback**: Comprehensive error handling with automatic rollback
- **Monitoring & Metrics**: Built-in monitoring with Prometheus metrics
- **Retry Logic**: Configurable retry mechanisms for failed provisioning
- **Audit Logging**: Complete audit trail for all provisioning activities
- **Security**: Role-based permissions and data isolation

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API    │    │   Master DB     │
│   Registration  │───▶│   Controller     │───▶│   (Global)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Provisioning   │
                       │   Service        │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Organization   │
                       │   Schema         │
                       │   (Isolated)     │
                       └──────────────────┘
```

## Installation

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Set Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Configure Database**
   - Edit `config/database-config.yaml` or `config/database-config.json`
   - Set up your SQL Server database credentials
   - Configure schema naming conventions

4. **Create Required Database Users**
   ```sql
   -- Create application user
   CREATE LOGIN hrms_app_user WITH PASSWORD = 'your_password';
   CREATE USER hrms_app_user FOR LOGIN hrms_app_user;

   -- Create provisioning user (with elevated privileges)
   CREATE LOGIN hrms_provisioner WITH PASSWORD = 'your_password';
   CREATE USER hrms_provisioner FOR LOGIN hrms_provisioner;
   ALTER ROLE hrms_provisioner ADD MEMBER db_ddladmin;

   -- Create readonly user
   CREATE LOGIN hrms_readonly WITH PASSWORD = 'your_password';
   CREATE USER hrms_readonly FOR LOGIN hrms_readonly;
   ALTER ROLE hrms_readonly ADD MEMBER db_datareader;
   ```

## Configuration

### Database Configuration

The system uses a flexible configuration system supporting both YAML and JSON formats:

```yaml
# config/database-config.yaml
environments:
  development:
    master_db:
      server: "localhost"
      port: 1433
      database: "hrms_master_dev"
      trusted_connection: true
      trust_server_certificate: true
    provisioning:
      auto_provision: true
      schema_prefix: "org_"
      schema_suffix: "_dev"
      timeout_seconds: 300
```

### Environment Variables

```bash
# Database
DB_DEV_PASSWORD=your_dev_password
DB_STAGING_PASSWORD=your_staging_password
DB_PROD_PASSWORD=your_prod_password
DB_PROVISIONER_PASSWORD=your_provisioner_password

# Application
NODE_ENV=development
LOG_LEVEL=info
LOG_DIR=./logs

# Monitoring
WEBHOOK_URL=https://your-webhook-url.com
```

## Usage

### Automatic Provisioning

When a new organization registers through the API, the system automatically:

1. Creates organization record in master database
2. Provisions isolated schema for the organization
3. Executes all migration files
4. Seeds default data
5. Sets up permissions
6. Validates schema creation
7. Activates the organization

```javascript
// Example API call
POST /auth/register-organization
{
  "organization": {
    "name": "Tech Innovators Inc",
    "domain": "techinnovators.com",
    "industry": "Technology"
  },
  "admin": {
    "name": "John Smith",
    "email": "<EMAIL>",
    "password": "securePassword123"
  }
}
```

### Manual Provisioning

For existing organizations or manual setup:

```bash
# Using CLI script
npm run provision -- --organization-id=uuid-here

# Using API endpoint (super admin only)
POST /admin/organizations/{organizationId}/provision
```

### Monitoring

Access monitoring endpoints:

```bash
# Prometheus metrics
curl http://localhost:3000/metrics

# Health check
curl http://localhost:3000/health

# Provisioning status
curl http://localhost:3000/admin/provisioning/status/{organizationId}
```

## Database Schema

The system creates a complete HRMS schema for each organization including:

### Core Tables
- `organizations` - Organization information
- `users` - User accounts and authentication
- `employee_details` - Employee HR information
- `employee_education` - Educational qualifications
- `employee_skills` - Skills and competencies

### Functional Modules
- **Attendance**: `attendance_records`, `work_schedules`, `overtime_records`
- **Leave Management**: `leave_types`, `leave_balances`, `leave_requests`
- **Task Management**: `tasks`, `task_updates`, `task_comments`
- **Performance**: `performance_reviews`, `performance_goals`
- **Payroll**: `payroll_cycles`, `employee_payroll`, `employee_benefits`
- **Recruitment**: `job_postings`, `job_applications`, `interview_schedules`

### System Tables
- `organization_settings` - Configuration settings
- `audit_logs` - Audit trail
- `notifications` - User notifications
- `file_uploads` - File management

## Migration Files

The system executes migration files in order:

1. `001_create_core_tables.sql` - Core organization and user tables
2. `002_create_attendance_tables.sql` - Attendance management
3. `003_create_leave_tables.sql` - Leave management
4. `004_create_task_tables.sql` - Task management
5. `005_create_performance_tables.sql` - Performance management
6. `006_create_payroll_tables.sql` - Payroll management
7. `007_create_recruitment_tables.sql` - Recruitment management
8. `008_create_system_tables.sql` - System configuration
9. `009_create_indexes.sql` - Performance indexes
10. `010_create_constraints.sql` - Additional constraints and views

## Seed Data

Default data is seeded for each organization:

1. `001_default_leave_types.sql` - Leave types and policies
2. `002_default_system_settings.sql` - Organization settings
3. `003_default_subscription_plans.sql` - Subscription information
4. `004_default_job_categories.sql` - Job categories and templates

## API Endpoints

### Organization Registration
- `POST /auth/register-organization` - Register new organization
- `GET /auth/registration-status/:organizationId` - Check registration status

### Admin Endpoints (Super Admin Only)
- `POST /admin/organizations/:organizationId/provision` - Manual provisioning
- `GET /admin/provisioning/status/:organizationId` - Provisioning status
- `GET /admin/provisioning/metrics` - System metrics

### Monitoring Endpoints
- `GET /health` - Health check
- `GET /metrics` - Prometheus metrics
- `GET /status` - System status

## Error Handling

The system includes comprehensive error handling:

- **Validation Errors**: Input validation with detailed error messages
- **Database Errors**: Connection and query error handling
- **Provisioning Errors**: Automatic rollback on failure
- **Retry Logic**: Configurable retry with exponential backoff
- **Notifications**: Email and webhook notifications for failures

## Monitoring & Metrics

Built-in monitoring with Prometheus metrics:

- **Provisioning Metrics**: Duration, success/failure rates, error types
- **Database Metrics**: Connection pools, query performance
- **API Metrics**: Request rates, response times, error rates
- **System Metrics**: Memory usage, disk usage, active organizations

## Security

- **Data Isolation**: Each organization has isolated schema
- **Role-based Access**: Proper database user permissions
- **Audit Logging**: Complete audit trail for compliance
- **Input Validation**: Comprehensive input validation
- **Error Sanitization**: Safe error messages in production

## Backup & Recovery

- **Automatic Backups**: Optional automatic schema backups
- **Rollback Capability**: Automatic rollback on provisioning failure
- **Data Retention**: Configurable data retention policies

## Development

### Running Tests
```bash
npm test                # Run all tests
npm run test:watch      # Watch mode
npm run test:coverage   # Coverage report
```

### Linting
```bash
npm run lint           # Check code style
npm run lint:fix       # Fix code style issues
```

### Development Mode
```bash
npm run dev           # Start with nodemon
```

## Deployment

### Docker Deployment
```bash
# Build image
npm run docker:build

# Run container
npm run docker:run
```

### Environment Setup
1. Set up SQL Server database
2. Configure environment variables
3. Run database migrations
4. Start the application

## Troubleshooting

### Common Issues

1. **Schema Already Exists**
   - Check if organization was previously registered
   - Use manual cleanup if needed

2. **Permission Denied**
   - Verify database user permissions
   - Check provisioning user privileges

3. **Migration Failures**
   - Check migration file syntax
   - Verify database connectivity
   - Review error logs

4. **Timeout Errors**
   - Increase timeout settings in configuration
   - Check database performance
   - Review system resources

### Logs

Check application logs for detailed error information:
```bash
tail -f logs/application.log
tail -f logs/database-provisioning.log
tail -f logs/error.log
```

## Support

For issues and questions:
- Check the troubleshooting section
- Review application logs
- Contact the development team
- Create an issue in the repository

## License

MIT License - see LICENSE file for details.
