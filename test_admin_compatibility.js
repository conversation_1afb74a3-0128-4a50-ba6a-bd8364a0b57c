// Test script to verify Organization Admin backward compatibility
const API_BASE = 'http://localhost:5020/api/v1';

async function testAdminCompatibility() {
    console.log('🧪 Testing Organization Admin Backward Compatibility...\n');

    try {
        // Step 1: Login as Organization Admin
        console.log('1️⃣ Logging in as Organization Admin...');
        const adminLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!adminLoginResponse.ok) {
            throw new Error(`Admin login failed: ${adminLoginResponse.status}`);
        }

        const adminLoginData = await adminLoginResponse.json();
        const adminToken = adminLoginData.data.token;
        const organizationId = adminLoginData.data.user.organization?.id;
        
        console.log(`✅ Admin login successful. Organization ID: ${organizationId}`);

        // Step 2: Test Admin API Access
        console.log('\n2️⃣ Testing Organization Admin API Access...');
        
        const testEndpoints = [
            { name: 'Dashboard', url: '/dashboard' },
            { name: 'Employee List', url: '/employee-management/employees' },
            { name: 'Leave Requests (Admin View)', url: '/leave/requests' },
            { name: 'Attendance Records (Admin View)', url: '/attendance/records' },
            { name: 'Tasks (Admin View)', url: '/tasks' }
        ];

        for (const endpoint of testEndpoints) {
            console.log(`\n   Testing ${endpoint.name}...`);
            try {
                const response = await fetch(`${API_BASE}${endpoint.url}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'X-Organization-ID': organizationId,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log(`   ✅ ${endpoint.name}: SUCCESS (${response.status})`);
                    console.log(`   📊 Response: ${JSON.stringify(data).substring(0, 100)}...`);
                } else {
                    const errorText = await response.text();
                    console.log(`   ❌ ${endpoint.name}: FAILED (${response.status})`);
                    console.log(`   🚨 Error: ${errorText.substring(0, 200)}...`);
                }
            } catch (error) {
                console.log(`   ❌ ${endpoint.name}: ERROR - ${error.message}`);
            }
        }

        // Step 3: Test Admin-only operations
        console.log('\n3️⃣ Testing Admin-only Operations...');
        
        // Test creating an employee (admin-only operation)
        console.log('\n   Testing Employee Creation (Admin-only)...');
        try {
            const createEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${adminToken}`,
                    'X-Organization-ID': organizationId
                },
                body: JSON.stringify({
                    name: 'Compatibility Test Employee',
                    email: '<EMAIL>',
                    role: 'Employee',
                    employeeDetails: {
                        employeeId: 'COMP001',
                        jobTitle: 'Test Developer',
                        department: 'Testing',
                        joinDate: new Date().toISOString(),
                        employmentType: 'full-time',
                        baseSalary: 60000,
                        annualCTC: 72000
                    }
                })
            });

            if (createEmployeeResponse.ok) {
                const createEmployeeData = await createEmployeeResponse.json();
                console.log(`   ✅ Employee Creation: SUCCESS (${createEmployeeResponse.status})`);
                console.log(`   👤 Created employee: ${createEmployeeData.data.name} (${createEmployeeData.data.email})`);
            } else {
                const errorText = await createEmployeeResponse.text();
                if (createEmployeeResponse.status === 409) {
                    console.log(`   ⚠️ Employee Creation: Employee already exists (${createEmployeeResponse.status})`);
                } else {
                    console.log(`   ❌ Employee Creation: FAILED (${createEmployeeResponse.status})`);
                    console.log(`   🚨 Error: ${errorText.substring(0, 200)}...`);
                }
            }
        } catch (error) {
            console.log(`   ❌ Employee Creation: ERROR - ${error.message}`);
        }

        console.log('\n🎉 Organization Admin Backward Compatibility Test Completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testAdminCompatibility();
