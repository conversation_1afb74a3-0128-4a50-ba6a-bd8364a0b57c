// Direct database cleanup script for Test Company removal
import sql from 'mssql';

const config = {
    server: '**************',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function cleanupTestCompanyData() {
    console.log('🗑️ Direct Database Cleanup - Removing Test Company...\n');
    console.log('=' .repeat(60));

    let pool;
    try {
        // Connect to database
        console.log('1️⃣ Connecting to SQL Server database...');
        pool = await sql.connect(config);
        console.log('✅ Connected to database successfully');

        // Step 1: Check current state
        console.log('\n2️⃣ Checking current database state...');
        console.log('-'.repeat(30));
        
        const orgQuery = `
            SELECT Id, Name, Domain, Industry, Status, CreatedAt 
            FROM Organizations 
            ORDER BY Name
        `;
        
        const orgResult = await pool.request().query(orgQuery);
        console.log(`Found ${orgResult.recordset.length} organizations:`);
        
        let testCompanyOrgId = null;
        let planSquareOrgId = null;
        
        orgResult.recordset.forEach((org, index) => {
            console.log(`   ${index + 1}. ${org.Name} (${org.Id})`);
            console.log(`      Domain: ${org.Domain}`);
            console.log(`      Status: ${org.Status}`);
            
            if (org.Name === 'Test Company') {
                testCompanyOrgId = org.Id;
            } else if (org.Name === 'PlanSquare' || org.Name === 'PlanSquare ') {
                planSquareOrgId = org.Id;
            }
        });

        if (!testCompanyOrgId) {
            console.log('⚠️ Test Company organization not found. May already be deleted.');
            return;
        }

        if (!planSquareOrgId) {
            console.log('❌ PlanSquare organization not found. Cannot proceed safely.');
            return;
        }

        console.log(`\n🎯 Target for deletion: Test Company (${testCompanyOrgId})`);
        console.log(`🛡️ Preserving: PlanSquare (${planSquareOrgId})`);

        // Step 2: Check users in both organizations
        console.log('\n3️⃣ Checking users in organizations...');
        console.log('-'.repeat(30));
        
        const userQuery = `
            SELECT Id, Name, Email, Role, OrganizationId, IsActive
            FROM Users 
            WHERE OrganizationId IN ('${testCompanyOrgId}', '${planSquareOrgId}')
            ORDER BY OrganizationId, Name
        `;
        
        const userResult = await pool.request().query(userQuery);
        console.log(`Found ${userResult.recordset.length} users:`);
        
        let testCompanyUsers = [];
        let planSquareUsers = [];
        
        userResult.recordset.forEach(user => {
            if (user.OrganizationId === testCompanyOrgId) {
                testCompanyUsers.push(user);
                console.log(`   🗑️ Test Company: ${user.Name} (${user.Email}) - ${user.Role}`);
            } else if (user.OrganizationId === planSquareOrgId) {
                planSquareUsers.push(user);
                console.log(`   🛡️ PlanSquare: ${user.Name} (${user.Email}) - ${user.Role}`);
            }
        });

        console.log(`\n📊 User Summary:`);
        console.log(`   - Test Company users to delete: ${testCompanyUsers.length}`);
        console.log(`   - PlanSquare users to preserve: ${planSquareUsers.length}`);

        // Step 3: Delete Test Company users
        console.log('\n4️⃣ Deleting Test Company users...');
        console.log('-'.repeat(30));
        
        if (testCompanyUsers.length > 0) {
            // Delete employee details first (foreign key constraint)
            const deleteEmployeeDetailsQuery = `
                DELETE FROM EmployeeDetails 
                WHERE UserId IN (
                    SELECT Id FROM Users WHERE OrganizationId = '${testCompanyOrgId}'
                )
            `;
            
            const employeeDetailsResult = await pool.request().query(deleteEmployeeDetailsQuery);
            console.log(`✅ Deleted ${employeeDetailsResult.rowsAffected[0]} employee detail records`);

            // Delete attendance records
            const deleteAttendanceQuery = `
                DELETE FROM AttendanceRecords 
                WHERE UserId IN (
                    SELECT Id FROM Users WHERE OrganizationId = '${testCompanyOrgId}'
                )
            `;
            
            const attendanceResult = await pool.request().query(deleteAttendanceQuery);
            console.log(`✅ Deleted ${attendanceResult.rowsAffected[0]} attendance records`);

            // Delete leave requests
            const deleteLeaveQuery = `
                DELETE FROM LeaveRequests 
                WHERE UserId IN (
                    SELECT Id FROM Users WHERE OrganizationId = '${testCompanyOrgId}'
                )
            `;
            
            const leaveResult = await pool.request().query(deleteLeaveQuery);
            console.log(`✅ Deleted ${leaveResult.rowsAffected[0]} leave requests`);

            // Delete users
            const deleteUsersQuery = `DELETE FROM Users WHERE OrganizationId = '${testCompanyOrgId}'`;
            const usersResult = await pool.request().query(deleteUsersQuery);
            console.log(`✅ Deleted ${usersResult.rowsAffected[0]} users`);
        }

        // Step 4: Delete Test Company organization
        console.log('\n5️⃣ Deleting Test Company organization...');
        console.log('-'.repeat(30));
        
        const deleteOrgQuery = `DELETE FROM Organizations WHERE Id = '${testCompanyOrgId}'`;
        const orgDeleteResult = await pool.request().query(deleteOrgQuery);
        console.log(`✅ Deleted ${orgDeleteResult.rowsAffected[0]} organization record`);

        // Step 5: Try to drop Test Company schema
        console.log('\n6️⃣ Dropping Test Company schema...');
        console.log('-'.repeat(30));
        
        const schemaName = `org_${testCompanyOrgId.replace(/-/g, '')}`.toLowerCase();
        console.log(`Schema name: ${schemaName}`);
        
        try {
            // Check if schema exists
            const schemaCheckQuery = `
                SELECT SCHEMA_NAME 
                FROM INFORMATION_SCHEMA.SCHEMATA 
                WHERE SCHEMA_NAME = '${schemaName}'
            `;
            
            const schemaCheckResult = await pool.request().query(schemaCheckQuery);
            
            if (schemaCheckResult.recordset.length > 0) {
                // Drop all objects in schema first
                const dropObjectsQuery = `
                    DECLARE @sql NVARCHAR(MAX) = '';
                    SELECT @sql = @sql + 'DROP TABLE [${schemaName}].[' + TABLE_NAME + '];' + CHAR(13)
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = '${schemaName}';
                    EXEC sp_executesql @sql;
                `;
                
                await pool.request().query(dropObjectsQuery);
                console.log(`✅ Dropped all objects in schema ${schemaName}`);
                
                // Drop schema
                const dropSchemaQuery = `DROP SCHEMA [${schemaName}]`;
                await pool.request().query(dropSchemaQuery);
                console.log(`✅ Dropped schema ${schemaName}`);
            } else {
                console.log(`ℹ️ Schema ${schemaName} does not exist`);
            }
        } catch (schemaError) {
            console.log(`⚠️ Schema cleanup warning: ${schemaError.message}`);
        }

        // Step 6: Verify cleanup
        console.log('\n7️⃣ Verifying cleanup...');
        console.log('-'.repeat(30));
        
        const verifyOrgQuery = `SELECT COUNT(*) as count FROM Organizations`;
        const verifyOrgResult = await pool.request().query(verifyOrgQuery);
        console.log(`Remaining organizations: ${verifyOrgResult.recordset[0].count}`);
        
        const verifyUserQuery = `SELECT COUNT(*) as count FROM Users`;
        const verifyUserResult = await pool.request().query(verifyUserQuery);
        console.log(`Remaining users: ${verifyUserResult.recordset[0].count}`);
        
        const planSquareCheckQuery = `
            SELECT o.Name as OrgName, u.Name as UserName, u.Email, u.Role
            FROM Organizations o
            JOIN Users u ON o.Id = u.OrganizationId
            WHERE o.Name = 'PlanSquare'
            ORDER BY u.Name
        `;
        
        const planSquareCheckResult = await pool.request().query(planSquareCheckQuery);
        console.log(`\nPlanSquare users remaining: ${planSquareCheckResult.recordset.length}`);
        planSquareCheckResult.recordset.forEach(user => {
            console.log(`   ✅ ${user.UserName} (${user.Email}) - ${user.Role}`);
        });

        console.log('\n8️⃣ CLEANUP COMPLETED SUCCESSFULLY!');
        console.log('-'.repeat(30));
        console.log('✅ Test Company organization deleted');
        console.log('✅ Test Company users deleted');
        console.log('✅ Test Company data cleaned up');
        console.log('✅ PlanSquare organization preserved');
        console.log('✅ PlanSquare users preserved');
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Restart the backend application');
        console.log('2. Run: node verify_cleanup.js');
        console.log('3. Test multi-tenant isolation with single organization');

    } catch (error) {
        console.error('❌ Database cleanup failed:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        if (pool) {
            await pool.close();
            console.log('\n🔌 Database connection closed');
        }
    }
}

// Run the cleanup
cleanupTestCompanyData();
