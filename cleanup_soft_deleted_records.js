// Clean up soft-deleted employee records that might be causing conflicts
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function cleanupSoftDeletedRecords() {
    console.log('🧹 Cleaning up Soft-Deleted Employee Records...\n');
    console.log('=' .repeat(60));

    try {
        // Connect to database
        console.log('\n1️⃣ CONNECTING TO DATABASE');
        console.log('-'.repeat(30));
        
        const pool = await sql.connect(config);
        console.log('✅ Connected to SQL Server database');

        // Step 1: Find PlanSquare organization
        const orgQuery = `
            SELECT Id, Name, Domain
            FROM Organizations 
            WHERE Name LIKE '%Plan%' OR Domain LIKE '%plan%'
        `;
        
        const orgResult = await pool.request().query(orgQuery);
        const planSquareOrgId = orgResult.recordset[0]?.Id;
        
        if (!planSquareOrgId) {
            console.log('❌ PlanSquare organization not found');
            return;
        }

        console.log(`✅ Found PlanSquare Organization ID: ${planSquareOrgId}`);

        // Step 2: Check all employee details (including soft-deleted)
        console.log('\n2️⃣ CHECKING ALL EMPLOYEE DETAILS');
        console.log('-'.repeat(30));
        
        const allEmployeeDetailsQuery = `
            SELECT ed.Id, ed.UserId, ed.EmployeeId, ed.JobTitle, ed.Department, 
                   ed.IsDeleted, ed.CreatedAt, ed.DeletedAt,
                   u.Name, u.Email, u.IsDeleted as UserDeleted, u.OrganizationId
            FROM EmployeeDetails ed
            LEFT JOIN Users u ON ed.UserId = u.Id
            ORDER BY ed.CreatedAt DESC
        `;
        
        const allEmpDetailsResult = await pool.request().query(allEmployeeDetailsQuery);
        console.log(`📊 Found ${allEmpDetailsResult.recordset.length} employee detail records:`);
        
        let planSquareRecords = [];
        let otherRecords = [];
        
        allEmpDetailsResult.recordset.forEach((emp, index) => {
            const status = emp.IsDeleted ? 'DELETED' : 'ACTIVE';
            const userStatus = emp.UserDeleted ? 'USER_DELETED' : 'USER_ACTIVE';
            console.log(`   ${index + 1}. ${emp.Name || 'N/A'} (${emp.Email || 'N/A'}) - Employee ID: ${emp.EmployeeId || 'N/A'}`);
            console.log(`      Status: ${status}, User: ${userStatus}, Org: ${emp.OrganizationId || 'N/A'}`);
            
            if (emp.OrganizationId === planSquareOrgId) {
                planSquareRecords.push(emp);
            } else {
                otherRecords.push(emp);
            }
        });

        console.log(`\n📊 PlanSquare records: ${planSquareRecords.length}`);
        console.log(`📊 Other organization records: ${otherRecords.length}`);

        // Step 3: Hard delete soft-deleted employee details that are not from PlanSquare
        console.log('\n3️⃣ CLEANING UP SOFT-DELETED RECORDS');
        console.log('-'.repeat(30));
        
        const softDeletedNonPlanSquare = otherRecords.filter(emp => emp.IsDeleted);
        console.log(`🗑️  Found ${softDeletedNonPlanSquare.length} soft-deleted records from other organizations`);
        
        if (softDeletedNonPlanSquare.length > 0) {
            for (const emp of softDeletedNonPlanSquare) {
                try {
                    console.log(`   Deleting: ${emp.Name || 'N/A'} (Employee ID: ${emp.EmployeeId || 'N/A'})`);
                    
                    const deleteQuery = `DELETE FROM EmployeeDetails WHERE Id = '${emp.Id}'`;
                    await pool.request().query(deleteQuery);
                    console.log(`   ✅ Deleted employee detail record`);
                } catch (error) {
                    console.log(`   ❌ Failed to delete: ${error.message}`);
                }
            }
        }

        // Step 4: Also clean up soft-deleted PlanSquare records (except active ones)
        console.log('\n4️⃣ CLEANING UP PLANSQUARE SOFT-DELETED RECORDS');
        console.log('-'.repeat(30));
        
        const softDeletedPlanSquare = planSquareRecords.filter(emp => emp.IsDeleted);
        console.log(`🗑️  Found ${softDeletedPlanSquare.length} soft-deleted PlanSquare records`);
        
        if (softDeletedPlanSquare.length > 0) {
            for (const emp of softDeletedPlanSquare) {
                try {
                    console.log(`   Deleting: ${emp.Name || 'N/A'} (Employee ID: ${emp.EmployeeId || 'N/A'})`);
                    
                    const deleteQuery = `DELETE FROM EmployeeDetails WHERE Id = '${emp.Id}'`;
                    await pool.request().query(deleteQuery);
                    console.log(`   ✅ Deleted employee detail record`);
                } catch (error) {
                    console.log(`   ❌ Failed to delete: ${error.message}`);
                }
            }
        }

        // Step 5: Clean up soft-deleted users that are not from PlanSquare
        console.log('\n5️⃣ CLEANING UP SOFT-DELETED USERS');
        console.log('-'.repeat(30));
        
        const softDeletedUsersQuery = `
            SELECT Id, Name, Email, OrganizationId, IsDeleted, DeletedAt
            FROM Users 
            WHERE IsDeleted = 1 AND OrganizationId != '${planSquareOrgId}'
        `;
        
        const softDeletedUsersResult = await pool.request().query(softDeletedUsersQuery);
        console.log(`🗑️  Found ${softDeletedUsersResult.recordset.length} soft-deleted users from other organizations`);
        
        if (softDeletedUsersResult.recordset.length > 0) {
            for (const user of softDeletedUsersResult.recordset) {
                try {
                    console.log(`   Deleting user: ${user.Name} (${user.Email})`);
                    
                    const deleteUserQuery = `DELETE FROM Users WHERE Id = '${user.Id}'`;
                    await pool.request().query(deleteUserQuery);
                    console.log(`   ✅ Deleted user record`);
                } catch (error) {
                    console.log(`   ❌ Failed to delete user: ${error.message}`);
                }
            }
        }

        // Step 6: Verify cleanup
        console.log('\n6️⃣ VERIFYING CLEANUP');
        console.log('-'.repeat(30));
        
        const finalEmployeeDetailsQuery = `
            SELECT COUNT(*) as TotalCount,
                   SUM(CASE WHEN IsDeleted = 1 THEN 1 ELSE 0 END) as SoftDeletedCount,
                   SUM(CASE WHEN IsDeleted = 0 THEN 1 ELSE 0 END) as ActiveCount
            FROM EmployeeDetails
        `;
        
        const finalResult = await pool.request().query(finalEmployeeDetailsQuery);
        const stats = finalResult.recordset[0];
        
        console.log(`📊 Final employee details statistics:`);
        console.log(`   Total records: ${stats.TotalCount}`);
        console.log(`   Active records: ${stats.ActiveCount}`);
        console.log(`   Soft-deleted records: ${stats.SoftDeletedCount}`);

        // Check PlanSquare specific records
        const planSquareStatsQuery = `
            SELECT COUNT(*) as TotalCount,
                   SUM(CASE WHEN ed.IsDeleted = 1 THEN 1 ELSE 0 END) as SoftDeletedCount,
                   SUM(CASE WHEN ed.IsDeleted = 0 THEN 1 ELSE 0 END) as ActiveCount
            FROM EmployeeDetails ed
            INNER JOIN Users u ON ed.UserId = u.Id
            WHERE u.OrganizationId = '${planSquareOrgId}'
        `;
        
        const planSquareStatsResult = await pool.request().query(planSquareStatsQuery);
        const planSquareStats = planSquareStatsResult.recordset[0];
        
        console.log(`\n📊 PlanSquare employee details statistics:`);
        console.log(`   Total records: ${planSquareStats.TotalCount}`);
        console.log(`   Active records: ${planSquareStats.ActiveCount}`);
        console.log(`   Soft-deleted records: ${planSquareStats.SoftDeletedCount}`);

        await pool.close();
        console.log('\n✅ Database connection closed');

        console.log('\n' + '='.repeat(60));
        console.log('🏁 CLEANUP COMPLETED');
        console.log('✅ Database cleaned up successfully');
        console.log('🚀 Ready to try creating Priya again');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ CLEANUP FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the database connection and try again.');
    }
}

// Run the cleanup
cleanupSoftDeletedRecords();
