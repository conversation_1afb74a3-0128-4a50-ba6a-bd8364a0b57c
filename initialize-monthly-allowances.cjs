// Script to initialize monthly allowances for all existing employees
const sql = require('mssql');

// Database configuration
const config = {
    server: '**************',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: true,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function initializeMonthlyAllowances() {
    let pool;
    
    try {
        console.log('🔗 Connecting to SQL Server...');
        pool = await sql.connect(config);
        
        console.log('📊 Getting all active employees...');
        
        // Get all active employees from all organizations
        const employeesResult = await pool.request().query(`
            SELECT DISTINCT u.Id as UserId, u.Email, u.Name, u.OrganizationId
            FROM Users u
            WHERE u.IsActive = 1
            AND u.Role IN ('Employee', 'OrgAdmin')
            AND u.IsDeleted = 0
            ORDER BY u.OrganizationId, u.Email
        `);
        
        const employees = employeesResult.recordset;
        console.log(`👥 Found ${employees.length} active employees`);
        
        if (employees.length === 0) {
            console.log('ℹ️ No employees found to initialize');
            return;
        }
        
        // Get current date info
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1; // JavaScript months are 0-based
        
        console.log(`📅 Current date: ${currentMonth}/${currentYear}`);
        
        let totalInitialized = 0;
        let totalSkipped = 0;
        
        // Process each employee
        for (const employee of employees) {
            console.log(`\n👤 Processing ${employee.Name} (${employee.Email})`);
            
            // Check if employee already has monthly allowances
            const existingAllowancesResult = await pool.request()
                .input('userId', sql.UniqueIdentifier, employee.UserId)
                .query(`
                    SELECT COUNT(*) as Count 
                    FROM MonthlyLeaveAllowances 
                    WHERE UserId = @userId
                `);
            
            const existingCount = existingAllowancesResult.recordset[0].Count;
            
            if (existingCount > 0) {
                console.log(`   ⏭️ Skipping - already has ${existingCount} monthly allowance records`);
                totalSkipped++;
                continue;
            }
            
            // Initialize monthly allowances for current year
            // Give them allowances from January to current month
            let monthsInitialized = 0;
            
            for (let month = 1; month <= currentMonth; month++) {
                // Calculate base allowance (1 per month)
                const baseAllowance = 1;
                
                // For current month, give them full allowance
                // For past months, assume no usage for simplicity
                let usedLeaves = 0;
                let carriedForward = 0;
                
                // Calculate carried forward from previous month
                if (month > 1) {
                    // Each month they get 1 leave, so carried forward accumulates
                    carriedForward = month - 1; // Simplified: assume all previous months carried forward
                }
                
                // Insert monthly allowance record
                await pool.request()
                    .input('userId', sql.UniqueIdentifier, employee.UserId)
                    .input('year', sql.Int, currentYear)
                    .input('month', sql.Int, month)
                    .input('baseAllowance', sql.Int, baseAllowance)
                    .input('carriedForward', sql.Int, carriedForward)
                    .input('usedLeaves', sql.Int, usedLeaves)
                    .input('isProcessed', sql.Bit, month < currentMonth ? 1 : 0)
                    .input('createdAt', sql.DateTime2, new Date())
                    .input('updatedAt', sql.DateTime2, new Date())
                    .query(`
                        INSERT INTO MonthlyLeaveAllowances 
                        (Id, UserId, Year, Month, BaseAllowance, CarriedForward, UsedLeaves, IsProcessed, CreatedAt, UpdatedAt)
                        VALUES 
                        (NEWID(), @userId, @year, @month, @baseAllowance, @carriedForward, @usedLeaves, @isProcessed, @createdAt, @updatedAt)
                    `);
                
                monthsInitialized++;
            }
            
            console.log(`   ✅ Initialized ${monthsInitialized} monthly allowance records`);
            totalInitialized++;
        }
        
        console.log('\n🎉 Monthly Allowance Initialization Complete!');
        console.log(`📊 Summary:`);
        console.log(`   - Employees processed: ${employees.length}`);
        console.log(`   - Employees initialized: ${totalInitialized}`);
        console.log(`   - Employees skipped: ${totalSkipped}`);
        
    } catch (error) {
        console.error('❌ Error initializing monthly allowances:', error);
        throw error;
    } finally {
        if (pool) {
            await pool.close();
            console.log('🔌 Database connection closed');
        }
    }
}

// Run the initialization
initializeMonthlyAllowances().catch(console.error);
