-- Simple and safe script to delete TechCorp Solutions organization
-- This focuses only on the core tables that definitely exist

USE dbHRMS;
GO

-- Start transaction for atomic operation
BEGIN TRANSACTION;

DECLARE @OrganizationId UNIQUEIDENTIFIER = '67DD72CC-5D99-4065-BA4A-0F2EE36443AE';
DECLARE @OrganizationName NVARCHAR(255) = 'TechCorp Solutions';

-- Verify the organization exists
IF NOT EXISTS (SELECT 1 FROM Organizations WHERE Id = @OrganizationId AND Name = @OrganizationName)
BEGIN
    PRINT 'Organization "' + @OrganizationName + '" with specified ID not found.';
    ROLLBACK TRANSACTION;
    RETURN;
END

PRINT 'Found organization: ' + @OrganizationName + ' (ID: ' + CAST(@OrganizationId AS NVARCHAR(50)) + ')';

-- Get all users in this organization
DECLARE @UserIds TABLE (UserId UNIQUEIDENTIFIER);
INSERT INTO @UserIds (UserId)
SELECT Id FROM Users WHERE OrganizationId = @OrganizationId;

DECLARE @UserCount INT = (SELECT COUNT(*) FROM @UserIds);
PRINT 'Found ' + CAST(@UserCount AS NVARCHAR(10)) + ' users in this organization';

-- Display users to be deleted
SELECT 'Users to be deleted:' AS Info, Name, Email, Role FROM Users WHERE OrganizationId = @OrganizationId;

PRINT 'Starting deletion process...';

-- Step 1: Delete leave-related data
DECLARE @LeaveRequestCount INT = 0;
SELECT @LeaveRequestCount = COUNT(*) FROM LeaveRequests WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM LeaveRequests WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@LeaveRequestCount AS NVARCHAR(10)) + ' leave requests';

DECLARE @LeaveBalanceCount INT = 0;
SELECT @LeaveBalanceCount = COUNT(*) FROM LeaveBalances WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM LeaveBalances WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@LeaveBalanceCount AS NVARCHAR(10)) + ' leave balances';

DECLARE @MonthlyAllowanceCount INT = 0;
SELECT @MonthlyAllowanceCount = COUNT(*) FROM MonthlyLeaveAllowances WHERE UserId IN (SELECT UserId FROM @UserIds);

-- Delete monthly leave usage first (foreign key dependency)
DECLARE @MonthlyUsageCount INT = 0;
SELECT @MonthlyUsageCount = COUNT(*) 
FROM MonthlyLeaveUsages mlu
INNER JOIN MonthlyLeaveAllowances mla ON mlu.MonthlyLeaveAllowanceId = mla.Id
WHERE mla.UserId IN (SELECT UserId FROM @UserIds);

DELETE mlu
FROM MonthlyLeaveUsages mlu
INNER JOIN MonthlyLeaveAllowances mla ON mlu.MonthlyLeaveAllowanceId = mla.Id
WHERE mla.UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@MonthlyUsageCount AS NVARCHAR(10)) + ' monthly leave usage records';

-- Now delete monthly allowances
DELETE FROM MonthlyLeaveAllowances WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@MonthlyAllowanceCount AS NVARCHAR(10)) + ' monthly leave allowances';

-- Step 2: Delete attendance records
DECLARE @AttendanceCount INT = 0;
SELECT @AttendanceCount = COUNT(*) FROM AttendanceRecords WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM AttendanceRecords WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@AttendanceCount AS NVARCHAR(10)) + ' attendance records';

-- Step 3: Delete task-related data
-- Delete task comments first
DECLARE @TaskCommentCount INT = 0;
SELECT @TaskCommentCount = COUNT(*) FROM TaskComments tc
INNER JOIN Tasks t ON tc.TaskId = t.Id
WHERE t.AssignedToId IN (SELECT UserId FROM @UserIds) OR t.CreatedBy IN (SELECT UserId FROM @UserIds);

DELETE tc FROM TaskComments tc
INNER JOIN Tasks t ON tc.TaskId = t.Id
WHERE t.AssignedToId IN (SELECT UserId FROM @UserIds) OR t.CreatedBy IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@TaskCommentCount AS NVARCHAR(10)) + ' task comments';

-- Delete task updates
DECLARE @TaskUpdateCount INT = 0;
SELECT @TaskUpdateCount = COUNT(*) FROM TaskUpdates WHERE UpdatedBy IN (SELECT UserId FROM @UserIds);
DELETE FROM TaskUpdates WHERE UpdatedBy IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@TaskUpdateCount AS NVARCHAR(10)) + ' task updates';

-- Delete tasks
DECLARE @TaskCount INT = 0;
SELECT @TaskCount = COUNT(*) FROM Tasks WHERE AssignedToId IN (SELECT UserId FROM @UserIds) OR CreatedBy IN (SELECT UserId FROM @UserIds);
DELETE FROM Tasks WHERE AssignedToId IN (SELECT UserId FROM @UserIds) OR CreatedBy IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@TaskCount AS NVARCHAR(10)) + ' tasks';

-- Step 4: Delete performance reviews
DECLARE @PerformanceCount INT = 0;
SELECT @PerformanceCount = COUNT(*) FROM PerformanceReviews WHERE EmployeeId IN (SELECT UserId FROM @UserIds) OR ReviewerId IN (SELECT UserId FROM @UserIds);
DELETE FROM PerformanceReviews WHERE EmployeeId IN (SELECT UserId FROM @UserIds) OR ReviewerId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@PerformanceCount AS NVARCHAR(10)) + ' performance reviews';

-- Step 5: Delete employee details
DECLARE @EmployeeDetailCount INT = 0;
SELECT @EmployeeDetailCount = COUNT(*) FROM EmployeeDetails WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM EmployeeDetails WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@EmployeeDetailCount AS NVARCHAR(10)) + ' employee detail records';

-- Step 6: Delete organization-specific data
-- Delete leave types for this organization
DECLARE @LeaveTypeCount INT = 0;
SELECT @LeaveTypeCount = COUNT(*) FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
DELETE FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
DELETE FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
PRINT 'Deleted ' + CAST(@LeaveTypeCount AS NVARCHAR(10)) + ' organization leave types';

-- Step 7: Delete users from this organization
DELETE FROM Users WHERE OrganizationId = @OrganizationId;
PRINT 'Deleted ' + CAST(@UserCount AS NVARCHAR(10)) + ' users';

-- Step 8: Delete the organization itself
DELETE FROM Organizations WHERE Id = @OrganizationId;
PRINT 'Deleted organization: ' + @OrganizationName;

-- Commit the transaction
COMMIT TRANSACTION;

PRINT '';
PRINT '🎉 SUCCESS: TechCorp Solutions completely deleted from database!';
PRINT '============================================================';
PRINT 'Summary of deleted data:';
PRINT '- Organization: ' + @OrganizationName;
PRINT '- Users: ' + CAST(@UserCount AS NVARCHAR(10));
PRINT '- Leave requests: ' + CAST(@LeaveRequestCount AS NVARCHAR(10));
PRINT '- Leave balances: ' + CAST(@LeaveBalanceCount AS NVARCHAR(10));
PRINT '- Monthly leave allowances: ' + CAST(@MonthlyAllowanceCount AS NVARCHAR(10));
PRINT '- Monthly leave usage: ' + CAST(@MonthlyUsageCount AS NVARCHAR(10));
PRINT '- Attendance records: ' + CAST(@AttendanceCount AS NVARCHAR(10));
PRINT '- Task comments: ' + CAST(@TaskCommentCount AS NVARCHAR(10));
PRINT '- Task updates: ' + CAST(@TaskUpdateCount AS NVARCHAR(10));
PRINT '- Tasks: ' + CAST(@TaskCount AS NVARCHAR(10));
PRINT '- Performance reviews: ' + CAST(@PerformanceCount AS NVARCHAR(10));
PRINT '- Employee details: ' + CAST(@EmployeeDetailCount AS NVARCHAR(10));
PRINT '- Leave types: ' + CAST(@LeaveTypeCount AS NVARCHAR(10));

-- Verify deletion
IF NOT EXISTS (SELECT 1 FROM Organizations WHERE Id = @OrganizationId)
BEGIN
    PRINT '';
    PRINT '✅ VERIFICATION PASSED: TechCorp Solutions successfully removed!';
    PRINT 'The organization and all its associated data has been completely deleted.';
END
ELSE
BEGIN
    PRINT '';
    PRINT '❌ ERROR: Organization still exists - deletion may have failed';
END

-- Show remaining organizations
PRINT '';
PRINT 'Remaining organizations in database:';
SELECT Name, Domain, EmployeeCount, Status FROM Organizations ORDER BY Name;
