// Script to reset employee password for testing
const API_BASE = 'http://localhost:5020/api/v1';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'PlanSquare@123'
};

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    console.error('Request failed:', error);
    return { status: 500, error: error.message };
  }
}

async function login(credentials) {
  console.log(`🔐 Logging in as ${credentials.email}...`);
  
  const result = await makeRequest(`${API_BASE}/auth/login`, {
    method: 'POST',
    body: JSON.stringify(credentials)
  });
  
  if (result.status === 200 && result.data.success) {
    console.log(`✅ Login successful for ${credentials.email}`);
    return result.data.data.token;
  } else {
    console.log(`❌ Login failed for ${credentials.email}:`, result.data);
    return null;
  }
}

async function getEmployees(token) {
  console.log('📋 Fetching employees...');
  
  const result = await makeRequest(`${API_BASE}/employee-management/employees`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Employees fetched successfully');
    console.log('📊 Employee data structure:', JSON.stringify(result.data.data, null, 2));
    return result.data.data.employees || result.data.data || [];
  } else {
    console.log('❌ Failed to fetch employees:', result.data);
    return [];
  }
}

async function resetEmployeePassword(token, employeeId) {
  console.log(`🔄 Resetting password for employee ${employeeId}...`);
  
  const newPassword = 'Employee123!';
  
  const result = await makeRequest(`${API_BASE}/employee-management/employees/${employeeId}/reset-password`, {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: JSON.stringify({ newPassword })
  });
  
  if (result.status === 200 && result.data.success) {
    console.log(`✅ Password reset successful for employee ${employeeId}`);
    console.log(`   New password: ${newPassword}`);
    return newPassword;
  } else {
    console.log(`❌ Password reset failed for employee ${employeeId}:`, result.data);
    return null;
  }
}

async function testEmployeeLogin(email, password) {
  console.log(`🧪 Testing login for ${email}...`);
  
  const result = await makeRequest(`${API_BASE}/auth/login`, {
    method: 'POST',
    body: JSON.stringify({ email, password })
  });
  
  if (result.status === 200 && result.data.success) {
    console.log(`✅ Employee login successful for ${email}`);
    return result.data.data.token;
  } else {
    console.log(`❌ Employee login failed for ${email}:`, result.data);
    return null;
  }
}

async function resetAndTestEmployeeCredentials() {
  console.log('🔧 Resetting Employee Credentials for Testing');
  console.log('=' .repeat(50));
  
  // Step 1: Login as admin
  const adminToken = await login(ADMIN_CREDENTIALS);
  if (!adminToken) {
    console.log('❌ Failed: Could not login as admin');
    return;
  }
  
  // Step 2: Get employees
  const employees = await getEmployees(adminToken);
  if (employees.length === 0) {
    console.log('❌ Failed: No employees found');
    return;
  }
  
  // Step 3: Find Abhishek
  const abhishek = employees.find(emp => emp.email === '<EMAIL>');
  if (!abhishek) {
    console.log('❌ Failed: Abhishek not found');
    return;
  }
  
  console.log(`📋 Found Abhishek: ${abhishek.name} (${abhishek.email})`);
  console.log(`   User ID: ${abhishek.id}`);
  
  // Step 4: Reset password
  const newPassword = await resetEmployeePassword(adminToken, abhishek.id);
  if (!newPassword) {
    console.log('❌ Failed: Could not reset password');
    return;
  }
  
  // Step 5: Test login with new password
  const employeeToken = await testEmployeeLogin(abhishek.email, newPassword);
  if (employeeToken) {
    console.log('\n🎉 SUCCESS: Employee credentials reset and tested!');
    console.log(`✅ Email: ${abhishek.email}`);
    console.log(`✅ Password: ${newPassword}`);
    console.log('✅ Login working correctly');
    
    console.log('\n📝 Updated test credentials:');
    console.log(`   EMPLOYEE_CREDENTIALS = {`);
    console.log(`     email: '${abhishek.email}',`);
    console.log(`     password: '${newPassword}'`);
    console.log(`   };`);
  } else {
    console.log('\n❌ FAILED: Password reset but login still not working');
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Reset completed');
}

// Run the reset
resetAndTestEmployeeCredentials().catch(console.error);
