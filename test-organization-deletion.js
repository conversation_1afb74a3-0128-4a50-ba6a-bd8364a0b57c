// Test script for organization deletion functionality
const API_BASE = 'http://localhost:5020/api/v1';

async function testOrganizationDeletion() {
    console.log('🧪 Testing Organization Deletion Functionality');
    console.log('='.repeat(50));

    try {
        // Test 1: Try to delete a non-existent organization
        console.log('\n📋 Test 1: Delete non-existent organization');
        const response1 = await fetch(`${API_BASE}/super-admin/organizations/by-name/NonExistentOrg?force=true`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result1 = await response1.json();
        console.log('Status:', response1.status);
        console.log('Response:', JSON.stringify(result1, null, 2));
        
        if (response1.status === 404 && result1.error?.code === 'ORGANIZATION_NOT_FOUND') {
            console.log('✅ Test 1 PASSED: Correctly handled non-existent organization');
        } else {
            console.log('❌ Test 1 FAILED: Unexpected response for non-existent organization');
        }

        // Test 2: Get list of organizations to see what exists
        console.log('\n📋 Test 2: Get list of organizations');
        const response2 = await fetch(`${API_BASE}/super-admin/organizations`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result2 = await response2.json();
        console.log('Status:', response2.status);
        console.log('Organizations found:', result2.data?.organizations?.length || 0);
        
        if (result2.data?.organizations) {
            console.log('Organization names:');
            result2.data.organizations.forEach((org, index) => {
                console.log(`  ${index + 1}. ${org.name} (${org.status})`);
            });
        }

        // Test 3: Try to delete "TechCorp Solutions" if it exists
        const techCorpOrg = result2.data?.organizations?.find(org => 
            org.name.toLowerCase().includes('techcorp') || 
            org.name.toLowerCase().includes('tech corp')
        );

        if (techCorpOrg) {
            console.log(`\n📋 Test 3: Delete organization "${techCorpOrg.name}"`);
            const response3 = await fetch(`${API_BASE}/super-admin/organizations/by-name/${encodeURIComponent(techCorpOrg.name)}?force=true`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result3 = await response3.json();
            console.log('Status:', response3.status);
            console.log('Response:', JSON.stringify(result3, null, 2));
            
            if (response3.status === 200 && result3.success) {
                console.log('✅ Test 3 PASSED: Successfully deleted organization');
                
                // Verify deletion by checking if organization still exists
                console.log('\n📋 Test 4: Verify organization was deleted');
                const response4 = await fetch(`${API_BASE}/super-admin/organizations`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result4 = await response4.json();
                const stillExists = result4.data?.organizations?.find(org => org.name === techCorpOrg.name);
                
                if (!stillExists) {
                    console.log('✅ Test 4 PASSED: Organization successfully removed from database');
                } else {
                    console.log('❌ Test 4 FAILED: Organization still exists in database');
                }
            } else {
                console.log('❌ Test 3 FAILED: Could not delete organization');
            }
        } else {
            console.log('\n📋 Test 3: SKIPPED - No TechCorp Solutions organization found');
            console.log('💡 You can create a test organization first, then run this test again');
        }

        // Test 5: Test deletion without force flag (should fail if organization has users)
        const testOrg = result2.data?.organizations?.find(org => org.name !== 'Plan Square');
        if (testOrg) {
            console.log(`\n📋 Test 5: Try to delete "${testOrg.name}" without force flag`);
            const response5 = await fetch(`${API_BASE}/super-admin/organizations/by-name/${encodeURIComponent(testOrg.name)}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result5 = await response5.json();
            console.log('Status:', response5.status);
            console.log('Response:', JSON.stringify(result5, null, 2));
            
            if (response5.status === 400 && result5.error?.code === 'ORGANIZATION_HAS_USERS') {
                console.log('✅ Test 5 PASSED: Correctly prevented deletion without force flag');
            } else if (response5.status === 200) {
                console.log('ℹ️  Test 5 INFO: Organization had no users, deletion succeeded');
            } else {
                console.log('❌ Test 5 FAILED: Unexpected response');
            }
        }

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error('Make sure the backend is running on http://localhost:5020');
    }

    console.log('\n' + '='.repeat(50));
    console.log('🏁 Testing completed');
}

// Run the test
testOrganizationDeletion();
