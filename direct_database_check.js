// Direct database check to see what's in the PlanSquare organization schema
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function checkDatabase() {
    console.log('🔍 Direct Database Check for PlanSquare Organization...\n');
    console.log('=' .repeat(60));

    try {
        // Connect to database
        console.log('\n1️⃣ CONNECTING TO DATABASE');
        console.log('-'.repeat(30));
        
        const pool = await sql.connect(config);
        console.log('✅ Connected to SQL Server database');

        // Step 1: Find PlanSquare organization
        console.log('\n2️⃣ FINDING PLANSQUARE ORGANIZATION');
        console.log('-'.repeat(30));
        
        const orgQuery = `
            SELECT Id, Name, Domain, Industry, EmployeeCount, Status, CreatedAt
            FROM Organizations 
            WHERE Name LIKE '%Plan%' OR Domain LIKE '%plan%'
        `;
        
        const orgResult = await pool.request().query(orgQuery);
        console.log(`📊 Found ${orgResult.recordset.length} organization(s):`);
        
        let planSquareOrgId = null;
        orgResult.recordset.forEach((org, index) => {
            console.log(`   ${index + 1}. ${org.Name} (${org.Domain}) - ID: ${org.Id}`);
            console.log(`      Industry: ${org.Industry}, Employees: ${org.EmployeeCount}, Status: ${org.Status}`);
            if (org.Name.toLowerCase().includes('plan') || org.Domain.toLowerCase().includes('plan')) {
                planSquareOrgId = org.Id;
            }
        });

        if (!planSquareOrgId) {
            console.log('❌ PlanSquare organization not found');
            return;
        }

        console.log(`\n✅ Using PlanSquare Organization ID: ${planSquareOrgId}`);

        // Step 2: Check users in master database
        console.log('\n3️⃣ CHECKING USERS IN MASTER DATABASE');
        console.log('-'.repeat(30));
        
        const usersQuery = `
            SELECT Id, Name, Email, Role, IsActive, IsDeleted, CreatedAt, DeletedAt
            FROM Users 
            WHERE OrganizationId = '${planSquareOrgId}'
            ORDER BY CreatedAt
        `;
        
        const usersResult = await pool.request().query(usersQuery);
        console.log(`📊 Found ${usersResult.recordset.length} user(s) in master database:`);
        
        usersResult.recordset.forEach((user, index) => {
            const status = user.IsDeleted ? 'DELETED' : (user.IsActive ? 'ACTIVE' : 'INACTIVE');
            console.log(`   ${index + 1}. ${user.Name} (${user.Email}) - ${user.Role} - ${status}`);
            console.log(`      ID: ${user.Id}`);
            console.log(`      Created: ${user.CreatedAt}, Deleted: ${user.DeletedAt || 'N/A'}`);
        });

        // Step 3: Check employee details in master database
        console.log('\n4️⃣ CHECKING EMPLOYEE DETAILS IN MASTER DATABASE');
        console.log('-'.repeat(30));
        
        const employeeDetailsQuery = `
            SELECT ed.Id, ed.UserId, ed.EmployeeId, ed.JobTitle, ed.Department, 
                   ed.EmploymentStatus, ed.IsDeleted, ed.CreatedAt, ed.DeletedAt,
                   u.Name, u.Email
            FROM EmployeeDetails ed
            INNER JOIN Users u ON ed.UserId = u.Id
            WHERE u.OrganizationId = '${planSquareOrgId}'
            ORDER BY ed.CreatedAt
        `;
        
        const empDetailsResult = await pool.request().query(employeeDetailsQuery);
        console.log(`📊 Found ${empDetailsResult.recordset.length} employee detail(s) in master database:`);
        
        empDetailsResult.recordset.forEach((emp, index) => {
            const status = emp.IsDeleted ? 'DELETED' : emp.EmploymentStatus;
            console.log(`   ${index + 1}. ${emp.Name} (${emp.Email}) - Employee ID: ${emp.EmployeeId}`);
            console.log(`      Job: ${emp.JobTitle}, Dept: ${emp.Department}, Status: ${status}`);
            console.log(`      Created: ${emp.CreatedAt}, Deleted: ${emp.DeletedAt || 'N/A'}`);
        });

        // Step 4: Check organization-specific schema
        console.log('\n5️⃣ CHECKING ORGANIZATION SCHEMA');
        console.log('-'.repeat(30));
        
        const schemaName = `org_${planSquareOrgId.replace(/-/g, '')}`.toLowerCase();
        console.log(`🔍 Checking schema: ${schemaName}`);
        
        // Check if schema exists
        const schemaExistsQuery = `
            SELECT SCHEMA_NAME 
            FROM INFORMATION_SCHEMA.SCHEMATA 
            WHERE SCHEMA_NAME = '${schemaName}'
        `;
        
        const schemaResult = await pool.request().query(schemaExistsQuery);
        
        if (schemaResult.recordset.length > 0) {
            console.log(`✅ Schema ${schemaName} exists`);
            
            // Check tables in the schema
            const tablesQuery = `
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = '${schemaName}'
                ORDER BY TABLE_NAME
            `;
            
            const tablesResult = await pool.request().query(tablesQuery);
            console.log(`📊 Found ${tablesResult.recordset.length} table(s) in schema:`);
            
            tablesResult.recordset.forEach((table, index) => {
                console.log(`   ${index + 1}. ${table.TABLE_NAME}`);
            });

            // Check if there are any records in organization-specific tables
            if (tablesResult.recordset.some(t => t.TABLE_NAME === 'Users')) {
                const orgUsersQuery = `SELECT COUNT(*) as UserCount FROM [${schemaName}].Users`;
                const orgUsersResult = await pool.request().query(orgUsersQuery);
                console.log(`📊 Users in org schema: ${orgUsersResult.recordset[0].UserCount}`);
            }

            if (tablesResult.recordset.some(t => t.TABLE_NAME === 'EmployeeDetails')) {
                const orgEmpQuery = `SELECT COUNT(*) as EmpCount FROM [${schemaName}].EmployeeDetails`;
                const orgEmpResult = await pool.request().query(orgEmpQuery);
                console.log(`📊 Employee details in org schema: ${orgEmpResult.recordset[0].EmpCount}`);
            }
        } else {
            console.log(`❌ Schema ${schemaName} does not exist`);
        }

        // Step 5: Check for any employee ID conflicts
        console.log('\n6️⃣ CHECKING FOR EMPLOYEE ID CONFLICTS');
        console.log('-'.repeat(30));
        
        const allEmployeeIdsQuery = `
            SELECT DISTINCT EmployeeId, COUNT(*) as Count
            FROM EmployeeDetails
            WHERE EmployeeId IS NOT NULL
            GROUP BY EmployeeId
            HAVING COUNT(*) > 0
            ORDER BY EmployeeId
        `;
        
        const allEmpIdsResult = await pool.request().query(allEmployeeIdsQuery);
        console.log(`📊 All Employee IDs in database (${allEmpIdsResult.recordset.length}):`);
        
        allEmpIdsResult.recordset.forEach((emp, index) => {
            console.log(`   ${index + 1}. ${emp.EmployeeId} (used ${emp.Count} time(s))`);
        });

        await pool.close();
        console.log('\n✅ Database connection closed');

        console.log('\n' + '='.repeat(60));
        console.log('🏁 DATABASE CHECK COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ DATABASE CHECK FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the database connection and try again.');
    }
}

// Run the database check
checkDatabase();
