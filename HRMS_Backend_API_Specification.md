# HRMS Application - Backend API Specification

## Overview
This document provides comprehensive REST API specifications for the HRMS (Human Resource Management System) application. The APIs support multi-tenant architecture with role-based access control for super admins, organization admins, and employees.

## API Architecture
- **Base URL**: `https://api.hrms.example.com/v1`
- **Authentication**: JWT <PERSON>ken
- **Content Type**: `application/json`
- **Response Format**: JSON with consistent error handling
- **Rate Limiting**: 1000 requests per hour per user
- **API Versioning**: URL path versioning (/v1/)

## Authentication & Authorization

### Authentication Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id> (required for org-specific endpoints)
```

### Role-Based Access Control
- **super_admin**: Full system access, can manage all organizations
- **org_admin**: Full access within their organization
- **employee**: Limited access to their own data and assigned tasks

---

## Authentication APIs

### 1. User Login
**Endpoint**: `POST /auth/login`
**Access**: Public
**Description**: Authenticate user and return JWT token

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "org_admin",
      "organization": {
        "id": "org-uuid",
        "name": "Tech Innovators Inc",
        "domain": "techinnovators.com"
      }
    },
    "token": "jwt_token_here",
    "expires_in": 86400
  }
}
```

**Error Response (401 Unauthorized)**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid email or password"
  }
}
```

### 2. Organization Registration
**Endpoint**: `POST /auth/register-organization`
**Access**: Public
**Description**: Register new organization with admin user

**Request Body**:
```json
{
  "organization": {
    "name": "Tech Innovators Inc",
    "domain": "techinnovators.com",
    "industry": "Technology"
  },
  "admin": {
    "name": "John Smith",
    "email": "<EMAIL>",
    "password": "securePassword123"
  }
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "organization": {
      "id": "org-uuid",
      "name": "Tech Innovators Inc",
      "status": "pending"
    },
    "message": "Organization registered successfully. Admin approval required."
  }
}
```

### 3. Refresh Token
**Endpoint**: `POST /auth/refresh`
**Access**: Authenticated
**Description**: Refresh JWT token

**Request Body**:
```json
{
  "refresh_token": "refresh_token_here"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "token": "new_jwt_token",
    "expires_in": 86400
  }
}
```

### 4. Logout
**Endpoint**: `POST /auth/logout`
**Access**: Authenticated
**Description**: Invalidate current session

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## User Management APIs

### 5. Get Current User Profile
**Endpoint**: `GET /users/me`
**Access**: Authenticated
**Description**: Get current user's profile information

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "employee",
    "avatar_url": "https://example.com/avatar.jpg",
    "organization": {
      "id": "org-uuid",
      "name": "Tech Innovators Inc"
    },
    "employee_details": {
      "employee_id": "TI-EMP-001",
      "job_title": "Senior Developer",
      "department": "Engineering",
      "join_date": "2024-01-15",
      "manager": "John Manager"
    }
  }
}
```

### 6. Update User Profile
**Endpoint**: `PUT /users/me`
**Access**: Authenticated
**Description**: Update current user's profile

**Request Body**:
```json
{
  "name": "John Updated Doe",
  "phone": "******-123-4567",
  "address": "123 Main St, City, State"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "name": "John Updated Doe",
    "phone": "******-123-4567",
    "address": "123 Main St, City, State",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 7. Get Organization Users
**Endpoint**: `GET /users`
**Access**: org_admin, super_admin
**Description**: Get all users in organization with pagination

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `department`: Filter by department
- `role`: Filter by role
- `search`: Search by name or email

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user-uuid",
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "employee",
        "department": "Engineering",
        "job_title": "Senior Developer",
        "status": "active",
        "join_date": "2024-01-15"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 95,
      "items_per_page": 20
    }
  }
}
```

### 8. Create New Employee
**Endpoint**: `POST /users`
**Access**: org_admin
**Description**: Create new employee user

**Request Body**:
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "role": "employee",
  "employee_details": {
    "employee_id": "TI-EMP-002",
    "job_title": "Frontend Developer",
    "department": "Engineering",
    "manager_id": "manager-uuid",
    "join_date": "2024-02-01",
    "employment_type": "full-time",
    "base_salary": 75000.00,
    "annual_ctc": 100000.00
  }
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "new-user-uuid",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "employee",
    "employee_details": {
      "employee_id": "TI-EMP-002",
      "job_title": "Frontend Developer",
      "department": "Engineering"
    },
    "temporary_password": "temp_password_123"
  }
}
```

---

## Attendance Management APIs

### 9. Check In/Out
**Endpoint**: `POST /attendance/checkin` | `POST /attendance/checkout`
**Access**: Authenticated
**Description**: Record employee check-in or check-out

**Request Body (Check-in)**:
```json
{
  "timestamp": "2024-01-15T09:00:00Z",
  "location": "Office",
  "notes": "Started work on project X"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "attendance-uuid",
    "date": "2024-01-15",
    "check_in_time": "2024-01-15T09:00:00Z",
    "status": "present",
    "message": "Check-in recorded successfully"
  }
}
```

### 10. Get Attendance Records
**Endpoint**: `GET /attendance`
**Access**: Authenticated (own records), org_admin (all records)
**Description**: Get attendance records with filtering

**Query Parameters**:
- `user_id`: Specific user (org_admin only)
- `start_date`: Start date (YYYY-MM-DD)
- `end_date`: End date (YYYY-MM-DD)
- `status`: Filter by status (present, absent, late)

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": "attendance-uuid",
        "date": "2024-01-15",
        "check_in_time": "2024-01-15T09:00:00Z",
        "check_out_time": "2024-01-15T18:00:00Z",
        "total_hours": 8.5,
        "status": "present",
        "notes": "Regular working day"
      }
    ],
    "summary": {
      "total_days": 20,
      "present_days": 18,
      "absent_days": 1,
      "late_days": 1,
      "total_hours": 152.5
    }
  }
}
```

---

## Leave Management APIs

### 11. Get Leave Balance
**Endpoint**: `GET /leave/balance`
**Access**: Authenticated (own balance), org_admin (any user)
**Description**: Get leave balance for current year

**Query Parameters**:
- `user_id`: Specific user (org_admin only)
- `year`: Year (default: current year)

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "balances": [
      {
        "leave_type": "Annual Leave",
        "total_allocated": 25,
        "used_leaves": 10,
        "remaining_leaves": 15,
        "carried_forward": 0
      },
      {
        "leave_type": "Sick Leave",
        "total_allocated": 12,
        "used_leaves": 3,
        "remaining_leaves": 9,
        "carried_forward": 0
      }
    ]
  }
}
```

### 12. Apply for Leave
**Endpoint**: `POST /leave/requests`
**Access**: Authenticated
**Description**: Submit leave application

**Request Body**:
```json
{
  "leave_type_id": "leave-type-uuid",
  "from_date": "2024-02-15",
  "to_date": "2024-02-17",
  "duration_type": "full-day",
  "reason": "Family vacation"
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "leave-request-uuid",
    "from_date": "2024-02-15",
    "to_date": "2024-02-17",
    "total_days": 3,
    "status": "pending",
    "reason": "Family vacation",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### 13. Get Leave Requests
**Endpoint**: `GET /leave/requests`
**Access**: Authenticated (own requests), org_admin (all requests)
**Description**: Get leave requests with filtering

**Query Parameters**:
- `user_id`: Specific user (org_admin only)
- `status`: Filter by status (pending, approved, rejected)
- `start_date`: Filter from date
- `end_date`: Filter to date

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "requests": [
      {
        "id": "leave-request-uuid",
        "user": {
          "id": "user-uuid",
          "name": "John Doe",
          "employee_id": "TI-EMP-001"
        },
        "leave_type": "Annual Leave",
        "from_date": "2024-02-15",
        "to_date": "2024-02-17",
        "total_days": 3,
        "status": "pending",
        "reason": "Family vacation",
        "applied_date": "2024-01-15"
      }
    ]
  }
}
```

### 14. Approve/Reject Leave
**Endpoint**: `PUT /leave/requests/{id}/status`
**Access**: org_admin, manager
**Description**: Approve or reject leave request

**Request Body**:
```json
{
  "status": "approved",
  "comments": "Approved for the requested dates"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "leave-request-uuid",
    "status": "approved",
    "approved_by": "manager-uuid",
    "approved_at": "2024-01-15T14:30:00Z",
    "comments": "Approved for the requested dates"
  }
}
```

---

## Task Management APIs

### 15. Get Tasks
**Endpoint**: `GET /tasks`
**Access**: Authenticated
**Description**: Get tasks assigned to user or created by user

**Query Parameters**:
- `assigned_to`: Filter by assignee (org_admin only)
- `status`: Filter by status (pending, in-progress, completed)
- `priority`: Filter by priority (low, medium, high, urgent)
- `due_date`: Filter by due date

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": "task-uuid",
        "title": "Complete quarterly report",
        "description": "Prepare Q1 financial report",
        "priority": "high",
        "status": "in-progress",
        "progress_percentage": 75,
        "assigned_to": {
          "id": "user-uuid",
          "name": "John Doe"
        },
        "assigned_by": {
          "id": "manager-uuid",
          "name": "Jane Manager"
        },
        "due_date": "2024-02-15",
        "estimated_hours": 8,
        "actual_hours": 6,
        "created_at": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### 16. Create Task
**Endpoint**: `POST /tasks`
**Access**: org_admin, manager
**Description**: Create new task assignment

**Request Body**:
```json
{
  "title": "Complete quarterly report",
  "description": "Prepare and submit Q1 financial report",
  "assigned_to": "user-uuid",
  "priority": "high",
  "category": "Reports",
  "estimated_hours": 8,
  "due_date": "2024-02-15"
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "task-uuid",
    "title": "Complete quarterly report",
    "status": "pending",
    "assigned_to": {
      "id": "user-uuid",
      "name": "John Doe"
    },
    "due_date": "2024-02-15",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

### 17. Update Task Progress
**Endpoint**: `PUT /tasks/{id}/progress`
**Access**: Assigned user, org_admin
**Description**: Update task progress and status

**Request Body**:
```json
{
  "progress_percentage": 75,
  "status": "in-progress",
  "hours_spent": 2.5,
  "update_notes": "Completed data analysis section",
  "blockers": "Waiting for approval from finance team",
  "next_steps": "Create visualizations and prepare summary"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "task-uuid",
    "progress_percentage": 75,
    "status": "in-progress",
    "actual_hours": 6.5,
    "updated_at": "2024-01-15T14:30:00Z"
  }
}
```

### 18. Add Task Comment
**Endpoint**: `POST /tasks/{id}/comments`
**Access**: Authenticated (task participants)
**Description**: Add comment to task

**Request Body**:
```json
{
  "comment": "Great progress! Please focus on the charts section."
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "comment-uuid",
    "comment": "Great progress! Please focus on the charts section.",
    "author": {
      "id": "user-uuid",
      "name": "Jane Manager"
    },
    "created_at": "2024-01-15T15:00:00Z"
  }
}
```

---

## Performance Management APIs

### 19. Get Performance Reviews
**Endpoint**: `GET /performance/reviews`
**Access**: Authenticated (own reviews), org_admin (all reviews)
**Description**: Get performance reviews

**Query Parameters**:
- `employee_id`: Specific employee (org_admin only)
- `reviewer_id`: Specific reviewer
- `status`: Filter by status (scheduled, completed)
- `period`: Filter by review period

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "reviews": [
      {
        "id": "review-uuid",
        "employee": {
          "id": "user-uuid",
          "name": "John Doe",
          "employee_id": "TI-EMP-001"
        },
        "reviewer": {
          "id": "manager-uuid",
          "name": "Jane Manager"
        },
        "review_period": "Q4 2023",
        "overall_rating": 4.2,
        "status": "completed",
        "review_date": "2024-01-15",
        "feedback": "Excellent performance with strong leadership skills"
      }
    ]
  }
}
```

### 20. Create Performance Review
**Endpoint**: `POST /performance/reviews`
**Access**: org_admin, manager
**Description**: Schedule new performance review

**Request Body**:
```json
{
  "employee_id": "user-uuid",
  "review_period": "Q1 2024",
  "review_type": "quarterly",
  "review_date": "2024-04-15",
  "goals": [
    {
      "title": "Improve code quality",
      "description": "Reduce bug count by 20%",
      "weight_percentage": 30
    }
  ]
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "review-uuid",
    "employee_id": "user-uuid",
    "review_period": "Q1 2024",
    "status": "scheduled",
    "review_date": "2024-04-15",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

---

## Payroll Management APIs

### 21. Get Employee Payroll
**Endpoint**: `GET /payroll/employee/{id}`
**Access**: Authenticated (own payroll), org_admin (any employee)
**Description**: Get employee payroll records

**Query Parameters**:
- `year`: Filter by year
- `month`: Filter by month

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "current_month": {
      "gross_salary": 708000,
      "net_salary": 566400,
      "total_deductions": 141600,
      "tax_deductions": 100000,
      "benefits": 41600,
      "status": "paid",
      "pay_date": "2024-01-31"
    },
    "ytd_summary": {
      "ytd_gross": 8496000,
      "ytd_net": 6796800,
      "ytd_tax": 1200000
    },
    "pay_stubs": [
      {
        "period": "January 2024",
        "gross": 708000,
        "net": 566400,
        "status": "paid",
        "pay_date": "2024-01-31"
      }
    ]
  }
}
```

### 22. Get Employee Benefits
**Endpoint**: `GET /payroll/benefits/{employee_id}`
**Access**: Authenticated (own benefits), org_admin (any employee)
**Description**: Get employee benefits information

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "benefits": [
      {
        "id": "benefit-uuid",
        "name": "Health Insurance",
        "type": "health-insurance",
        "employee_contribution": 25000,
        "employer_contribution": 37500,
        "status": "active",
        "effective_from": "2024-01-01"
      },
      {
        "id": "benefit-uuid-2",
        "name": "Provident Fund",
        "type": "provident-fund",
        "employee_contribution": 35400,
        "employer_contribution": 17700,
        "status": "active",
        "effective_from": "2024-01-01"
      }
    ]
  }
}
```

---

## Recruitment Management APIs

### 23. Get Job Postings
**Endpoint**: `GET /recruitment/jobs`
**Access**: org_admin, super_admin
**Description**: Get job postings for organization

**Query Parameters**:
- `status`: Filter by status (draft, active, closed)
- `department`: Filter by department

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "id": "job-uuid",
        "title": "Senior Frontend Developer",
        "department": "Engineering",
        "location": "New York",
        "employment_type": "full-time",
        "experience_level": "senior",
        "salary_range_min": 80000,
        "salary_range_max": 120000,
        "status": "active",
        "applications_count": 25,
        "posted_date": "2024-01-15"
      }
    ]
  }
}
```

### 24. Create Job Posting
**Endpoint**: `POST /recruitment/jobs`
**Access**: org_admin
**Description**: Create new job posting

**Request Body**:
```json
{
  "title": "Senior Frontend Developer",
  "department": "Engineering",
  "description": "We are looking for an experienced frontend developer...",
  "requirements": "5+ years of React experience, TypeScript knowledge",
  "location": "New York",
  "employment_type": "full-time",
  "experience_level": "senior",
  "salary_range_min": 80000,
  "salary_range_max": 120000,
  "application_deadline": "2024-03-15"
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "job-uuid",
    "title": "Senior Frontend Developer",
    "status": "draft",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

### 25. Get Job Applications
**Endpoint**: `GET /recruitment/jobs/{id}/applications`
**Access**: org_admin
**Description**: Get applications for specific job posting

**Query Parameters**:
- `status`: Filter by application status

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "applications": [
      {
        "id": "application-uuid",
        "candidate_name": "John Smith",
        "candidate_email": "<EMAIL>",
        "candidate_phone": "******-123-4567",
        "experience_years": 6,
        "current_salary": 75000,
        "expected_salary": 95000,
        "status": "screening",
        "applied_date": "2024-01-20",
        "resume_url": "https://example.com/resume.pdf"
      }
    ],
    "pipeline_summary": {
      "applied": 25,
      "screening": 8,
      "interview": 3,
      "selected": 1,
      "rejected": 13
    }
  }
}
```

---

## Super Admin APIs

### 26. Get All Organizations
**Endpoint**: `GET /admin/organizations`
**Access**: super_admin
**Description**: Get all organizations in the system

**Query Parameters**:
- `status`: Filter by status (active, pending, suspended)
- `industry`: Filter by industry
- `search`: Search by name or domain

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "organizations": [
      {
        "id": "org-uuid",
        "name": "Tech Innovators Inc",
        "domain": "techinnovators.com",
        "industry": "Technology",
        "employee_count": 150,
        "status": "active",
        "subscription_plan": "professional",
        "monthly_revenue": 1250000,
        "join_date": "2024-01-15",
        "admin_email": "<EMAIL>"
      }
    ],
    "summary": {
      "total_organizations": 3,
      "active_organizations": 2,
      "pending_organizations": 1,
      "total_revenue": 3583000,
      "total_employees": 430
    }
  }
}
```

### 27. Update Organization Status
**Endpoint**: `PUT /admin/organizations/{id}/status`
**Access**: super_admin
**Description**: Approve, suspend, or activate organization

**Request Body**:
```json
{
  "status": "active",
  "reason": "Organization approved after verification"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "org-uuid",
    "status": "active",
    "updated_at": "2024-01-15T10:00:00Z"
  }
}
```

### 28. Get System Analytics
**Endpoint**: `GET /admin/analytics`
**Access**: super_admin
**Description**: Get system-wide analytics and metrics

**Query Parameters**:
- `period`: Time period (7d, 30d, 90d, 1y)

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_revenue": 3583000,
      "revenue_growth": 12.5,
      "total_users": 430,
      "user_growth": 8.3,
      "total_organizations": 3,
      "system_uptime": 99.9
    },
    "revenue_by_plan": [
      {
        "plan": "Enterprise",
        "revenue": 1666000,
        "percentage": 46.5,
        "organizations": 1
      }
    ],
    "industry_breakdown": [
      {
        "industry": "Technology",
        "organizations": 1,
        "users": 150,
        "revenue": 1250000
      }
    ],
    "feature_usage": [
      {
        "feature": "Employee Management",
        "usage_percentage": 95,
        "organizations_using": 3
      }
    ]
  }
}
```

### 29. Get/Update System Settings
**Endpoint**: `GET /admin/settings` | `PUT /admin/settings`
**Access**: super_admin
**Description**: Manage system-wide configuration

**GET Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "settings": {
      "platform_name": "HRMS Portal",
      "maintenance_mode": false,
      "registration_enabled": true,
      "max_organizations": 100,
      "security": {
        "password_min_length": 8,
        "session_timeout": 30,
        "two_factor_required": false
      },
      "notifications": {
        "email_notifications": true,
        "system_alerts": true
      }
    }
  }
}
```

**PUT Request Body**:
```json
{
  "settings": {
    "maintenance_mode": true,
    "security": {
      "password_min_length": 10,
      "two_factor_required": true
    }
  }
}
```

---

## Organization Admin APIs

### 30. Get Organization Dashboard
**Endpoint**: `GET /organization/dashboard`
**Access**: org_admin
**Description**: Get organization dashboard metrics

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "metrics": {
      "total_employees": 150,
      "active_employees": 148,
      "new_hires_this_month": 5,
      "pending_leave_requests": 8,
      "attendance_rate": 94.2,
      "average_performance_rating": 4.1
    },
    "recent_activities": [
      {
        "type": "employee",
        "message": "New employee John Doe joined Engineering",
        "timestamp": "2024-01-15T10:00:00Z"
      }
    ],
    "upcoming_reviews": [
      {
        "employee_name": "Jane Smith",
        "review_date": "2024-02-15",
        "review_type": "quarterly"
      }
    ]
  }
}
```

### 31. Get Billing Information
**Endpoint**: `GET /organization/billing`
**Access**: org_admin
**Description**: Get organization billing and subscription details

**Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "subscription": {
      "plan": "Professional",
      "status": "active",
      "billing_cycle": "monthly",
      "price": 2499,
      "employee_limit": 500,
      "current_employees": 150,
      "next_billing_date": "2024-02-15"
    },
    "invoices": [
      {
        "id": "invoice-uuid",
        "invoice_number": "INV-2024-001",
        "amount": 2499,
        "status": "paid",
        "issue_date": "2024-01-15",
        "due_date": "2024-01-30"
      }
    ],
    "usage": {
      "features_used": [
        "Employee Management",
        "Attendance Tracking",
        "Leave Management",
        "Payroll Processing"
      ]
    }
  }
}
```

---

## Error Handling

### Standard Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Specific field error details"
    }
  }
}
```

### Common Error Codes
- `UNAUTHORIZED`: Authentication required or invalid token
- `FORBIDDEN`: Insufficient permissions for the requested action
- `NOT_FOUND`: Requested resource not found
- `VALIDATION_ERROR`: Request validation failed
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_SERVER_ERROR`: Server error occurred

### HTTP Status Codes
- `200 OK`: Successful GET, PUT requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

---

## API Integration Notes

### Authentication Flow
1. User logs in with email/password
2. Server validates credentials and returns JWT token
3. Client includes token in Authorization header for subsequent requests
4. Token expires after configured time (default: 24 hours)
5. Client can refresh token using refresh endpoint

### Multi-tenancy
- Organization-specific data requires `X-Organization-ID` header
- Super admin can access any organization's data
- Organization admins and employees can only access their organization's data

### Rate Limiting
- 1000 requests per hour per user
- Higher limits for super admin accounts
- Rate limit headers included in responses

### Pagination
- Default page size: 20 items
- Maximum page size: 100 items
- Pagination metadata included in responses

### Data Validation
- All input data validated according to database schema constraints
- Detailed validation error messages returned for client-side handling
- Date formats: ISO 8601 (YYYY-MM-DD for dates, YYYY-MM-DDTHH:mm:ssZ for timestamps)

This API specification provides a comprehensive foundation for backend development and frontend-backend integration of the HRMS application.
