// Fix admin user deleted status
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function fixAdminDeletedStatus() {
    console.log('🔧 Fixing Admin User Deleted Status...\n');

    let pool;
    try {
        // Connect to database
        console.log('1️⃣ Connecting to database...');
        pool = await sql.connect(config);
        console.log('✅ Connected successfully');

        // Check current status
        console.log('\n2️⃣ Checking current admin status...');
        const checkQuery = `
            SELECT Id, Name, Email, Role, IsActive, IsDeleted
            FROM Users 
            WHERE Email = '<EMAIL>'
        `;
        
        const checkResult = await pool.request().query(checkQuery);
        
        if (checkResult.recordset.length === 0) {
            console.log('❌ Admin user not found');
            return;
        }
        
        const adminUser = checkResult.recordset[0];
        console.log(`Current status:`);
        console.log(`   - Name: ${adminUser.Name}`);
        console.log(`   - Email: ${adminUser.Email}`);
        console.log(`   - Role: ${adminUser.Role}`);
        console.log(`   - Active: ${adminUser.IsActive}`);
        console.log(`   - Deleted: ${adminUser.IsDeleted} ${adminUser.IsDeleted ? '❌ PROBLEM!' : '✅ OK'}`);

        // Fix the deleted status
        if (adminUser.IsDeleted) {
            console.log('\n3️⃣ Fixing deleted status...');
            const updateQuery = `
                UPDATE Users 
                SET IsDeleted = 0, IsActive = 1, UpdatedAt = GETUTCDATE()
                WHERE Email = '<EMAIL>'
            `;
            
            await pool.request().query(updateQuery);
            console.log('✅ Updated admin user status');
            
            // Verify the fix
            const verifyResult = await pool.request().query(checkQuery);
            const updatedUser = verifyResult.recordset[0];
            console.log(`\nVerification:`);
            console.log(`   - Active: ${updatedUser.IsActive} ${updatedUser.IsActive ? '✅' : '❌'}`);
            console.log(`   - Deleted: ${updatedUser.IsDeleted} ${updatedUser.IsDeleted ? '❌' : '✅'}`);
        } else {
            console.log('\n✅ Admin user status is already correct');
        }

        // Also fix any other deleted PlanSquare users that should be active
        console.log('\n4️⃣ Checking other PlanSquare users...');
        const otherUsersQuery = `
            SELECT u.Id, u.Name, u.Email, u.Role, u.IsActive, u.IsDeleted
            FROM Users u
            JOIN Organizations o ON u.OrganizationId = o.Id
            WHERE o.Name = 'PlanSquare ' AND u.Email != '<EMAIL>'
            ORDER BY u.Name
        `;
        
        const otherUsersResult = await pool.request().query(otherUsersQuery);
        console.log(`Found ${otherUsersResult.recordset.length} other PlanSquare users:`);
        
        for (const user of otherUsersResult.recordset) {
            console.log(`\n   ${user.Name} (${user.Email})`);
            console.log(`      - Role: ${user.Role}`);
            console.log(`      - Active: ${user.IsActive}, Deleted: ${user.IsDeleted}`);
            
            // Fix users that should be active but are marked as deleted
            if (user.IsDeleted && (user.Email === '<EMAIL>' || user.Email === '<EMAIL>')) {
                console.log(`      🔧 Fixing ${user.Email}...`);
                const fixUserQuery = `
                    UPDATE Users 
                    SET IsDeleted = 0, IsActive = 1, UpdatedAt = GETUTCDATE()
                    WHERE Id = '${user.Id}'
                `;
                await pool.request().query(fixUserQuery);
                console.log(`      ✅ Fixed ${user.Email}`);
            }
        }

        // Final verification
        console.log('\n5️⃣ Final verification...');
        const finalQuery = `
            SELECT u.Name, u.Email, u.Role, u.IsActive, u.IsDeleted
            FROM Users u
            JOIN Organizations o ON u.OrganizationId = o.Id
            WHERE o.Name = 'PlanSquare ' AND u.IsActive = 1 AND u.IsDeleted = 0
            ORDER BY 
                CASE WHEN u.Role = 'OrgAdmin' THEN 0 ELSE 1 END,
                u.Name
        `;
        
        const finalResult = await pool.request().query(finalQuery);
        console.log(`Active PlanSquare users (should appear in employee list): ${finalResult.recordset.length}`);
        
        finalResult.recordset.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.Name} (${user.Email}) - ${user.Role}`);
        });

        console.log('\n6️⃣ SUMMARY');
        console.log('-'.repeat(30));
        console.log('✅ Admin user deleted status fixed');
        console.log('✅ Other PlanSquare users verified');
        console.log('✅ Admin should now appear in employee list');
        console.log('\n🎯 Next: Test the employee list API again');

    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    } finally {
        if (pool) {
            await pool.close();
            console.log('\n🔌 Database connection closed');
        }
    }
}

// Run the fix
fixAdminDeletedStatus();
