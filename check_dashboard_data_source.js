// Check what data the dashboard is using vs actual employee management data
const API_BASE = 'http://localhost:5020/api/v1';

async function checkDashboardDataSource() {
    console.log('🔍 Checking Dashboard Data Source vs Employee Management...\n');
    console.log('=' .repeat(60));

    try {
        // Step 1: Login as PlanSquare Admin
        console.log('\n1️⃣ PLANSQUARE ADMIN LOGIN');
        console.log('-'.repeat(30));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`PlanSquare admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        
        console.log(`✅ PlanSquare Admin login successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);

        // Step 2: Get Dashboard Data
        console.log('\n2️⃣ FETCHING DASHBOARD DATA');
        console.log('-'.repeat(30));
        
        const dashboardResponse = await fetch(`${API_BASE}/dashboard/organization-admin`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (dashboardResponse.ok) {
            const dashboardData = await dashboardResponse.json();
            console.log('📊 Dashboard Metrics:');
            console.log(`   Total Employees: ${dashboardData.data.metrics.totalEmployees}`);
            console.log(`   Active Employees: ${dashboardData.data.metrics.activeEmployees}`);
            console.log(`   New Hires This Month: ${dashboardData.data.metrics.newHiresThisMonth}`);
            console.log(`   Pending Leave Requests: ${dashboardData.data.metrics.pendingLeaveRequests}`);
            console.log(`   Attendance Rate: ${dashboardData.data.metrics.attendanceRate}%`);
            console.log(`   Average Performance Rating: ${dashboardData.data.metrics.averagePerformanceRating}`);
        } else {
            console.log('❌ Failed to fetch dashboard data');
        }

        // Step 3: Get Employee Management Data
        console.log('\n3️⃣ FETCHING EMPLOYEE MANAGEMENT DATA');
        console.log('-'.repeat(30));
        
        const employeesResponse = await fetch(`${API_BASE}/employee-management/employees?limit=100`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (employeesResponse.ok) {
            const employeesData = await employeesResponse.json();
            const employees = employeesData.data.items;
            
            console.log('📊 Employee Management Data:');
            console.log(`   Total Employees: ${employees.length}`);
            console.log(`   Active Employees: ${employees.filter(e => e.isActive).length}`);
            
            const employeeRoles = employees.filter(e => e.role === 'employee');
            console.log(`   Employees (role=employee): ${employeeRoles.length}`);
            
            console.log('\n📋 All Users:');
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - Role: ${emp.role}, Active: ${emp.isActive}`);
            });
        } else {
            console.log('❌ Failed to fetch employee management data');
        }

        // Step 4: Get Employee Stats
        console.log('\n4️⃣ FETCHING EMPLOYEE STATS');
        console.log('-'.repeat(30));
        
        const statsResponse = await fetch(`${API_BASE}/employee-management/stats`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            console.log('📊 Employee Stats:');
            console.log(`   Total Employees: ${statsData.data.totalEmployees}`);
            console.log(`   Active Employees: ${statsData.data.activeEmployees}`);
            console.log(`   Inactive Employees: ${statsData.data.inactiveEmployees}`);
            console.log(`   New Hires This Month: ${statsData.data.newHiresThisMonth}`);
            console.log(`   Department Breakdown:`, statsData.data.departmentBreakdown);
        } else {
            console.log('❌ Failed to fetch employee stats');
        }

        // Step 5: Test different dashboard endpoint
        console.log('\n5️⃣ TESTING GENERIC DASHBOARD ENDPOINT');
        console.log('-'.repeat(30));
        
        const genericDashboardResponse = await fetch(`${API_BASE}/dashboard/stats`, {
            method: 'GET',
            headers: { 
                'Authorization': `Bearer ${planSquareToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (genericDashboardResponse.ok) {
            const genericDashboardData = await genericDashboardResponse.json();
            console.log('📊 Generic Dashboard Data:');
            console.log(JSON.stringify(genericDashboardData.data, null, 2));
        } else {
            console.log('❌ Failed to fetch generic dashboard data');
        }

        console.log('\n' + '='.repeat(60));
        console.log('🏁 DATA SOURCE CHECK COMPLETED');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ CHECK FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the API connection and try again.');
    }
}

// Run the check
checkDashboardDataSource();
