// Script to test <PERSON>bhishek's check-out functionality
const API_BASE = 'http://localhost:5020/api/v1';

async function testAbhishekCheckout() {
    console.log('🧪 Testing Abhishek Check-out Functionality...\n');

    try {
        // Step 1: Login as Abhishek
        console.log('1️⃣ Logging in as Abhishek...');
        const employeeLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'gryZ29HX$aTf'
            })
        });

        if (!employeeLoginResponse.ok) {
            throw new Error(`Abhishek login failed: ${employeeLoginResponse.status}`);
        }

        const employeeLoginData = await employeeLoginResponse.json();
        const employeeToken = employeeLoginData.data.token;
        const employeeOrgId = employeeLoginData.data.user.organization?.id;
        
        console.log(`✅ Abhishek login successful. Organization ID: ${employeeOrgId}`);

        // Step 2: Test Attendance Check-out
        console.log(`\n2️⃣ Testing Attendance Check-out...`);
        const checkOutResponse = await fetch(`${API_BASE}/attendance/checkout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                timestamp: new Date().toISOString(),
                notes: 'Ending work for the day'
            })
        });

        console.log(`   Check-out Response Status: ${checkOutResponse.status}`);
        
        if (checkOutResponse.ok) {
            const checkOutData = await checkOutResponse.json();
            console.log(`   ✅ Check-out: SUCCESS (${checkOutResponse.status})`);
            console.log(`   📊 Response: ${JSON.stringify(checkOutData, null, 2)}`);
        } else {
            const errorText = await checkOutResponse.text();
            console.log(`   ❌ Check-out: FAILED (${checkOutResponse.status})`);
            console.log(`   🚨 Error: ${errorText}`);
        }

        // Step 3: Verify updated attendance records
        console.log(`\n3️⃣ Verifying updated attendance records...`);
        const attendanceRecordsResponse = await fetch(`${API_BASE}/attendance/records`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${employeeToken}`,
                'X-Organization-ID': employeeOrgId,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Attendance Records Response Status: ${attendanceRecordsResponse.status}`);
        
        if (attendanceRecordsResponse.ok) {
            const data = await attendanceRecordsResponse.json();
            console.log(`   ✅ Attendance Records: SUCCESS (${attendanceRecordsResponse.status})`);
            console.log(`   📊 Response: ${JSON.stringify(data, null, 2)}`);
        } else {
            const errorText = await attendanceRecordsResponse.text();
            console.log(`   ❌ Attendance Records: FAILED (${attendanceRecordsResponse.status})`);
            console.log(`   🚨 Error: ${errorText}`);
        }

        console.log('\n🎉 Abhishek Check-out Test Completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testAbhishekCheckout();
