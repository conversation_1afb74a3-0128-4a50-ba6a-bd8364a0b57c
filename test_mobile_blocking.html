<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRMS Mobile Blocking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .test-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-result.info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-allowed { background: #28a745; }
        .status-blocked { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <h1>🔒 HRMS Mobile Device Blocking Test</h1>
    <p>This page tests the mobile device detection and blocking functionality for the HRMS application.</p>

    <div class="test-container">
        <h2>📱 Current Device Detection</h2>
        <div id="deviceInfo" class="device-info">
            <p><strong>Loading device information...</strong></p>
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 Backend API Tests</h2>
        <button onclick="testBackendMobileBlocking()">Test Backend Mobile Blocking</button>
        <button onclick="testLoginWithMobileHeaders()">Test Login with Mobile Headers</button>
        <button onclick="testDesktopAccess()">Test Desktop Access</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>🌐 Frontend Tests</h2>
        <button onclick="simulateMobileDevice()">Simulate Mobile Device</button>
        <button onclick="simulateTabletDevice()">Simulate Tablet Device</button>
        <button onclick="simulateDesktopDevice()">Simulate Desktop Device</button>
        <button onclick="testResponsiveBreakpoints()">Test Responsive Breakpoints</button>
        
        <div id="frontendResults"></div>
    </div>

    <div class="test-container">
        <h2>🔗 Quick Access Links</h2>
        <p>Test the actual HRMS application:</p>
        <a href="http://localhost:8081" target="_blank" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">Open HRMS Application</a>
        <a href="http://localhost:8081?allowMobile=true" target="_blank" style="display: inline-block; padding: 10px 20px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px; margin: 5px;">Open with Mobile Override</a>
    </div>

    <script>
        const API_BASE = 'http://localhost:5020/api/v1';
        
        // Device detection functions
        function detectCurrentDevice() {
            const userAgent = navigator.userAgent;
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            // Mobile detection patterns
            const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|phone/i;
            const tabletRegex = /iPad|Android.*Tablet|Kindle|Silk|PlayBook|BB10.*Touch|RIM.*Tablet|Tab.*Build|Nexus [0-9]|Galaxy Tab/i;
            
            const isMobileUA = mobileRegex.test(userAgent) && !tabletRegex.test(userAgent);
            const isTabletUA = tabletRegex.test(userAgent);
            const isMobileScreen = screenWidth < 768;
            const isTabletScreen = screenWidth >= 768 && screenWidth < 1024;
            
            const isMobile = isMobileUA || (isMobileScreen && touchSupport);
            const isTablet = isTabletUA || (isTabletScreen && touchSupport && !isMobileUA);
            const isDesktop = !isMobile && !isTablet;
            
            let deviceType = 'desktop';
            let status = 'allowed';
            if (isMobile) {
                deviceType = 'mobile';
                status = 'blocked';
            } else if (isTablet) {
                deviceType = 'tablet';
                status = 'blocked';
            }
            
            return {
                deviceType,
                status,
                isMobile,
                isTablet,
                isDesktop,
                userAgent,
                screenWidth,
                screenHeight,
                touchSupport,
                isMobileUA,
                isTabletUA,
                isMobileScreen,
                isTabletScreen
            };
        }
        
        function updateDeviceInfo() {
            const device = detectCurrentDevice();
            const statusClass = device.status === 'allowed' ? 'status-allowed' : 'status-blocked';
            
            document.getElementById('deviceInfo').innerHTML = `
                <p><span class="status-indicator ${statusClass}"></span><strong>Device Type:</strong> ${device.deviceType.toUpperCase()} (${device.status})</p>
                <p><strong>Screen Size:</strong> ${device.screenWidth}x${device.screenHeight}</p>
                <p><strong>Touch Support:</strong> ${device.touchSupport ? 'Yes' : 'No'}</p>
                <p><strong>User Agent Detection:</strong> Mobile: ${device.isMobileUA}, Tablet: ${device.isTabletUA}</p>
                <p><strong>Screen Size Detection:</strong> Mobile: ${device.isMobileScreen}, Tablet: ${device.isTabletScreen}</p>
                <p><strong>Final Detection:</strong> Mobile: ${device.isMobile}, Tablet: ${device.isTablet}, Desktop: ${device.isDesktop}</p>
                <details>
                    <summary>User Agent String</summary>
                    <p style="word-break: break-all; font-size: 12px; color: #666;">${device.userAgent}</p>
                </details>
            `;
        }
        
        function addResult(message, type = 'info', containerId = 'testResults') {
            const resultsDiv = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('frontendResults').innerHTML = '';
        }
        
        // Backend API tests
        async function testBackendMobileBlocking() {
            try {
                addResult('🔄 Testing backend mobile device blocking...', 'info');
                
                const device = detectCurrentDevice();
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Device-Type': device.deviceType,
                        'X-Screen-Resolution': `${device.screenWidth}x${device.screenHeight}`,
                        'X-Touch-Support': device.touchSupport.toString(),
                        'X-Mobile-Detection': 'test'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 403 && data.error?.code === 'MOBILE_ACCESS_BLOCKED') {
                    addResult(`✅ Mobile blocking working correctly!<br>
                        📱 Device Type: ${device.deviceType}<br>
                        🚫 Status: ${response.status}<br>
                        💬 Message: ${data.error.message}<br>
                        📋 Details: ${JSON.stringify(data.error.details, null, 2)}`, 'success');
                } else if (device.isDesktop) {
                    addResult(`✅ Desktop access allowed as expected!<br>
                        💻 Device Type: ${device.deviceType}<br>
                        ✅ Status: ${response.status}`, 'success');
                } else {
                    addResult(`⚠️ Unexpected response for ${device.deviceType} device:<br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data, null, 2)}`, 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Backend test failed: ${error.message}`, 'error');
            }
        }
        
        async function testLoginWithMobileHeaders() {
            try {
                addResult('🔄 Testing login with explicit mobile headers...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                        'X-Device-Type': 'mobile',
                        'X-Screen-Resolution': '375x667',
                        'X-Touch-Support': 'true'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin123!'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 403 && data.error?.code === 'MOBILE_ACCESS_BLOCKED') {
                    addResult(`✅ Mobile headers correctly blocked!<br>
                        🚫 Status: ${response.status}<br>
                        💬 Message: ${data.error.message}`, 'success');
                } else {
                    addResult(`⚠️ Mobile headers not blocked:<br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data, null, 2)}`, 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Mobile header test failed: ${error.message}`, 'error');
            }
        }
        
        async function testDesktopAccess() {
            try {
                addResult('🔄 Testing desktop access...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'X-Device-Type': 'desktop',
                        'X-Screen-Resolution': '1920x1080',
                        'X-Touch-Support': 'false'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'invalid'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 401 || data.error?.code === 'INVALID_CREDENTIALS') {
                    addResult(`✅ Desktop access allowed (got expected auth error)!<br>
                        💻 Status: ${response.status}<br>
                        💬 Message: ${data.error?.message || 'Authentication failed as expected'}`, 'success');
                } else if (response.status === 403 && data.error?.code === 'MOBILE_ACCESS_BLOCKED') {
                    addResult(`❌ Desktop access incorrectly blocked!<br>
                        🚫 Status: ${response.status}<br>
                        💬 Message: ${data.error.message}`, 'error');
                } else {
                    addResult(`ℹ️ Desktop test result:<br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data, null, 2)}`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ Desktop test failed: ${error.message}`, 'error');
            }
        }
        
        // Frontend tests
        function simulateMobileDevice() {
            addResult('📱 Simulating mobile device...', 'info', 'frontendResults');
            // This would require more complex simulation in a real test environment
            addResult('ℹ️ To fully test mobile simulation, resize your browser window to < 768px width and refresh the HRMS application.', 'info', 'frontendResults');
        }
        
        function simulateTabletDevice() {
            addResult('📱 Simulating tablet device...', 'info', 'frontendResults');
            addResult('ℹ️ To fully test tablet simulation, resize your browser window to 768-1024px width and refresh the HRMS application.', 'info', 'frontendResults');
        }
        
        function simulateDesktopDevice() {
            addResult('💻 Simulating desktop device...', 'info', 'frontendResults');
            addResult('ℹ️ To fully test desktop simulation, resize your browser window to > 1024px width and refresh the HRMS application.', 'info', 'frontendResults');
        }
        
        function testResponsiveBreakpoints() {
            const breakpoints = [
                { name: 'Mobile', width: 375 },
                { name: 'Mobile Large', width: 414 },
                { name: 'Tablet', width: 768 },
                { name: 'Tablet Large', width: 1024 },
                { name: 'Desktop', width: 1200 },
                { name: 'Desktop Large', width: 1920 }
            ];
            
            addResult('📏 Testing responsive breakpoints...', 'info', 'frontendResults');
            
            breakpoints.forEach(bp => {
                const wouldBlock = bp.width < 768 || (bp.width < 1024 && 'ontouchstart' in window);
                const status = wouldBlock ? 'blocked' : 'allowed';
                const statusClass = wouldBlock ? 'error' : 'success';
                
                addResult(`${bp.name} (${bp.width}px): ${status}`, statusClass, 'frontendResults');
            });
        }
        
        // Initialize
        updateDeviceInfo();
        
        // Update device info on resize
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });
    </script>
</body>
</html>
