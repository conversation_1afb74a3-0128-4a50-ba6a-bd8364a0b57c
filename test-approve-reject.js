// Test script for Monthly Leave System functionality
const API_BASE = 'http://localhost:5020/api/v1';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'PlanSquare@123'
};

const EMPLOYEE_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'ChangedPassword123!'
};

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    console.error('Request failed:', error);
    return { status: 500, error: error.message };
  }
}

async function login(credentials) {
  console.log(`🔐 Logging in as ${credentials.email}...`);
  
  const result = await makeRequest(`${API_BASE}/auth/login`, {
    method: 'POST',
    body: JSON.stringify(credentials)
  });
  
  if (result.status === 200 && result.data.success) {
    console.log(`✅ Login successful for ${credentials.email}`);
    return result.data.data.token;
  } else {
    console.log(`❌ Login failed for ${credentials.email}:`, result.data);
    return null;
  }
}

async function applyLeave(token, daysOffset = 30) {
  console.log('📝 Applying for leave (no leave type needed)...');

  // Apply for leave - use a future date
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + daysOffset + 10); // Future date
  const endDate = new Date(futureDate); // Same day - 1 day leave

  const leaveRequest = {
    // No LeaveTypeId needed for simplified monthly system
    FromDate: futureDate.toISOString(),
    ToDate: endDate.toISOString(),
    DurationType: 'full-day',
    Reason: 'Test leave for simplified monthly leave system'
  };
  
  const result = await makeRequest(`${API_BASE}/leave/apply`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(leaveRequest)
  });
  
  if ((result.status === 200 || result.status === 201) && result.data.success) {
    console.log('✅ Leave application successful');
    console.log(`   Request ID: ${result.data.data.id}`);
    return result.data.data.id;
  } else {
    console.log('❌ Leave application failed:', result.data);
    return null;
  }
}

async function getLeaveRequests(token) {
  console.log('📋 Getting organization leave requests...');
  
  const result = await makeRequest(`${API_BASE}/organization-admin/leave/requests`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Leave requests fetched successfully');
    const requests = result.data.data.requests || [];
    console.log(`   Found ${requests.length} requests`);
    
    // Find pending requests
    const pendingRequests = requests.filter(r => r.status === 'pending');
    console.log(`   Pending requests: ${pendingRequests.length}`);
    
    return pendingRequests;
  } else {
    console.log('❌ Failed to get leave requests:', result.data);
    return [];
  }
}

async function approveLeave(token, requestId, comments = 'Approved via test script') {
  console.log(`✅ Approving leave request ${requestId}...`);
  
  const result = await makeRequest(`${API_BASE}/organization-admin/leave/requests/${requestId}/approve-reject`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: 'approved',
      comments: comments
    })
  });
  
  console.log(`   Status: ${result.status}`);
  console.log(`   Response:`, JSON.stringify(result.data, null, 2));
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Leave request approved successfully');
    return true;
  } else {
    console.log('❌ Failed to approve leave request');
    return false;
  }
}

async function rejectLeave(token, requestId, comments = 'Rejected via test script') {
  console.log(`❌ Rejecting leave request ${requestId}...`);
  
  const result = await makeRequest(`${API_BASE}/organization-admin/leave/requests/${requestId}/approve-reject`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: 'rejected',
      comments: comments
    })
  });
  
  console.log(`   Status: ${result.status}`);
  console.log(`   Response:`, JSON.stringify(result.data, null, 2));
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Leave request rejected successfully');
    return true;
  } else {
    console.log('❌ Failed to reject leave request');
    return false;
  }
}

async function testMonthlyLeaveSystem() {
  console.log('🧪 Testing Monthly Leave System');
  console.log('=' .repeat(60));

  // Test leave balance API first
  console.log('📊 Testing Leave Balance API...');
  const empToken = await login(EMPLOYEE_CREDENTIALS);
  if (!empToken) {
    console.log('❌ Test failed: Could not login as employee');
    return;
  }

  const balanceResult = await makeRequest(`${API_BASE}/leave/balance`, {
    headers: { 'Authorization': `Bearer ${empToken}` }
  });

  console.log('📊 Leave Balance Response:', JSON.stringify(balanceResult.data, null, 2));

  // Test leave application and approval workflow
  console.log('\n🔄 Testing Leave Application and Approval Workflow...');

  // Apply for leave
  const leaveResult = await applyLeave(empToken, 200); // Apply for far future date
  if (!leaveResult) {
    console.log('❌ Test failed: Could not apply for leave');
    return;
  }

  console.log('✅ Leave application successful, ID:', leaveResult.id);

  // Login as admin and approve the leave
  console.log('🔐 Logging in as admin...');
  const orgAdminToken = await login(ADMIN_CREDENTIALS);
  if (!orgAdminToken) {
    console.log('❌ Test failed: Could not login as admin');
    return;
  }

  // Get organization leave requests
  console.log('📋 Getting organization leave requests...');
  const orgRequestsResult = await makeRequest(`${API_BASE}/organization-admin/leave/requests`, {
    headers: { 'Authorization': `Bearer ${orgAdminToken}` }
  });

  if (!orgRequestsResult.data.success) {
    console.log('❌ Failed to get organization leave requests:', orgRequestsResult.data);
    return;
  }

  console.log('✅ Successfully retrieved organization leave requests!');

  const pendingRequests = orgRequestsResult.data.data.requests.filter(r => r.status === 'pending');
  console.log(`📊 Found ${pendingRequests.length} pending leave requests`);

  if (pendingRequests.length === 0) {
    console.log('❌ No pending requests found to approve');
    return;
  }

  // Approve the first pending request
  const requestToApprove = pendingRequests[0];
  console.log('✅ Approving leave request:', requestToApprove.id);

  const approveResult = await makeRequest(`${API_BASE}/organization-admin/leave/requests/${requestToApprove.id}/approve-reject`, {
    method: 'PUT',
    headers: { 'Authorization': `Bearer ${orgAdminToken}` },
    body: JSON.stringify({
      status: 'approved',
      comments: 'Approved via automated test'
    })
  });

  if (approveResult.data.success) {
    console.log('🎉 Leave request approved successfully!');
    console.log('📊 Monthly Leave System Test Completed Successfully!');
  } else {
    console.log('❌ Failed to approve leave request:', approveResult.data);
  }
  
  // Step 1: Employee applies for leave
  const employeeToken = await login(EMPLOYEE_CREDENTIALS);
  if (!employeeToken) {
    console.log('❌ Test failed: Could not login as employee');
    return;
  }
  
  const leaveRequestId = await applyLeave(employeeToken);
  if (!leaveRequestId) {
    console.log('❌ Test failed: Could not apply for leave');
    return;
  }
  
  // Step 2: Admin logs in
  const adminToken = await login(ADMIN_CREDENTIALS);
  if (!adminToken) {
    console.log('❌ Test failed: Could not login as admin');
    return;
  }
  
  // Step 3: Admin gets leave requests
  const leaveRequests = await getLeaveRequests(adminToken);
  if (leaveRequests.length === 0) {
    console.log('❌ Test failed: No pending leave requests found');
    return;
  }
  
  // Find our newly created request
  const ourRequest = leaveRequests.find(r => r.id === leaveRequestId) || leaveRequests[0];
  console.log(`🎯 Testing with request: ${ourRequest.id} (${ourRequest.user?.name || 'Unknown'})`);
  
  // Step 4: Test approval
  console.log('\n📝 Testing Approval...');
  const approvalSuccess = await approveLeave(adminToken, ourRequest.id);

  // Step 5: Apply for another leave to test rejection
  console.log('\n📝 Applying for another leave to test rejection...');
  const secondLeaveRequestId = await applyLeave(employeeToken, 120); // Use different dates

  if (secondLeaveRequestId) {
    console.log('\n📝 Testing Rejection...');
    const rejectionSuccess = await rejectLeave(adminToken, secondLeaveRequestId, 'Testing rejection functionality');

    if (approvalSuccess && rejectionSuccess) {
      console.log('\n🎉 SUCCESS: Complete Approve/Reject functionality is working!');
      console.log('✅ Employee can apply for leave');
      console.log('✅ Admin can see leave requests');
      console.log('✅ Admin can approve leave requests');
      console.log('✅ Admin can reject leave requests');
    } else {
      console.log('\n❌ FAILED: Some approve/reject functionality has issues');
    }
  } else {
    if (approvalSuccess) {
      console.log('\n🎉 SUCCESS: Approve functionality is working!');
      console.log('✅ Employee can apply for leave');
      console.log('✅ Admin can see leave requests');
      console.log('✅ Admin can approve leave requests');
    } else {
      console.log('\n❌ FAILED: Approve/Reject functionality has issues');
    }
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Test completed');
}

// Run the test
testMonthlyLeaveSystem().catch(console.error);
