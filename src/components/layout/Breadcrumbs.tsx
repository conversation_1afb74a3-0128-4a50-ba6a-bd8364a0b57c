import { useLocation, Link } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive?: boolean;
}

const Breadcrumbs = () => {
  const location = useLocation();
  const { user } = useAuth();

  // Generate breadcrumb items based on current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const path = location.pathname;
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      path: '/dashboard'
    });

    // Handle different route patterns
    if (path === '/dashboard') {
      breadcrumbs[0].isActive = true;
      return breadcrumbs;
    }

    // Super Admin routes
    if (path.startsWith('/super-admin/')) {
      const page = path.replace('/super-admin/', '');
      switch (page) {
        case 'organizations':
          breadcrumbs.push({ label: 'Organizations', path: '/super-admin/organizations', isActive: true });
          break;
        case 'analytics':
          breadcrumbs.push({ label: 'Analytics', path: '/super-admin/analytics', isActive: true });
          break;
        case 'system-settings':
          breadcrumbs.push({ label: 'System Settings', path: '/super-admin/system-settings', isActive: true });
          break;
      }
    }

    // Organization Admin routes
    else if (path.startsWith('/org-admin/')) {
      const page = path.replace('/org-admin/', '');
      switch (page) {
        case 'employee-management':
          breadcrumbs.push({ label: 'Employee Management', path: '/org-admin/employee-management', isActive: true });
          break;
        case 'attendance':
          breadcrumbs.push({ label: 'Attendance Management', path: '/org-admin/attendance', isActive: true });
          break;
        case 'leave':
          breadcrumbs.push({ label: 'Leave Management', path: '/org-admin/leave', isActive: true });
          break;
        case 'tasks':
          breadcrumbs.push({ label: 'Task Management', path: '/org-admin/tasks', isActive: true });
          break;
        case 'performance':
          breadcrumbs.push({ label: 'Performance Management', path: '/org-admin/performance', isActive: true });
          break;
        case 'payroll':
          breadcrumbs.push({ label: 'Payroll Management', path: '/org-admin/payroll', isActive: true });
          break;
        case 'recruitment':
          breadcrumbs.push({ label: 'Recruitment', path: '/org-admin/recruitment', isActive: true });
          break;
        case 'billing':
          breadcrumbs.push({ label: 'Billing & Subscription', path: '/org-admin/billing', isActive: true });
          break;
      }
    }

    // Employee routes
    else if (path.startsWith('/employee/')) {
      const page = path.replace('/employee/', '');
      switch (page) {
        case 'attendance':
          breadcrumbs.push({ label: 'My Attendance', path: '/employee/attendance', isActive: true });
          break;
        case 'leave':
          breadcrumbs.push({ label: 'My Leaves', path: '/employee/leave', isActive: true });
          break;
        case 'tasks':
          breadcrumbs.push({ label: 'My Tasks', path: '/employee/tasks', isActive: true });
          break;
        case 'performance':
          breadcrumbs.push({ label: 'Performance', path: '/employee/performance', isActive: true });
          break;
        case 'payroll':
          breadcrumbs.push({ label: 'My Payroll', path: '/employee/payroll', isActive: true });
          break;
        case 'details':
          breadcrumbs.push({ label: 'My Details', path: '/employee/details', isActive: true });
          break;
      }
    }

    // Shared routes
    else if (path === '/profile') {
      breadcrumbs.push({ label: 'My Profile', path: '/profile', isActive: true });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Don't show breadcrumbs if only dashboard
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
      <Home className="h-4 w-4" />
      {breadcrumbs.map((item, index) => (
        <div key={item.path} className="flex items-center space-x-1">
          {index > 0 && <ChevronRight className="h-4 w-4" />}
          {item.isActive ? (
            <span className="font-medium text-foreground">{item.label}</span>
          ) : (
            <Link
              to={item.path}
              className="hover:text-foreground transition-colors"
            >
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
};

export default Breadcrumbs;
