import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Home,
  Calendar,
  Clock,
  CheckSquare,
  TrendingUp,
  User,
  Users,
  DollarSign,
  Menu,
  X,
  Building2,
  BarChart3,
  Settings,
  CreditCard,
  UserCog,
  UserPlus
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

import { useAuth } from '@/contexts/AuthContext';

const Sidebar = ({ isOpen, setIsOpen }: SidebarProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Define menu items based on user role
  const getMenuItems = () => {
    const baseItems = [
      { id: 'dashboard', label: 'Dashboard', icon: Home, path: '/dashboard' }
    ];

    if (user?.role === 'super_admin') {
      return [
        ...baseItems,
        { id: 'organizations', label: 'Organizations', icon: Building2, path: '/super-admin/organizations' },
        { id: 'analytics', label: 'Analytics', icon: BarChart3, path: '/super-admin/analytics' },
        { id: 'system-settings', label: 'System Settings', icon: Settings, path: '/super-admin/system-settings' },
        { id: 'profile', label: 'My Profile', icon: User, path: '/profile' }
      ];
    }

    if (user?.role === 'org_admin') {
      return [
        ...baseItems,
        { id: 'employee-management', label: 'Employee Management', icon: UserCog, path: '/org-admin/employee-management' },
        { id: 'attendance', label: 'Attendance Management', icon: Clock, path: '/org-admin/attendance' },
        { id: 'leave', label: 'Leave Management', icon: Calendar, path: '/org-admin/leave' },
        { id: 'tasks', label: 'Task Management', icon: CheckSquare, path: '/org-admin/tasks' },
        { id: 'performance', label: 'Performance', icon: TrendingUp, path: '/org-admin/performance' },
        { id: 'payroll', label: 'Payroll', icon: DollarSign, path: '/org-admin/payroll' },
        { id: 'recruitment', label: 'Recruitment', icon: UserPlus, path: '/org-admin/recruitment' },
        { id: 'billing', label: 'Billing & Subscription', icon: CreditCard, path: '/org-admin/billing' },
        { id: 'profile', label: 'My Profile', icon: User, path: '/profile' }
      ];
    }

    if (user?.role === 'employee') {
      return [
        ...baseItems,
        { id: 'attendance', label: 'My Attendance', icon: Clock, path: '/employee/attendance' },
        { id: 'leave', label: 'My Leaves', icon: Calendar, path: '/employee/leave' },
        { id: 'tasks', label: 'My Tasks', icon: CheckSquare, path: '/employee/tasks' },
        { id: 'performance', label: 'Performance', icon: TrendingUp, path: '/employee/performance' },
        { id: 'payroll', label: 'My Payroll', icon: DollarSign, path: '/employee/payroll' },
        { id: 'employee-details', label: 'My Details', icon: User, path: '/employee/details' },
        { id: 'profile', label: 'My Profile', icon: User, path: '/profile' }
      ];
    }

    return baseItems;
  };

  // Check if current path matches menu item
  const isActiveItem = (itemPath: string) => {
    return location.pathname === itemPath;
  };

  const menuItems = getMenuItems();

  return (
    <>
      <div className={cn(
        "fixed left-0 top-0 h-full bg-card/80 backdrop-blur-md border-r border-border transition-all duration-300 z-50",
        isOpen ? "w-64" : "w-16"
      )}>
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            {isOpen && (
              <div className="flex items-center space-x-2">
                <Building2 className="h-8 w-8 text-primary" />
                <div className="flex flex-col">
                  <span className="text-lg font-bold text-primary">
                    HRMS Portal
                  </span>
                  {user?.organization && (
                    <span className="text-xs text-muted-foreground truncate max-w-[160px]">
                      {user.organization.name}
                    </span>
                  )}
                </div>
              </div>
            )}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 rounded-lg hover:bg-muted transition-colors"
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>

        <nav className="p-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = isActiveItem(item.path);
            return (
              <button
                key={item.id}
                onClick={() => navigate(item.path)}
                className={cn(
                  "w-full flex items-center justify-start space-x-3 px-3 py-3 rounded-lg transition-all duration-200 group text-left",
                  isActive
                    ? "bg-primary text-primary-foreground shadow-lg"
                    : "hover:bg-muted text-foreground"
                )}
              >
                <Icon className={cn(
                  "h-5 w-5 flex-shrink-0 min-w-[20px]",
                  isActive ? "text-primary-foreground" : "text-muted-foreground"
                )} />
                {isOpen && (
                  <span className="font-medium text-left flex-1">{item.label}</span>
                )}
              </button>
            );
          })}
        </nav>

        {/* Role Badge */}
        {isOpen && user && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-muted rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-xs font-medium text-foreground capitalize">
                  {user.role.replace('_', ' ')}
                </span>
              </div>
              {user.organization && (
                <p className="text-xs text-muted-foreground mt-1 truncate">
                  {user.organization.name}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Sidebar;
