import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from '@/components/layout/Sidebar';
import Breadcrumbs from '@/components/layout/Breadcrumbs';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { user, logout } = useAuth();
  const location = useLocation();

  // Get page title from current route
  const getPageTitle = () => {
    const path = location.pathname;
    
    // Dashboard titles
    if (path === '/dashboard') {
      return `${user?.role?.replace('_', ' ')} Dashboard`;
    }
    
    // Super Admin pages
    if (path.startsWith('/super-admin/')) {
      const page = path.replace('/super-admin/', '');
      switch (page) {
        case 'organizations': return 'Organizations';
        case 'analytics': return 'Analytics';
        case 'system-settings': return 'System Settings';
        default: return 'Super Admin';
      }
    }
    
    // Organization Admin pages
    if (path.startsWith('/org-admin/')) {
      const page = path.replace('/org-admin/', '');
      switch (page) {
        case 'employee-management': return 'Employee Management';
        case 'attendance': return 'Attendance Management';
        case 'leave': return 'Leave Management';
        case 'tasks': return 'Task Management';
        case 'performance': return 'Performance Management';
        case 'payroll': return 'Payroll Management';
        case 'recruitment': return 'Recruitment';
        case 'billing': return 'Billing & Subscription';
        default: return 'Organization Admin';
      }
    }
    
    // Employee pages
    if (path.startsWith('/employee/')) {
      const page = path.replace('/employee/', '');
      switch (page) {
        case 'attendance': return 'My Attendance';
        case 'leave': return 'My Leaves';
        case 'tasks': return 'My Tasks';
        case 'performance': return 'Performance';
        case 'payroll': return 'My Payroll';
        case 'details': return 'My Details';
        default: return 'Employee';
      }
    }
    
    // Shared pages
    if (path === '/profile') {
      return 'My Profile';
    }
    
    return 'HRMS';
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        <Sidebar 
          isOpen={sidebarOpen}
          setIsOpen={setSidebarOpen}
        />
        <main className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
          {/* Top Header */}
          <div className="bg-card/80 backdrop-blur-md border-b border-border px-6 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <h2 className="text-lg font-semibold capitalize text-foreground">
                  {getPageTitle()}
                </h2>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {user?.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="hidden md:block">
                    <p className="text-sm font-medium text-foreground">{user?.name}</p>
                    <p className="text-xs text-muted-foreground">{user?.role?.replace('_', ' ')}</p>
                  </div>
                </div>
                <Button variant="outline" size="sm" onClick={logout} className="flex items-center space-x-2">
                  <LogOut className="h-4 w-4" />
                  <span className="hidden md:inline">Logout</span>
                </Button>
              </div>
            </div>
          </div>

          <div className="p-6">
            <Breadcrumbs />
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
