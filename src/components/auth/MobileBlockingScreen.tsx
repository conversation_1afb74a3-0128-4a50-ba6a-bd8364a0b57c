import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Building2, Monitor, Smartphone, Tablet, AlertTriangle, RefreshCw } from 'lucide-react';

interface MobileBlockingScreenProps {
  deviceType: 'mobile' | 'tablet' | 'desktop';
  userAgent: string;
  onRetry?: () => void;
}

const MobileBlockingScreen: React.FC<MobileBlockingScreenProps> = ({ 
  deviceType, 
  userAgent, 
  onRetry 
}) => {
  const getDeviceIcon = () => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="h-16 w-16 text-red-500 mx-auto mb-4" />;
      case 'tablet':
        return <Tablet className="h-16 w-16 text-red-500 mx-auto mb-4" />;
      default:
        return <Monitor className="h-16 w-16 text-green-500 mx-auto mb-4" />;
    }
  };

  const getDeviceMessage = () => {
    switch (deviceType) {
      case 'mobile':
        return {
          title: 'Mobile Access Restricted',
          description: 'This application is not optimized for mobile phones.',
          recommendation: 'Please use a desktop or laptop computer to access the HRMS system.'
        };
      case 'tablet':
        return {
          title: 'Tablet Access Restricted',
          description: 'This application is not optimized for tablet devices.',
          recommendation: 'Please use a desktop or laptop computer to access the HRMS system.'
        };
      default:
        return {
          title: 'Device Access Allowed',
          description: 'Your device is compatible with this application.',
          recommendation: 'You can proceed to use the HRMS system.'
        };
    }
  };

  const message = getDeviceMessage();
  const isBlocked = deviceType === 'mobile' || deviceType === 'tablet';

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-lg">
        <div className="text-center mb-8">
          <Building2 className="mx-auto h-12 w-12 text-blue-600 mb-4" />
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            HRMS Portal
          </h1>
          <p className="text-gray-600 mt-2">Human Resource Management System</p>
        </div>

        <Card className="shadow-lg border-2 border-red-200">
          <CardHeader className="text-center">
            {getDeviceIcon()}
            <CardTitle className={`text-xl ${isBlocked ? 'text-red-600' : 'text-green-600'}`}>
              {message.title}
            </CardTitle>
            <CardDescription className="text-base">
              {message.description}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <Alert className={`${isBlocked ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}`}>
              <AlertTriangle className={`h-4 w-4 ${isBlocked ? 'text-red-600' : 'text-green-600'}`} />
              <AlertDescription className={`${isBlocked ? 'text-red-800' : 'text-green-800'}`}>
                <strong>Recommendation:</strong> {message.recommendation}
              </AlertDescription>
            </Alert>

            {isBlocked && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-800 mb-2">Supported Devices:</h3>
                  <div className="flex items-center space-x-2 text-blue-700">
                    <Monitor className="h-4 w-4" />
                    <span>Desktop Computers</span>
                  </div>
                  <div className="flex items-center space-x-2 text-blue-700 mt-1">
                    <Monitor className="h-4 w-4" />
                    <span>Laptop Computers</span>
                  </div>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-semibold text-red-800 mb-2">Unsupported Devices:</h3>
                  <div className="flex items-center space-x-2 text-red-700">
                    <Smartphone className="h-4 w-4" />
                    <span>Mobile Phones</span>
                  </div>
                  <div className="flex items-center space-x-2 text-red-700 mt-1">
                    <Tablet className="h-4 w-4" />
                    <span>Tablets</span>
                  </div>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-800 mb-2">Why Desktop Only?</h3>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Complex data entry and management interfaces</li>
                    <li>• Multiple window and tab support required</li>
                    <li>• Keyboard shortcuts for efficient navigation</li>
                    <li>• Large screen real estate for data visualization</li>
                    <li>• Enhanced security features for sensitive HR data</li>
                  </ul>
                </div>

                {onRetry && (
                  <div className="flex justify-center pt-4">
                    <Button 
                      onClick={onRetry}
                      variant="outline"
                      className="flex items-center space-x-2"
                    >
                      <RefreshCw className="h-4 w-4" />
                      <span>Check Again</span>
                    </Button>
                  </div>
                )}

                <div className="text-xs text-gray-500 mt-4 p-3 bg-gray-100 rounded">
                  <strong>Technical Details:</strong><br />
                  Device Type: {deviceType}<br />
                  Screen Size: {window.innerWidth}x{window.innerHeight}<br />
                  User Agent: {userAgent.substring(0, 100)}...
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center mt-6 text-sm text-gray-500">
          <p>For technical support, please contact your system administrator.</p>
        </div>
      </div>
    </div>
  );
};

export default MobileBlockingScreen;
