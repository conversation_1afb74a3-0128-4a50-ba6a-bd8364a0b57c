import React, { useState, useEffect } from 'react';
import { useEnhancedMobileDetection } from '@/hooks/use-mobile';
import MobileBlockingScreen from './MobileBlockingScreen';
import { getEffectiveMobileBlockingConfig } from '@/config/mobile-blocking';

interface MobileDetectionWrapperProps {
  children: React.ReactNode;
  enableMobileBlocking?: boolean;
}

const MobileDetectionWrapper: React.FC<MobileDetectionWrapperProps> = ({
  children,
  enableMobileBlocking = true
}) => {
  const deviceInfo = useEnhancedMobileDetection();
  const [retryCount, setRetryCount] = useState(0);
  const [showBlocking, setShowBlocking] = useState(false);
  const config = getEffectiveMobileBlockingConfig();

  useEffect(() => {
    // Only show blocking if mobile blocking is enabled and device is mobile/tablet
    const shouldBlock = enableMobileBlocking && config.enabled && (deviceInfo.isMobile || deviceInfo.isTablet);

    if (shouldBlock) {
      setShowBlocking(true);

      // Log the mobile access attempt for monitoring (if enabled)
      if (config.logging.logAccessAttempts) {
        console.warn('Mobile device access blocked:', {
          deviceType: deviceInfo.deviceType,
          userAgent: deviceInfo.userAgent,
          screenSize: `${deviceInfo.screenWidth}x${deviceInfo.screenHeight}`,
          touchSupport: deviceInfo.touchSupport,
          timestamp: new Date().toISOString(),
          retryCount
        });
      }

      // Send device info to backend for logging (if enabled)
      if (config.logging.sendLogsToBackend && typeof window !== 'undefined') {
        // Add custom headers for enhanced backend detection
        const originalFetch = window.fetch;
        window.fetch = function(input, init = {}) {
          const headers = new Headers(init.headers);
          
          // Add device detection headers
          headers.set('X-Device-Type', deviceInfo.deviceType);
          headers.set('X-Screen-Resolution', `${deviceInfo.screenWidth}x${deviceInfo.screenHeight}`);
          headers.set('X-Touch-Support', deviceInfo.touchSupport.toString());
          headers.set('X-Mobile-Detection', 'enhanced');
          
          return originalFetch(input, {
            ...init,
            headers
          });
        };
      }
    } else {
      setShowBlocking(false);
    }
  }, [deviceInfo, enableMobileBlocking, config.enabled, retryCount]);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    // Force re-detection by triggering a window resize event
    window.dispatchEvent(new Event('resize'));
    
    // If after retry the device is still detected as mobile, keep showing the blocking screen
    setTimeout(() => {
      if (deviceInfo.isMobile || deviceInfo.isTablet) {
        setShowBlocking(true);
      }
    }, 100);
  };

  // Show mobile blocking screen if mobile/tablet is detected and blocking is enabled
  if (showBlocking && enableMobileBlocking && config.enabled) {
    return (
      <MobileBlockingScreen
        deviceType={deviceInfo.deviceType}
        userAgent={deviceInfo.userAgent}
        onRetry={config.ui.showRetryButton ? handleRetry : undefined}
      />
    );
  }

  // Render children for desktop devices or when mobile blocking is disabled
  return <>{children}</>;
};

export default MobileDetectionWrapper;
