import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckSquare,
  Clock,
  AlertTriangle,
  TrendingUp,
  Search,
  Filter,
  Download,
  Eye,
  Plus,
  Users,
  BarChart3,
  Loader2,
  Calendar,
  Target,
  Activity,
  MessageSquare,
  User
} from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import TaskDetailsModal from './TaskDetailsModal';
import AssignTaskModal from './AssignTaskModal';

interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  progressPercentage: number;
  assignedTo: {
    id: string;
    name: string;
    employeeId?: string;
  };
  assignedBy: {
    id: string;
    name: string;
    employeeId?: string;
  };
  dueDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  createdAt: string;
  category?: string;
}

interface TaskStatistics {
  totalTasks: number;
  pendingTasks: number;
  inProgressTasks: number;
  completedTasks: number;
  overdueTasks: number;
  averageCompletionTime: number;
  completionRate: number;
  priorityBreakdown: Array<{
    priority: string;
    taskCount: number;
    completedCount: number;
    completionRate: number;
  }>;
  categoryBreakdown: Array<{
    category: string;
    taskCount: number;
    completedCount: number;
    averageCompletionTime: number;
  }>;
  departmentBreakdown: Array<{
    department: string;
    taskCount: number;
    completedCount: number;
    productivityScore: number;
  }>;
  employeeProductivity: Array<{
    employee: {
      id: string;
      name: string;
      employeeId?: string;
    };
    assignedTasks: number;
    completedTasks: number;
    completionRate: number;
    averageCompletionTime: number;
    overdueTasks: number;
  }>;
}

const AdminTaskManagement = () => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskStatistics, setTaskStatistics] = useState<TaskStatistics | null>(null);
  const [dailyUpdates, setDailyUpdates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 20;

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load tasks
      const tasksParams = {
        status: statusFilter !== 'all' ? statusFilter : undefined,
        priority: priorityFilter !== 'all' ? priorityFilter : undefined,
        department: departmentFilter !== 'all' ? departmentFilter : undefined,
        category: categoryFilter !== 'all' ? categoryFilter : undefined,
        page: currentPage,
        pageSize: pageSize
      };
      
      const tasksResponse = await apiService.getOrganizationTasks(tasksParams);
      
      if (tasksResponse.success && tasksResponse.data) {
        setTasks(tasksResponse.data.tasks || []);
        setTotalPages(tasksResponse.data.totalPages || 1);
      } else {
        toast.error('Failed to load tasks');
      }

      // Load task statistics
      const statsResponse = await apiService.getTaskStatistics();

      if (statsResponse.success && statsResponse.data) {
        setTaskStatistics(statsResponse.data.statistics);
      } else {
        toast.error('Failed to load task statistics');
      }

      // Load daily updates
      const updatesResponse = await apiService.getTaskUpdates();

      if (updatesResponse.success && updatesResponse.data) {
        setDailyUpdates(updatesResponse.data.updates || []);
      } else {
        console.log('Failed to load daily updates');
      }
    } catch (error) {
      console.error('Error loading task data:', error);
      toast.error('Failed to load task data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [currentPage, statusFilter, priorityFilter, departmentFilter, categoryFilter]);

  const filteredTasks = useMemo(() => {
    return tasks.filter(task =>
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.assignedTo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.assignedTo.employeeId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.category?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [tasks, searchTerm]);

  const handleViewDetails = (task: Task) => {
    setSelectedTask(task);
    setShowDetailsModal(true);
  };

  const handleTaskAssigned = async () => {
    setShowAssignModal(false);
    await loadData();
  };

  const handleUpdateTaskStatus = async (taskId: string, status: string, comments?: string, progressPercentage?: number) => {
    try {
      const response = await apiService.updateTaskStatus(taskId, { status, comments, progressPercentage });
      
      if (response.success) {
        toast.success(`Task status updated to ${status} successfully`);
        setShowDetailsModal(false);
        await loadData();
      } else {
        toast.error(response.error?.message || 'Failed to update task status');
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Failed to update task status');
    }
  };

  const handleExportReport = () => {
    try {
      // Create CSV content
      const headers = ['Task Title', 'Assigned To', 'Employee ID', 'Priority', 'Status', 'Progress %', 'Category', 'Due Date', 'Estimated Hours', 'Actual Hours', 'Created Date'];
      const csvContent = [
        headers.join(','),
        ...filteredTasks.map(task => [
          `"${task.title}"`,
          `"${task.assignedTo.name}"`,
          `"${task.assignedTo.employeeId || ''}"`,
          `"${task.priority}"`,
          `"${task.status}"`,
          task.progressPercentage,
          `"${task.category || ''}"`,
          `"${task.dueDate ? formatDate(task.dueDate) : ''}"`,
          task.estimatedHours || 0,
          task.actualHours || 0,
          `"${formatDate(task.createdAt)}"`
        ].join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `task-report-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success('Task report exported successfully');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export report');
    }
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', icon: Target },
      medium: { color: 'bg-blue-100 text-blue-800', icon: Target },
      high: { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
      urgent: { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{priority}</span>
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'in-progress': { color: 'bg-blue-100 text-blue-800', icon: Activity },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckSquare },
      cancelled: { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{status.replace('-', ' ')}</span>
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading task data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Task Management</h1>
          <p className="text-gray-600 mt-1">Monitor and manage tasks across your organization</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="flex items-center space-x-2" onClick={handleExportReport}>
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </Button>
          <Button
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
            onClick={() => setShowAssignModal(true)}
          >
            <Plus className="h-4 w-4" />
            <span>Assign Task</span>
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {taskStatistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
              <Target className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{taskStatistics.totalTasks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{taskStatistics.pendingTasks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Activity className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{taskStatistics.inProgressTasks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckSquare className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{taskStatistics.completedTasks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{taskStatistics.overdueTasks}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="tasks" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="productivity">Productivity</TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by task title, employee name, or category..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priority</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="hr">HR</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" className="flex items-center space-x-2">
                    <Filter className="h-4 w-4" />
                    <span>More Filters</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tasks Table */}
          <Card>
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Task</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Assigned To</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Priority</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Progress</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Due Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTasks.map((task) => (
                      <tr key={task.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium text-gray-900">{task.title}</p>
                            <p className="text-sm text-gray-600">{task.category}</p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 font-medium text-sm">
                                {task.assignedTo.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{task.assignedTo.name}</p>
                              <p className="text-sm text-gray-600">{task.assignedTo.employeeId}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">{getPriorityBadge(task.priority)}</td>
                        <td className="py-3 px-4">{getStatusBadge(task.status)}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${task.progressPercentage}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-600">{task.progressPercentage}%</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-600">
                          {task.dueDate ? formatDate(task.dueDate) : 'No due date'}
                        </td>
                        <td className="py-3 px-4">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-1"
                            onClick={() => handleViewDetails(task)}
                          >
                            <Eye className="h-3 w-3" />
                            <span>View</span>
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-6">
                  <p className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </p>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {taskStatistics && (
            <>
              {/* Priority Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Priority Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {taskStatistics.priorityBreakdown.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          {getPriorityBadge(item.priority)}
                          <div>
                            <p className="font-medium text-gray-900">{item.count || 0} tasks</p>
                            <p className="text-sm text-gray-600">Priority: {item.priority}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-blue-600">{(item.percentage || 0).toFixed(1)}%</p>
                          <p className="text-sm text-gray-600">completion rate</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Category Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Category Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {taskStatistics.categoryBreakdown.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{item.category}</p>
                          <p className="text-sm text-gray-600">{item.count || 0} tasks</p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-green-600">{(item.percentage || 0).toFixed(1)}%</p>
                          <p className="text-sm text-gray-600">completion rate</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Department Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Department Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {taskStatistics.departmentBreakdown.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{item.department}</p>
                          <p className="text-sm text-gray-600">{item.totalTasks || 0} tasks • {item.completedTasks || 0} completed</p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-purple-600">{(item.completionRate || 0).toFixed(1)}%</p>
                          <p className="text-sm text-gray-600">completion rate</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="productivity" className="space-y-6">
          {taskStatistics && (
            <Card>
              <CardHeader>
                <CardTitle>Employee Productivity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Employee</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Assigned</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Completed</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Completion Rate</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Avg Time</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Overdue</th>
                      </tr>
                    </thead>
                    <tbody>
                      {taskStatistics.employeeProductivity.map((employee, index) => (
                        <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-blue-600 font-medium text-sm">
                                  {(employee.employeeName || 'U').split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{employee.employeeName || 'Unknown'}</p>
                                <p className="text-sm text-gray-600">ID: {employee.employeeId}</p>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-600">{employee.totalTasks || 0}</td>
                          <td className="py-3 px-4 text-gray-600">{employee.completedTasks || 0}</td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-green-600 h-2 rounded-full"
                                  style={{ width: `${employee.completionRate || 0}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-600">{(employee.completionRate || 0).toFixed(1)}%</span>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-600">{(employee.averageCompletionTime || 0).toFixed(1)} days</td>
                          <td className="py-3 px-4">
                            <Badge className="bg-green-100 text-green-800">Active</Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Daily Updates Section */}
      {!loading && dailyUpdates.length > 0 && (
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5" />
                <span>Recent Daily Updates</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dailyUpdates.slice(0, 10).map((update, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{update.employee?.name || 'Unknown Employee'}</p>
                            <p className="text-sm text-gray-600">{update.taskTitle || 'Task Update'}</p>
                          </div>
                        </div>
                        <div className="ml-10">
                          <p className="text-gray-700 mb-2">{update.updateNotes}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Progress: {update.progressPercentage || 0}%</span>
                            <span>Hours: {update.hoursSpent || 0}</span>
                            <span>Date: {new Date(update.updateDate || update.createdAt).toLocaleDateString()}</span>
                          </div>
                          {update.blockers && (
                            <div className="mt-2">
                              <span className="text-sm font-medium text-red-600">Blockers: </span>
                              <span className="text-sm text-gray-700">{update.blockers}</span>
                            </div>
                          )}
                          {update.nextSteps && (
                            <div className="mt-1">
                              <span className="text-sm font-medium text-green-600">Next Steps: </span>
                              <span className="text-sm text-gray-700">{update.nextSteps}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={`${
                          update.updateType === 'Daily' ? 'bg-blue-100 text-blue-800' :
                          update.updateType === 'Weekly' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {update.updateType || 'Update'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {dailyUpdates.length > 10 && (
                <div className="mt-4 text-center">
                  <Button variant="outline" size="sm">
                    View All Updates ({dailyUpdates.length})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Task Details Modal */}
      {selectedTask && (
        <TaskDetailsModal
          task={selectedTask}
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          onUpdateStatus={handleUpdateTaskStatus}
        />
      )}

      {/* Assign Task Modal */}
      <AssignTaskModal
        isOpen={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        onTaskAssigned={handleTaskAssigned}
      />
    </div>
  );
};

export default AdminTaskManagement;
