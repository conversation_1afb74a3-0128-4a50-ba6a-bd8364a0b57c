import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Users,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import { apiService } from '@/services/api';
import AddOrganizationModal from './AddOrganizationModal';
import EditOrganizationModal from './EditOrganizationModal';
import AdminCredentialsModal from './AdminCredentialsModal';

interface Organization {
  id: string;
  name: string;
  domain: string;
  industry: string;
  employeeCount: number;
  status: string;
  subscriptionPlan: string;
  monthlyRevenue: number;
  joinDate: string;
  adminEmail: string;
}

const OrganizationsPage = () => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCredentialsModal, setShowCredentialsModal] = useState(false);

  useEffect(() => {
    fetchOrganizations();
  }, [searchTerm, statusFilter, industryFilter]);

  const fetchOrganizations = async () => {
    try {
      setLoading(true);
      const response = await apiService.getOrganizations({
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        industry: industryFilter !== 'all' ? industryFilter : undefined,
      });

      if (response.success && response.data) {
        setOrganizations(response.data.organizations || []);
      } else {
        setError(response.error?.message || 'Failed to load organizations');
      }
    } catch (err) {
      setError('Failed to load organizations');
      console.error('Organizations fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (orgId: string, newStatus: string) => {
    try {
      const response = await apiService.updateOrganizationStatus(orgId, { 
        status: newStatus,
        reason: `Status changed to ${newStatus} by super admin`
      });

      if (response.success) {
        // Refresh the organizations list
        fetchOrganizations();
      } else {
        alert('Failed to update organization status: ' + (response.error?.message || 'Unknown error'));
      }
    } catch (err) {
      alert('Failed to update organization status');
      console.error('Status update error:', err);
    }
  };

  const handleViewDetails = async (org: Organization) => {
    try {
      const response = await apiService.getOrganizationDetails(org.id);
      if (response.success && response.data) {
        setSelectedOrg({ ...org, ...response.data });
        setShowDetails(true);
      } else {
        alert('Failed to load organization details');
      }
    } catch (err) {
      alert('Failed to load organization details');
      console.error('Details fetch error:', err);
    }
  };

  const handleEditOrganization = (org: Organization) => {
    setSelectedOrg(org);
    setShowEditModal(true);
  };

  const handleViewCredentials = (org: Organization) => {
    setSelectedOrg(org);
    setShowCredentialsModal(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      pending: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      suspended: { variant: 'destructive' as const, icon: AlertTriangle, color: 'text-red-600' },
      inactive: { variant: 'outline' as const, icon: XCircle, color: 'text-gray-600' },
    };

    const config = statusConfig[status.toLowerCase() as keyof typeof statusConfig] || statusConfig.inactive;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center space-x-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const getUniqueIndustries = () => {
    const industries = [...new Set(organizations.map(org => org.industry))];
    return industries.filter(Boolean);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading organizations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 mt-20">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
        <p>{error}</p>
        <Button onClick={fetchOrganizations} className="mt-4">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Organizations</h1>
          <p className="text-gray-600">Manage all registered organizations in the system</p>
        </div>
        <Button
          className="flex items-center space-x-2"
          onClick={() => setShowAddModal(true)}
        >
          <Plus className="h-4 w-4" />
          <span>Add Organization</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="suspended">Suspended</option>
                <option value="inactive">Inactive</option>
              </select>
              <select
                value={industryFilter}
                onChange={(e) => setIndustryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Industries</option>
                {getUniqueIndustries().map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Organizations List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {organizations.map((org) => (
          <Card key={org.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <Building2 className="h-8 w-8 text-blue-600" />
                  <div>
                    <CardTitle className="text-lg">{org.name}</CardTitle>
                    <CardDescription>{org.domain}</CardDescription>
                  </div>
                </div>
                {getStatusBadge(org.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span>{org.employeeCount} employees</span>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <span>${org.monthlyRevenue.toLocaleString()}/mo</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>{new Date(org.joinDate).toLocaleDateString()}</span>
                </div>
                <div className="text-sm text-gray-600">
                  {org.industry}
                </div>
              </div>
              
              <div className="text-sm">
                <p className="text-gray-600">Admin: {org.adminEmail}</p>
                <p className="text-gray-600">Plan: {org.subscriptionPlan}</p>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewDetails(org)}
                  className="flex-1"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditOrganization(org)}
                  className="flex-1"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                {org.status === 'active' ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange(org.id, 'suspended')}
                    className="text-red-600 hover:text-red-700"
                  >
                    Suspend
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange(org.id, 'active')}
                    className="text-green-600 hover:text-green-700"
                  >
                    Activate
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {organizations.length === 0 && !loading && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No organizations found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'all' || industryFilter !== 'all'
              ? 'Try adjusting your search criteria'
              : 'Get started by adding your first organization'}
          </p>
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Organization
          </Button>
        </div>
      )}

      {/* Organization Details Modal */}
      {showDetails && selectedOrg && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2 className="text-2xl font-bold">{selectedOrg.name}</h2>
                  <p className="text-gray-600">{selectedOrg.domain}</p>
                </div>
                <Button variant="outline" onClick={() => setShowDetails(false)}>
                  Close
                </Button>
              </div>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Organization Details</h3>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">Industry:</span> {selectedOrg.industry}</p>
                      <p><span className="font-medium">Status:</span> {getStatusBadge(selectedOrg.status)}</p>
                      <p><span className="font-medium">Employees:</span> {selectedOrg.employeeCount}</p>
                      <p><span className="font-medium">Monthly Revenue:</span> ${selectedOrg.monthlyRevenue.toLocaleString()}</p>
                      <p><span className="font-medium">Subscription:</span> {selectedOrg.subscriptionPlan}</p>
                      <p><span className="font-medium">Join Date:</span> {new Date(selectedOrg.joinDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Admin Contact</h3>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">Email:</span> {selectedOrg.adminEmail}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewCredentials(selectedOrg)}
                      className="mt-3"
                    >
                      View Admin Credentials
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Organization Modal */}
      <AddOrganizationModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={fetchOrganizations}
      />

      {/* Edit Organization Modal */}
      <EditOrganizationModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={fetchOrganizations}
        organization={selectedOrg}
      />

      {/* Admin Credentials Modal */}
      <AdminCredentialsModal
        isOpen={showCredentialsModal}
        onClose={() => setShowCredentialsModal(false)}
        organization={selectedOrg}
      />
    </div>
  );
};

export default OrganizationsPage;
