import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { 
  Clock, 
  Users, 
  TrendingUp, 
  Calendar as CalendarIcon, 
  Search, 
  Filter, 
  Download, 
  Eye,
  UserCheck,
  UserX,
  AlertCircle,
  BarChart3,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import EmployeeAttendanceDetails from './EmployeeAttendanceDetails';

interface EmployeeAttendance {
  id: string;
  name: string;
  employeeId: string;
  department: string;
  status: 'present' | 'absent' | 'late' | 'on_leave';
  checkInTime?: string;
  checkOutTime?: string;
  totalHours?: number;
  location?: string;
}

interface AttendanceStats {
  totalEmployees: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  onLeaveToday: number;
  attendanceRate: number;
}

const AdminAttendanceManagement = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [dateRange, setDateRange] = useState({ start: new Date(), end: new Date() });
  const [selectedEmployee, setSelectedEmployee] = useState<{ id: string; name: string } | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Data states
  const [employees, setEmployees] = useState<any[]>([]);
  const [attendanceData, setAttendanceData] = useState<EmployeeAttendance[]>([]);
  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats>({
    totalEmployees: 0,
    presentToday: 0,
    absentToday: 0,
    lateToday: 0,
    onLeaveToday: 0,
    attendanceRate: 0
  });

  // Load organization attendance data using the new real-time API
  const loadData = async () => {
    try {
      setLoading(true);

      // Use the new organization attendance API that provides real-time data
      const organizationAttendanceResponse = await apiService.getOrganizationAttendance({
        date: selectedDate.toISOString()
      });

      if (organizationAttendanceResponse.success && organizationAttendanceResponse.data) {
        const orgData = organizationAttendanceResponse.data;

        // Set employees data from the organization attendance response
        setEmployees(orgData.employees || []);

        // Process attendance data for display with IST formatting
        const processedAttendanceData = orgData.employees.map((emp: any) => {
          return {
            id: emp.userId,
            name: emp.name,
            employeeId: emp.employeeId,
            department: emp.department,
            status: emp.status,
            checkInTime: emp.checkInTime,
            checkOutTime: emp.checkOutTime,
            totalHours: emp.totalHours,
            location: emp.location
          };
        });

        setAttendanceData(processedAttendanceData);

        // Set statistics from the API response
        setAttendanceStats({
          totalEmployees: orgData.statistics.totalEmployees,
          presentToday: orgData.statistics.presentToday,
          absentToday: orgData.statistics.absentToday,
          lateToday: orgData.statistics.lateToday,
          onLeaveToday: orgData.statistics.onLeaveToday,
          attendanceRate: orgData.statistics.attendanceRate
        });
      } else {
        toast.error('Failed to load organization attendance data');
      }
    } catch (error) {
      console.error('Error loading organization attendance data:', error);
      toast.error('Failed to load attendance data');
    } finally {
      setLoading(false);
    }
  };

  // Add real-time refresh functionality
  const refreshData = async () => {
    await loadData();
    toast.success('Attendance data refreshed');
  };

  // IST formatting functions
  const formatISTTime = (dateTimeString: string | null | undefined) => {
    if (!dateTimeString) return '-';

    try {
      const date = new Date(dateTimeString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return '-';
      }

      // Format in IST (Indian Standard Time)
      return date.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

    } catch (error) {
      return '-';
    }
  };

  const formatISTDateTime = (dateTimeString: string | null | undefined) => {
    if (!dateTimeString) return '-';

    try {
      const date = new Date(dateTimeString);
      if (isNaN(date.getTime())) return '-';

      return date.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      return '-';
    }
  };

  const formatISTDate = (dateString: string | null | undefined) => {
    if (!dateString) return '-';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '-';

      return date.toLocaleDateString('en-IN', {
        timeZone: 'Asia/Kolkata',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return '-';
    }
  };

  useEffect(() => {
    loadData();
  }, [selectedDate]);

  // Auto-refresh every 30 seconds for real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading) {
        loadData();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [loading]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Filter employees based on search and filters
  const filteredAttendanceData = useMemo(() => {
    return attendanceData.filter(employee => {
      const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           employee.employeeId.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDepartment = !filterDepartment || filterDepartment === 'all' || employee.department === filterDepartment;
      const matchesStatus = !filterStatus || filterStatus === 'all' || employee.status === filterStatus;

      return matchesSearch && matchesDepartment && matchesStatus;
    });
  }, [attendanceData, searchTerm, filterDepartment, filterStatus]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      present: { label: 'Present', className: 'bg-green-100 text-green-800 border-green-200' },
      absent: { label: 'Absent', className: 'bg-red-100 text-red-800 border-red-200' },
      late: { label: 'Late', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      on_leave: { label: 'On Leave', className: 'bg-blue-100 text-blue-800 border-blue-200' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.absent;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const formatTime = (timeString?: string | null) => {
    // Handle null/undefined/empty cases
    if (!timeString || timeString === 'null' || timeString === 'undefined') {
      return '-';
    }

    try {
      // Create Date object from the ISO string
      const date = new Date(timeString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return '-';
      }

      // Format in IST
      return date.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

    } catch (error) {
      return '-';
    }
  };

  const departments = useMemo(() => {
    const depts = [...new Set(employees.map(emp => emp.department).filter(dept => dept && dept.trim() !== ''))];
    return depts;
  }, [employees]);

  const exportToCSV = (data: EmployeeAttendance[], filename: string) => {
    const headers = ['Employee Name', 'Employee ID', 'Department', 'Status', 'Check In', 'Check Out', 'Total Hours', 'Location'];
    const csvContent = [
      headers.join(','),
      ...data.map(emp => [
        emp.name,
        emp.employeeId,
        emp.department,
        emp.status,
        formatTime(emp.checkInTime),
        formatTime(emp.checkOutTime),
        emp.totalHours ? `${emp.totalHours}h` : '-',
        emp.location || '-'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Report exported successfully');
  };

  const handleExportReport = () => {
    const today = new Date().toISOString().split('T')[0];
    const filename = `attendance-report-${today}.csv`;
    exportToCSV(filteredAttendanceData, filename);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading attendance data...</span>
      </div>
    );
  }

  // Show employee details if selected
  if (selectedEmployee) {
    return (
      <EmployeeAttendanceDetails
        employeeId={selectedEmployee.id}
        employeeName={selectedEmployee.name}
        onBack={() => setSelectedEmployee(null)}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Attendance Management</h1>
          <p className="text-gray-600 mt-1">Monitor and manage employee attendance across your organization</p>
          <p className="text-sm text-blue-600 mt-1">
            📍 All times displayed in Indian Standard Time (IST) •
            Last updated: {formatISTDateTime(currentTime.toISOString())}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            className="flex items-center space-x-2"
            onClick={refreshData}
            disabled={loading}
          >
            <Clock className="h-4 w-4" />
            <span>Refresh</span>
          </Button>
          <Button variant="outline" className="flex items-center space-x-2" onClick={handleExportReport}>
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </Button>
          <Button variant="outline" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{attendanceStats.totalEmployees}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Present Today</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{attendanceStats.presentToday}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Absent Today</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{attendanceStats.absentToday}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Late Arrivals</CardTitle>
            <AlertCircle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{attendanceStats.lateToday}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{attendanceStats.attendanceRate}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Today's Overview</TabsTrigger>
          <TabsTrigger value="employees">Employee Details</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Today's Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Date Selector */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CalendarIcon className="h-5 w-5" />
                  <span>Select Date</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setSelectedDate(date)}
                  className="rounded-md border"
                />
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Today's Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">On Time</p>
                        <p className="text-sm text-gray-600">Arrived before 9:00 AM</p>
                      </div>
                      <div className="text-2xl font-bold text-green-600">
                        {attendanceStats.presentToday}
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">Late Arrivals</p>
                        <p className="text-sm text-gray-600">Arrived after 9:00 AM</p>
                      </div>
                      <div className="text-2xl font-bold text-yellow-600">
                        {attendanceStats.lateToday}
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">Absent</p>
                        <p className="text-sm text-gray-600">No check-in recorded</p>
                      </div>
                      <div className="text-2xl font-bold text-red-600">
                        {attendanceStats.absentToday}
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">On Leave</p>
                        <p className="text-sm text-gray-600">Approved leave</p>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">
                        {attendanceStats.onLeaveToday}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Recent Check-ins</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredAttendanceData
                  .filter(emp => emp.checkInTime)
                  .sort((a, b) => new Date(b.checkInTime!).getTime() - new Date(a.checkInTime!).getTime())
                  .slice(0, 10)
                  .map((employee) => (
                    <div key={employee.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium">
                            {employee.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{employee.name}</p>
                          <p className="text-sm text-gray-600">{employee.department}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatTime(employee.checkInTime)}</p>
                        {getStatusBadge(employee.status)}
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Employee Details Tab */}
        <TabsContent value="employees">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Employee Attendance Details</CardTitle>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      {departments.map(dept => (
                        <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="present">Present</SelectItem>
                      <SelectItem value="absent">Absent</SelectItem>
                      <SelectItem value="late">Late</SelectItem>
                      <SelectItem value="on_leave">On Leave</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Employee</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Department</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Check In</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Check Out</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Hours</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAttendanceData.map((employee) => (
                      <tr key={employee.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 text-sm font-medium">
                                {employee.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{employee.name}</p>
                              <p className="text-sm text-gray-600">{employee.employeeId}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-600">{employee.department}</td>
                        <td className="py-3 px-4">{getStatusBadge(employee.status)}</td>
                        <td className="py-3 px-4 text-gray-600">{formatTime(employee.checkInTime)}</td>
                        <td className="py-3 px-4 text-gray-600">{formatTime(employee.checkOutTime)}</td>
                        <td className="py-3 px-4 text-gray-600">
                          {employee.totalHours ? `${employee.totalHours}h` : '-'}
                        </td>
                        <td className="py-3 px-4">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-1"
                            onClick={() => setSelectedEmployee({ id: employee.id, name: employee.name })}
                          >
                            <Eye className="h-3 w-3" />
                            <span>View</span>
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle>Generate Report</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Report Type</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily Report</SelectItem>
                      <SelectItem value="weekly">Weekly Report</SelectItem>
                      <SelectItem value="monthly">Monthly Report</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Department</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All departments" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      {departments.map(dept => (
                        <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Date Range</label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input type="date" placeholder="Start date" />
                    <Input type="date" placeholder="End date" />
                  </div>
                </div>
                <Button className="w-full" onClick={handleExportReport}>
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Monthly Attendance Report - November 2024</p>
                      <p className="text-sm text-gray-600">Generated on Nov 30, 2024</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Weekly Summary - Week 48</p>
                      <p className="text-sm text-gray-600">Generated on Nov 25, 2024</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Department Wise Analysis - Engineering</p>
                      <p className="text-sm text-gray-600">Generated on Nov 20, 2024</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Attendance Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Attendance trend chart will be displayed here</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Department Wise Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {departments.slice(0, 5).map((dept, index) => {
                    const deptEmployees = attendanceData.filter(emp => emp.department === dept);
                    const presentCount = deptEmployees.filter(emp => emp.status === 'present' || emp.status === 'late').length;
                    const attendanceRate = deptEmployees.length > 0 ? Math.round((presentCount / deptEmployees.length) * 100) : 0;

                    return (
                      <div key={dept} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-700">{dept}</span>
                          <span className="text-sm text-gray-600">{attendanceRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${attendanceRate}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Key Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">Peak Check-in Time</h4>
                    <p className="text-2xl font-bold text-blue-600">8:45 AM</p>
                    <p className="text-sm text-blue-700">Most employees arrive around this time</p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900">Best Performing Dept</h4>
                    <p className="text-2xl font-bold text-green-600">Engineering</p>
                    <p className="text-sm text-green-700">95% attendance rate this month</p>
                  </div>
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <h4 className="font-medium text-yellow-900">Average Work Hours</h4>
                    <p className="text-2xl font-bold text-yellow-600">8.2 hrs</p>
                    <p className="text-sm text-yellow-700">Per employee per day</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminAttendanceManagement;
