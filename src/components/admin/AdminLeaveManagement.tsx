import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { 
  Calendar as CalendarIcon, 
  Users, 
  TrendingUp, 
  Search, 
  Filter, 
  Download, 
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  BarChart3,
  Loader2,
  FileText
} from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import LeaveRequestDetailsModal from './LeaveRequestDetailsModal';

interface LeaveRequest {
  id: string;
  user: {
    id: string;
    name: string;
    employeeId?: string;
  };
  leaveType: string;
  fromDate: string;
  toDate: string;
  totalDays: number;
  status: 'pending' | 'approved' | 'rejected';
  reason: string;
  appliedDate: string;
  approvedBy?: string;
  approvedAt?: string;
  comments?: string;
}

interface LeaveStatistics {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  totalLeaveDays: number;
  averageLeavePerEmployee: number;
  leaveTypeBreakdown: Array<{
    leaveType: string;
    requestCount: number;
    totalDays: number;
  }>;
  departmentBreakdown: Array<{
    department: string;
    requestCount: number;
    totalDays: number;
    utilizationRate: number;
  }>;
}

const AdminLeaveManagement = () => {
  const { user } = useAuth();
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [leaveStatistics, setLeaveStatistics] = useState<LeaveStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 20;

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load leave requests
      const requestsParams = {
        status: statusFilter !== 'all' ? statusFilter : undefined,
        department: departmentFilter !== 'all' ? departmentFilter : undefined,
        page: currentPage,
        pageSize: pageSize
      };
      
      const requestsResponse = await apiService.getOrganizationLeaveRequests(requestsParams);
      
      if (requestsResponse.success && requestsResponse.data) {
        setLeaveRequests(requestsResponse.data.requests || []);
        setTotalPages(requestsResponse.data.totalPages || 1);
      } else {
        toast.error('Failed to load leave requests');
      }

      // Load leave statistics
      const statsResponse = await apiService.getLeaveStatistics();
      
      if (statsResponse.success && statsResponse.data) {
        setLeaveStatistics(statsResponse.data.statistics);
      } else {
        toast.error('Failed to load leave statistics');
      }
    } catch (error) {
      console.error('Error loading leave data:', error);
      toast.error('Failed to load leave data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [currentPage, statusFilter, departmentFilter]);

  const filteredLeaveRequests = useMemo(() => {
    return leaveRequests.filter(request =>
      request.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.user.employeeId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.reason.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [leaveRequests, searchTerm]);

  const handleViewDetails = (request: LeaveRequest) => {
    setSelectedRequest(request);
    setShowDetailsModal(true);
  };

  const handleApproveReject = async (requestId: string, status: 'approved' | 'rejected', comments?: string) => {
    try {
      const response = await apiService.approveRejectLeave(requestId, { status, comments });

      if (response.success) {
        toast.success(`Leave request ${status} successfully`);
        setShowDetailsModal(false);
        await loadData();
      } else {
        toast.error(response.error?.message || `Failed to ${status} leave request`);
      }
    } catch (error) {
      console.error(`Error ${status} leave request:`, error);
      toast.error(`Failed to ${status} leave request`);
    }
  };

  const handleQuickApprove = async (requestId: string) => {
    try {
      const response = await apiService.approveRejectLeave(requestId, {
        status: 'approved',
        comments: 'Quick approved by admin'
      });

      if (response.success) {
        toast.success('Leave request approved successfully');
        await loadData();
      } else {
        toast.error(response.error?.message || 'Failed to approve leave request');
      }
    } catch (error) {
      console.error('Error approving leave request:', error);
      toast.error('Failed to approve leave request');
    }
  };

  const handleQuickReject = async (requestId: string) => {
    try {
      const response = await apiService.approveRejectLeave(requestId, {
        status: 'rejected',
        comments: 'Quick rejected by admin'
      });

      if (response.success) {
        toast.success('Leave request rejected successfully');
        await loadData();
      } else {
        toast.error(response.error?.message || 'Failed to reject leave request');
      }
    } catch (error) {
      console.error('Error rejecting leave request:', error);
      toast.error('Failed to reject leave request');
    }
  };

  const handleExportReport = () => {
    try {
      // Create CSV content
      const headers = ['Employee Name', 'Employee ID', 'Type', 'From Date', 'To Date', 'Total Days', 'Status', 'Reason', 'Applied Date', 'Approved By', 'Approved Date', 'Comments'];
      const csvContent = [
        headers.join(','),
        ...filteredLeaveRequests.map(request => [
          `"${request.user.name}"`,
          `"${request.user.employeeId || ''}"`,
          `"Leave Request"`,
          `"${formatDate(request.fromDate)}"`,
          `"${formatDate(request.toDate)}"`,
          request.totalDays,
          `"${request.status}"`,
          `"${request.reason.replace(/"/g, '""')}"`,
          `"${formatDate(request.appliedDate)}"`,
          `"${request.approvedBy || ''}"`,
          `"${request.approvedAt ? formatDate(request.approvedAt) : ''}"`,
          `"${request.comments?.replace(/"/g, '""') || ''}"`
        ].join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `leave-report-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Leave report exported successfully');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export report');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading leave data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600 mt-1">Monitor and manage employee leave requests across your organization</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="flex items-center space-x-2" onClick={handleExportReport}>
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </Button>
          <Button variant="outline" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {leaveStatistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <FileText className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{leaveStatistics.totalRequests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{leaveStatistics.pendingRequests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved Requests</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{leaveStatistics.approvedRequests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Leave Days</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{leaveStatistics.totalLeaveDays}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="requests" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="requests">Leave Requests</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="requests" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by employee name, ID, or reason..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="hr">HR</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" className="flex items-center space-x-2">
                    <Filter className="h-4 w-4" />
                    <span>More Filters</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Leave Requests Table */}
          <Card>
            <CardHeader>
              <CardTitle>Leave Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Employee</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Duration</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Days</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Applied Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredLeaveRequests.map((request) => (
                      <tr key={request.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 font-medium">
                                {request.user.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{request.user.name}</p>
                              <p className="text-sm text-gray-600">{request.user.employeeId}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-600">Leave Request</td>
                        <td className="py-3 px-4 text-gray-600">
                          {formatDate(request.fromDate)} - {formatDate(request.toDate)}
                        </td>
                        <td className="py-3 px-4 text-gray-600">{request.totalDays}</td>
                        <td className="py-3 px-4">{getStatusBadge(request.status)}</td>
                        <td className="py-3 px-4 text-gray-600">{formatDate(request.appliedDate)}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center space-x-1"
                              onClick={() => handleViewDetails(request)}
                            >
                              <Eye className="h-3 w-3" />
                              <span>View</span>
                            </Button>

                            {request.status === 'pending' && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center space-x-1 text-green-600 hover:text-green-700 hover:bg-green-50"
                                  onClick={() => handleQuickApprove(request.id)}
                                >
                                  <CheckCircle className="h-3 w-3" />
                                  <span>Approve</span>
                                </Button>

                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center space-x-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleQuickReject(request.id)}
                                >
                                  <XCircle className="h-3 w-3" />
                                  <span>Reject</span>
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-6">
                  <p className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </p>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {leaveStatistics && (
            <>
              {/* Monthly Leave Usage */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Leave Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">Total Leave Requests</p>
                        <p className="text-sm text-gray-600">All leave requests in the system</p>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-blue-600">{leaveStatistics.totalRequests || 0}</p>
                        <p className="text-sm text-gray-600">requests</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Department Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Department Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {leaveStatistics.departmentBreakdown.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{item.department}</p>
                          <p className="text-sm text-gray-600">{item.requestCount} requests</p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-green-600">{item.utilizationRate.toFixed(1)}%</p>
                          <p className="text-sm text-gray-600">utilization</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Leave Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Advanced reporting features coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Leave Request Details Modal */}
      {selectedRequest && (
        <LeaveRequestDetailsModal
          request={selectedRequest}
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          onApproveReject={handleApproveReject}
        />
      )}
    </div>
  );
};

export default AdminLeaveManagement;
