import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Users, 
  CheckCircle, 
  AlertCircle, 
  Download,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { formatINR } from '@/lib/utils';

const BillingSubscription = () => {
  const { user } = useAuth();
  const [isEditingPayment, setIsEditingPayment] = useState(false);

  // Mock subscription data
  const subscriptionData = {
    plan: 'Professional',
    status: 'active',
    billingCycle: 'monthly',
    price: 2499,
    employeeLimit: 500,
    currentEmployees: 150,
    nextBillingDate: '2024-09-15',
    features: [
      'Unlimited employee records',
      'Advanced analytics',
      'Custom reporting',
      'API access',
      'Priority support',
      'Single sign-on (SSO)'
    ]
  };

  // Mock billing history
  const billingHistory = [
    {
      id: 'inv-001',
      date: '2024-08-15',
      amount: 2499,
      status: 'paid',
      description: 'Professional Plan - Monthly',
      downloadUrl: '#'
    },
    {
      id: 'inv-002',
      date: '2024-07-15',
      amount: 2499,
      status: 'paid',
      description: 'Professional Plan - Monthly',
      downloadUrl: '#'
    },
    {
      id: 'inv-003',
      date: '2024-06-15',
      amount: 2499,
      status: 'paid',
      description: 'Professional Plan - Monthly',
      downloadUrl: '#'
    }
  ];

  // Mock payment methods
  const paymentMethods = [
    {
      id: 'pm-001',
      type: 'card',
      last4: '4242',
      brand: 'Visa',
      expiryMonth: 12,
      expiryYear: 2026,
      isDefault: true
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "destructive" | "secondary"> = {
      active: 'default',
      cancelled: 'destructive',
      past_due: 'destructive',
      trialing: 'secondary'
    };
    const variant = variants[status] || 'default';
    const colors: Record<string, string> = {
      active: 'text-green-600',
      cancelled: 'text-red-600',
      past_due: 'text-red-600',
      trialing: 'text-blue-600'
    };
    return (
      <div className="flex items-center space-x-2">
        <CheckCircle className={`h-4 w-4 ${colors[status] || 'text-gray-600'}`} />
        <Badge variant={variant}>{status.replace('_', ' ')}</Badge>
      </div>
    );
  };

  const getInvoiceStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "destructive" | "secondary"> = {
      paid: 'default',
      pending: 'secondary',
      failed: 'destructive'
    };
    const variant = variants[status] || 'default';
    return <Badge variant={variant}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Billing & Subscription</h1>
          <p className="text-gray-600">Manage your organization's subscription and billing information</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Download Invoice</span>
          </Button>
          <Button className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4" />
            <span>Update Payment</span>
          </Button>
        </div>
      </div>

      {/* Current Subscription */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Current Subscription</span>
            {getStatusBadge(subscriptionData.status)}
          </CardTitle>
          <CardDescription>Your current plan and usage details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Plan</Label>
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold">{subscriptionData.plan}</span>
                <Button variant="outline" size="sm">Upgrade</Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Monthly Cost</Label>
              <div className="flex items-center space-x-1">
                <DollarSign className="h-5 w-5 text-green-600" />
                <span className="text-2xl font-bold">{formatINR(subscriptionData.price)}</span>
                <span className="text-gray-600">/{subscriptionData.billingCycle}</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Employee Usage</Label>
              <div className="flex items-center space-x-1">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="text-2xl font-bold">{subscriptionData.currentEmployees}</span>
                <span className="text-gray-600">/ {subscriptionData.employeeLimit}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${(subscriptionData.currentEmployees / subscriptionData.employeeLimit) * 100}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Next Billing</Label>
              <div className="flex items-center space-x-1">
                <Calendar className="h-5 w-5 text-purple-600" />
                <span className="text-lg font-semibold">{subscriptionData.nextBillingDate}</span>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          <div>
            <h3 className="text-lg font-semibold mb-3">Plan Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {subscriptionData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Methods */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Payment Methods</span>
              <Button size="sm" className="flex items-center space-x-1">
                <Plus className="h-4 w-4" />
                <span>Add</span>
              </Button>
            </CardTitle>
            <CardDescription>Manage your payment methods</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CreditCard className="h-8 w-8 text-gray-600" />
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{method.brand} •••• {method.last4}</span>
                        {method.isDefault && <Badge variant="secondary">Default</Badge>}
                      </div>
                      <p className="text-sm text-gray-600">
                        Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Billing Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Billing Information</span>
              <Button size="sm" variant="outline">
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
            </CardTitle>
            <CardDescription>Your organization's billing details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">Organization</Label>
                <p className="text-sm">{user?.organization?.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">Billing Email</Label>
                <p className="text-sm">{user?.email}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">Address</Label>
                <p className="text-sm">123 Business Street<br />Suite 100<br />City, State 12345</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">Tax ID</Label>
                <p className="text-sm">12-3456789</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>Your recent invoices and payments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {billingHistory.map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                    <DollarSign className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{invoice.description}</span>
                      {getInvoiceStatusBadge(invoice.status)}
                    </div>
                    <p className="text-sm text-gray-600">Invoice #{invoice.id} • {invoice.date}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="font-semibold">{formatINR(invoice.amount)}</span>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Analytics */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Analytics</CardTitle>
          <CardDescription>Track your organization's platform usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">150</div>
              <p className="text-sm text-gray-600">Active Employees</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">98.5%</div>
              <p className="text-sm text-gray-600">System Uptime</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">2.4GB</div>
              <p className="text-sm text-gray-600">Data Storage Used</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BillingSubscription;
