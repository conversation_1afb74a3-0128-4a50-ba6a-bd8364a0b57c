import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  X, 
  Building2, 
  Globe, 
  Briefcase,
  DollarSign,
  CreditCard,
  Save,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { apiService } from '@/services/api';

interface EditOrganizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  organization: {
    id: string;
    name: string;
    domain: string;
    industry: string;
    subscriptionPlan: string;
    monthlyRevenue: number;
  } | null;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  industry: string;
  subscriptionPlan: string;
  monthlyRevenue: number;
}

const EditOrganizationModal = ({ isOpen, onClose, onSuccess, organization }: EditOrganizationModalProps) => {
  const [formData, setFormData] = useState<OrganizationFormData>({
    name: '',
    domain: '',
    industry: '',
    subscriptionPlan: '',
    monthlyRevenue: 0
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Initialize form data when organization changes
  useEffect(() => {
    if (organization) {
      setFormData({
        name: organization.name,
        domain: organization.domain,
        industry: organization.industry,
        subscriptionPlan: organization.subscriptionPlan,
        monthlyRevenue: organization.monthlyRevenue
      });
    }
  }, [organization]);

  const handleInputChange = (field: keyof OrganizationFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      setError('Organization name is required');
      return false;
    }
    if (!formData.domain.trim()) {
      setError('Domain is required');
      return false;
    }
    if (!formData.industry.trim()) {
      setError('Industry is required');
      return false;
    }
    if (!formData.subscriptionPlan.trim()) {
      setError('Subscription plan is required');
      return false;
    }
    if (formData.monthlyRevenue < 0) {
      setError('Monthly revenue cannot be negative');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!organization || !validateForm()) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateOrganization(organization.id, {
        name: formData.name,
        domain: formData.domain,
        industry: formData.industry,
        subscriptionPlan: formData.subscriptionPlan,
        monthlyRevenue: formData.monthlyRevenue
      });

      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          onSuccess(); // Refresh the organizations list
          handleClose();
        }, 1500);
      } else {
        setError(response.error?.message || 'Failed to update organization');
      }
    } catch (err) {
      setError('Failed to update organization. Please try again.');
      console.error('Organization update error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setError(null);
    setSuccess(false);
    onClose();
  };

  if (!isOpen || !organization) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <Building2 className="h-6 w-6 text-blue-600" />
                <div>
                  <CardTitle className="text-xl">
                    {success ? 'Organization Updated Successfully!' : 'Edit Organization'}
                  </CardTitle>
                  <CardDescription>
                    {success 
                      ? 'Organization details have been updated' 
                      : 'Modify organization information and settings'
                    }
                  </CardDescription>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {success ? (
              // Success state
              <div className="text-center py-8">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-800 mb-2">Update Successful!</h3>
                <p className="text-green-700">
                  The organization details have been updated successfully.
                </p>
              </div>
            ) : (
              // Form state
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center space-x-2">
                    <Building2 className="h-5 w-5" />
                    <span>Organization Information</span>
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Organization Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="e.g., Acme Corporation"
                        disabled={loading}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="domain">Domain *</Label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="domain"
                          value={formData.domain}
                          onChange={(e) => handleInputChange('domain', e.target.value)}
                          placeholder="e.g., acme.com"
                          className="pl-10"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="industry">Industry *</Label>
                    <div className="relative">
                      <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="industry"
                        value={formData.industry}
                        onChange={(e) => handleInputChange('industry', e.target.value)}
                        placeholder="e.g., Technology, Healthcare, Finance"
                        className="pl-10"
                        disabled={loading}
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center space-x-2">
                    <CreditCard className="h-5 w-5" />
                    <span>Subscription & Revenue</span>
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="subscriptionPlan">Subscription Plan *</Label>
                      <select
                        id="subscriptionPlan"
                        value={formData.subscriptionPlan}
                        onChange={(e) => handleInputChange('subscriptionPlan', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={loading}
                      >
                        <option value="">Select Plan</option>
                        <option value="Basic">Basic</option>
                        <option value="Standard">Standard</option>
                        <option value="Premium">Premium</option>
                        <option value="Enterprise">Enterprise</option>
                      </select>
                    </div>
                    
                    <div>
                      <Label htmlFor="monthlyRevenue">Monthly Revenue ($)</Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="monthlyRevenue"
                          type="number"
                          min="0"
                          step="0.01"
                          value={formData.monthlyRevenue}
                          onChange={(e) => handleInputChange('monthlyRevenue', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className="pl-10"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <Button type="button" variant="outline" onClick={handleClose} disabled={loading}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Updating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Update Organization
                      </>
                    )}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditOrganizationModal;
