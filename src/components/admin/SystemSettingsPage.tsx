import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { 
  Settings, 
  Shield, 
  Bell, 
  Database, 
  Server,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Lock,
  Globe,
  Users
} from 'lucide-react';
import { apiService } from '@/services/api';
import type { SystemSettings, UpdateSystemSettingsRequest } from '@/services/api';

const SystemSettingsPage = () => {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    maintenanceMode: false,
    passwordMinLength: 8,
    sessionTimeout: 1440,
    twoFactorRequired: false,
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await apiService.getSystemSettings();

      if (response.success && response.data) {
        setSettings(response.data);
        setFormData({
          maintenanceMode: response.data.settings.maintenanceMode,
          passwordMinLength: response.data.settings.security.passwordMinLength,
          sessionTimeout: response.data.settings.security.sessionTimeout,
          twoFactorRequired: response.data.settings.security.twoFactorRequired,
        });
      } else {
        setError(response.error?.message || 'Failed to load system settings');
      }
    } catch (err) {
      setError('Failed to load system settings');
      console.error('Settings fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccessMessage(null);

      const updateData: UpdateSystemSettingsRequest = {
        settings: {
          maintenanceMode: formData.maintenanceMode,
          security: {
            passwordMinLength: formData.passwordMinLength,
            sessionTimeout: formData.sessionTimeout,
            twoFactorRequired: formData.twoFactorRequired,
          }
        }
      };

      const response = await apiService.updateSystemSettings(updateData);

      if (response.success) {
        setSuccessMessage('System settings updated successfully');
        fetchSettings(); // Refresh to get latest data
      } else {
        setError(response.error?.message || 'Failed to update system settings');
      }
    } catch (err) {
      setError('Failed to update system settings');
      console.error('Settings update error:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading system settings...</p>
        </div>
      </div>
    );
  }

  if (error && !settings) {
    return (
      <div className="text-center text-red-500 mt-20">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
        <p>{error}</p>
        <Button onClick={fetchSettings} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600">Configure global system settings and preferences</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchSettings}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Platform Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Platform Settings</span>
          </CardTitle>
          <CardDescription>General platform configuration</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="text-sm font-medium text-gray-700">Platform Name</label>
            <Input
              value={settings?.settings.platformName || 'HRMS Platform'}
              disabled
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">Platform name cannot be changed</p>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700">Maximum Organizations</label>
            <Input
              type="number"
              value={settings?.settings.maxOrganizations || 1000}
              disabled
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">Contact support to modify this limit</p>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">Registration Enabled</label>
              <p className="text-xs text-gray-500">Allow new organization registrations</p>
            </div>
            <Switch
              checked={settings?.settings.registrationEnabled || false}
              disabled
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">Maintenance Mode</label>
              <p className="text-xs text-gray-500">Put the system in maintenance mode</p>
            </div>
            <Switch
              checked={formData.maintenanceMode}
              onCheckedChange={(checked) => handleInputChange('maintenanceMode', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Security Settings</span>
          </CardTitle>
          <CardDescription>Configure security policies and requirements</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="text-sm font-medium text-gray-700">Minimum Password Length</label>
            <Input
              type="number"
              min="6"
              max="32"
              value={formData.passwordMinLength}
              onChange={(e) => handleInputChange('passwordMinLength', parseInt(e.target.value))}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">Minimum characters required for passwords</p>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700">Session Timeout (minutes)</label>
            <Input
              type="number"
              min="30"
              max="10080"
              value={formData.sessionTimeout}
              onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">User session timeout in minutes (30 min - 7 days)</p>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">Two-Factor Authentication</label>
              <p className="text-xs text-gray-500">Require 2FA for all users</p>
            </div>
            <Switch
              checked={formData.twoFactorRequired}
              onCheckedChange={(checked) => handleInputChange('twoFactorRequired', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Notification Settings</span>
          </CardTitle>
          <CardDescription>Configure system notifications and alerts</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">Email Notifications</label>
              <p className="text-xs text-gray-500">Send email notifications to users</p>
            </div>
            <Switch
              checked={settings?.settings.notifications.emailNotifications || false}
              disabled
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">System Alerts</label>
              <p className="text-xs text-gray-500">Enable system-wide alerts and warnings</p>
            </div>
            <Switch
              checked={settings?.settings.notifications.systemAlerts || false}
              disabled
            />
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>System Information</span>
          </CardTitle>
          <CardDescription>Current system status and information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-700">System Status</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Operational</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Database Status</p>
                <div className="flex items-center space-x-2 mt-1">
                  <Database className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-gray-600">Connected</span>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-700">Last Backup</p>
                <p className="text-sm text-gray-600 mt-1">
                  {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">System Version</p>
                <p className="text-sm text-gray-600 mt-1">HRMS v1.0.0</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Danger Zone</span>
          </CardTitle>
          <CardDescription>Irreversible and destructive actions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50">
              <Database className="h-4 w-4 mr-2" />
              Backup Database
            </Button>
            <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50" disabled>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset System (Coming Soon)
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemSettingsPage;
