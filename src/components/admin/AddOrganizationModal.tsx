import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  X, 
  Building2, 
  User, 
  Mail, 
  Globe, 
  Briefcase,
  Copy,
  CheckCircle,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react';
import { apiService } from '@/services/api';

interface AddOrganizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  industry: string;
  adminName: string;
  adminEmail: string;
}

interface AdminCredentials {
  email: string;
  name: string;
  temporaryPassword: string;
  requirePasswordReset: boolean;
  generatedAt: string;
}

const AddOrganizationModal = ({ isOpen, onClose, onSuccess }: AddOrganizationModalProps) => {
  const [formData, setFormData] = useState<OrganizationFormData>({
    name: '',
    domain: '',
    industry: '',
    adminName: '',
    adminEmail: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [adminCredentials, setAdminCredentials] = useState<AdminCredentials | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleInputChange = (field: keyof OrganizationFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      setError('Organization name is required');
      return false;
    }
    if (!formData.domain.trim()) {
      setError('Domain is required');
      return false;
    }
    if (!formData.industry.trim()) {
      setError('Industry is required');
      return false;
    }
    if (!formData.adminName.trim()) {
      setError('Admin name is required');
      return false;
    }
    if (!formData.adminEmail.trim()) {
      setError('Admin email is required');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.adminEmail)) {
      setError('Please enter a valid email address');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createOrganization({
        name: formData.name,
        domain: formData.domain,
        industry: formData.industry,
        adminName: formData.adminName,
        adminEmail: formData.adminEmail
      });

      if (response.success && response.data) {
        setSuccess(true);
        setAdminCredentials(response.data.adminCredentials);
      } else {
        setError(response.error?.message || 'Failed to create organization');
      }
    } catch (err) {
      setError('Failed to create organization. Please try again.');
      console.error('Organization creation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const handleClose = () => {
    if (success) {
      onSuccess(); // Refresh the organizations list
    }
    
    // Reset form
    setFormData({
      name: '',
      domain: '',
      industry: '',
      adminName: '',
      adminEmail: ''
    });
    setError(null);
    setSuccess(false);
    setAdminCredentials(null);
    setShowPassword(false);
    setCopiedField(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <Building2 className="h-6 w-6 text-blue-600" />
                <div>
                  <CardTitle className="text-xl">
                    {success ? 'Organization Created Successfully!' : 'Add New Organization'}
                  </CardTitle>
                  <CardDescription>
                    {success 
                      ? 'Admin credentials have been generated automatically' 
                      : 'Create a new organization with admin credentials'
                    }
                  </CardDescription>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {success && adminCredentials ? (
              // Success state with credentials
              <div className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="font-semibold text-green-800">Organization Created</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    The organization has been created successfully with an active admin account.
                  </p>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Admin Login Credentials</h3>
                  <div className="bg-gray-50 border rounded-lg p-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Admin Name</Label>
                        <div className="flex items-center space-x-2 mt-1">
                          <Input value={adminCredentials.name} readOnly className="bg-white" />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCopy(adminCredentials.name, 'name')}
                          >
                            {copiedField === 'name' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                      
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Email</Label>
                        <div className="flex items-center space-x-2 mt-1">
                          <Input value={adminCredentials.email} readOnly className="bg-white" />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCopy(adminCredentials.email, 'email')}
                          >
                            {copiedField === 'email' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-700">Temporary Password</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <div className="relative flex-1">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            value={adminCredentials.temporaryPassword}
                            readOnly
                            className="bg-white pr-10"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopy(adminCredentials.temporaryPassword, 'password')}
                        >
                          {copiedField === 'password' ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>

                    <div className="text-sm text-gray-600 bg-blue-50 border border-blue-200 rounded p-3">
                      <p className="font-medium text-blue-800 mb-1">Important Notes:</p>
                      <ul className="list-disc list-inside space-y-1 text-blue-700">
                        <li>The admin can log in immediately with these credentials</li>
                        <li>Password reset will be required on first login</li>
                        <li>Save these credentials securely - they won't be shown again</li>
                        <li>The organization is now active and ready to use</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleClose} className="px-6">
                    Done
                  </Button>
                </div>
              </div>
            ) : (
              // Form state
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center space-x-2">
                    <Building2 className="h-5 w-5" />
                    <span>Organization Details</span>
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Organization Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="e.g., Acme Corporation"
                        disabled={loading}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="domain">Domain *</Label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="domain"
                          value={formData.domain}
                          onChange={(e) => handleInputChange('domain', e.target.value)}
                          placeholder="e.g., acme.com"
                          className="pl-10"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="industry">Industry *</Label>
                    <div className="relative">
                      <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="industry"
                        value={formData.industry}
                        onChange={(e) => handleInputChange('industry', e.target.value)}
                        placeholder="e.g., Technology, Healthcare, Finance"
                        className="pl-10"
                        disabled={loading}
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span>Organization Administrator</span>
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="adminName">Admin Name *</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="adminName"
                          value={formData.adminName}
                          onChange={(e) => handleInputChange('adminName', e.target.value)}
                          placeholder="e.g., John Smith"
                          className="pl-10"
                          disabled={loading}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="adminEmail">Admin Email *</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="adminEmail"
                          type="email"
                          value={formData.adminEmail}
                          onChange={(e) => handleInputChange('adminEmail', e.target.value)}
                          placeholder="e.g., <EMAIL>"
                          className="pl-10"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600 bg-blue-50 border border-blue-200 rounded p-3">
                    <p className="font-medium text-blue-800 mb-1">Automatic Credential Generation:</p>
                    <p className="text-blue-700">
                      A secure password will be automatically generated for the admin user. 
                      The credentials will be displayed after successful creation.
                    </p>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <Button type="button" variant="outline" onClick={handleClose} disabled={loading}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      'Create Organization'
                    )}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddOrganizationModal;
