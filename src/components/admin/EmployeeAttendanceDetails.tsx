import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Clock, 
  Calendar as CalendarIcon, 
  TrendingUp, 
  TrendingDown,
  User,
  MapPin,
  ArrowLeft,
  Download,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';

interface EmployeeAttendanceDetailsProps {
  employeeId: string;
  employeeName: string;
  onBack: () => void;
}

interface AttendanceRecord {
  id: string;
  date: string;
  checkInTime?: string;
  checkOutTime?: string;
  totalHours?: number;
  status: 'present' | 'absent' | 'late' | 'on_leave';
  location?: string;
  notes?: string;
}

interface AttendanceStats {
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  averageHours: number;
  attendanceRate: number;
  punctualityRate: number;
}

const EmployeeAttendanceDetails: React.FC<EmployeeAttendanceDetailsProps> = ({
  employeeId,
  employeeName,
  onBack
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(new Date());
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats>({
    totalDays: 0,
    presentDays: 0,
    absentDays: 0,
    lateDays: 0,
    averageHours: 0,
    attendanceRate: 0,
    punctualityRate: 0
  });

  const loadAttendanceData = async () => {
    try {
      setLoading(true);
      const startOfMonth = new Date(selectedMonth.getFullYear(), selectedMonth.getMonth(), 1);
      const endOfMonth = new Date(selectedMonth.getFullYear(), selectedMonth.getMonth() + 1, 0);

      const response = await apiService.getAttendanceRecords({
        userId: employeeId,
        startDate: startOfMonth.toISOString(),
        endDate: endOfMonth.toISOString()
      });

      if (response.success && response.data) {
        const records = response.data.records || [];
        setAttendanceRecords(records);
        calculateStats(records);
      } else {
        toast.error('Failed to load attendance data');
      }
    } catch (error) {
      console.error('Error loading attendance data:', error);
      toast.error('Failed to load attendance data');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (records: any[]) => {
    const totalDays = records.length;
    const presentDays = records.filter(r => r.status === 'present' || r.status === 'late').length;
    const absentDays = records.filter(r => r.status === 'absent').length;
    const lateDays = records.filter(r => r.status === 'late').length;
    
    const totalHours = records.reduce((sum, r) => sum + (r.totalHours || 0), 0);
    const averageHours = totalDays > 0 ? totalHours / totalDays : 0;
    
    const attendanceRate = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
    const punctualityRate = presentDays > 0 ? Math.round(((presentDays - lateDays) / presentDays) * 100) : 0;

    setAttendanceStats({
      totalDays,
      presentDays,
      absentDays,
      lateDays,
      averageHours: Math.round(averageHours * 10) / 10,
      attendanceRate,
      punctualityRate
    });
  };

  useEffect(() => {
    loadAttendanceData();
  }, [employeeId, selectedMonth]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      present: { label: 'Present', className: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },
      absent: { label: 'Absent', className: 'bg-red-100 text-red-800 border-red-200', icon: XCircle },
      late: { label: 'Late', className: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: AlertCircle },
      on_leave: { label: 'On Leave', className: 'bg-blue-100 text-blue-800 border-blue-200', icon: Clock }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.absent;
    const Icon = config.icon;
    
    return (
      <Badge className={`${config.className} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span>{config.label}</span>
      </Badge>
    );
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return '-';
    return new Date(timeString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString([], {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const exportToCSV = () => {
    const headers = ['Date', 'Status', 'Check In', 'Check Out', 'Total Hours', 'Location'];
    const csvContent = [
      headers.join(','),
      ...attendanceRecords.map(record => [
        formatDate(record.date),
        record.status,
        formatTime(record.checkInTime),
        formatTime(record.checkOutTime),
        record.totalHours ? `${record.totalHours}h` : '-',
        record.location || '-'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    const monthYear = selectedMonth.toISOString().slice(0, 7);
    link.setAttribute('download', `${employeeName.replace(/\s+/g, '_')}_attendance_${monthYear}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Attendance data exported successfully');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading attendance details...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack} className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Attendance Details</h1>
            <p className="text-gray-600 mt-1">{employeeName} - {selectedMonth.toLocaleDateString([], { month: 'long', year: 'numeric' })}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Select 
            value={`${selectedMonth.getFullYear()}-${selectedMonth.getMonth()}`}
            onValueChange={(value) => {
              const [year, month] = value.split('-').map(Number);
              setSelectedMonth(new Date(year, month, 1));
            }}
          >
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 12 }, (_, i) => {
                const date = new Date();
                date.setMonth(date.getMonth() - i);
                const value = `${date.getFullYear()}-${date.getMonth()}`;
                const label = date.toLocaleDateString([], { month: 'long', year: 'numeric' });
                return (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center space-x-2" onClick={exportToCSV}>
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{attendanceStats.attendanceRate}%</div>
            <p className="text-xs text-gray-600">{attendanceStats.presentDays} of {attendanceStats.totalDays} days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Punctuality Rate</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{attendanceStats.punctualityRate}%</div>
            <p className="text-xs text-gray-600">{attendanceStats.lateDays} late arrivals</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Hours</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{attendanceStats.averageHours}h</div>
            <p className="text-xs text-gray-600">per working day</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Absent Days</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{attendanceStats.absentDays}</div>
            <p className="text-xs text-gray-600">this month</p>
          </CardContent>
        </Card>
      </div>

      {/* Attendance Records */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Attendance Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Check In</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Check Out</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Total Hours</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Location</th>
                </tr>
              </thead>
              <tbody>
                {attendanceRecords.map((record) => (
                  <tr key={record.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4 font-medium text-gray-900">{formatDate(record.date)}</td>
                    <td className="py-3 px-4">{getStatusBadge(record.status)}</td>
                    <td className="py-3 px-4 text-gray-600">{formatTime(record.checkInTime)}</td>
                    <td className="py-3 px-4 text-gray-600">{formatTime(record.checkOutTime)}</td>
                    <td className="py-3 px-4 text-gray-600">
                      {record.totalHours ? `${record.totalHours}h` : '-'}
                    </td>
                    <td className="py-3 px-4 text-gray-600">
                      {record.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{record.location}</span>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeAttendanceDetails;
