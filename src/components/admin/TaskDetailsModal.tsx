import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { 
  User, 
  Calendar, 
  Clock, 
  Target, 
  Activity, 
  CheckSquare, 
  AlertTriangle,
  MessageSquare,
  TrendingUp,
  FileText
} from 'lucide-react';
import { toast } from 'sonner';

interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  progressPercentage: number;
  assignedTo: {
    id: string;
    name: string;
    employeeId?: string;
  };
  assignedBy: {
    id: string;
    name: string;
    employeeId?: string;
  };
  dueDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  createdAt: string;
  category?: string;
}

interface TaskDetailsModalProps {
  task: Task;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStatus: (taskId: string, status: string, comments?: string, progressPercentage?: number) => Promise<void>;
}

const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
  task,
  isOpen,
  onClose,
  onUpdateStatus
}) => {
  const [comments, setComments] = useState('');
  const [newStatus, setNewStatus] = useState(task.status);
  const [newProgress, setNewProgress] = useState(task.progressPercentage.toString());
  const [processing, setProcessing] = useState(false);
  const [showUpdateForm, setShowUpdateForm] = useState(false);

  const handleUpdateStatus = async () => {
    if (newStatus === task.status && parseInt(newProgress) === task.progressPercentage) {
      toast.error('No changes detected');
      return;
    }

    setProcessing(true);
    try {
      await onUpdateStatus(
        task.id, 
        newStatus, 
        comments || undefined, 
        parseInt(newProgress)
      );
      setComments('');
      setShowUpdateForm(false);
    } catch (error) {
      console.error('Error updating task:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', icon: Target },
      medium: { color: 'bg-blue-100 text-blue-800', icon: Target },
      high: { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
      urgent: { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{priority}</span>
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'in-progress': { color: 'bg-blue-100 text-blue-800', icon: Activity },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckSquare },
      cancelled: { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{status.replace('-', ' ')}</span>
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <span>Task Details</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Task Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-semibold text-gray-900">{task.title}</h3>
              <div className="flex space-x-2">
                {getPriorityBadge(task.priority)}
                {getStatusBadge(task.status)}
              </div>
            </div>
            
            {task.description && (
              <p className="text-gray-700 mb-4">{task.description}</p>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Category</p>
                <p className="font-medium">{task.category || 'No category'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Progress</p>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${task.progressPercentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{task.progressPercentage}%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Assignment Information */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Assignment Details</span>
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Assigned To</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-medium text-sm">
                      {task.assignedTo.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{task.assignedTo.name}</p>
                    <p className="text-sm text-gray-600">{task.assignedTo.employeeId}</p>
                  </div>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-600">Assigned By</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <span className="text-gray-600 font-medium text-sm">
                      {task.assignedBy.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{task.assignedBy.name}</p>
                    <p className="text-sm text-gray-600">{task.assignedBy.employeeId}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Timeline Information */}
          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Timeline & Effort</span>
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Created Date</p>
                <p className="font-medium">{formatDateTime(task.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Due Date</p>
                <p className="font-medium">{task.dueDate ? formatDate(task.dueDate) : 'No due date'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Estimated Hours</p>
                <p className="font-medium">{task.estimatedHours || 'Not specified'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Actual Hours</p>
                <p className="font-medium">{task.actualHours || 0}</p>
              </div>
            </div>
          </div>

          {/* Update Task Section */}
          <div className="border-t pt-4">
            {!showUpdateForm ? (
              <div className="flex justify-between items-center">
                <h3 className="font-medium text-gray-900">Update Task Status</h3>
                <Button
                  onClick={() => setShowUpdateForm(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Update Task
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>Update Task Status & Progress</span>
                </h3>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={newStatus} onValueChange={setNewStatus}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-yellow-500" />
                            <span>Pending</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="in-progress">
                          <div className="flex items-center space-x-2">
                            <Activity className="h-4 w-4 text-blue-500" />
                            <span>In Progress</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="completed">
                          <div className="flex items-center space-x-2">
                            <CheckSquare className="h-4 w-4 text-green-500" />
                            <span>Completed</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="cancelled">
                          <div className="flex items-center space-x-2">
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                            <span>Cancelled</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="progress">Progress (%)</Label>
                    <Input
                      id="progress"
                      type="number"
                      min="0"
                      max="100"
                      value={newProgress}
                      onChange={(e) => setNewProgress(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="comments">Comments (Optional)</Label>
                  <Textarea
                    id="comments"
                    placeholder="Add comments about this update..."
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    className="mt-1"
                    rows={3}
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={handleUpdateStatus}
                    disabled={processing}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {processing ? 'Updating...' : 'Update Task'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowUpdateForm(false);
                      setNewStatus(task.status);
                      setNewProgress(task.progressPercentage.toString());
                      setComments('');
                    }}
                    disabled={processing}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TaskDetailsModal;
