import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  X, 
  User, 
  Mail, 
  Key,
  Copy,
  CheckCircle,
  AlertTriangle,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';
import { apiService } from '@/services/api';

interface AdminCredentialsModalProps {
  isOpen: boolean;
  onClose: () => void;
  organization: {
    id: string;
    name: string;
    domain: string;
  } | null;
}

interface AdminCredentials {
  email: string;
  name: string;
  temporaryPassword: string;
  requirePasswordReset: boolean;
  generatedAt: string;
}

const AdminCredentialsModal = ({ isOpen, onClose, organization }: AdminCredentialsModalProps) => {
  const [credentials, setCredentials] = useState<AdminCredentials | null>(null);
  const [loading, setLoading] = useState(false);
  const [resetting, setResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && organization) {
      fetchCredentials();
    }
  }, [isOpen, organization]);

  const fetchCredentials = async () => {
    if (!organization) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getOrganizationAdminCredentials(organization.id);

      if (response.success && response.data) {
        setCredentials(response.data);
      } else {
        setError(response.error?.message || 'Failed to load admin credentials');
      }
    } catch (err) {
      setError('Failed to load admin credentials');
      console.error('Credentials fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!organization) return;

    try {
      setResetting(true);
      setError(null);

      const response = await apiService.resetOrganizationAdminPassword(organization.id);

      if (response.success && response.data) {
        setCredentials(response.data);
        setShowPassword(true); // Show the new password
      } else {
        setError(response.error?.message || 'Failed to reset admin password');
      }
    } catch (err) {
      setError('Failed to reset admin password');
      console.error('Password reset error:', err);
    } finally {
      setResetting(false);
    }
  };

  const handleCopy = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const handleClose = () => {
    setCredentials(null);
    setError(null);
    setShowPassword(false);
    setCopiedField(null);
    onClose();
  };

  if (!isOpen || !organization) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <Key className="h-6 w-6 text-blue-600" />
                <div>
                  <CardTitle className="text-xl">Admin Credentials</CardTitle>
                  <CardDescription>
                    Manage administrator credentials for {organization.name}
                  </CardDescription>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Loading credentials...</p>
                </div>
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            ) : credentials ? (
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <User className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold text-blue-800">Organization Administrator</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    These are the current administrator credentials for {organization.name}.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Admin Name</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <Input value={credentials.name} readOnly className="bg-gray-50" />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopy(credentials.name, 'name')}
                        >
                          {copiedField === 'name' ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Email</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <Input value={credentials.email} readOnly className="bg-gray-50" />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopy(credentials.email, 'email')}
                        >
                          {copiedField === 'email' ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">Password</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <div className="relative flex-1">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          value={credentials.temporaryPassword === '***HIDDEN***' ? '***HIDDEN***' : credentials.temporaryPassword}
                          readOnly
                          className={`pr-10 ${
                            credentials.temporaryPassword === '***HIDDEN***'
                              ? 'bg-gray-100 text-gray-500 border-gray-300'
                              : 'bg-green-50 text-green-800 border-green-300'
                          }`}
                        />
                        {credentials.temporaryPassword !== '***HIDDEN***' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopy(credentials.temporaryPassword, 'password')}
                        disabled={credentials.temporaryPassword === '***HIDDEN***'}
                        title={credentials.temporaryPassword === '***HIDDEN***' ? 'Reset password to copy new credentials' : 'Copy password'}
                      >
                        {copiedField === 'password' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    <p><span className="font-medium">Last Updated:</span> {new Date(credentials.generatedAt).toLocaleString()}</p>
                    <p><span className="font-medium">Password Reset Required:</span> {credentials.requirePasswordReset ? 'Yes' : 'No'}</p>
                  </div>

                  {credentials.temporaryPassword === '***HIDDEN***' && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        <div className="text-sm text-yellow-800">
                          <p className="font-medium mb-1">Password Hidden for Security</p>
                          <p>
                            Existing passwords are not displayed for security reasons.
                            Click "Reset Password" below to generate a new password that can be copied.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between items-center pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={handleResetPassword}
                    disabled={resetting}
                    className="flex items-center space-x-2"
                  >
                    {resetting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                        <span>Resetting...</span>
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4" />
                        <span>Reset Password</span>
                      </>
                    )}
                  </Button>
                  
                  <Button onClick={handleClose}>
                    Close
                  </Button>
                </div>
              </div>
            ) : null}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminCredentialsModal;
