import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  User, 
  Calendar, 
  Clock, 
  FileText, 
  CheckCircle, 
  XCircle, 
  MessageSquare,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface LeaveRequest {
  id: string;
  user: {
    id: string;
    name: string;
    employeeId?: string;
  };
  leaveType: string;
  fromDate: string;
  toDate: string;
  totalDays: number;
  status: 'pending' | 'approved' | 'rejected';
  reason: string;
  appliedDate: string;
  approvedBy?: string;
  approvedAt?: string;
  comments?: string;
}

interface LeaveRequestDetailsModalProps {
  request: LeaveRequest;
  isOpen: boolean;
  onClose: () => void;
  onApproveReject: (requestId: string, status: 'approved' | 'rejected', comments?: string) => Promise<void>;
}

const LeaveRequestDetailsModal: React.FC<LeaveRequestDetailsModalProps> = ({
  request,
  isOpen,
  onClose,
  onApproveReject
}) => {
  const [comments, setComments] = useState('');
  const [processing, setProcessing] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState<'approve' | 'reject' | null>(null);

  const handleApprove = async () => {
    if (showConfirmation !== 'approve') {
      setShowConfirmation('approve');
      return;
    }

    setProcessing(true);
    try {
      await onApproveReject(request.id, 'approved', comments);
      setComments('');
      setShowConfirmation(null);
    } catch (error) {
      console.error('Error approving leave:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (showConfirmation !== 'reject') {
      setShowConfirmation('reject');
      return;
    }

    if (!comments.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    setProcessing(true);
    try {
      await onApproveReject(request.id, 'rejected', comments);
      setComments('');
      setShowConfirmation(null);
    } catch (error) {
      console.error('Error rejecting leave:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <span>Leave Request Details</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Employee Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Employee Information</span>
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-medium">{request.user.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Employee ID</p>
                <p className="font-medium">{request.user.employeeId || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Leave Details */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Leave Details</span>
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Request Type</p>
                <p className="font-medium">Leave Request</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Days</p>
                <p className="font-medium">{request.totalDays} days</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">From Date</p>
                <p className="font-medium">{formatDate(request.fromDate)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">To Date</p>
                <p className="font-medium">{formatDate(request.toDate)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Applied Date</p>
                <p className="font-medium">{formatDateTime(request.appliedDate)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <div className="mt-1">{getStatusBadge(request.status)}</div>
              </div>
            </div>
          </div>

          {/* Reason */}
          <div>
            <h3 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
              <MessageSquare className="h-4 w-4" />
              <span>Reason</span>
            </h3>
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-700">{request.reason}</p>
            </div>
          </div>

          {/* Approval Information */}
          {(request.approvedBy || request.comments) && (
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">Approval Information</h3>
              <div className="grid grid-cols-2 gap-4">
                {request.approvedBy && (
                  <div>
                    <p className="text-sm text-gray-600">Approved/Rejected By</p>
                    <p className="font-medium">{request.approvedBy}</p>
                  </div>
                )}
                {request.approvedAt && (
                  <div>
                    <p className="text-sm text-gray-600">Date</p>
                    <p className="font-medium">{formatDateTime(request.approvedAt)}</p>
                  </div>
                )}
              </div>
              {request.comments && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600">Comments</p>
                  <div className="bg-white rounded p-2 mt-1">
                    <p className="text-gray-700">{request.comments}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action Section for Pending Requests */}
          {request.status === 'pending' && (
            <div className="border-t pt-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="comments">Comments (Optional for approval, Required for rejection)</Label>
                  <Textarea
                    id="comments"
                    placeholder="Add your comments here..."
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    className="mt-1"
                    rows={3}
                  />
                </div>

                {showConfirmation && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium text-yellow-800">
                        Confirm {showConfirmation === 'approve' ? 'Approval' : 'Rejection'}
                      </span>
                    </div>
                    <p className="text-sm text-yellow-700 mb-3">
                      Are you sure you want to {showConfirmation} this leave request? This action cannot be undone.
                    </p>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant={showConfirmation === 'approve' ? 'default' : 'destructive'}
                        onClick={showConfirmation === 'approve' ? handleApprove : handleReject}
                        disabled={processing}
                      >
                        {processing ? 'Processing...' : `Confirm ${showConfirmation === 'approve' ? 'Approval' : 'Rejection'}`}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowConfirmation(null)}
                        disabled={processing}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}

                {!showConfirmation && (
                  <div className="flex space-x-3">
                    <Button
                      className="flex-1 bg-green-600 hover:bg-green-700"
                      onClick={handleApprove}
                      disabled={processing}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                    <Button
                      variant="destructive"
                      className="flex-1"
                      onClick={handleReject}
                      disabled={processing}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LeaveRequestDetailsModal;
