
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Users, Plus, Search, Filter, Calendar, MapPin, DollarSign, Clock } from 'lucide-react';

const RecruitmentPortal = () => {
  const [activeTab, setActiveTab] = useState('jobs');
  const [showJobForm, setShowJobForm] = useState(false);

  const jobOpenings = [
    {
      id: 1,
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      location: 'New York, NY',
      type: 'Full-time',
      salary: '$80,000 - $120,000',
      applications: 25,
      posted: '2024-01-10',
      status: 'Active'
    },
    {
      id: 2,
      title: 'Product Manager',
      department: 'Product',
      location: 'Remote',
      type: 'Full-time',
      salary: '$90,000 - $130,000',
      applications: 18,
      posted: '2024-01-08',
      status: 'Active'
    },
    {
      id: 3,
      title: 'HR Specialist',
      department: 'Human Resources',
      location: 'Chicago, IL',
      type: 'Full-time',
      salary: '$55,000 - $75,000',
      applications: 32,
      posted: '2024-01-05',
      status: 'On Hold'
    }
  ];

  const candidates = [
    {
      id: 1,
      name: 'John Smith',
      position: 'Senior Frontend Developer',
      email: '<EMAIL>',
      phone: '+****************',
      experience: '5+ years',
      status: 'Interview Scheduled',
      stage: 'Technical Interview',
      appliedDate: '2024-01-12'
    },
    {
      id: 2,
      name: 'Emma Davis',
      position: 'Product Manager',
      email: '<EMAIL>',
      phone: '+****************',
      experience: '3+ years',
      status: 'Under Review',
      stage: 'Resume Review',
      appliedDate: '2024-01-11'
    },
    {
      id: 3,
      name: 'Michael Johnson',
      position: 'HR Specialist',
      email: '<EMAIL>',
      phone: '+****************',
      experience: '4+ years',
      status: 'Offer Extended',
      stage: 'Final Decision',
      appliedDate: '2024-01-09'
    }
  ];

  const recruitmentStats = [
    { label: 'Active Job Openings', value: 12, icon: Users, color: 'text-blue-600', bg: 'bg-blue-100' },
    { label: 'Total Applications', value: 156, icon: Search, color: 'text-green-600', bg: 'bg-green-100' },
    { label: 'Interviews Scheduled', value: 24, icon: Calendar, color: 'text-orange-600', bg: 'bg-orange-100' },
    { label: 'Offers Extended', value: 8, icon: Clock, color: 'text-purple-600', bg: 'bg-purple-100' },
  ];

  const recruitmentPipeline = [
    { stage: 'Applied', count: 156, percentage: 100 },
    { stage: 'Phone Screen', count: 89, percentage: 57 },
    { stage: 'Technical Interview', count: 45, percentage: 29 },
    { stage: 'Final Interview', count: 24, percentage: 15 },
    { stage: 'Offer Extended', count: 8, percentage: 5 },
    { stage: 'Hired', count: 5, percentage: 3 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'On Hold': return 'bg-yellow-100 text-yellow-800';
      case 'Closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getCandidateStatusColor = (status: string) => {
    switch (status) {
      case 'Interview Scheduled': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Offer Extended': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Recruitment Portal</h1>
          <p className="text-gray-600 mt-1">Manage job openings, candidates, and hiring processes</p>
        </div>
        <Button 
          onClick={() => setShowJobForm(!showJobForm)}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Post New Job
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {recruitmentStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bg}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Create Job Form */}
      {showJobForm && (
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Post New Job Opening</CardTitle>
          </CardHeader>
          <CardContent>
            <form className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input id="jobTitle" placeholder="Enter job title..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="hr">Human Resources</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" placeholder="e.g., New York, NY" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="jobType">Employment Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-time">Full-time</SelectItem>
                      <SelectItem value="part-time">Part-time</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="internship">Internship</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="salary">Salary Range</Label>
                  <Input id="salary" placeholder="e.g., $80,000 - $120,000" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Job Description</Label>
                <Textarea 
                  id="description" 
                  placeholder="Enter job description, requirements, and responsibilities..."
                  rows={5}
                />
              </div>

              <div className="flex space-x-4">
                <Button type="submit" className="bg-green-600 hover:bg-green-700">
                  Post Job
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowJobForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={activeTab === 'jobs' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('jobs')}
          className="text-sm"
        >
          Job Openings
        </Button>
        <Button
          variant={activeTab === 'candidates' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('candidates')}
          className="text-sm"
        >
          Candidates
        </Button>
        <Button
          variant={activeTab === 'pipeline' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('pipeline')}
          className="text-sm"
        >
          Recruitment Pipeline
        </Button>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'jobs' && (
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Job Openings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Job Title</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Department</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Location</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Salary</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Applications</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {jobOpenings.map((job) => (
                    <tr key={job.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4 text-gray-900 font-medium">{job.title}</td>
                      <td className="py-3 px-4 text-gray-700">{job.department}</td>
                      <td className="py-3 px-4 text-gray-700">
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4 text-gray-500" />
                          <span>{job.location}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{job.type}</td>
                      <td className="py-3 px-4 text-gray-700">
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-4 w-4 text-gray-500" />
                          <span>{job.salary}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-700 font-semibold">{job.applications}</td>
                      <td className="py-3 px-4">
                        <Badge className={getStatusColor(job.status)}>
                          {job.status}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <Button size="sm" variant="outline">
                          View Details
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'candidates' && (
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Candidates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Name</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Position</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Contact</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Experience</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Stage</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Applied</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {candidates.map((candidate) => (
                    <tr key={candidate.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4 text-gray-900 font-medium">{candidate.name}</td>
                      <td className="py-3 px-4 text-gray-700">{candidate.position}</td>
                      <td className="py-3 px-4 text-gray-700">
                        <div className="text-xs">
                          <div>{candidate.email}</div>
                          <div className="text-gray-500">{candidate.phone}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{candidate.experience}</td>
                      <td className="py-3 px-4">
                        <Badge className={getCandidateStatusColor(candidate.status)}>
                          {candidate.status}
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{candidate.stage}</td>
                      <td className="py-3 px-4 text-gray-700">{candidate.appliedDate}</td>
                      <td className="py-3 px-4">
                        <Button size="sm" variant="outline">
                          Review
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'pipeline' && (
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Recruitment Pipeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {recruitmentPipeline.map((stage, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">{stage.stage}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-bold text-gray-900">{stage.count}</span>
                      <span className="text-xs text-gray-500">({stage.percentage}%)</span>
                    </div>
                  </div>
                  <Progress value={stage.percentage} className="h-3" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RecruitmentPortal;
