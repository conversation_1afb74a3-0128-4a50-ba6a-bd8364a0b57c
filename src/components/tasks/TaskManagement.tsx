
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, CheckSquare, Clock, AlertTriangle, User, Calendar, MessageSquare, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';

const TaskManagement = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('my-tasks');
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [selectedTaskForUpdate, setSelectedTaskForUpdate] = useState<string>('');
  const [myTasks, setMyTasks] = useState<Array<{
    id: number;
    title: string;
    description: string;
    priority: string;
    status: string;
    dueDate: string;
    progress: number;
    assignedBy: string;
    category: string;
    estimatedHours: number;
    actualHours: number;
    isSelfCreated?: boolean;
    taskType?: string;
  }>>([]);

  const [dailyUpdates, setDailyUpdates] = useState<Array<{
    id: number;
    date: string;
    task: string;
    update: string;
    progress: number;
    hoursSpent: number;
    blockers: string;
    nextSteps: string;
  }>>([]);

  const [taskComments, setTaskComments] = useState<Array<{
    id: number;
    taskId: number;
    author: string;
    comment: string;
    date: string;
    time: string;
  }>>([]);

  useEffect(() => {
    const loadTaskData = async () => {
      try {
        // Load employee tasks from API
        const tasksRes = await apiService.getEmployeeTasks();
        if (tasksRes.success && tasksRes.data) {
          const tasks = (tasksRes.data.tasks || []).map((t: any, index: number) => ({
            id: t.id || index + 1,
            title: t.title || 'Untitled Task',
            description: t.description || 'No description',
            priority: t.priority || 'Medium',
            status: t.status || 'Pending',
            dueDate: t.dueDate ? new Date(t.dueDate).toISOString().slice(0, 10) : new Date().toISOString().slice(0, 10),
            progress: t.progressPercentage || 0,
            assignedBy: t.assignedBy?.name || 'Unknown',
            category: t.category || 'General',
            estimatedHours: t.estimatedHours || 0,
            actualHours: t.actualHours || 0,
            isSelfCreated: t.isSelfCreated || false,
            taskType: t.taskType || 'Regular'
          }));
          setMyTasks(tasks);

          // Load daily updates from API
          const updatesRes = await apiService.getEmployeeDailyUpdates();
          if (updatesRes.success && updatesRes.data) {
            const updates = (updatesRes.data.updates || []).map((u: any, index: number) => ({
              id: u.id || index + 1,
              date: u.updateDate ? new Date(u.updateDate).toISOString().slice(0, 10) : new Date().toISOString().slice(0, 10),
              task: u.taskTitle || 'Unknown Task',
              update: u.updateNotes || 'No update notes',
              progress: u.progressPercentage || 0,
              hoursSpent: u.hoursSpent || 0,
              blockers: u.blockers || 'None',
              nextSteps: u.nextSteps || 'Continue with current work'
            }));
            setDailyUpdates(updates);
          }

          // Mock comments for now (can be enhanced later)
          const comments = tasks.slice(0, 2).map((task: any, index: number) => ({
            id: index + 1,
            taskId: task.id,
            author: task.assignedBy,
            comment: `Please update progress on ${task.title}`,
            date: new Date().toISOString().slice(0, 10),
            time: '10:30 AM'
          }));
          setTaskComments(comments);
        } else {
          toast.error('Failed to load tasks');
        }
      } catch (error) {
        console.error('Error loading task data:', error);
        toast.error('Failed to load task data');
      }
    };

    loadTaskData();
  }, []);

  const handleCreateTask = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);

    try {
      const taskData = {
        title: formData.get('title') as string,
        description: formData.get('description') as string,
        priority: formData.get('priority') as string || 'medium',
        category: formData.get('category') as string,
        estimatedHours: formData.get('estimatedHours') ? parseFloat(formData.get('estimatedHours') as string) : undefined,
        dueDate: formData.get('dueDate') as string,
        taskType: 'SelfCreated'
      };

      const response = await apiService.createSelfTask(taskData);

      if (response.success) {
        toast.success('Self task created successfully!');
        setShowCreateForm(false);
        // Reload tasks
        window.location.reload();
      } else {
        toast.error(response.error?.message || 'Failed to create task');
      }
    } catch (error) {
      console.error('Error creating task:', error);
      toast.error('Failed to create task');
    }
  };

  const handleDailyUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);

    if (!selectedTaskForUpdate) {
      toast.error('Please select a task');
      return;
    }

    try {
      const updateData = {
        taskId: selectedTaskForUpdate,
        updateNotes: formData.get('updateNotes') as string,
        progressPercentage: formData.get('progressPercentage') ? parseInt(formData.get('progressPercentage') as string) : undefined,
        hoursSpent: formData.get('hoursSpent') ? parseFloat(formData.get('hoursSpent') as string) : undefined,
        blockers: formData.get('blockers') as string,
        nextSteps: formData.get('nextSteps') as string,
        updateType: 'Daily'
      };

      const response = await apiService.createDailyUpdate(updateData);

      if (response.success) {
        toast.success('Daily update submitted successfully!');
        setShowUpdateDialog(false);
        setSelectedTaskForUpdate('');
        // Reload data
        window.location.reload();
      } else {
        toast.error(response.error?.message || 'Failed to submit daily update');
      }
    } catch (error) {
      console.error('Error submitting daily update:', error);
      toast.error('Failed to submit daily update');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-orange-100 text-orange-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Task Management</h1>
          <p className="text-gray-600 mt-1">Manage your tasks and track daily progress updates</p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Create Task
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Task</DialogTitle>
                <DialogDescription>Create a personal task to track your work</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateTask} className="space-y-4">
                <div className="space-y-2">
                  <Label>Task Title</Label>
                  <Input name="title" placeholder="Enter task title" required />
                </div>

                <div className="space-y-2">
                  <Label>Description</Label>
                  <Textarea name="description" placeholder="Describe the task" rows={3} />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Priority</Label>
                    <Select name="priority" defaultValue="medium">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Input name="category" placeholder="e.g., Development" />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Estimated Hours</Label>
                    <Input name="estimatedHours" type="number" placeholder="0" step="0.5" min="0" />
                  </div>
                  <div className="space-y-2">
                    <Label>Due Date</Label>
                    <Input name="dueDate" type="date" />
                  </div>
                </div>

                <div className="flex space-x-2 pt-4">
                  <Button type="submit" className="flex-1">Create Task</Button>
                  <Button type="button" variant="outline" className="flex-1" onClick={() => setShowCreateForm(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={showUpdateDialog} onOpenChange={setShowUpdateDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <MessageSquare className="h-4 w-4 mr-2" />
                Daily Update
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Submit Daily Update</DialogTitle>
                <DialogDescription>Share your progress and blockers</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleDailyUpdate} className="space-y-4">
                <div className="space-y-2">
                  <Label>Select Task</Label>
                  <Select value={selectedTaskForUpdate} onValueChange={setSelectedTaskForUpdate} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose task" />
                    </SelectTrigger>
                    <SelectContent>
                      {myTasks.filter(t => t.status !== 'Completed').map(task => (
                        <SelectItem key={task.id} value={task.id.toString()}>{task.title}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Progress Update</Label>
                  <Textarea name="updateNotes" placeholder="What did you work on today?" rows={3} required />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Progress %</Label>
                    <Input name="progressPercentage" type="number" placeholder="0-100" min="0" max="100" />
                  </div>
                  <div className="space-y-2">
                    <Label>Hours Spent</Label>
                    <Input name="hoursSpent" type="number" placeholder="0.5" step="0.5" min="0" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Blockers (if any)</Label>
                  <Textarea name="blockers" placeholder="Any obstacles or issues?" rows={2} />
                </div>

                <div className="space-y-2">
                  <Label>Next Steps</Label>
                  <Textarea name="nextSteps" placeholder="What will you work on next?" rows={2} />
                </div>

                <div className="flex space-x-2 pt-4">
                  <Button type="submit" className="flex-1">Submit Update</Button>
                  <Button type="button" variant="outline" className="flex-1" onClick={() => setShowUpdateDialog(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>



      {/* Task Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckSquare className="h-8 w-8 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {myTasks.filter(t => t.status === 'Completed').length}
            </div>
            <div className="text-sm text-gray-600">Completed Tasks</div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {myTasks.filter(t => t.status === 'In Progress').length}
            </div>
            <div className="text-sm text-gray-600">In Progress</div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {myTasks.filter(t => t.status === 'Pending').length}
            </div>
            <div className="text-sm text-gray-600">Pending</div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(myTasks.reduce((acc, t) => acc + t.progress, 0) / myTasks.length)}%
            </div>
            <div className="text-sm text-gray-600">Average Progress</div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="my-tasks">My Tasks</TabsTrigger>
          <TabsTrigger value="daily-updates">Daily Updates</TabsTrigger>
          <TabsTrigger value="comments">Comments & Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="my-tasks">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {myTasks.map((task) => (
              <Card key={task.id} className="border-0 shadow-lg bg-white/50 backdrop-blur-sm hover:shadow-xl transition-shadow">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <h3 className="text-lg font-semibold text-gray-900">{task.title}</h3>
                      <div className="flex space-x-2">
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority}
                        </Badge>
                        <Badge className={getStatusColor(task.status)}>
                          {task.status}
                        </Badge>
                        {task.isSelfCreated && (
                          <Badge className="bg-purple-100 text-purple-800">
                            Self-Created
                          </Badge>
                        )}
                        <Badge className="bg-gray-100 text-gray-800">
                          {task.taskType}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 text-sm">{task.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Category:</span>
                        <span className="ml-1 font-medium">{task.category}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Due:</span>
                        <span className="ml-1 font-medium">{task.dueDate}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span className="font-medium">{task.progress}%</span>
                      </div>
                      <Progress value={task.progress} className="h-2" />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Estimated:</span>
                        <span className="ml-1 font-medium">{task.estimatedHours}h</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Actual:</span>
                        <span className="ml-1 font-medium">{task.actualHours}h</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center text-sm text-gray-600">
                      <span>Assigned by: {task.assignedBy}</span>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => {
                          setSelectedTask(task);
                          setShowUpdateDialog(true);
                        }}
                      >
                        Update Progress
                      </Button>
                      <Button size="sm" variant="outline">
                        Add Comment
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="daily-updates">
          <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Daily Progress Updates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {dailyUpdates.map((update) => (
                  <div key={update.id} className="border-l-4 border-blue-500 pl-4 pb-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold text-gray-900">{update.task}</h4>
                        <p className="text-sm text-gray-600 flex items-center space-x-4">
                          <span><Calendar className="h-3 w-3 inline mr-1" />{update.date}</span>
                          <span><Clock className="h-3 w-3 inline mr-1" />{update.hoursSpent}h spent</span>
                        </p>
                      </div>
                      <Badge variant="outline">{update.progress}%</Badge>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Progress Update:</p>
                        <p className="text-gray-700">{update.update}</p>
                      </div>
                      {update.blockers && (
                        <div>
                          <p className="text-sm font-medium text-red-700">Blockers:</p>
                          <p className="text-red-600">{update.blockers}</p>
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium text-green-700">Next Steps:</p>
                        <p className="text-green-600">{update.nextSteps}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comments">
          <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Comments & Feedback</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {taskComments.map((comment) => (
                  <div key={comment.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                          {comment.author.split(' ').map(n => n[0]).join('')}
                        </div>
                        <span className="font-medium text-sm">{comment.author}</span>
                      </div>
                      <div className="text-right text-xs text-gray-500">
                        <p>{comment.date}</p>
                        <p>{comment.time}</p>
                      </div>
                    </div>
                    <p className="text-gray-700 ml-8">{comment.comment}</p>
                    <p className="text-xs text-gray-500 mt-2 ml-8">
                      Task: {myTasks.find(t => t.id === comment.taskId)?.title}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TaskManagement;
