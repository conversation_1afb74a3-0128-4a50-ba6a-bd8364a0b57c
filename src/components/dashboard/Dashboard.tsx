
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Users,
  Calendar,
  Clock,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Star,
  Target,
  ArrowRight
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Welcome to HRMS</h1>
          <p className="text-gray-600 mt-1">Your Human Resource Management System</p>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-gray-500" />
          <span className="text-sm text-gray-600">{new Date().toLocaleString()}</span>
        </div>
      </div>

      {/* Role-based Dashboard Redirect */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            <span>Dashboard Access</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="mb-6">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {user?.name ? `Hello, ${user.name}!` : 'Welcome!'}
              </h3>
              <p className="text-gray-600 mb-4">
                {user?.role
                  ? `You are logged in as: ${user.role.replace('_', ' ').toUpperCase()}`
                  : 'Please ensure you have the correct role assigned to access your dashboard.'
                }
              </p>
              {user?.organization && (
                <p className="text-sm text-gray-500 mb-6">
                  Organization: {user.organization.name}
                </p>
              )}
            </div>

            <div className="space-y-4">
              <p className="text-gray-600">
                Your role-specific dashboard should load automatically. If you're seeing this page,
                please contact your administrator or try refreshing the page.
              </p>

              <Button
                onClick={() => window.location.reload()}
                className="flex items-center space-x-2"
              >
                <TrendingUp className="h-4 w-4" />
                <span>Refresh Dashboard</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span>System Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900">System Online</h3>
              <p className="text-sm text-gray-600">All services operational</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-3">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900">User Management</h3>
              <p className="text-sm text-gray-600">Role-based access active</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-3">
                <Star className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900">HRMS Platform</h3>
              <p className="text-sm text-gray-600">Version 1.0.0</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
