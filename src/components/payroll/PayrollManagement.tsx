
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { DollarSign, Download, Calendar, TrendingUp, CreditCard, FileText } from 'lucide-react';
import { useEffect, useState } from 'react';
import apiService from '@/services/api';
import { toast } from 'sonner';

const PayrollManagement = () => {
  const [payrollSummary, setPayrollSummary] = useState({
    grossPay: 0,
    netPay: 0,
    totalDeductions: 0,
    taxDeductions: 0,
    benefits: 0,
    ytdGross: 0,
    ytdNet: 0,
    ytdTax: 0,
  });

  const [payStubs, setPayStubs] = useState<Array<{ period: string; payDate: string; gross: number; net: number; status: string }>>([]);

  const [deductions, setDeductions] = useState<Array<{ category: string; amount: number; percentage: number }>>([]);

  const [benefits, setBenefits] = useState<Array<{ id: string; name: string; employeeContribution: number; employerContribution: number; status: string }>>([]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  useEffect(() => {
    const load = async () => {
      const [payrollRes, benefitsRes] = await Promise.all([
        apiService.getEmployeePayroll(),
        apiService.getEmployeeBenefits(),
      ]);

      if (payrollRes.success && payrollRes.data) {
        const cm = payrollRes.data.currentMonth;
        const ytd = payrollRes.data.ytdSummary;
        setPayrollSummary({
          grossPay: Number(cm.grossSalary || 0),
          netPay: Number(cm.netSalary || 0),
          totalDeductions: Number(cm.totalDeductions || 0),
          taxDeductions: Number(cm.taxDeductions || 0),
          benefits: Number(cm.benefits || 0),
          ytdGross: Number(ytd.ytdGross || 0),
          ytdNet: Number(ytd.ytdNet || 0),
          ytdTax: Number(ytd.ytdTax || 0),
        });
        setPayStubs((payrollRes.data.payStubs || []).map((p: any) => ({
          period: p.period,
          payDate: p.payDate,
          gross: Number(p.gross || 0),
          net: Number(p.net || 0),
          status: (p.status || '').toString().toUpperCase(),
        })));
        // Deductions are not directly in DTO; infer from totals for now
        setDeductions([
          { category: 'Tax Deductions', amount: Number(cm.taxDeductions || 0), percentage: 0 },
        ]);
      } else if (!payrollRes.success) {
        toast.error(payrollRes.error?.message || 'Failed to load payroll');
      }

      if (benefitsRes.success && benefitsRes.data) {
        setBenefits((benefitsRes.data.benefits || []).map((b: any) => ({
          id: b.id,
          name: b.name,
          employeeContribution: Number(b.employeeContribution || 0),
          employerContribution: Number(b.employerContribution || 0),
          status: (b.status || '').toString(),
        })));
      } else if (!benefitsRes.success) {
        toast.error(benefitsRes.error?.message || 'Failed to load benefits');
      }
    };

    load();
  }, []);


  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Payroll Management</h1>
        <p className="text-muted-foreground mt-1">View your salary, benefits, and payroll information</p>
      </div>

      {/* Current Pay Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border border-border bg-primary text-primary-foreground">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm opacity-90">Gross Pay</p>
                <p className="text-2xl font-bold">{formatCurrency(payrollSummary.grossPay)}</p>
                <p className="text-xs opacity-80">This Month</p>
              </div>
              <DollarSign className="h-8 w-8 opacity-80" />
            </div>
          </CardContent>
        </Card>

        <Card className="border border-border bg-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Net Pay</p>
                <p className="text-2xl font-bold text-foreground">{formatCurrency(payrollSummary.netPay)}</p>
                <p className="text-xs text-muted-foreground">Take Home</p>
              </div>
              <CreditCard className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="border border-border bg-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Deductions</p>
                <p className="text-2xl font-bold text-foreground">{formatCurrency(payrollSummary.totalDeductions)}</p>
                <p className="text-xs text-muted-foreground">Total Deducted</p>
              </div>
              <FileText className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="border border-border bg-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">YTD Gross</p>
                <p className="text-2xl font-bold text-foreground">{formatCurrency(payrollSummary.ytdGross)}</p>
                <p className="text-xs text-muted-foreground">Year to Date</p>
              </div>
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Pay Stubs */}
        <Card className="border border-border bg-card">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Recent Pay Stubs</span>
              </span>
              <Button size="sm" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-primary-foreground">
                <Download className="h-4 w-4 mr-2" />
                Download All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {payStubs.map((stub, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                  <div>
                    <h4 className="font-semibold text-foreground">{stub.period}</h4>
                    <p className="text-sm text-muted-foreground">Pay Date: {stub.payDate}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-muted-foreground">Gross: {formatCurrency(stub.gross)}</span>
                      <span className="text-xs text-muted-foreground">Net: {formatCurrency(stub.net)}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-primary/10 text-primary border-primary">{stub.status}</Badge>
                    <Button size="sm" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-primary-foreground">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Deductions Breakdown */}
        <Card className="border border-border bg-card">
          <CardHeader>
            <CardTitle>Deductions Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {deductions.map((deduction, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-foreground">{deduction.category}</span>
                    <div className="text-right">
                      <div className="text-sm font-bold text-foreground">{formatCurrency(deduction.amount)}</div>
                      <div className="text-xs text-muted-foreground">{deduction.percentage}%</div>
                    </div>
                  </div>
                  <Progress value={deduction.percentage * 5} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Benefits Overview */}
      <Card className="border border-border bg-card">
        <CardHeader>
          <CardTitle>Benefits Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Benefit</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Employee Contribution</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Employer Contribution</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Total Value</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Status</th>
                </tr>
              </thead>
              <tbody>
                {benefits.map((benefit, index) => (
                  <tr key={index} className="border-b border-border hover:bg-muted/50 transition-colors">
                    <td className="py-3 px-4 text-foreground font-medium">{benefit.name}</td>
                    <td className="py-3 px-4 text-foreground">{formatCurrency(benefit.employeeContribution)}</td>
                    <td className="py-3 px-4 text-foreground">{formatCurrency(benefit.employerContribution)}</td>
                    <td className="py-3 px-4 text-foreground font-semibold">
                      {formatCurrency(benefit.employeeContribution + benefit.employerContribution)}
                    </td>
                    <td className="py-3 px-4">
                      <Badge className="bg-primary/10 text-primary border-primary">{benefit.status}</Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Year-to-Date Summary */}
      <Card className="border border-border bg-card">
        <CardHeader>
          <CardTitle>Year-to-Date Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">{formatCurrency(payrollSummary.ytdGross)}</div>
              <div className="text-sm text-muted-foreground">YTD Gross Income</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">{formatCurrency(payrollSummary.ytdNet)}</div>
              <div className="text-sm text-muted-foreground">YTD Net Income</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">{formatCurrency(payrollSummary.ytdTax)}</div>
              <div className="text-sm text-muted-foreground">YTD Tax Paid</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PayrollManagement;
