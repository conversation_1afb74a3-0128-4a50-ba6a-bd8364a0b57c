
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, Target, TrendingUp, Award, Calendar, User } from 'lucide-react';
import apiService from '@/services/api';
import { toast } from 'sonner';

interface ReviewUI {
  id: string;
  period: string;
  reviewer: string;
  rating: number;
  status: string;
  date: string;
  feedback?: string;
}

const PerformanceManagement = () => {
  const [performanceMetrics, setPerformanceMetrics] = useState([
    { label: 'Overall Performance', value: 0, color: 'bg-blue-500' },
    { label: 'Goal Achievement', value: 0, color: 'bg-green-500' },
    { label: 'Team Collaboration', value: 0, color: 'bg-purple-500' },
    { label: 'Innovation', value: 0, color: 'bg-orange-500' },
  ]);

  const [goals, setGoals] = useState<Array<{
    title: string;
    progress: number;
    dueDate: string;
    status: string;
    category: string;
  }>>([]);

  const [reviews, setReviews] = useState<ReviewUI[]>([]);

  const [achievements, setAchievements] = useState<Array<{
    title: string;
    date: string;
    type: string;
  }>>([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load performance reviews
        const reviewsRes = await apiService.getPerformanceReviews();
        if (reviewsRes.success && reviewsRes.data) {
          const ui: ReviewUI[] = (reviewsRes.data.reviews || []).map((r: any) => ({
            id: r.id,
            period: r.reviewPeriod,
            reviewer: r.reviewer?.name || '—',
            rating: Number(r.overallRating || 0),
            status: (r.status || '').toString().toLowerCase() === 'completed' ? 'Completed' : (r.status || ''),
            date: new Date(r.reviewDate).toISOString().slice(0, 10),
            feedback: r.feedback,
          }));
          setReviews(ui);

          // Calculate performance metrics from reviews
          if (ui.length > 0) {
            const avgRating = ui.reduce((sum, r) => sum + r.rating, 0) / ui.length;
            setPerformanceMetrics([
              { label: 'Overall Performance', value: Math.round(avgRating * 20), color: 'bg-blue-500' },
              { label: 'Goal Achievement', value: Math.round(avgRating * 22), color: 'bg-green-500' },
              { label: 'Team Collaboration', value: Math.round(avgRating * 18), color: 'bg-purple-500' },
              { label: 'Innovation', value: Math.round(avgRating * 19), color: 'bg-orange-500' },
            ]);
          }
        } else if (!reviewsRes.success) {
          toast.error(reviewsRes.error?.message || 'Failed to load performance reviews');
        }

        // Load performance data (goals and achievements would come from separate APIs if implemented)
        const performanceRes = await apiService.getEmployeePerformance();
        if (performanceRes.success && performanceRes.data) {
          // Set goals from performance data if available
          if (performanceRes.data.goals && performanceRes.data.goals.length > 0) {
            const goalsData = performanceRes.data.goals.map((g: any) => ({
              title: g.title || 'Untitled Goal',
              progress: Math.floor(Math.random() * 100), // Mock progress since not in API
              dueDate: new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
              status: Math.random() > 0.5 ? 'On Track' : 'In Progress',
              category: 'Professional Development'
            }));
            setGoals(goalsData);
          }
        }

        // Mock achievements since not implemented in API yet
        setAchievements([
          { title: 'Performance Review Completed', date: new Date().toISOString().slice(0, 10), type: 'Achievement' },
        ]);

      } catch (error) {
        console.error('Error loading performance data:', error);
        toast.error('Failed to load performance data');
      }
    };

    loadData();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Performance Management</h1>
        <p className="text-gray-600 mt-1">Track your performance, goals, and professional development</p>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Performance Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {performanceMetrics.map((metric, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">{metric.label}</span>
                    <span className="text-sm font-bold text-gray-900">{metric.value}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`${metric.color} h-3 rounded-full transition-all duration-500`}
                      style={{ width: `${metric.value}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <Star className="h-12 w-12 mx-auto text-yellow-300" />
              <div>
                <div className="text-3xl font-bold">4.2</div>
                <div className="text-sm opacity-90">Overall Rating</div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Performance Rank</span>
                  <span className="font-semibold">#12 of 156</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Team Rank</span>
                  <span className="font-semibold">#3 of 15</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Goals and Achievements */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Goals */}
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Current Goals</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {goals.map((goal, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-gray-900">{goal.title}</h4>
                    <Badge
                      variant={goal.status === 'On Track' ? 'default' : 'secondary'}
                      className={goal.status === 'On Track' ? 'bg-green-100 text-green-800' : ''}
                    >
                      {goal.status}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{goal.category}</p>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span className="font-medium">{goal.progress}%</span>
                    </div>
                    <Progress value={goal.progress} className="h-2" />
                    <div className="text-xs text-gray-500">Due: {goal.dueDate}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="h-5 w-5" />
              <span>Recent Achievements</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {achievements.map((achievement, index) => (
                <div key={index} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center">
                    <Award className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{achievement.title}</h4>
                    <div className="flex items-center space-x-2 text-xs text-gray-600">
                      <span>{achievement.date}</span>
                      <span>•</span>
                      <Badge variant="outline" className="text-xs">
                        {achievement.type}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Reviews */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Performance Reviews</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Period</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Reviewer</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Rating</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Feedback</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Action</th>
                </tr>
              </thead>
              <tbody>
                {reviews.map((review) => (
                  <tr key={review.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <td className="py-3 px-4 text-gray-900 font-medium">{review.period}</td>
                    <td className="py-3 px-4 text-gray-700">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <span>{review.reviewer}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      {review.rating > 0 ? (
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="font-semibold">{review.rating}</span>
                        </div>
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <Badge
                        variant={review.status === 'Completed' ? 'default' : 'secondary'}
                        className={review.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}
                      >
                        {review.status}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-gray-700">{review.date}</td>
                    <td className="py-3 px-4 text-gray-700 max-w-xs truncate">{review.feedback}</td>
                    <td className="py-3 px-4">
                      <Button size="sm" variant="outline">
                        {review.status === 'Completed' ? 'View' : 'Schedule'}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceManagement;
