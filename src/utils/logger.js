/**
 * Logger Utility
 * Provides structured logging for the HRMS application
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

class Logger {
  constructor() {
    this.logger = null;
    this.initialize();
  }

  initialize() {
    // Ensure log directory exists
    const logDir = process.env.LOG_DIR || path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Define log format
    const logFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
      }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        return JSON.stringify({
          timestamp,
          level,
          message,
          ...meta
        });
      })
    );

    // Create logger instance
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      defaultMeta: {
        service: 'hrms-provisioning',
        environment: process.env.NODE_ENV || 'development'
      },
      transports: [
        // Console transport
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
              const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
              return `${timestamp} [${level}]: ${message} ${metaStr}`;
            })
          )
        }),

        // File transport for all logs
        new winston.transports.File({
          filename: path.join(logDir, 'application.log'),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 10,
          tailable: true
        }),

        // File transport for errors only
        new winston.transports.File({
          filename: path.join(logDir, 'error.log'),
          level: 'error',
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true
        }),

        // File transport for database provisioning
        new winston.transports.File({
          filename: path.join(logDir, 'database-provisioning.log'),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 10,
          tailable: true,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      ],

      // Handle uncaught exceptions
      exceptionHandlers: [
        new winston.transports.File({
          filename: path.join(logDir, 'exceptions.log')
        })
      ],

      // Handle unhandled promise rejections
      rejectionHandlers: [
        new winston.transports.File({
          filename: path.join(logDir, 'rejections.log')
        })
      ]
    });

    // Add request ID to logs if available
    this.logger.add(new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, requestId, ...meta }) => {
          const reqId = requestId ? `[${requestId}] ` : '';
          const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
          return `${timestamp} [${level.toUpperCase()}] ${reqId}${message}${metaStr}`;
        })
      )
    }));
  }

  // Log methods with context
  debug(message, meta = {}) {
    this.logger.debug(message, this.addContext(meta));
  }

  info(message, meta = {}) {
    this.logger.info(message, this.addContext(meta));
  }

  warn(message, meta = {}) {
    this.logger.warn(message, this.addContext(meta));
  }

  error(message, meta = {}) {
    // Handle Error objects
    if (meta instanceof Error) {
      meta = {
        error: {
          message: meta.message,
          stack: meta.stack,
          name: meta.name
        }
      };
    }
    this.logger.error(message, this.addContext(meta));
  }

  // Specialized logging methods for database provisioning
  provisioningStart(organizationId, schemaName, meta = {}) {
    this.info('Database provisioning started', {
      ...meta,
      organizationId,
      schemaName,
      event: 'provisioning_start'
    });
  }

  provisioningSuccess(organizationId, schemaName, duration, meta = {}) {
    this.info('Database provisioning completed successfully', {
      ...meta,
      organizationId,
      schemaName,
      duration,
      event: 'provisioning_success'
    });
  }

  provisioningError(organizationId, schemaName, error, meta = {}) {
    this.error('Database provisioning failed', {
      ...meta,
      organizationId,
      schemaName,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      event: 'provisioning_error'
    });
  }

  migrationExecuted(fileName, schemaName, duration, meta = {}) {
    this.debug('Migration file executed', {
      ...meta,
      fileName,
      schemaName,
      duration,
      event: 'migration_executed'
    });
  }

  seedExecuted(fileName, schemaName, duration, meta = {}) {
    this.debug('Seed file executed', {
      ...meta,
      fileName,
      schemaName,
      duration,
      event: 'seed_executed'
    });
  }

  // Performance logging
  performance(operation, duration, meta = {}) {
    this.info('Performance metric', {
      ...meta,
      operation,
      duration,
      event: 'performance'
    });
  }

  // Security logging
  securityEvent(event, userId, organizationId, meta = {}) {
    this.warn('Security event', {
      ...meta,
      event,
      userId,
      organizationId,
      category: 'security'
    });
  }

  // API request logging
  apiRequest(method, url, statusCode, duration, userId, meta = {}) {
    this.info('API request', {
      ...meta,
      method,
      url,
      statusCode,
      duration,
      userId,
      event: 'api_request'
    });
  }

  // Database operation logging
  dbOperation(operation, table, duration, meta = {}) {
    this.debug('Database operation', {
      ...meta,
      operation,
      table,
      duration,
      event: 'db_operation'
    });
  }

  // Add context information to log entries
  addContext(meta = {}) {
    return {
      ...meta,
      timestamp: new Date().toISOString(),
      pid: process.pid,
      hostname: require('os').hostname(),
      version: process.env.npm_package_version || '1.0.0'
    };
  }

  // Create child logger with additional context
  child(context = {}) {
    return {
      debug: (message, meta = {}) => this.debug(message, { ...context, ...meta }),
      info: (message, meta = {}) => this.info(message, { ...context, ...meta }),
      warn: (message, meta = {}) => this.warn(message, { ...context, ...meta }),
      error: (message, meta = {}) => this.error(message, { ...context, ...meta }),
      
      // Specialized methods
      provisioningStart: (orgId, schema, meta = {}) => this.provisioningStart(orgId, schema, { ...context, ...meta }),
      provisioningSuccess: (orgId, schema, duration, meta = {}) => this.provisioningSuccess(orgId, schema, duration, { ...context, ...meta }),
      provisioningError: (orgId, schema, error, meta = {}) => this.provisioningError(orgId, schema, error, { ...context, ...meta }),
      migrationExecuted: (file, schema, duration, meta = {}) => this.migrationExecuted(file, schema, duration, { ...context, ...meta }),
      seedExecuted: (file, schema, duration, meta = {}) => this.seedExecuted(file, schema, duration, { ...context, ...meta }),
      performance: (operation, duration, meta = {}) => this.performance(operation, duration, { ...context, ...meta }),
      securityEvent: (event, userId, orgId, meta = {}) => this.securityEvent(event, userId, orgId, { ...context, ...meta }),
      apiRequest: (method, url, status, duration, userId, meta = {}) => this.apiRequest(method, url, status, duration, userId, { ...context, ...meta }),
      dbOperation: (operation, table, duration, meta = {}) => this.dbOperation(operation, table, duration, { ...context, ...meta })
    };
  }

  // Get logger instance for direct access
  getLogger() {
    return this.logger;
  }

  // Graceful shutdown
  async close() {
    return new Promise((resolve) => {
      this.logger.end(() => {
        resolve();
      });
    });
  }
}

// Create singleton instance
const loggerInstance = new Logger();

module.exports = loggerInstance;
