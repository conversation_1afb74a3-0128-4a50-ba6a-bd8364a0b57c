import * as React from "react"

const MO<PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

// Enhanced mobile detection with multiple methods
export function useEnhancedMobileDetection() {
  const [deviceInfo, setDeviceInfo] = React.useState({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    userAgent: '',
    screenWidth: 0,
    screenHeight: 0,
    touchSupport: false,
    deviceType: 'desktop' as 'mobile' | 'tablet' | 'desktop'
  })

  React.useEffect(() => {
    const detectDevice = () => {
      const userAgent = navigator.userAgent || ''
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight
      const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0

      // User agent based detection
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|phone/i
      const tabletRegex = /iPad|Android.*Tablet|Kindle|Silk|PlayBook|BB10.*Touch|RIM.*Tablet|Tab.*Build|Nexus [0-9]|Galaxy Tab/i

      const isMobileUA = mobileRegex.test(userAgent) && !tabletRegex.test(userAgent)
      const isTabletUA = tabletRegex.test(userAgent)

      // Screen size based detection
      const isMobileScreen = screenWidth < MOBILE_BREAKPOINT
      const isTabletScreen = screenWidth >= MOBILE_BREAKPOINT && screenWidth < 1024

      // Combined detection
      const isMobile = isMobileUA || (isMobileScreen && touchSupport)
      const isTablet = isTabletUA || (isTabletScreen && touchSupport && !isMobileUA)
      const isDesktop = !isMobile && !isTablet

      let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop'
      if (isMobile) deviceType = 'mobile'
      else if (isTablet) deviceType = 'tablet'

      setDeviceInfo({
        isMobile,
        isTablet,
        isDesktop,
        userAgent,
        screenWidth,
        screenHeight,
        touchSupport,
        deviceType
      })
    }

    // Initial detection
    detectDevice()

    // Listen for resize events
    const handleResize = () => detectDevice()
    window.addEventListener('resize', handleResize)

    // Listen for orientation changes
    const handleOrientationChange = () => {
      setTimeout(detectDevice, 100) // Small delay to ensure dimensions are updated
    }
    window.addEventListener('orientationchange', handleOrientationChange)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [])

  return deviceInfo
}
