
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '@/services/api';

export type UserRole = 'super_admin' | 'org_admin' | 'employee';

export interface Organization {
  id: string;
  name: string;
  domain: string;
  industry: string;
  employeeCount: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  organizationId?: string;
  organization?: Organization;
  avatar?: string;
  employeeDetails?: {
    employeeId: string;
    jobTitle: string;
    department: string;
    manager: string;
    joinDate: string;
    employmentType: string;
    employmentStatus: string;
    workLocation: string;
    phone: string;
    address: string;
    dateOfBirth: string;
    baseSalary: string;
    annualCTC: string;
    nextSalaryReview: string;
    education: Array<{
      degree: string;
      institution: string;
      year: string;
    }>;
    skills: Array<{
      name: string;
      level: number;
    }>;
    performanceRating: number;
    totalExperience: string;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);







export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await apiService.login({ email, password });

      // Handle mobile blocking error specifically
      if (!response.success && response.error?.code === 'MOBILE_ACCESS_BLOCKED') {
        console.warn('Login blocked due to mobile device detection:', response.error);
        // Don't set user state, let the mobile detection wrapper handle the UI
        return false;
      }

      if (response.success && response.data) {
        // Map backend role to frontend role format
        const mapRole = (backendRole: string): 'super_admin' | 'org_admin' | 'employee' => {
          switch (backendRole.toLowerCase()) {
            case 'superadmin': return 'super_admin';
            case 'orgadmin': return 'org_admin';
            case 'employee': return 'employee';
            default: return 'employee';
          }
        };

        const userData: User = {
          id: response.data.user.id,
          email: response.data.user.email,
          name: response.data.user.name,
          role: mapRole(response.data.user.role),
          organizationId: response.data.user.organization?.id,
          organization: response.data.user.organization ? {
            id: response.data.user.organization.id,
            name: response.data.user.organization.name,
            domain: response.data.user.organization.domain,
            industry: response.data.user.organization.industry,
            employeeCount: response.data.user.organization.employeeCount
          } : undefined,
          employeeDetails: response.data.user.employeeDetails ? {
            employeeId: response.data.user.employeeDetails.employeeId || '',
            jobTitle: response.data.user.employeeDetails.jobTitle || '',
            department: response.data.user.employeeDetails.department || '',
            manager: response.data.user.employeeDetails.manager || '',
            joinDate: response.data.user.employeeDetails.joinDate || '',
            employmentType: response.data.user.employeeDetails.employmentType || '',
            employmentStatus: response.data.user.employeeDetails.employmentStatus || '',
            workLocation: response.data.user.employeeDetails.workLocation || '',
            phone: response.data.user.employeeDetails.phone || '',
            address: response.data.user.employeeDetails.address || '',
            dateOfBirth: response.data.user.employeeDetails.dateOfBirth || '',
            baseSalary: response.data.user.employeeDetails.baseSalary || '',
            annualCTC: response.data.user.employeeDetails.annualCTC || '',
            nextSalaryReview: response.data.user.employeeDetails.nextSalaryReview || '',
            education: response.data.user.employeeDetails.education || [],
            skills: response.data.user.employeeDetails.skills || [],
            performanceRating: response.data.user.employeeDetails.performanceRating || 0,
            totalExperience: response.data.user.employeeDetails.totalExperience || ''
          } : undefined
        };

        setUser(userData);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Always clear the user state regardless of API success/failure
      setUser(null);
    }
  };

  // Check for existing authentication on mount
  useEffect(() => {
    const checkAuthState = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (!token) {
          setIsLoading(false);
          return;
        }

        // Try to get current user with existing token
        const response = await apiService.getCurrentUser();
        if (response.success && response.data) {
          // Map backend role to frontend role format
          const mapRole = (backendRole: string): 'super_admin' | 'org_admin' | 'employee' => {
            switch (backendRole.toLowerCase()) {
              case 'superadmin': return 'super_admin';
              case 'orgadmin': return 'org_admin';
              case 'employee': return 'employee';
              default: return 'employee';
            }
          };

          const userData: User = {
            id: response.data.id,
            email: response.data.email,
            name: response.data.name,
            role: mapRole(response.data.role),
            organizationId: response.data.organization?.id,
            organization: response.data.organization ? {
              id: response.data.organization.id,
              name: response.data.organization.name,
              domain: response.data.organization.domain,
              industry: response.data.organization.industry,
              employeeCount: response.data.organization.employeeCount
            } : undefined,
            employeeDetails: response.data.employeeDetails ? {
              employeeId: response.data.employeeDetails.employeeId || '',
              jobTitle: response.data.employeeDetails.jobTitle || '',
              department: response.data.employeeDetails.department || '',
              manager: response.data.employeeDetails.manager || '',
              joinDate: response.data.employeeDetails.joinDate || '',
              employmentType: response.data.employeeDetails.employmentType || '',
              employmentStatus: response.data.employeeDetails.employmentStatus || '',
              workLocation: response.data.employeeDetails.workLocation || '',
              phone: response.data.employeeDetails.phone || '',
              address: response.data.employeeDetails.address || '',
              dateOfBirth: response.data.employeeDetails.dateOfBirth || '',
              baseSalary: response.data.employeeDetails.baseSalary || '',
              annualCTC: response.data.employeeDetails.annualCTC || '',
              nextSalaryReview: response.data.employeeDetails.nextSalaryReview || '',
              education: response.data.employeeDetails.education || [],
              skills: response.data.employeeDetails.skills || [],
              performanceRating: response.data.employeeDetails.performanceRating || 0,
              totalExperience: response.data.employeeDetails.totalExperience || ''
            } : undefined
          };

          setUser(userData);
        } else {
          // Token is invalid, clear it
          localStorage.removeItem('authToken');
          localStorage.removeItem('organizationId');
        }
      } catch (error) {
        console.error('Failed to restore authentication state:', error);
        // Clear invalid tokens
        localStorage.removeItem('authToken');
        localStorage.removeItem('organizationId');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);



  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated: !!user,
      isLoading,
      login,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
