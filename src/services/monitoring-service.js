/**
 * Monitoring Service
 * Provides metrics collection and monitoring for database provisioning
 */

const prometheus = require('prom-client');
const logger = require('../utils/logger');

class MonitoringService {
  constructor() {
    this.metrics = {};
    this.register = new prometheus.Registry();
    this.initialize();
  }

  initialize() {
    // Enable default metrics collection
    prometheus.collectDefaultMetrics({
      register: this.register,
      prefix: 'hrms_provisioning_'
    });

    // Database provisioning metrics
    this.metrics.provisioningDuration = new prometheus.Histogram({
      name: 'hrms_provisioning_duration_seconds',
      help: 'Duration of database schema provisioning in seconds',
      labelNames: ['organization_id', 'schema_name', 'status'],
      buckets: [1, 5, 10, 30, 60, 120, 300, 600] // seconds
    });

    this.metrics.provisioningTotal = new prometheus.Counter({
      name: 'hrms_provisioning_total',
      help: 'Total number of provisioning attempts',
      labelNames: ['status', 'retry_attempt']
    });

    this.metrics.provisioningErrors = new prometheus.Counter({
      name: 'hrms_provisioning_errors_total',
      help: 'Total number of provisioning errors',
      labelNames: ['error_type', 'organization_id']
    });

    // Migration execution metrics
    this.metrics.migrationDuration = new prometheus.Histogram({
      name: 'hrms_migration_duration_seconds',
      help: 'Duration of individual migration file execution',
      labelNames: ['file_name', 'schema_name'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30] // seconds
    });

    this.metrics.migrationErrors = new prometheus.Counter({
      name: 'hrms_migration_errors_total',
      help: 'Total number of migration execution errors',
      labelNames: ['file_name', 'error_type']
    });

    // Database connection metrics
    this.metrics.dbConnections = new prometheus.Gauge({
      name: 'hrms_db_connections_active',
      help: 'Number of active database connections',
      labelNames: ['pool_type']
    });

    this.metrics.dbConnectionErrors = new prometheus.Counter({
      name: 'hrms_db_connection_errors_total',
      help: 'Total number of database connection errors',
      labelNames: ['error_type']
    });

    // Schema validation metrics
    this.metrics.schemaValidation = new prometheus.Counter({
      name: 'hrms_schema_validation_total',
      help: 'Total number of schema validations',
      labelNames: ['schema_name', 'status']
    });

    // API request metrics
    this.metrics.apiRequests = new prometheus.Counter({
      name: 'hrms_api_requests_total',
      help: 'Total number of API requests',
      labelNames: ['method', 'endpoint', 'status_code']
    });

    this.metrics.apiDuration = new prometheus.Histogram({
      name: 'hrms_api_request_duration_seconds',
      help: 'Duration of API requests in seconds',
      labelNames: ['method', 'endpoint'],
      buckets: [0.1, 0.5, 1, 2, 5, 10] // seconds
    });

    // System resource metrics
    this.metrics.memoryUsage = new prometheus.Gauge({
      name: 'hrms_memory_usage_bytes',
      help: 'Memory usage in bytes',
      labelNames: ['type']
    });

    this.metrics.diskUsage = new prometheus.Gauge({
      name: 'hrms_disk_usage_bytes',
      help: 'Disk usage in bytes',
      labelNames: ['mount_point']
    });

    // Organization metrics
    this.metrics.activeOrganizations = new prometheus.Gauge({
      name: 'hrms_organizations_active_total',
      help: 'Total number of active organizations'
    });

    this.metrics.organizationsByPlan = new prometheus.Gauge({
      name: 'hrms_organizations_by_plan',
      help: 'Number of organizations by subscription plan',
      labelNames: ['plan']
    });

    // Register all metrics
    Object.values(this.metrics).forEach(metric => {
      this.register.registerMetric(metric);
    });

    // Start periodic metric collection
    this.startPeriodicCollection();

    logger.info('Monitoring service initialized');
  }

  /**
   * Record provisioning start
   * @param {string} organizationId - Organization ID
   * @param {string} schemaName - Schema name
   */
  recordProvisioningStart(organizationId, schemaName) {
    this.metrics.provisioningTotal.inc({ status: 'started', retry_attempt: '1' });
    logger.debug('Recorded provisioning start', { organizationId, schemaName });
  }

  /**
   * Record provisioning success
   * @param {string} organizationId - Organization ID
   * @param {string} schemaName - Schema name
   * @param {number} duration - Duration in milliseconds
   */
  recordProvisioningSuccess(organizationId, schemaName, duration) {
    const durationSeconds = duration / 1000;
    
    this.metrics.provisioningDuration
      .labels(organizationId, schemaName, 'success')
      .observe(durationSeconds);
    
    this.metrics.provisioningTotal.inc({ status: 'success', retry_attempt: '1' });
    
    logger.info('Recorded provisioning success', { 
      organizationId, 
      schemaName, 
      duration: durationSeconds 
    });
  }

  /**
   * Record provisioning failure
   * @param {string} organizationId - Organization ID
   * @param {string} schemaName - Schema name
   * @param {number} duration - Duration in milliseconds
   * @param {string} errorType - Error type
   * @param {number} retryAttempt - Retry attempt number
   */
  recordProvisioningFailure(organizationId, schemaName, duration, errorType, retryAttempt = 1) {
    const durationSeconds = duration / 1000;
    
    this.metrics.provisioningDuration
      .labels(organizationId, schemaName, 'failure')
      .observe(durationSeconds);
    
    this.metrics.provisioningTotal.inc({ 
      status: 'failure', 
      retry_attempt: retryAttempt.toString() 
    });
    
    this.metrics.provisioningErrors.inc({ 
      error_type: errorType, 
      organization_id: organizationId 
    });
    
    logger.warn('Recorded provisioning failure', { 
      organizationId, 
      schemaName, 
      duration: durationSeconds,
      errorType,
      retryAttempt
    });
  }

  /**
   * Record migration execution
   * @param {string} fileName - Migration file name
   * @param {string} schemaName - Schema name
   * @param {number} duration - Duration in milliseconds
   * @param {boolean} success - Success status
   * @param {string} errorType - Error type if failed
   */
  recordMigrationExecution(fileName, schemaName, duration, success = true, errorType = null) {
    const durationSeconds = duration / 1000;
    
    this.metrics.migrationDuration
      .labels(fileName, schemaName)
      .observe(durationSeconds);
    
    if (!success && errorType) {
      this.metrics.migrationErrors.inc({ 
        file_name: fileName, 
        error_type: errorType 
      });
    }
    
    logger.debug('Recorded migration execution', { 
      fileName, 
      schemaName, 
      duration: durationSeconds, 
      success 
    });
  }

  /**
   * Record database connection metrics
   * @param {string} poolType - Pool type (master, provisioning)
   * @param {number} activeConnections - Number of active connections
   */
  recordDbConnections(poolType, activeConnections) {
    this.metrics.dbConnections.labels(poolType).set(activeConnections);
  }

  /**
   * Record database connection error
   * @param {string} errorType - Error type
   */
  recordDbConnectionError(errorType) {
    this.metrics.dbConnectionErrors.inc({ error_type: errorType });
    logger.warn('Recorded database connection error', { errorType });
  }

  /**
   * Record schema validation
   * @param {string} schemaName - Schema name
   * @param {boolean} success - Validation success
   */
  recordSchemaValidation(schemaName, success) {
    const status = success ? 'success' : 'failure';
    this.metrics.schemaValidation.inc({ schema_name: schemaName, status });
    
    logger.debug('Recorded schema validation', { schemaName, success });
  }

  /**
   * Record API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {number} statusCode - HTTP status code
   * @param {number} duration - Duration in milliseconds
   */
  recordApiRequest(method, endpoint, statusCode, duration) {
    const durationSeconds = duration / 1000;
    
    this.metrics.apiRequests.inc({ 
      method, 
      endpoint, 
      status_code: statusCode.toString() 
    });
    
    this.metrics.apiDuration
      .labels(method, endpoint)
      .observe(durationSeconds);
    
    logger.debug('Recorded API request', { 
      method, 
      endpoint, 
      statusCode, 
      duration: durationSeconds 
    });
  }

  /**
   * Update system resource metrics
   */
  updateSystemMetrics() {
    try {
      const memUsage = process.memoryUsage();
      
      this.metrics.memoryUsage.labels('rss').set(memUsage.rss);
      this.metrics.memoryUsage.labels('heap_used').set(memUsage.heapUsed);
      this.metrics.memoryUsage.labels('heap_total').set(memUsage.heapTotal);
      this.metrics.memoryUsage.labels('external').set(memUsage.external);
      
      // Disk usage would require additional system calls
      // Implementation depends on your specific requirements
      
    } catch (error) {
      logger.error('Failed to update system metrics:', error);
    }
  }

  /**
   * Update organization metrics
   * @param {number} activeCount - Number of active organizations
   * @param {Object} planCounts - Organization counts by plan
   */
  updateOrganizationMetrics(activeCount, planCounts = {}) {
    this.metrics.activeOrganizations.set(activeCount);
    
    Object.entries(planCounts).forEach(([plan, count]) => {
      this.metrics.organizationsByPlan.labels(plan).set(count);
    });
  }

  /**
   * Start periodic metric collection
   */
  startPeriodicCollection() {
    // Update system metrics every 30 seconds
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000);

    // Update organization metrics every 5 minutes
    setInterval(async () => {
      try {
        await this.collectOrganizationMetrics();
      } catch (error) {
        logger.error('Failed to collect organization metrics:', error);
      }
    }, 300000);
  }

  /**
   * Collect organization metrics from database
   */
  async collectOrganizationMetrics() {
    // This would connect to your master database and collect metrics
    // Implementation depends on your database setup
    logger.debug('Collecting organization metrics');
  }

  /**
   * Get metrics for Prometheus scraping
   * @returns {string} Metrics in Prometheus format
   */
  async getMetrics() {
    return this.register.metrics();
  }

  /**
   * Get metrics summary
   * @returns {Object} Metrics summary
   */
  getMetricsSummary() {
    return {
      provisioning: {
        total: this.metrics.provisioningTotal,
        errors: this.metrics.provisioningErrors
      },
      migrations: {
        errors: this.metrics.migrationErrors
      },
      database: {
        connections: this.metrics.dbConnections,
        errors: this.metrics.dbConnectionErrors
      },
      api: {
        requests: this.metrics.apiRequests
      }
    };
  }

  /**
   * Reset all metrics (useful for testing)
   */
  reset() {
    this.register.clear();
    this.initialize();
    logger.info('Monitoring metrics reset');
  }

  /**
   * Create middleware for Express.js to automatically record API metrics
   */
  createExpressMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        this.recordApiRequest(
          req.method,
          req.route ? req.route.path : req.path,
          res.statusCode,
          duration
        );
      });
      
      next();
    };
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();

module.exports = monitoringService;
