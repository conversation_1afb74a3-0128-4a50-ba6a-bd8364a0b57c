/**
 * Database Provisioning Service
 * Handles automated database schema creation for new organizations
 * Supports multi-tenant architecture with isolated schemas
 */

const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
const DatabaseConfigLoader = require('../config/database-config-loader');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class DatabaseProvisioningService {
  constructor() {
    this.configLoader = new DatabaseConfigLoader();
    this.config = null;
    this.masterPool = null;
    this.provisioningPool = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the provisioning service
   */
  async initialize() {
    try {
      this.config = this.configLoader.loadConfig();
      
      // Create master database connection pool
      const dbConfig = this.configLoader.getDatabaseConfig();
      this.masterPool = new Pool({
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.username,
        password: dbConfig.password,
        ssl: dbConfig.ssl_mode === 'require' ? { rejectUnauthorized: false } : false,
        max: dbConfig.max_connections || 20,
        idleTimeoutMillis: (dbConfig.idle_timeout || 300) * 1000,
        connectionTimeoutMillis: (dbConfig.connection_timeout || 30) * 1000
      });

      // Create provisioning user connection pool (with elevated privileges)
      const securityConfig = this.configLoader.getSecurityConfig();
      if (securityConfig.provisioning_user) {
        this.provisioningPool = new Pool({
          host: dbConfig.host,
          port: dbConfig.port,
          database: dbConfig.database,
          user: securityConfig.provisioning_user.username,
          password: securityConfig.provisioning_user.password,
          ssl: dbConfig.ssl_mode === 'require' ? { rejectUnauthorized: false } : false,
          max: 5, // Limited connections for provisioning
          idleTimeoutMillis: 60000,
          connectionTimeoutMillis: 30000
        });
      } else {
        this.provisioningPool = this.masterPool;
      }

      this.isInitialized = true;
      logger.info('Database provisioning service initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize database provisioning service:', error);
      throw error;
    }
  }

  /**
   * Provision database schema for a new organization
   * @param {Object} organization - Organization data
   * @returns {Promise<Object>} Provisioning result
   */
  async provisionOrganizationSchema(organization) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    const provisioningConfig = this.configLoader.getProvisioningConfig();
    const featureFlags = this.configLoader.getFeatureFlags();
    
    // Validate organization data
    this.validateOrganizationData(organization);
    
    // Generate schema name
    const schemaName = this.configLoader.generateSchemaName(organization);
    logger.info(`Starting schema provisioning for organization: ${organization.name}, schema: ${schemaName}`);

    const client = await this.provisioningPool.connect();
    let rollbackRequired = false;
    
    try {
      // Start transaction
      await client.query('BEGIN');
      rollbackRequired = true;

      // Check if schema already exists
      const schemaExists = await this.checkSchemaExists(client, schemaName);
      if (schemaExists) {
        throw new Error(`Schema ${schemaName} already exists`);
      }

      // Create schema
      await this.createSchema(client, schemaName);
      logger.info(`Created schema: ${schemaName}`);

      // Set search path
      await client.query(`SET search_path TO ${schemaName}, public`);

      // Execute migration files
      await this.executeMigrationFiles(client, schemaName, organization);

      // Execute seed files
      await this.executeSeedFiles(client, schemaName, organization);

      // Set permissions
      await this.setSchemaPermissions(client, schemaName);

      // Validate schema creation
      if (featureFlags.enable_schema_validation) {
        await this.validateSchemaCreation(client, schemaName);
      }

      // Create backup if enabled
      const backupConfig = this.config.backup;
      if (backupConfig && backupConfig.auto_backup_new_schemas) {
        await this.createSchemaBackup(schemaName);
      }

      // Commit transaction
      await client.query('COMMIT');
      rollbackRequired = false;

      const duration = Date.now() - startTime;
      logger.info(`Schema provisioning completed successfully for ${schemaName} in ${duration}ms`);

      // Log audit entry
      await this.logProvisioningAudit(organization, schemaName, 'SUCCESS', duration);

      return {
        success: true,
        schemaName,
        organizationId: organization.id,
        duration,
        message: 'Schema provisioned successfully'
      };

    } catch (error) {
      logger.error(`Schema provisioning failed for ${schemaName}:`, error);

      // Rollback if needed
      if (rollbackRequired && featureFlags.enable_rollback_on_failure) {
        try {
          await client.query('ROLLBACK');
          await this.cleanupFailedSchema(client, schemaName);
          logger.info(`Rollback completed for failed schema: ${schemaName}`);
        } catch (rollbackError) {
          logger.error(`Rollback failed for schema ${schemaName}:`, rollbackError);
        }
      }

      // Log audit entry
      await this.logProvisioningAudit(organization, schemaName, 'FAILED', Date.now() - startTime, error.message);

      // Send notifications if configured
      await this.sendFailureNotification(organization, schemaName, error);

      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Validate organization data before provisioning
   * @param {Object} organization - Organization data
   */
  validateOrganizationData(organization) {
    const validationConfig = this.configLoader.getValidationConfig();
    
    if (!organization || typeof organization !== 'object') {
      throw new Error('Invalid organization data');
    }

    // Check required fields
    if (validationConfig.required_fields) {
      for (const field of validationConfig.required_fields) {
        if (!organization[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }
    }

    // Validate domain
    if (validationConfig.domain_validation && organization.domain) {
      const domainValidation = validationConfig.domain_validation;
      
      if (organization.domain.length < domainValidation.min_length || 
          organization.domain.length > domainValidation.max_length) {
        throw new Error(`Domain length must be between ${domainValidation.min_length} and ${domainValidation.max_length} characters`);
      }

      if (domainValidation.blocked_domains && domainValidation.blocked_domains.includes(organization.domain)) {
        throw new Error(`Domain ${organization.domain} is not allowed`);
      }
    }

    // Validate organization name
    if (validationConfig.name_validation && organization.name) {
      const nameValidation = validationConfig.name_validation;
      
      if (organization.name.length < nameValidation.min_length || 
          organization.name.length > nameValidation.max_length) {
        throw new Error(`Organization name length must be between ${nameValidation.min_length} and ${nameValidation.max_length} characters`);
      }
    }
  }

  /**
   * Check if schema exists
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   * @returns {Promise<boolean>} Schema exists
   */
  async checkSchemaExists(client, schemaName) {
    const result = await client.query(
      'SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1',
      [schemaName]
    );
    return result.rows.length > 0;
  }

  /**
   * Create database schema
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   */
  async createSchema(client, schemaName) {
    await client.query(`CREATE SCHEMA IF NOT EXISTS ${schemaName}`);
    
    // Grant usage to application users
    const securityConfig = this.configLoader.getSecurityConfig();
    if (securityConfig.default_permissions) {
      const permissions = securityConfig.default_permissions;
      
      if (permissions.owner) {
        await client.query(`ALTER SCHEMA ${schemaName} OWNER TO ${permissions.owner}`);
      }
      
      if (permissions.read_users) {
        for (const user of permissions.read_users) {
          await client.query(`GRANT USAGE ON SCHEMA ${schemaName} TO ${user}`);
        }
      }
    }
  }

  /**
   * Execute migration files
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   * @param {Object} organization - Organization data
   */
  async executeMigrationFiles(client, schemaName, organization) {
    const provisioningConfig = this.configLoader.getProvisioningConfig();
    const migrationFiles = provisioningConfig.migration_files || [];

    logger.info(`Executing ${migrationFiles.length} migration files for schema: ${schemaName}`);

    for (const migrationFile of migrationFiles) {
      try {
        const filePath = path.join(process.cwd(), migrationFile);
        let sqlContent = await fs.readFile(filePath, 'utf8');
        
        // Replace placeholders
        sqlContent = this.replacePlaceholders(sqlContent, schemaName, organization);
        
        // Execute SQL
        await client.query(sqlContent);
        logger.debug(`Executed migration file: ${migrationFile}`);
        
      } catch (error) {
        logger.error(`Failed to execute migration file ${migrationFile}:`, error);
        throw new Error(`Migration failed at file: ${migrationFile} - ${error.message}`);
      }
    }
  }

  /**
   * Execute seed files
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   * @param {Object} organization - Organization data
   */
  async executeSeedFiles(client, schemaName, organization) {
    const provisioningConfig = this.configLoader.getProvisioningConfig();
    const seedFiles = provisioningConfig.seed_files || [];

    logger.info(`Executing ${seedFiles.length} seed files for schema: ${schemaName}`);

    for (const seedFile of seedFiles) {
      try {
        const filePath = path.join(process.cwd(), seedFile);
        let sqlContent = await fs.readFile(filePath, 'utf8');
        
        // Replace placeholders
        sqlContent = this.replacePlaceholders(sqlContent, schemaName, organization);
        
        // Execute SQL
        await client.query(sqlContent);
        logger.debug(`Executed seed file: ${seedFile}`);
        
      } catch (error) {
        logger.error(`Failed to execute seed file ${seedFile}:`, error);
        throw new Error(`Seeding failed at file: ${seedFile} - ${error.message}`);
      }
    }
  }

  /**
   * Replace placeholders in SQL content
   * @param {string} sqlContent - SQL content
   * @param {string} schemaName - Schema name
   * @param {Object} organization - Organization data
   * @returns {string} Processed SQL content
   */
  replacePlaceholders(sqlContent, schemaName, organization) {
    const provisioningConfig = this.configLoader.getProvisioningConfig();
    const replacements = {
      ...provisioningConfig.replacements,
      '{SCHEMA_NAME}': schemaName,
      '{ORG_ID}': organization.id || uuidv4(),
      '{CREATED_DATE}': new Date().toISOString()
    };

    let processedContent = sqlContent;
    for (const [placeholder, value] of Object.entries(replacements)) {
      processedContent = processedContent.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
    }

    return processedContent;
  }

  /**
   * Set schema permissions
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   */
  async setSchemaPermissions(client, schemaName) {
    const securityConfig = this.configLoader.getSecurityConfig();

    if (securityConfig.default_permissions) {
      const permissions = securityConfig.default_permissions;

      // Grant permissions to write users
      if (permissions.write_users) {
        for (const user of permissions.write_users) {
          await client.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA ${schemaName} TO ${user}`);
          await client.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA ${schemaName} TO ${user}`);
          await client.query(`GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA ${schemaName} TO ${user}`);
        }
      }

      // Grant read permissions to read users
      if (permissions.read_users) {
        for (const user of permissions.read_users) {
          await client.query(`GRANT SELECT ON ALL TABLES IN SCHEMA ${schemaName} TO ${user}`);
          await client.query(`GRANT SELECT ON ALL SEQUENCES IN SCHEMA ${schemaName} TO ${user}`);
          await client.query(`GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA ${schemaName} TO ${user}`);
        }
      }
    }
  }

  /**
   * Validate schema creation
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   */
  async validateSchemaCreation(client, schemaName) {
    // Check if essential tables exist
    const essentialTables = [
      'organizations', 'users', 'employee_details', 'attendance_records',
      'leave_types', 'leave_balances', 'leave_requests', 'tasks'
    ];

    for (const tableName of essentialTables) {
      const result = await client.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = $1 AND table_name = $2
      `, [schemaName, tableName]);

      if (result.rows.length === 0) {
        throw new Error(`Essential table ${tableName} not found in schema ${schemaName}`);
      }
    }

    // Check if organization record exists
    const orgResult = await client.query(`
      SELECT COUNT(*) as count
      FROM ${schemaName}.organizations
    `);

    if (parseInt(orgResult.rows[0].count) === 0) {
      throw new Error(`No organization record found in schema ${schemaName}`);
    }

    logger.info(`Schema validation passed for: ${schemaName}`);
  }

  /**
   * Create schema backup
   * @param {string} schemaName - Schema name
   */
  async createSchemaBackup(schemaName) {
    try {
      const backupConfig = this.config.backup;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = backupConfig.backup_naming
        .replace('{schema_name}', schemaName)
        .replace('{timestamp}', timestamp);

      const backupPath = path.join(backupConfig.backup_location, backupFileName);

      // Create backup directory if it doesn't exist
      await fs.mkdir(path.dirname(backupPath), { recursive: true });

      // Note: In a real implementation, you would use pg_dump here
      // This is a placeholder for the backup logic
      logger.info(`Schema backup created: ${backupPath}`);

    } catch (error) {
      logger.error(`Failed to create backup for schema ${schemaName}:`, error);
      // Don't throw error as backup failure shouldn't fail the provisioning
    }
  }

  /**
   * Cleanup failed schema
   * @param {Object} client - Database client
   * @param {string} schemaName - Schema name
   */
  async cleanupFailedSchema(client, schemaName) {
    try {
      await client.query(`DROP SCHEMA IF EXISTS ${schemaName} CASCADE`);
      logger.info(`Cleaned up failed schema: ${schemaName}`);
    } catch (error) {
      logger.error(`Failed to cleanup schema ${schemaName}:`, error);
    }
  }

  /**
   * Log provisioning audit
   * @param {Object} organization - Organization data
   * @param {string} schemaName - Schema name
   * @param {string} status - Status (SUCCESS/FAILED)
   * @param {number} duration - Duration in milliseconds
   * @param {string} errorMessage - Error message if failed
   */
  async logProvisioningAudit(organization, schemaName, status, duration, errorMessage = null) {
    try {
      const client = await this.masterPool.connect();

      await client.query(`
        INSERT INTO provisioning_audit_log
        (organization_id, organization_name, schema_name, status, duration_ms, error_message, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [
        organization.id,
        organization.name,
        schemaName,
        status,
        duration,
        errorMessage
      ]);

      client.release();
    } catch (error) {
      logger.error('Failed to log provisioning audit:', error);
    }
  }

  /**
   * Send failure notification
   * @param {Object} organization - Organization data
   * @param {string} schemaName - Schema name
   * @param {Error} error - Error object
   */
  async sendFailureNotification(organization, schemaName, error) {
    try {
      const errorHandlingConfig = this.configLoader.getErrorHandlingConfig();

      if (errorHandlingConfig.notifications && errorHandlingConfig.notifications.enable_email_alerts) {
        // Send email notification (implementation depends on your email service)
        logger.info(`Sending failure notification for schema: ${schemaName}`);

        // Webhook notification
        if (errorHandlingConfig.notifications.webhook_url) {
          // Send webhook notification (implementation depends on your webhook service)
          logger.info(`Sending webhook notification for failed provisioning: ${schemaName}`);
        }
      }
    } catch (notificationError) {
      logger.error('Failed to send failure notification:', notificationError);
    }
  }

  /**
   * Provision schema with retry logic
   * @param {Object} organization - Organization data
   * @returns {Promise<Object>} Provisioning result
   */
  async provisionWithRetry(organization) {
    const errorHandlingConfig = this.configLoader.getErrorHandlingConfig();
    const retryConfig = errorHandlingConfig.retry || {};

    const maxAttempts = retryConfig.max_attempts || 3;
    const baseDelay = (retryConfig.base_delay_seconds || 5) * 1000;
    const maxDelay = (retryConfig.max_delay_seconds || 60) * 1000;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await this.provisionOrganizationSchema(organization);
      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }

        // Calculate delay based on backoff strategy
        let delay = baseDelay;
        if (retryConfig.backoff_strategy === 'exponential') {
          delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
        }

        logger.warn(`Provisioning attempt ${attempt} failed, retrying in ${delay}ms:`, error.message);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Get provisioning status
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Object>} Provisioning status
   */
  async getProvisioningStatus(organizationId) {
    try {
      const client = await this.masterPool.connect();

      const result = await client.query(`
        SELECT * FROM provisioning_audit_log
        WHERE organization_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [organizationId]);

      client.release();

      if (result.rows.length === 0) {
        return { status: 'NOT_FOUND' };
      }

      return {
        status: result.rows[0].status,
        schemaName: result.rows[0].schema_name,
        duration: result.rows[0].duration_ms,
        createdAt: result.rows[0].created_at,
        errorMessage: result.rows[0].error_message
      };

    } catch (error) {
      logger.error('Failed to get provisioning status:', error);
      throw error;
    }
  }

  /**
   * Manually trigger schema creation for existing organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Object>} Provisioning result
   */
  async manualProvision(organizationId) {
    try {
      // Get organization data from master database
      const client = await this.masterPool.connect();

      const result = await client.query(`
        SELECT * FROM organizations WHERE id = $1
      `, [organizationId]);

      client.release();

      if (result.rows.length === 0) {
        throw new Error(`Organization not found: ${organizationId}`);
      }

      const organization = result.rows[0];
      return await this.provisionWithRetry(organization);

    } catch (error) {
      logger.error('Manual provisioning failed:', error);
      throw error;
    }
  }

  /**
   * Close database connections
   */
  async close() {
    try {
      if (this.masterPool) {
        await this.masterPool.end();
      }
      if (this.provisioningPool && this.provisioningPool !== this.masterPool) {
        await this.provisioningPool.end();
      }
      logger.info('Database provisioning service closed');
    } catch (error) {
      logger.error('Error closing database provisioning service:', error);
    }
  }
}

module.exports = DatabaseProvisioningService;
