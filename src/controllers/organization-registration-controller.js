/**
 * Organization Registration Controller
 * Handles organization registration with automated database provisioning
 */

const DatabaseProvisioningService = require('../services/database-provisioning-service');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcrypt');

class OrganizationRegistrationController {
  constructor() {
    this.provisioningService = new DatabaseProvisioningService();
  }

  /**
   * Register new organization with automated schema provisioning
   * POST /auth/register-organization
   */
  async registerOrganization(req, res) {
    const startTime = Date.now();
    let organizationId = null;
    
    try {
      const { organization, admin } = req.body;
      
      // Validate request data
      this.validateRegistrationData(organization, admin);
      
      // Generate organization ID
      organizationId = uuidv4();
      const adminId = uuidv4();
      
      // Hash admin password
      const hashedPassword = await bcrypt.hash(admin.password, 12);
      
      // Prepare organization data for provisioning
      const organizationData = {
        id: organizationId,
        name: organization.name,
        domain: organization.domain,
        industry: organization.industry,
        status: 'pending', // Will be activated after successful provisioning
        admin: {
          id: adminId,
          name: admin.name,
          email: admin.email,
          password_hash: hashedPassword
        }
      };

      // Step 1: Create organization record in master database
      await this.createMasterOrganizationRecord(organizationData);
      logger.info(`Created master organization record: ${organizationId}`);

      // Step 2: Provision dedicated schema for organization
      const provisioningResult = await this.provisioningService.provisionWithRetry(organizationData);
      logger.info(`Schema provisioning completed: ${provisioningResult.schemaName}`);

      // Step 3: Update organization status to active
      await this.activateOrganization(organizationId, provisioningResult.schemaName);

      // Step 4: Send welcome notifications
      await this.sendWelcomeNotifications(organizationData);

      const duration = Date.now() - startTime;
      
      // Return success response
      res.status(201).json({
        success: true,
        data: {
          organization: {
            id: organizationId,
            name: organization.name,
            domain: organization.domain,
            status: 'active',
            schema_name: provisioningResult.schemaName
          },
          admin: {
            id: adminId,
            name: admin.name,
            email: admin.email
          },
          provisioning: {
            duration,
            schema_name: provisioningResult.schemaName
          }
        },
        message: 'Organization registered and provisioned successfully'
      });

    } catch (error) {
      logger.error('Organization registration failed:', error);

      // Cleanup on failure
      if (organizationId) {
        await this.cleanupFailedRegistration(organizationId);
      }

      // Return error response
      res.status(500).json({
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: error.message || 'Organization registration failed',
          details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      });
    }
  }

  /**
   * Get organization registration status
   * GET /auth/registration-status/:organizationId
   */
  async getRegistrationStatus(req, res) {
    try {
      const { organizationId } = req.params;
      
      // Get organization status from master database
      const organizationStatus = await this.getOrganizationStatus(organizationId);
      
      // Get provisioning status
      const provisioningStatus = await this.provisioningService.getProvisioningStatus(organizationId);
      
      res.json({
        success: true,
        data: {
          organization: organizationStatus,
          provisioning: provisioningStatus
        }
      });

    } catch (error) {
      logger.error('Failed to get registration status:', error);
      
      res.status(500).json({
        success: false,
        error: {
          code: 'STATUS_CHECK_FAILED',
          message: 'Failed to retrieve registration status'
        }
      });
    }
  }

  /**
   * Manually trigger schema provisioning for existing organization
   * POST /admin/organizations/:organizationId/provision
   */
  async manualProvision(req, res) {
    try {
      const { organizationId } = req.params;
      
      // Check if user has admin privileges
      if (req.user.role !== 'super_admin') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PRIVILEGES',
            message: 'Only super admins can manually trigger provisioning'
          }
        });
      }

      // Trigger manual provisioning
      const result = await this.provisioningService.manualProvision(organizationId);
      
      res.json({
        success: true,
        data: result,
        message: 'Manual provisioning completed successfully'
      });

    } catch (error) {
      logger.error('Manual provisioning failed:', error);
      
      res.status(500).json({
        success: false,
        error: {
          code: 'MANUAL_PROVISIONING_FAILED',
          message: error.message || 'Manual provisioning failed'
        }
      });
    }
  }

  /**
   * Validate registration data
   * @param {Object} organization - Organization data
   * @param {Object} admin - Admin user data
   */
  validateRegistrationData(organization, admin) {
    // Validate organization data
    if (!organization || typeof organization !== 'object') {
      throw new Error('Invalid organization data');
    }

    const requiredOrgFields = ['name', 'domain', 'industry'];
    for (const field of requiredOrgFields) {
      if (!organization[field] || typeof organization[field] !== 'string') {
        throw new Error(`Missing or invalid organization field: ${field}`);
      }
    }

    // Validate admin data
    if (!admin || typeof admin !== 'object') {
      throw new Error('Invalid admin data');
    }

    const requiredAdminFields = ['name', 'email', 'password'];
    for (const field of requiredAdminFields) {
      if (!admin[field] || typeof admin[field] !== 'string') {
        throw new Error(`Missing or invalid admin field: ${field}`);
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(admin.email)) {
      throw new Error('Invalid email format');
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(organization.domain)) {
      throw new Error('Invalid domain format');
    }

    // Validate password strength
    if (admin.password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }
  }

  /**
   * Create organization record in master database
   * @param {Object} organizationData - Organization data
   */
  async createMasterOrganizationRecord(organizationData) {
    const { Pool } = require('pg');
    const DatabaseConfigLoader = require('../config/database-config-loader');
    
    const configLoader = new DatabaseConfigLoader();
    const dbConfig = configLoader.getDatabaseConfig();
    
    const pool = new Pool({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl: dbConfig.ssl_mode === 'require' ? { rejectUnauthorized: false } : false
    });

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      // Insert organization
      await client.query(`
        INSERT INTO organizations (id, name, domain, industry, status, created_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `, [
        organizationData.id,
        organizationData.name,
        organizationData.domain,
        organizationData.industry,
        organizationData.status
      ]);

      // Insert admin user
      await client.query(`
        INSERT INTO users (id, organization_id, name, email, password_hash, role, is_active, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      `, [
        organizationData.admin.id,
        organizationData.id,
        organizationData.admin.name,
        organizationData.admin.email,
        organizationData.admin.password_hash,
        'org_admin',
        true
      ]);

      await client.query('COMMIT');
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
      await pool.end();
    }
  }

  /**
   * Activate organization after successful provisioning
   * @param {string} organizationId - Organization ID
   * @param {string} schemaName - Schema name
   */
  async activateOrganization(organizationId, schemaName) {
    const { Pool } = require('pg');
    const DatabaseConfigLoader = require('../config/database-config-loader');
    
    const configLoader = new DatabaseConfigLoader();
    const dbConfig = configLoader.getDatabaseConfig();
    
    const pool = new Pool({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl: dbConfig.ssl_mode === 'require' ? { rejectUnauthorized: false } : false
    });

    const client = await pool.connect();
    
    try {
      await client.query(`
        UPDATE organizations 
        SET status = 'active', schema_name = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [organizationId, schemaName]);
      
    } finally {
      client.release();
      await pool.end();
    }
  }

  /**
   * Get organization status from master database
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Object>} Organization status
   */
  async getOrganizationStatus(organizationId) {
    const { Pool } = require('pg');
    const DatabaseConfigLoader = require('../config/database-config-loader');
    
    const configLoader = new DatabaseConfigLoader();
    const dbConfig = configLoader.getDatabaseConfig();
    
    const pool = new Pool({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl: dbConfig.ssl_mode === 'require' ? { rejectUnauthorized: false } : false
    });

    const client = await pool.connect();
    
    try {
      const result = await client.query(`
        SELECT id, name, domain, industry, status, schema_name, created_at, updated_at
        FROM organizations 
        WHERE id = $1
      `, [organizationId]);
      
      if (result.rows.length === 0) {
        throw new Error('Organization not found');
      }
      
      return result.rows[0];
      
    } finally {
      client.release();
      await pool.end();
    }
  }

  /**
   * Send welcome notifications
   * @param {Object} organizationData - Organization data
   */
  async sendWelcomeNotifications(organizationData) {
    try {
      // Send welcome email to admin
      logger.info(`Sending welcome email to: ${organizationData.admin.email}`);
      
      // Send Slack notification if configured
      logger.info(`Organization ${organizationData.name} successfully registered and provisioned`);
      
    } catch (error) {
      logger.error('Failed to send welcome notifications:', error);
      // Don't throw error as notification failure shouldn't fail registration
    }
  }

  /**
   * Cleanup failed registration
   * @param {string} organizationId - Organization ID
   */
  async cleanupFailedRegistration(organizationId) {
    try {
      const { Pool } = require('pg');
      const DatabaseConfigLoader = require('../config/database-config-loader');
      
      const configLoader = new DatabaseConfigLoader();
      const dbConfig = configLoader.getDatabaseConfig();
      
      const pool = new Pool({
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.username,
        password: dbConfig.password,
        ssl: dbConfig.ssl_mode === 'require' ? { rejectUnauthorized: false } : false
      });

      const client = await pool.connect();
      
      try {
        // Delete organization and related records
        await client.query('DELETE FROM organizations WHERE id = $1', [organizationId]);
        logger.info(`Cleaned up failed registration: ${organizationId}`);
        
      } finally {
        client.release();
        await pool.end();
      }
      
    } catch (error) {
      logger.error('Failed to cleanup failed registration:', error);
    }
  }
}

module.exports = OrganizationRegistrationController;
