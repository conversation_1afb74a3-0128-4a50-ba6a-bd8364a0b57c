@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Black, White, Sky Blue Theme */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    /* Sky Blue Primary */
    --primary: 199 89% 48%;
    --primary-foreground: 0 0% 100%;

    /* Light Sky Blue Secondary */
    --secondary: 199 89% 94%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    /* Sky Blue Accent */
    --accent: 199 89% 90%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 199 89% 48%;

    --radius: 0.5rem;

    /* Sidebar with sky blue theme */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 199 89% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 199 89% 94%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 90%;
    --sidebar-ring: 199 89% 48%;

    /* Custom sky blue gradient */
    --gradient-sky: linear-gradient(135deg, hsl(199, 89%, 48%), hsl(199, 89%, 70%));
    --gradient-dark: linear-gradient(135deg, hsl(0, 0%, 0%), hsl(0, 0%, 20%));
  }

  .dark {
    --background: 0 0% 6%;
    --foreground: 0 0% 100%;

    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 100%;

    --primary: 199 89% 48%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62% 31%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 199 89% 48%;

    --sidebar-background: 0 0% 6%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 199 89% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 15%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 199 89% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}