// Mobile device blocking configuration
export const MO<PERSON>LE_BLOCKING_CONFIG = {
  // Enable/disable mobile device blocking
  enabled: true,
  
  // Device detection settings
  detection: {
    // Screen width breakpoint for mobile detection (pixels)
    mobileBreakpoint: 768,
    
    // Screen width breakpoint for tablet detection (pixels)
    tabletBreakpoint: 1024,
    
    // Enable user agent based detection
    enableUserAgentDetection: true,
    
    // Enable screen size based detection
    enableScreenSizeDetection: true,
    
    // Enable touch support detection
    enableTouchDetection: true,
    
    // Require multiple detection methods to confirm mobile device
    requireMultipleDetectionMethods: false,
  },
  
  // UI settings
  ui: {
    // Show retry button on mobile blocking screen
    showRetryButton: true,
    
    // Show technical details on mobile blocking screen
    showTechnicalDetails: true,
    
    // Show device type recommendations
    showDeviceRecommendations: true,
    
    // Custom blocking message (optional)
    customMessage: null as string | null,
  },
  
  // Logging settings
  logging: {
    // Log mobile access attempts
    logAccessAttempts: true,
    
    // Log device detection details
    logDetectionDetails: false,
    
    // Send mobile access logs to backend
    sendLogsToBackend: true,
  },
  
  // Development settings
  development: {
    // Disable mobile blocking in development mode
    disableInDevelopment: false,
    
    // Show mobile detection debug info
    showDebugInfo: false,
    
    // Allow override via URL parameter (?allowMobile=true)
    allowUrlOverride: true,
  },
  
  // Supported and blocked device types
  devices: {
    supported: ['Desktop', 'Laptop'],
    blocked: ['Mobile', 'Tablet', 'Smartphone'],
  },
  
  // Error messages
  messages: {
    mobileBlocked: 'Access from mobile devices is not allowed',
    tabletBlocked: 'Access from tablet devices is not allowed',
    desktopRequired: 'This HRMS application is designed for desktop/laptop computers only',
    contactSupport: 'For technical support, please contact your system administrator',
  },
};

// Helper function to check if mobile blocking should be bypassed
export const shouldBypassMobileBlocking = (): boolean => {
  // Check development settings
  if (MOBILE_BLOCKING_CONFIG.development.disableInDevelopment && process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // Check URL override
  if (MOBILE_BLOCKING_CONFIG.development.allowUrlOverride) {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('allowMobile') === 'true') {
      console.warn('Mobile blocking bypassed via URL parameter');
      return true;
    }
  }
  
  return false;
};

// Helper function to get effective mobile blocking configuration
export const getEffectiveMobileBlockingConfig = () => {
  return {
    ...MOBILE_BLOCKING_CONFIG,
    enabled: MOBILE_BLOCKING_CONFIG.enabled && !shouldBypassMobileBlocking(),
  };
};
