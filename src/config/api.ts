// API Configuration
export const API_CONFIG = {
  BASE_URL: 'http://localhost:5020/api/v1',
  TIMEOUT: 10000,
  HEADERS: {
    'Content-Type': 'application/json',
  },
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
  },
  
  // Health
  HEALTH: {
    CHECK: '/health',
    INFO: '/health/info',
  },
  
  // User Management
  USERS: {
    ME: '/users/me',
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    LIST: '/users',
    CREATE: '/users',
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
  },

  // Employee Management
  EMPLOYEE_MANAGEMENT: {
    LIST: '/employee-management/employees',
    CREATE: '/employee-management/employees',
    GET: (id: string) => `/employee-management/employees/${id}`,
    UPDATE: (id: string) => `/employee-management/employees/${id}`,
    DELETE: (id: string) => `/employee-management/employees/${id}`,
    HIERARCHY: '/employee-management/hierarchy',
    BY_MANAGER: (managerId: string) => `/employee-management/employees/by-manager/${managerId}`,
    BY_DEPARTMENT: (department: string) => `/employee-management/employees/by-department/${department}`,
    STATS: '/employee-management/stats',
    VALIDATE_HIERARCHY: '/employee-management/validate-hierarchy',
  },
  
  // Attendance
  ATTENDANCE: {
    CHECK_IN: '/attendance/checkin',
    CHECK_OUT: '/attendance/checkout',
    RECORDS: '/attendance/records',
    ORGANIZATION: '/attendance/organization',
  },
  
  // Leave Management
  LEAVE: {
    TYPES: '/leave/types',
    BALANCE: '/leave/balance',
    APPLY: '/leave/apply',
    REQUESTS: '/leave/requests',
    UPDATE_STATUS: (id: string) => `/leave/requests/${id}/status`,
  },
  
  // Task Management
  TASKS: {
    LIST: '/tasks',
    CREATE: '/tasks',
    UPDATE: (id: string) => `/tasks/${id}`,
    DELETE: (id: string) => `/tasks/${id}`,
    UPDATE_PROGRESS: (id: string) => `/tasks/${id}/progress`,
    COMMENTS: (id: string) => `/tasks/${id}/comments`,
  },

  // Employee Task Management
  EMPLOYEE_TASKS: {
    CREATE_SELF: '/employee/tasks/self',
    LIST: '/employee/tasks',
    UPDATES: '/employee/tasks/updates',
    CAN_VIEW: (id: string) => `/employee/tasks/${id}/can-view`,
    CAN_EDIT: (id: string) => `/employee/tasks/${id}/can-edit`,
  },

  // Manager Task Visibility
  MANAGER_TASKS: {
    LIST: '/manager/tasks',
    UPDATES: '/manager/tasks/updates',
    EMPLOYEE_TASKS: (employeeId: string) => `/manager/tasks/employee/${employeeId}`,
    EMPLOYEE_UPDATES: (employeeId: string) => `/manager/tasks/employee/${employeeId}/updates`,
  },
  
  // Performance
  PERFORMANCE: {
    REVIEWS: '/performance/reviews',
    CREATE_REVIEW: '/performance/reviews',
  },
  
  // Payroll
  PAYROLL: {
    EMPLOYEE: '/payroll/employee',
    BENEFITS: '/payroll/benefits',
  },
  
  // Recruitment
  RECRUITMENT: {
    JOBS: '/recruitment/jobs',
    APPLICATIONS: (jobId: string) => `/recruitment/jobs/${jobId}/applications`,
  },
  
  // Organization Admin
  ORG_ADMIN: {
    DASHBOARD: '/organization-admin/dashboard',
    BILLING: '/organization-admin/billing',
    LEAVE_REQUESTS: '/organization-admin/leave/requests',
    LEAVE_STATISTICS: '/organization-admin/leave/statistics',
    APPROVE_REJECT_LEAVE: (id: string) => `/organization-admin/leave/requests/${id}/approve-reject`,
    TASKS: '/organization-admin/tasks',
    TASK_STATISTICS: '/organization-admin/tasks/statistics',
    ASSIGN_TASK: '/organization-admin/tasks/assign',
    TASK_UPDATES: '/organization-admin/tasks/updates',
    UPDATE_TASK_STATUS: (id: string) => `/organization-admin/tasks/${id}/status`,
    // Employee dashboard access for admins
    EMPLOYEE_DASHBOARD: (employeeId: string) => `/organization-admin/employees/${employeeId}/dashboard`,
    EMPLOYEE_PROFILE: (employeeId: string) => `/organization-admin/employees/${employeeId}/profile`,
    EMPLOYEE_ATTENDANCE: (employeeId: string) => `/organization-admin/employees/${employeeId}/attendance`,
  },

  // Dashboard
  DASHBOARD: {
    SUPER_ADMIN: '/dashboard/super-admin',
    ORG_ADMIN: '/dashboard/organization-admin',
    EMPLOYEE: '/dashboard/employee',
    STATS: '/dashboard/stats',
  },

  // Super Admin
  SUPER_ADMIN: {
    DASHBOARD: '/dashboard/super-admin',
    ORGANIZATIONS: '/super-admin/organizations',
    CREATE_ORGANIZATION: '/super-admin/organizations',
    ORGANIZATION_DETAILS: (id: string) => `/super-admin/organizations/${id}`,
    ORGANIZATION_ADMIN_CREDENTIALS: (id: string) => `/super-admin/organizations/${id}/admin-credentials`,
    RESET_ORGANIZATION_ADMIN_PASSWORD: (id: string) => `/super-admin/organizations/${id}/reset-admin-password`,
    UPDATE_ORGANIZATION: (id: string) => `/super-admin/organizations/${id}`,
    UPDATE_ORGANIZATION_STATUS: (id: string) => `/super-admin/organizations/${id}/status`,
    DELETE_ORGANIZATION: (id: string) => `/super-admin/organizations/${id}`,
    DELETE_ORGANIZATION_BY_NAME: (name: string) => `/super-admin/organizations/by-name/${encodeURIComponent(name)}`,
    ANALYTICS: '/super-admin/analytics',
    SETTINGS: '/super-admin/settings',
  },

};

// Helper function to build full URL
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};
