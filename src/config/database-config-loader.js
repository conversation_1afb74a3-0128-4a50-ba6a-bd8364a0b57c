/**
 * Database Configuration Loader
 * Handles loading and validation of database configuration from YAML/JSON files
 * with environment variable substitution and validation
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

class DatabaseConfigLoader {
  constructor() {
    this.config = null;
    this.environment = process.env.NODE_ENV || 'development';
    this.configPath = process.env.DB_CONFIG_PATH || path.join(__dirname, '../../config');
  }

  /**
   * Load configuration from file
   * @param {string} configFile - Optional config file name
   * @returns {Object} Loaded configuration
   */
  loadConfig(configFile = null) {
    try {
      const configFileName = configFile || this.detectConfigFile();
      const fullPath = path.join(this.configPath, configFileName);
      
      if (!fs.existsSync(fullPath)) {
        throw new Error(`Configuration file not found: ${fullPath}`);
      }

      const fileContent = fs.readFileSync(fullPath, 'utf8');
      
      // Parse based on file extension
      if (configFileName.endsWith('.yaml') || configFileName.endsWith('.yml')) {
        this.config = yaml.load(fileContent);
      } else if (configFileName.endsWith('.json')) {
        this.config = JSON.parse(fileContent);
      } else {
        throw new Error(`Unsupported configuration file format: ${configFileName}`);
      }

      // Substitute environment variables
      this.config = this.substituteEnvironmentVariables(this.config);
      
      // Validate configuration
      this.validateConfig();
      
      console.log(`Database configuration loaded from: ${fullPath}`);
      return this.config;
      
    } catch (error) {
      console.error('Failed to load database configuration:', error.message);
      throw error;
    }
  }

  /**
   * Detect available configuration file
   * @returns {string} Configuration file name
   */
  detectConfigFile() {
    const possibleFiles = [
      'database-config.yaml',
      'database-config.yml',
      'database-config.json'
    ];

    for (const file of possibleFiles) {
      if (fs.existsSync(path.join(this.configPath, file))) {
        return file;
      }
    }

    throw new Error('No database configuration file found. Expected: database-config.yaml or database-config.json');
  }

  /**
   * Substitute environment variables in configuration
   * @param {Object} obj - Configuration object
   * @returns {Object} Configuration with substituted values
   */
  substituteEnvironmentVariables(obj) {
    if (typeof obj === 'string') {
      // Replace ${VAR_NAME} with environment variable value
      return obj.replace(/\$\{([^}]+)\}/g, (match, varName) => {
        const value = process.env[varName];
        if (value === undefined) {
          console.warn(`Environment variable ${varName} is not set, using placeholder`);
          return match; // Keep placeholder if env var not found
        }
        return value;
      });
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.substituteEnvironmentVariables(item));
    }
    
    if (obj && typeof obj === 'object') {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = this.substituteEnvironmentVariables(value);
      }
      return result;
    }
    
    return obj;
  }

  /**
   * Validate configuration structure and required fields
   */
  validateConfig() {
    if (!this.config) {
      throw new Error('Configuration is null or undefined');
    }

    // Validate required top-level sections
    const requiredSections = ['environments', 'schema_naming', 'schema_template'];
    for (const section of requiredSections) {
      if (!this.config[section]) {
        throw new Error(`Missing required configuration section: ${section}`);
      }
    }

    // Validate environment configuration
    if (!this.config.environments[this.environment]) {
      throw new Error(`Configuration for environment '${this.environment}' not found`);
    }

    const envConfig = this.config.environments[this.environment];
    
    // Validate database connection settings
    const requiredDbFields = ['host', 'port', 'database', 'username', 'password'];
    for (const field of requiredDbFields) {
      if (!envConfig.master_db[field]) {
        throw new Error(`Missing required database field: master_db.${field}`);
      }
    }

    // Validate schema naming pattern
    if (!this.config.schema_naming.pattern) {
      throw new Error('Missing schema naming pattern');
    }

    // Validate migration files exist
    if (this.config.schema_template.migration_files) {
      for (const file of this.config.schema_template.migration_files) {
        const filePath = path.join(this.configPath, '..', file);
        if (!fs.existsSync(filePath)) {
          console.warn(`Migration file not found: ${filePath}`);
        }
      }
    }

    console.log('Database configuration validation passed');
  }

  /**
   * Get configuration for current environment
   * @returns {Object} Environment-specific configuration
   */
  getEnvironmentConfig() {
    if (!this.config) {
      this.loadConfig();
    }
    return this.config.environments[this.environment];
  }

  /**
   * Get database connection configuration
   * @returns {Object} Database connection settings
   */
  getDatabaseConfig() {
    const envConfig = this.getEnvironmentConfig();
    return envConfig.master_db;
  }

  /**
   * Get schema provisioning configuration
   * @returns {Object} Provisioning settings
   */
  getProvisioningConfig() {
    const envConfig = this.getEnvironmentConfig();
    return {
      ...envConfig.provisioning,
      ...this.config.schema_naming,
      ...this.config.schema_template
    };
  }

  /**
   * Get security configuration
   * @returns {Object} Security settings
   */
  getSecurityConfig() {
    return this.config.security || {};
  }

  /**
   * Get monitoring configuration
   * @returns {Object} Monitoring settings
   */
  getMonitoringConfig() {
    return this.config.monitoring || {};
  }

  /**
   * Get feature flags
   * @returns {Object} Feature flag settings
   */
  getFeatureFlags() {
    return this.config.features || {};
  }

  /**
   * Get validation rules
   * @returns {Object} Validation settings
   */
  getValidationConfig() {
    return this.config.validation || {};
  }

  /**
   * Get error handling configuration
   * @returns {Object} Error handling settings
   */
  getErrorHandlingConfig() {
    return this.config.error_handling || {};
  }

  /**
   * Generate schema name for organization
   * @param {Object} organization - Organization data
   * @returns {string} Generated schema name
   */
  generateSchemaName(organization) {
    const namingConfig = this.config.schema_naming;
    const envConfig = this.getEnvironmentConfig();
    
    let schemaName = namingConfig.pattern;
    
    // Replace placeholders
    const replacements = {
      '{schema_prefix}': envConfig.provisioning.schema_prefix || '',
      '{schema_suffix}': envConfig.provisioning.schema_suffix || '',
      '{org_id}': organization.id,
      '{org_domain}': this.cleanString(organization.domain),
      '{org_name_clean}': this.cleanString(organization.name)
    };

    for (const [placeholder, value] of Object.entries(replacements)) {
      schemaName = schemaName.replace(placeholder, value);
    }

    // Apply character replacements
    if (namingConfig.char_replacements) {
      for (const [from, to] of Object.entries(namingConfig.char_replacements)) {
        schemaName = schemaName.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      }
    }

    // Ensure schema name meets requirements
    schemaName = schemaName.toLowerCase();
    schemaName = schemaName.replace(/[^a-z0-9_]/g, '_');
    
    // Truncate if too long
    if (schemaName.length > namingConfig.max_length) {
      schemaName = schemaName.substring(0, namingConfig.max_length);
    }

    // Validate against reserved names
    const validation = this.getValidationConfig();
    if (validation.schema_validation && validation.schema_validation.reserved_names) {
      if (validation.schema_validation.reserved_names.includes(schemaName)) {
        throw new Error(`Schema name '${schemaName}' is reserved`);
      }
    }

    return schemaName;
  }

  /**
   * Clean string for use in schema names
   * @param {string} str - String to clean
   * @returns {string} Cleaned string
   */
  cleanString(str) {
    if (!str) return '';
    return str.toLowerCase()
              .replace(/[^a-z0-9]/g, '_')
              .replace(/_+/g, '_')
              .replace(/^_|_$/g, '');
  }

  /**
   * Reload configuration (useful for development)
   */
  reloadConfig() {
    this.config = null;
    return this.loadConfig();
  }

  /**
   * Get current environment
   * @returns {string} Current environment name
   */
  getCurrentEnvironment() {
    return this.environment;
  }

  /**
   * Set environment (useful for testing)
   * @param {string} env - Environment name
   */
  setEnvironment(env) {
    this.environment = env;
    this.config = null; // Force reload
  }
}

module.exports = DatabaseConfigLoader;
