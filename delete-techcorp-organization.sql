-- <PERSON>ript to completely delete TechCorp Solutions organization and all associated data
-- This script ensures complete removal while maintaining referential integrity

USE dbHRMS;
GO

-- Start transaction for atomic operation
BEGIN TRANSACTION;

DECLARE @OrganizationId UNIQUEIDENTIFIER = '67DD72CC-5D99-4065-BA4A-0F2EE36443AE';
DECLARE @OrganizationName NVARCHAR(255) = 'TechCorp Solutions';

-- Verify the organization exists
IF NOT EXISTS (SELECT 1 FROM Organizations WHERE Id = @OrganizationId AND Name = @OrganizationName)
BEGIN
    PRINT 'Organization "' + @OrganizationName + '" with specified ID not found.';
    ROLL<PERSON>CK TRANSACTION;
    RETURN;
END

PRINT 'Found organization: ' + @OrganizationName + ' (ID: ' + CAST(@OrganizationId AS NVARCHAR(50)) + ')';

-- Get all users in this organization
DECLARE @UserIds TABLE (UserId UNIQUEIDENTIFIER);
INSERT INTO @UserIds (UserId)
SELECT Id FROM Users WHERE OrganizationId = @OrganizationId;

DECLARE @UserCount INT = (SELECT COUNT(*) FROM @UserIds);
PRINT 'Found ' + CAST(@UserCount AS NVARCHAR(10)) + ' users in this organization';

-- Display users to be deleted
SELECT 'Users to be deleted:' AS Info, Name, Email, Role FROM Users WHERE OrganizationId = @OrganizationId;

-- Step 1: Delete from tenant-specific tables
-- Based on the current database structure, data is stored in the main database with OrganizationId filtering

PRINT 'Deleting organization-specific data...';

-- Delete attendance records for users in this organization
DECLARE @AttendanceCount INT;
SELECT @AttendanceCount = COUNT(*) FROM Attendance WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM Attendance WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@AttendanceCount AS NVARCHAR(10)) + ' attendance records';

-- Delete leave requests for users in this organization
DECLARE @LeaveRequestCount INT;
SELECT @LeaveRequestCount = COUNT(*) FROM LeaveRequests WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM LeaveRequests WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@LeaveRequestCount AS NVARCHAR(10)) + ' leave requests';

-- Delete leave balances for users in this organization
DECLARE @LeaveBalanceCount INT;
SELECT @LeaveBalanceCount = COUNT(*) FROM LeaveBalances WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM LeaveBalances WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@LeaveBalanceCount AS NVARCHAR(10)) + ' leave balances';

-- Delete monthly leave allowances for users in this organization
DECLARE @MonthlyAllowanceCount INT;
SELECT @MonthlyAllowanceCount = COUNT(*) FROM MonthlyLeaveAllowances WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM MonthlyLeaveAllowances WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@MonthlyAllowanceCount AS NVARCHAR(10)) + ' monthly leave allowances';

-- Delete monthly leave usage for users in this organization (via MonthlyLeaveAllowances)
DECLARE @MonthlyUsageCount INT;
SELECT @MonthlyUsageCount = COUNT(*)
FROM MonthlyLeaveUsages mlu
INNER JOIN MonthlyLeaveAllowances mla ON mlu.MonthlyLeaveAllowanceId = mla.Id
WHERE mla.UserId IN (SELECT UserId FROM @UserIds);

DELETE mlu
FROM MonthlyLeaveUsages mlu
INNER JOIN MonthlyLeaveAllowances mla ON mlu.MonthlyLeaveAllowanceId = mla.Id
WHERE mla.UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@MonthlyUsageCount AS NVARCHAR(10)) + ' monthly leave usage records';

-- Delete task updates first (foreign key dependency)
DECLARE @TaskUpdateCount INT;
SELECT @TaskUpdateCount = COUNT(*) FROM TaskUpdates WHERE UpdatedBy IN (SELECT UserId FROM @UserIds);
DELETE FROM TaskUpdates WHERE UpdatedBy IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@TaskUpdateCount AS NVARCHAR(10)) + ' task updates';

-- Delete tasks
DECLARE @TaskCount INT;
SELECT @TaskCount = COUNT(*) FROM Tasks WHERE AssignedToId IN (SELECT UserId FROM @UserIds) OR CreatedBy IN (SELECT UserId FROM @UserIds);
DELETE FROM Tasks WHERE AssignedToId IN (SELECT UserId FROM @UserIds) OR CreatedBy IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@TaskCount AS NVARCHAR(10)) + ' tasks';

-- Delete performance reviews
DECLARE @PerformanceCount INT;
SELECT @PerformanceCount = COUNT(*) FROM PerformanceReviews WHERE EmployeeId IN (SELECT UserId FROM @UserIds) OR ReviewerId IN (SELECT UserId FROM @UserIds);
DELETE FROM PerformanceReviews WHERE EmployeeId IN (SELECT UserId FROM @UserIds) OR ReviewerId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@PerformanceCount AS NVARCHAR(10)) + ' performance reviews';

-- Delete employee details
DECLARE @EmployeeDetailCount INT;
SELECT @EmployeeDetailCount = COUNT(*) FROM EmployeeDetails WHERE UserId IN (SELECT UserId FROM @UserIds);
DELETE FROM EmployeeDetails WHERE UserId IN (SELECT UserId FROM @UserIds);
PRINT 'Deleted ' + CAST(@EmployeeDetailCount AS NVARCHAR(10)) + ' employee detail records';

-- Step 2: Delete from master database tables

-- Delete leave types for this organization
DECLARE @LeaveTypeCount INT;
SELECT @LeaveTypeCount = COUNT(*) FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
DELETE FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
PRINT 'Deleted ' + CAST(@LeaveTypeCount AS NVARCHAR(10)) + ' organization leave types';

-- Delete users from this organization
DELETE FROM Users WHERE OrganizationId = @OrganizationId;
PRINT 'Deleted ' + CAST(@UserCount AS NVARCHAR(10)) + ' users';

-- Step 3: Delete the organization itself
DELETE FROM Organizations WHERE Id = @OrganizationId;
PRINT 'Deleted organization: ' + @OrganizationName;

-- Commit the transaction
COMMIT TRANSACTION;

PRINT '';
PRINT 'SUCCESS: Organization "' + @OrganizationName + '" and all associated data deleted successfully!';
PRINT '========================================================================';
PRINT 'Summary of deleted data:';
PRINT '- Organization: ' + @OrganizationName;
PRINT '- Users: ' + CAST(@UserCount AS NVARCHAR(10));
PRINT '- Attendance records: ' + CAST(@AttendanceCount AS NVARCHAR(10));
PRINT '- Leave requests: ' + CAST(@LeaveRequestCount AS NVARCHAR(10));
PRINT '- Leave balances: ' + CAST(@LeaveBalanceCount AS NVARCHAR(10));
PRINT '- Monthly leave allowances: ' + CAST(@MonthlyAllowanceCount AS NVARCHAR(10));
PRINT '- Monthly leave usage: ' + CAST(@MonthlyUsageCount AS NVARCHAR(10));
PRINT '- Task updates: ' + CAST(@TaskUpdateCount AS NVARCHAR(10));
PRINT '- Tasks: ' + CAST(@TaskCount AS NVARCHAR(10));
PRINT '- Performance reviews: ' + CAST(@PerformanceCount AS NVARCHAR(10));
PRINT '- Employee details: ' + CAST(@EmployeeDetailCount AS NVARCHAR(10));
PRINT '- Leave types: ' + CAST(@LeaveTypeCount AS NVARCHAR(10));

-- Verify deletion
IF NOT EXISTS (SELECT 1 FROM Organizations WHERE Id = @OrganizationId)
BEGIN
    PRINT '';
    PRINT 'VERIFICATION PASSED: Organization successfully removed from database';
    PRINT 'TechCorp Solutions and all its data has been completely deleted.';
END
ELSE
BEGIN
    PRINT '';
    PRINT 'ERROR: Organization still exists in database - deletion may have failed';
END

-- Show remaining organizations
PRINT '';
PRINT 'Remaining organizations in database:';
SELECT Name, Domain, EmployeeCount, Status FROM Organizations ORDER BY Name;
