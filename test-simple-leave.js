// Simple test to debug the leave application issue
const API_BASE = 'http://localhost:5020/api/v1';

const EMPLOYEE_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'ChangedPassword123!'
};

async function testLeaveApplication() {
  console.log('🧪 Testing Leave Application API');
  console.log('=' .repeat(50));
  
  // Step 1: Login
  console.log('1️⃣ Logging in...');
  const loginResponse = await fetch(`${API_BASE}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(EMPLOYEE_CREDENTIALS)
  });
  
  const loginData = await loginResponse.json();
  if (!loginResponse.ok || !loginData.success) {
    console.log('❌ Login failed:', loginData);
    return;
  }
  
  const token = loginData.data.token;
  console.log('✅ Login successful');
  
  // Step 2: Get leave types
  console.log('\n2️⃣ Getting leave types...');
  const typesResponse = await fetch(`${API_BASE}/leave/types`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const typesData = await typesResponse.json();
  if (!typesResponse.ok || !typesData.success) {
    console.log('❌ Failed to get leave types:', typesData);
    return;
  }
  
  console.log('📊 Leave types response:', JSON.stringify(typesData, null, 2));

  const leaveTypes = typesData.data.types || typesData.data.leaveTypes || typesData.data || [];
  console.log('✅ Leave types fetched:', leaveTypes.length);

  if (leaveTypes.length === 0) {
    console.log('❌ No leave types found');
    return;
  }

  const firstLeaveType = leaveTypes[0];
  console.log(`   Using: ${firstLeaveType.name} (${firstLeaveType.id})`);
  
  // Step 3: Test leave application with different approaches
  console.log('\n3️⃣ Testing leave application...');
  
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const dayAfter = new Date();
  dayAfter.setDate(dayAfter.getDate() + 2);
  
  const leaveRequest = {
    LeaveTypeId: firstLeaveType.id,
    FromDate: tomorrow.toISOString(),
    ToDate: dayAfter.toISOString(),
    DurationType: 'full-day',
    Reason: 'Test leave application'
  };
  
  console.log('📋 Request payload:', JSON.stringify(leaveRequest, null, 2));
  
  // Test 1: Standard request
  console.log('\n🔬 Test 1: Standard request');
  const applyResponse = await fetch(`${API_BASE}/leave/apply`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(leaveRequest)
  });
  
  console.log(`   Status: ${applyResponse.status}`);
  console.log(`   Status Text: ${applyResponse.statusText}`);
  console.log(`   Headers:`, Object.fromEntries(applyResponse.headers.entries()));
  
  const responseText = await applyResponse.text();
  console.log(`   Response: ${responseText}`);
  
  if (applyResponse.ok) {
    console.log('✅ Leave application successful!');
  } else {
    console.log('❌ Leave application failed');
    
    // Test 2: Try with different Content-Type
    console.log('\n🔬 Test 2: Without explicit Content-Type');
    const applyResponse2 = await fetch(`${API_BASE}/leave/apply`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(leaveRequest)
    });
    
    console.log(`   Status: ${applyResponse2.status}`);
    const responseText2 = await applyResponse2.text();
    console.log(`   Response: ${responseText2}`);
    
    // Test 3: Try with form data
    console.log('\n🔬 Test 3: With application/x-www-form-urlencoded');
    const formData = new URLSearchParams();
    Object.entries(leaveRequest).forEach(([key, value]) => {
      formData.append(key, value.toString());
    });
    
    const applyResponse3 = await fetch(`${API_BASE}/leave/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });
    
    console.log(`   Status: ${applyResponse3.status}`);
    const responseText3 = await applyResponse3.text();
    console.log(`   Response: ${responseText3}`);
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Test completed');
}

testLeaveApplication().catch(console.error);
