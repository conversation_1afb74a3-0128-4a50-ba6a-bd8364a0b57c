// Fix employee organization assignment by moving them to correct organizations
const API_BASE = 'http://localhost:5020/api/v1';

async function fixEmployeeOrganizationAssignment() {
    console.log('🔧 Fixing Employee Organization Assignment...\n');

    try {
        // Step 1: Create Abhishek in Test Company (where he should be)
        console.log('1️⃣ Creating Abhishek in Test Company');
        console.log('-'.repeat(40));
        
        const testAdminLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        const testAdminData = await testAdminLoginResponse.json();
        const testAdminToken = testAdminData.data.token;
        const testCompanyOrgId = testAdminData.data.user.organization?.id;
        
        console.log(`Test Company Organization ID: ${testCompanyOrgId}`);

        // Create Abhishek in Test Company
        const createAbhishekResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${testAdminToken}`,
                'X-Organization-ID': testCompanyOrgId
            },
            body: JSON.stringify({
                name: 'Abhishek Test Company',
                email: '<EMAIL>',
                role: 'Employee',
                employeeDetails: {
                    employeeId: 'TC_ABHISHEK_001',
                    jobTitle: 'Software Developer',
                    department: 'Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 75000,
                    annualCTC: 90000
                }
            })
        });

        if (createAbhishekResponse.ok) {
            const abhishekData = await createAbhishekResponse.json();
            console.log(`✅ Abhishek created in Test Company`);
            console.log(`   Email: ${abhishekData.data.email}`);
            console.log(`   Password: ${abhishekData.data.loginTemporaryPassword}`);
            console.log(`   User ID: ${abhishekData.data.id}`);
        } else {
            const errorText = await createAbhishekResponse.text();
            console.log(`❌ Failed to create Abhishek in Test Company: ${errorText}`);
        }

        // Step 2: Verify Test Company now only sees its own employees
        console.log('\n2️⃣ Verifying Test Company Employee Isolation');
        console.log('-'.repeat(40));
        
        const testEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${testAdminToken}`,
                'X-Organization-ID': testCompanyOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (testEmployeesResponse.ok) {
            const testEmployeesData = await testEmployeesResponse.json();
            const employees = testEmployeesData.data?.items || testEmployeesData.data || [];
            console.log(`Test Company now sees ${employees.length} employees:`);
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                console.log(`      - User ID: ${emp.id}`);
                console.log(`      - Employee ID: ${emp.employeeDetails?.employeeId}`);
                console.log(`      - Organization: ${emp.organization?.name || 'N/A'}`);
            });
        }

        // Step 3: Verify Plan Square still sees its employees
        console.log('\n3️⃣ Verifying Plan Square Employee Isolation');
        console.log('-'.repeat(40));
        
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`Plan Square Organization ID: ${planSquareOrgId}`);

        const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': planSquareOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (planSquareEmployeesResponse.ok) {
            const planSquareEmployeesData = await planSquareEmployeesResponse.json();
            const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
            console.log(`Plan Square now sees ${employees.length} employees:`);
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                console.log(`      - User ID: ${emp.id}`);
                console.log(`      - Employee ID: ${emp.employeeDetails?.employeeId}`);
                console.log(`      - Organization: ${emp.organization?.name || 'N/A'}`);
            });
        }

        // Step 4: Test cross-organization access again
        console.log('\n4️⃣ Testing Cross-Organization Access (Should Fail)');
        console.log('-'.repeat(40));
        
        const crossAccessResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': testCompanyOrgId, // Wrong org ID
                'Content-Type': 'application/json'
            }
        });

        console.log(`Cross-access Response Status: ${crossAccessResponse.status}`);
        
        if (crossAccessResponse.ok) {
            const crossAccessData = await crossAccessResponse.json();
            const employees = crossAccessData.data?.items || crossAccessData.data || [];
            console.log(`❌ STILL BROKEN: Cross-access allowed! Employees: ${employees.length}`);
        } else {
            console.log(`✅ Cross-access properly blocked`);
        }

        console.log('\n5️⃣ Summary');
        console.log('-'.repeat(40));
        console.log('✅ Created Abhishek in Test Company');
        console.log('🔍 Multi-tenant isolation status: Need to verify');
        console.log('\n📝 Next Steps:');
        console.log('   1. Verify each organization only sees its own employees');
        console.log('   2. Ensure cross-organization access is blocked');
        console.log('   3. Test attendance functionality with proper isolation');

    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

// Run the fix
fixEmployeeOrganizationAssignment();
