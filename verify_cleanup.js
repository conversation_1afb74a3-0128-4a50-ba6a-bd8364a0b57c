// Post-cleanup verification script
const API_BASE = 'http://localhost:5020/api/v1';

async function verifyCleanup() {
    console.log('🔍 Verifying Test Company Cleanup...\n');
    
    try {
        // Test 1: PlanSquare admin should work
        console.log('1️⃣ Testing PlanSquare Admin Login');
        console.log('-'.repeat(30));
        
        const planSquareLogin = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });
        
        console.log(`PlanSquare admin login: ${planSquareLogin.ok ? '✅ SUCCESS' : '❌ FAILED'}`);
        
        // Test 2: Test Company admin should fail
        console.log('\n2️⃣ Testing Test Company Admin Login (Should Fail)');
        console.log('-'.repeat(30));
        
        const testCompanyLogin = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });
        
        console.log(`Test Company admin login: ${testCompanyLogin.ok ? '❌ STILL WORKS (CLEANUP INCOMPLETE)' : '✅ BLOCKED (CLEANUP SUCCESS)'}`);
        
        // Test 3: Check employee isolation
        console.log('\n3️⃣ Testing Employee Data Isolation');
        console.log('-'.repeat(30));
        
        if (planSquareLogin.ok) {
            const planSquareData = await planSquareLogin.json();
            const token = planSquareData.data.token;
            const orgId = planSquareData.data.user.organization?.id;
            
            console.log(`PlanSquare Organization ID: ${orgId}`);
            
            const employeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'X-Organization-ID': orgId,
                    'Content-Type': 'application/json'
                }
            });
            
            if (employeesResponse.ok) {
                const employeesData = await employeesResponse.json();
                const employees = employeesData.data?.items || employeesData.data || [];
                console.log(`PlanSquare sees ${employees.length} employees:`);
                
                let hasTestCompanyEmployees = false;
                let planSquareEmployees = [];
                
                employees.forEach((emp, index) => {
                    console.log(`   ${index + 1}. ${emp.name} (${emp.email})`);
                    console.log(`      - User ID: ${emp.id}`);
                    console.log(`      - Employee ID: ${emp.employeeDetails?.employeeId}`);
                    console.log(`      - Department: ${emp.employeeDetails?.department}`);
                    
                    if (emp.email.includes('testcompany.com')) {
                        hasTestCompanyEmployees = true;
                    } else if (emp.email.includes('plan2intl.com')) {
                        planSquareEmployees.push(emp);
                    }
                });
                
                console.log(`\n📊 Employee Analysis:`);
                console.log(`   - Total employees visible: ${employees.length}`);
                console.log(`   - PlanSquare employees: ${planSquareEmployees.length}`);
                console.log(`   - Contains Test Company employees: ${hasTestCompanyEmployees ? '❌ YES (CLEANUP INCOMPLETE)' : '✅ NO (CLEANUP SUCCESS)'}`);
                
                // Test 4: Verify specific PlanSquare employees exist
                console.log('\n4️⃣ Verifying Expected PlanSquare Employees');
                console.log('-'.repeat(30));
                
                const expectedEmployees = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>'
                ];
                
                expectedEmployees.forEach(email => {
                    const found = employees.find(emp => emp.email === email);
                    console.log(`   ${email}: ${found ? '✅ FOUND' : '❌ MISSING'}`);
                });
                
            } else {
                console.log('❌ Failed to get employee data');
            }
        }
        
        // Test 5: Test attendance data isolation
        console.log('\n5️⃣ Testing Attendance Data Isolation');
        console.log('-'.repeat(30));
        
        if (planSquareLogin.ok) {
            const planSquareData = await planSquareLogin.json();
            const token = planSquareData.data.token;
            const orgId = planSquareData.data.user.organization?.id;
            
            const attendanceResponse = await fetch(`${API_BASE}/attendance/records`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'X-Organization-ID': orgId,
                    'Content-Type': 'application/json'
                }
            });
            
            if (attendanceResponse.ok) {
                const attendanceData = await attendanceResponse.json();
                const records = attendanceData.data?.records || [];
                console.log(`PlanSquare attendance records: ${records.length}`);
                console.log(`Attendance summary: ${JSON.stringify(attendanceData.data?.summary || {}, null, 2)}`);
            } else {
                console.log('❌ Failed to get attendance data');
            }
        }
        
        console.log('\n6️⃣ CLEANUP VERIFICATION SUMMARY');
        console.log('-'.repeat(30));
        console.log('✅ Verification completed!');
        console.log('\n🎯 Expected Results After Successful Cleanup:');
        console.log('   - PlanSquare admin login: ✅ SUCCESS');
        console.log('   - Test Company admin login: ✅ BLOCKED');
        console.log('   - PlanSquare employees only: ✅ NO TEST COMPANY DATA');
        console.log('   - Multi-tenant isolation: ✅ WORKING');
        
    } catch (error) {
        console.error('❌ Verification failed:', error.message);
    }
}

// Run the verification
verifyCleanup();
