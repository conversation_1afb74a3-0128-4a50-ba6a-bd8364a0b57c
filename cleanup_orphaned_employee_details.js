// Clean up orphaned employee detail records (where user is deleted but employee detail remains)
import sql from 'mssql';

const config = {
    server: '103.145.50.203',
    port: 2829,
    database: 'dbHRMS',
    user: 'userHRMS',
    password: 'P@ssw0rd123',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectTimeout: 30000,
        requestTimeout: 30000
    }
};

async function cleanupOrphanedEmployeeDetails() {
    console.log('🧹 Cleaning up Orphaned Employee Detail Records...\n');
    console.log('=' .repeat(60));

    try {
        // Connect to database
        console.log('\n1️⃣ CONNECTING TO DATABASE');
        console.log('-'.repeat(30));
        
        const pool = await sql.connect(config);
        console.log('✅ Connected to SQL Server database');

        // Step 1: Find PlanSquare organization
        const orgQuery = `
            SELECT Id, Name, Domain
            FROM Organizations 
            WHERE Name LIKE '%Plan%' OR Domain LIKE '%plan%'
        `;
        
        const orgResult = await pool.request().query(orgQuery);
        const planSquareOrgId = orgResult.recordset[0]?.Id;
        
        if (!planSquareOrgId) {
            console.log('❌ PlanSquare organization not found');
            return;
        }

        console.log(`✅ Found PlanSquare Organization ID: ${planSquareOrgId}`);

        // Step 2: Find orphaned employee details (where user is deleted)
        console.log('\n2️⃣ FINDING ORPHANED EMPLOYEE DETAILS');
        console.log('-'.repeat(30));
        
        const orphanedQuery = `
            SELECT ed.Id, ed.UserId, ed.EmployeeId, ed.JobTitle, ed.Department,
                   u.Name, u.Email, u.IsDeleted as UserDeleted, u.OrganizationId
            FROM EmployeeDetails ed
            LEFT JOIN Users u ON ed.UserId = u.Id
            WHERE u.IsDeleted = 1 OR u.Id IS NULL
            ORDER BY ed.CreatedAt DESC
        `;
        
        const orphanedResult = await pool.request().query(orphanedQuery);
        console.log(`📊 Found ${orphanedResult.recordset.length} orphaned employee detail records:`);
        
        let planSquareOrphans = [];
        let otherOrphans = [];
        
        orphanedResult.recordset.forEach((emp, index) => {
            console.log(`   ${index + 1}. ${emp.Name || 'N/A'} (${emp.Email || 'N/A'}) - Employee ID: ${emp.EmployeeId || 'N/A'}`);
            console.log(`      User ID: ${emp.UserId}, Org: ${emp.OrganizationId || 'N/A'}`);
            
            if (emp.OrganizationId === planSquareOrgId) {
                planSquareOrphans.push(emp);
            } else {
                otherOrphans.push(emp);
            }
        });

        console.log(`\n📊 PlanSquare orphaned records: ${planSquareOrphans.length}`);
        console.log(`📊 Other organization orphaned records: ${otherOrphans.length}`);

        // Step 3: Delete orphaned employee details from other organizations
        console.log('\n3️⃣ CLEANING UP OTHER ORGANIZATION ORPHANS');
        console.log('-'.repeat(30));
        
        if (otherOrphans.length > 0) {
            for (const emp of otherOrphans) {
                try {
                    console.log(`   Deleting: ${emp.Name || 'N/A'} (Employee ID: ${emp.EmployeeId || 'N/A'})`);
                    
                    const deleteQuery = `DELETE FROM EmployeeDetails WHERE Id = '${emp.Id}'`;
                    await pool.request().query(deleteQuery);
                    console.log(`   ✅ Deleted orphaned employee detail record`);
                } catch (error) {
                    console.log(`   ❌ Failed to delete: ${error.message}`);
                }
            }
        } else {
            console.log('✅ No orphaned records from other organizations found');
        }

        // Step 4: Delete orphaned employee details from PlanSquare (except current active users)
        console.log('\n4️⃣ CLEANING UP PLANSQUARE ORPHANS');
        console.log('-'.repeat(30));
        
        // First, let's check which PlanSquare users are actually active
        const activePlanSquareUsersQuery = `
            SELECT Id, Name, Email, IsDeleted
            FROM Users 
            WHERE OrganizationId = '${planSquareOrgId}' AND IsDeleted = 0
        `;
        
        const activeUsersResult = await pool.request().query(activePlanSquareUsersQuery);
        console.log(`📊 Active PlanSquare users: ${activeUsersResult.recordset.length}`);
        
        activeUsersResult.recordset.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.Name} (${user.Email}) - ID: ${user.Id}`);
        });

        const activeUserIds = activeUsersResult.recordset.map(u => u.Id);
        
        if (planSquareOrphans.length > 0) {
            for (const emp of planSquareOrphans) {
                try {
                    console.log(`   Deleting orphaned PlanSquare record: ${emp.Name || 'N/A'} (Employee ID: ${emp.EmployeeId || 'N/A'})`);
                    
                    const deleteQuery = `DELETE FROM EmployeeDetails WHERE Id = '${emp.Id}'`;
                    await pool.request().query(deleteQuery);
                    console.log(`   ✅ Deleted orphaned PlanSquare employee detail record`);
                } catch (error) {
                    console.log(`   ❌ Failed to delete: ${error.message}`);
                }
            }
        } else {
            console.log('✅ No orphaned PlanSquare records found');
        }

        // Step 5: Verify final state
        console.log('\n5️⃣ VERIFYING FINAL STATE');
        console.log('-'.repeat(30));
        
        const finalPlanSquareQuery = `
            SELECT ed.Id, ed.UserId, ed.EmployeeId, ed.JobTitle, ed.Department,
                   u.Name, u.Email, u.IsDeleted as UserDeleted
            FROM EmployeeDetails ed
            INNER JOIN Users u ON ed.UserId = u.Id
            WHERE u.OrganizationId = '${planSquareOrgId}' AND u.IsDeleted = 0
            ORDER BY ed.CreatedAt
        `;
        
        const finalResult = await pool.request().query(finalPlanSquareQuery);
        console.log(`📊 Final PlanSquare employee details: ${finalResult.recordset.length}`);
        
        finalResult.recordset.forEach((emp, index) => {
            console.log(`   ${index + 1}. ${emp.Name} (${emp.Email}) - Employee ID: ${emp.EmployeeId || 'N/A'}`);
            console.log(`      Job: ${emp.JobTitle || 'N/A'}, Dept: ${emp.Department || 'N/A'}`);
        });

        // Step 6: Check total employee details count
        const totalCountQuery = `SELECT COUNT(*) as TotalCount FROM EmployeeDetails`;
        const totalCountResult = await pool.request().query(totalCountQuery);
        console.log(`\n📊 Total employee details in database: ${totalCountResult.recordset[0].TotalCount}`);

        await pool.close();
        console.log('\n✅ Database connection closed');

        console.log('\n' + '='.repeat(60));
        console.log('🏁 ORPHANED RECORDS CLEANUP COMPLETED');
        console.log('✅ Database cleaned up successfully');
        console.log('🚀 Ready to try creating Priya again');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('\n❌ CLEANUP FAILED');
        console.error('Error:', error.message);
        console.error('\nPlease check the database connection and try again.');
    }
}

// Run the cleanup
cleanupOrphanedEmployeeDetails();
