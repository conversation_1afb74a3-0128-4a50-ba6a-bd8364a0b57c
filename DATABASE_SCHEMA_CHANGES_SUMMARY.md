# Database Schema Changes Summary - Leave Balance Removal

## Overview
This document outlines the database schema changes required to remove the leave balance/allocation functionality from the HRMS system while preserving core leave management features.

## Current Database State

### Tables That REMAIN (Core Functionality):
✅ **LeaveTypes** - Leave type definitions per organization
✅ **LeaveRequests** - Leave applications and approval workflow
✅ **Users** - User accounts and authentication
✅ **Organizations** - Organization data
✅ **EmployeeDetails** - Employee information
✅ **AttendanceRecords** - Attendance tracking

### Tables That Should Be REMOVED (Balance Functionality):
❌ **LeaveBalances** - Employee leave balances by type and year
❌ **MonthlyLeaveAllowances** - Monthly leave allowance tracking
❌ **MonthlyLeaveUsages** - Usage records against monthly allowances

## Required Migration

### Migration File: `RemoveLeaveBalanceTables.sql`
Location: `backend/HRMS.Infrastructure/Migrations/RemoveLeaveBalanceTables.sql`

**Purpose**: Remove balance-related tables while preserving core leave functionality

**Actions**:
1. Drop foreign key constraints
2. Drop indexes
3. Drop tables: MonthlyLeaveUsages, MonthlyLeaveAllowances, LeaveBalances
4. Verify core tables (LeaveRequests, LeaveTypes) remain intact
5. Add documentation comments

### Code Changes Made:

#### 1. DbContext Updates (`HRMSDbContext.cs`):
- ✅ Removed `DbSet<LeaveBalance>` 
- ✅ Removed `DbSet<MonthlyLeaveAllowance>`
- ✅ Removed `DbSet<MonthlyLeaveUsage>`
- ✅ Removed `ConfigureMonthlyLeave()` method
- ✅ Preserved `DbSet<LeaveRequest>` and `DbSet<LeaveType>`

#### 2. Entity Classes:
- ✅ `MonthlyLeaveAllowance.cs` - Still exists but unused
- ✅ `MonthlyLeaveUsage.cs` - Still exists but unused
- ✅ `LeaveBalance.cs` - Still exists but unused
- ✅ `LeaveRequest.cs` - Active and functional
- ✅ `LeaveType.cs` - Active and functional

#### 3. Repository Interfaces:
- ✅ `IMonthlyLeaveAllowanceRepository` - Still exists but unused
- ✅ `IMonthlyLeaveUsageRepository` - Still exists but unused
- ✅ `ILeaveRepository` - Active and functional

## Database Schema After Migration

### Active Tables:
```sql
-- Core leave management (ACTIVE)
LeaveTypes (
    Id, OrganizationId, Name, Description, 
    MaxDaysPerYear, IsCarryForward, IsActive, ...
)

LeaveRequests (
    Id, UserId, LeaveTypeId, FromDate, ToDate, 
    TotalDays, Status, Reason, ApprovedBy, ...
)

-- User and organization data (ACTIVE)
Users, Organizations, EmployeeDetails, AttendanceRecords, ...
```

### Removed Tables:
```sql
-- Balance tracking (REMOVED)
-- LeaveBalances
-- MonthlyLeaveAllowances  
-- MonthlyLeaveUsages
```

## Impact Assessment

### ✅ What Still Works:
- Leave application submission
- Leave approval/rejection workflow
- Leave history viewing
- Leave type management
- Admin leave management dashboard
- Employee leave request forms
- All authentication and authorization
- Integration with other HRMS modules

### ❌ What No Longer Works:
- Leave balance display
- Balance validation during application
- Monthly allowance allocation
- Balance synchronization
- Leave balance APIs (return empty data)

## Migration Execution Steps

1. **Backup Database** (CRITICAL)
   ```sql
   -- Create full backup before migration
   BACKUP DATABASE [dbHRMS] TO DISK = 'backup_path'
   ```

2. **Run Migration**
   ```sql
   -- Execute the migration script
   EXEC sp_executesql @sql = 'RemoveLeaveBalanceTables.sql'
   ```

3. **Verify Core Functionality**
   ```sql
   -- Verify essential tables exist
   SELECT name FROM sys.tables WHERE name IN ('LeaveRequests', 'LeaveTypes', 'Users')
   ```

4. **Test Application**
   - Test leave application process
   - Test leave approval workflow
   - Verify dashboard functionality

## Rollback Plan

If rollback is needed:
1. Restore from backup
2. Re-enable balance-related services in code
3. Re-add DbSet references in DbContext
4. Re-run original migrations

## Verification Checklist

- [ ] Migration script executed successfully
- [ ] Core tables (LeaveRequests, LeaveTypes) intact
- [ ] Balance tables removed
- [ ] Application starts without errors
- [ ] Leave application works
- [ ] Leave approval works
- [ ] Dashboard displays correctly
- [ ] No balance-related errors in logs

## Notes

- Entity classes for removed tables are preserved for potential future use
- Repository interfaces preserved but not registered in DI
- API endpoints return empty balance data for backward compatibility
- Frontend components updated to not display balance information
- All existing leave request data preserved
