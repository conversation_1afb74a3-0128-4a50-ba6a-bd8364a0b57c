<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRMS Authentication Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .credentials {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .url-test {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🔐 HRMS Authentication Integration Test</h1>
    
    <div class="test-container">
        <h2>📋 Test Credentials</h2>
        <div class="credentials">
            <strong>Super Admin:</strong> <EMAIL> / Admin123!<br>
            <strong>Organization Admin:</strong> <EMAIL> / Admin123!<br>
            <strong>Employee:</strong> <EMAIL> / ChangedPassword123!
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 Authentication Tests</h2>
        <button onclick="testSuperAdminLogin()">Test Super Admin Login</button>
        <button onclick="testOrgAdminLogin()">Test Org Admin Login</button>
        <button onclick="testEmployeeLogin()">Test Employee Login</button>
        <button onclick="testInvalidLogin()">Test Invalid Login</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>🌐 Frontend URL Tests</h2>
        <p>After successful login, test these URLs in the main application:</p>
        <div class="url-test">
            <strong>Dashboard:</strong> <a href="http://localhost:8081/dashboard" target="_blank">http://localhost:8081/dashboard</a>
        </div>
        <div class="url-test">
            <strong>Super Admin - Organizations:</strong> <a href="http://localhost:8081/super-admin/organizations" target="_blank">http://localhost:8081/super-admin/organizations</a>
        </div>
        <div class="url-test">
            <strong>Org Admin - Employee Management:</strong> <a href="http://localhost:8081/org-admin/employee-management" target="_blank">http://localhost:8081/org-admin/employee-management</a>
        </div>
        <div class="url-test">
            <strong>Employee - Attendance:</strong> <a href="http://localhost:8081/employee/attendance" target="_blank">http://localhost:8081/employee/attendance</a>
        </div>
    </div>

    <div class="test-container">
        <h2>🚀 Quick Login Links</h2>
        <p>Click these to automatically log in and test the routing:</p>
        <button onclick="loginAndRedirect('<EMAIL>', 'Admin123!', '/super-admin/organizations')">Login as Super Admin</button>
        <button onclick="loginAndRedirect('<EMAIL>', 'Admin123!', '/org-admin/employee-management')">Login as Org Admin</button>
        <button onclick="loginAndRedirect('<EMAIL>', 'ChangedPassword123!', '/employee/attendance')">Login as Employee</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:5020/api/v1';
        const FRONTEND_BASE = 'http://localhost:8081';

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        async function testLogin(email, password, userType) {
            try {
                addResult(`🔄 Testing ${userType} login...`, 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    addResult(`✅ ${userType} login successful!<br>
                        👤 User: ${data.data.user.name}<br>
                        🎭 Role: ${data.data.user.role}<br>
                        🏢 Organization: ${data.data.user.organization?.name || 'System Level'}<br>
                        🔑 Token: ${data.data.token ? 'Generated' : 'Missing'}`, 'success');
                    
                    // Store token for frontend testing
                    localStorage.setItem('authToken', data.data.token);
                    if (data.data.user.organization?.id) {
                        localStorage.setItem('organizationId', data.data.user.organization.id);
                    }
                    
                    return data.data;
                } else {
                    addResult(`❌ ${userType} login failed: ${data.error?.message || 'Unknown error'}`, 'error');
                    return null;
                }
            } catch (error) {
                addResult(`❌ ${userType} login error: ${error.message}`, 'error');
                return null;
            }
        }

        async function testSuperAdminLogin() {
            await testLogin('<EMAIL>', 'Admin123!', 'Super Admin');
        }

        async function testOrgAdminLogin() {
            await testLogin('<EMAIL>', 'Admin123!', 'Organization Admin');
        }

        async function testEmployeeLogin() {
            await testLogin('<EMAIL>', 'ChangedPassword123!', 'Employee');
        }

        async function testInvalidLogin() {
            await testLogin('<EMAIL>', 'wrongpassword', 'Invalid User (should fail)');
        }

        async function loginAndRedirect(email, password, redirectPath) {
            const loginData = await testLogin(email, password, 'Auto Login');
            
            if (loginData) {
                addResult(`🚀 Redirecting to ${redirectPath}...`, 'info');
                setTimeout(() => {
                    window.open(`${FRONTEND_BASE}${redirectPath}`, '_blank');
                }, 1000);
            }
        }

        // Test API connectivity on page load
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE}/health/info`);
                if (response.ok) {
                    addResult('✅ Backend API is accessible', 'success');
                } else {
                    addResult('⚠️ Backend API health check failed (but API might still work)', 'info');
                }
            } catch (error) {
                addResult('❌ Cannot connect to backend API. Make sure it\'s running on http://localhost:5020', 'error');
            }

            // Check if frontend is accessible
            try {
                const frontendResponse = await fetch(`${FRONTEND_BASE}/`);
                addResult('✅ Frontend is accessible', 'success');
            } catch (error) {
                addResult('❌ Cannot connect to frontend. Make sure it\'s running on http://localhost:8081', 'error');
            }
        };
    </script>
</body>
</html>
