// Simple test to verify the fix works
async function testFix() {
    const baseUrl = 'http://localhost:5020/api/v1';
    
    try {
        // Login as the employee who already has a leave request
        console.log('🔐 Logging in as existing employee...');
        const loginResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Tu3jUYEb$EaY'
            })
        });

        if (!loginResponse.ok) {
            console.error('❌ Login failed:', loginResponse.status);
            return;
        }

        const loginData = await loginResponse.json();
        const token = loginData.data.token;
        const userId = loginData.data.user.id;
        const orgId = loginData.data.user.organization?.id;
        
        console.log(`✅ Login successful!`);
        console.log(`   User ID: ${userId}`);
        console.log(`   Organization ID: ${orgId}`);

        // Test leave requests
        console.log('\n📋 Testing employee leave requests...');
        const leaveResponse = await fetch(`${baseUrl}/leave/requests`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'X-Organization-ID': orgId
            }
        });

        if (!leaveResponse.ok) {
            console.error('❌ Leave requests failed:', leaveResponse.status);
            return;
        }

        const leaveData = await leaveResponse.json();
        console.log(`✅ Leave requests response received`);
        console.log(`   Success: ${leaveData.success}`);
        console.log(`   Number of requests: ${leaveData.data?.requests?.length || 0}`);

        if (leaveData.data?.requests?.length > 0) {
            console.log('   Leave requests found:');
            leaveData.data.requests.forEach((request, index) => {
                console.log(`     ${index + 1}. ID: ${request.id}`);
                console.log(`        Status: ${request.status}`);
                console.log(`        From: ${request.fromDate} To: ${request.toDate}`);
                console.log(`        Reason: ${request.reason}`);
            });
            console.log('\n🎉 SUCCESS: Fix is working! Employee can see their leave history!');
        } else {
            console.log('\n❌ ISSUE STILL EXISTS: Employee cannot see their leave history!');
        }

    } catch (error) {
        console.error('❌ Error during test:', error.message);
    }
}

testFix();
