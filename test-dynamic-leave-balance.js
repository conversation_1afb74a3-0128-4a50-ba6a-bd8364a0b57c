/**
 * Test script for Dynamic Leave Balance Calculation System
 * Tests various scenarios to ensure the calculation logic is accurate
 */

// Test cases for dynamic leave balance calculation
const testCases = [
    {
        name: "Employee joined today",
        joinDate: new Date(),
        currentDate: new Date(),
        expectedTotal: 1,
        description: "Should get 1 initial leave on join date"
    },
    {
        name: "Employee joined 1 month ago (exact)",
        joinDate: new Date(2024, 10, 15), // November 15, 2024
        currentDate: new Date(2024, 11, 15), // December 15, 2024
        expectedTotal: 2,
        description: "Should get 1 initial + 1 monthly = 2 total"
    },
    {
        name: "Employee joined 2 months ago",
        joinDate: new Date(2024, 9, 15), // October 15, 2024
        currentDate: new Date(2024, 11, 15), // December 15, 2024
        expectedTotal: 3,
        description: "Should get 1 initial + 2 monthly = 3 total"
    },
    {
        name: "Employee joined mid-month, partial month",
        joinDate: new Date(2024, 10, 15), // November 15, 2024
        currentDate: new Date(2024, 11, 10), // December 10, 2024
        expectedTotal: 1,
        description: "Should get 1 initial only (no complete month yet)"
    },
    {
        name: "Employee joined 6 months ago",
        joinDate: new Date(2024, 5, 1), // June 1, 2024
        currentDate: new Date(2024, 11, 1), // December 1, 2024
        expectedTotal: 7,
        description: "Should get 1 initial + 6 monthly = 7 total"
    },
    {
        name: "Employee joined at year boundary",
        joinDate: new Date(2023, 11, 15), // December 15, 2023
        currentDate: new Date(2024, 2, 15), // March 15, 2024
        expectedTotal: 4,
        description: "Should get 1 initial + 3 monthly = 4 total (across year boundary)"
    }
];

/**
 * Calculate leave balance based on join date (mimics backend logic)
 */
function calculateLeaveBalance(joinDate, currentDate) {
    // Ensure we're working with dates only (no time component)
    joinDate = new Date(joinDate.getFullYear(), joinDate.getMonth(), joinDate.getDate());
    currentDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());

    // If current date is before join date, no leaves allocated
    if (currentDate < joinDate) {
        return {
            totalLeaves: 0,
            monthsCompleted: 0,
            nextAccrualDate: new Date(joinDate.getFullYear(), joinDate.getMonth() + 1, joinDate.getDate())
        };
    }

    // Calculate complete months since joining
    const monthsCompleted = calculateCompleteMonths(joinDate, currentDate);

    // Total leaves = 1 initial + 1 per complete month
    const totalLeaves = 1 + monthsCompleted;

    // Calculate next accrual date (first day of next month after the last completed month)
    const nextAccrualDate = new Date(joinDate.getFullYear(), joinDate.getMonth() + monthsCompleted + 1, joinDate.getDate());

    return {
        totalLeaves,
        monthsCompleted,
        nextAccrualDate
    };
}

/**
 * Calculate the number of complete months between two dates
 */
function calculateCompleteMonths(startDate, endDate) {
    if (endDate < startDate) return 0;

    let months = 0;
    let currentMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, startDate.getDate());

    // Count complete months from the month after joining
    while (currentMonth <= endDate) {
        months++;
        currentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, currentMonth.getDate());
    }

    return months;
}

/**
 * Run all test cases
 */
function runTests() {
    console.log("🧪 Testing Dynamic Leave Balance Calculation System\n");
    console.log("=" .repeat(80));

    let passedTests = 0;
    let totalTests = testCases.length;

    testCases.forEach((testCase, index) => {
        console.log(`\n📋 Test ${index + 1}: ${testCase.name}`);
        console.log(`   Join Date: ${testCase.joinDate.toDateString()}`);
        console.log(`   Current Date: ${testCase.currentDate.toDateString()}`);
        console.log(`   Expected Total: ${testCase.expectedTotal} leaves`);
        console.log(`   Description: ${testCase.description}`);

        const result = calculateLeaveBalance(testCase.joinDate, testCase.currentDate);
        
        console.log(`   📊 Calculation Result:`);
        console.log(`      - Total Leaves: ${result.totalLeaves}`);
        console.log(`      - Months Completed: ${result.monthsCompleted}`);
        console.log(`      - Next Accrual Date: ${result.nextAccrualDate.toDateString()}`);

        if (result.totalLeaves === testCase.expectedTotal) {
            console.log(`   ✅ PASSED`);
            passedTests++;
        } else {
            console.log(`   ❌ FAILED - Expected ${testCase.expectedTotal}, got ${result.totalLeaves}`);
        }
    });

    console.log("\n" + "=" .repeat(80));
    console.log(`📈 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log("🎉 All tests passed! Dynamic leave balance calculation is working correctly.");
    } else {
        console.log("⚠️  Some tests failed. Please review the calculation logic.");
    }

    return passedTests === totalTests;
}

/**
 * Test API endpoint (if running in Node.js environment)
 */
async function testAPIEndpoint() {
    console.log("\n🌐 Testing API Endpoint Integration");
    console.log("=" .repeat(50));

    try {
        // This would be used to test the actual API endpoint
        console.log("ℹ️  API endpoint testing requires authentication and employee data.");
        console.log("   To test the API:");
        console.log("   1. Login as an employee (e.g., <EMAIL>)");
        console.log("   2. Call GET /api/v1/leave/balance");
        console.log("   3. Verify the response shows dynamic calculation results");
        console.log("   4. Check that 'LeaveType' is 'Dynamic Leave Balance'");
        console.log("   5. Verify TotalAllocated matches expected calculation");
    } catch (error) {
        console.log(`❌ API test failed: ${error.message}`);
    }
}

// Run the tests
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = { calculateLeaveBalance, runTests };
} else {
    // Browser environment or direct execution
    runTests();
    testAPIEndpoint();
}

// If running directly with Node.js
if (require.main === module) {
    runTests();
    testAPIEndpoint();
}
