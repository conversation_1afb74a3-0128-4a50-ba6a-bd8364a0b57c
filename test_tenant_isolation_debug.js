// Debug script to test tenant isolation issues
const API_BASE = 'http://localhost:5020/api/v1';

async function debugTenantIsolation() {
    console.log('🔍 Debugging Multi-tenant Isolation Issues...\n');

    try {
        // Step 1: Login as Plan Square Admin
        console.log('1️⃣ Testing Plan Square Admin Access');
        console.log('-'.repeat(40));
        const planSquareLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'PlanSquare@123'
            })
        });

        if (!planSquareLoginResponse.ok) {
            throw new Error(`Plan Square admin login failed: ${planSquareLoginResponse.status}`);
        }

        const planSquareData = await planSquareLoginResponse.json();
        const planSquareToken = planSquareData.data.token;
        const planSquareOrgId = planSquareData.data.user.organization?.id;
        
        console.log(`✅ Plan Square Admin Login Successful`);
        console.log(`   👤 Name: ${planSquareData.data.user.name}`);
        console.log(`   📧 Email: ${planSquareData.data.user.email}`);
        console.log(`   🏢 Organization: ${planSquareData.data.user.organization?.name}`);
        console.log(`   🆔 Organization ID: ${planSquareOrgId}`);

        // Step 2: Get Plan Square employees
        console.log(`\n2️⃣ Getting Plan Square Employees`);
        console.log('-'.repeat(40));
        const planSquareEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${planSquareToken}`,
                'X-Organization-ID': planSquareOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (planSquareEmployeesResponse.ok) {
            const planSquareEmployeesData = await planSquareEmployeesResponse.json();
            const employees = planSquareEmployeesData.data?.items || planSquareEmployeesData.data || [];
            console.log(`✅ Plan Square Employees Retrieved: ${employees.length} employees`);
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - Org: ${emp.organization?.name || 'N/A'}`);
                if (emp.employeeDetails) {
                    console.log(`      Employee ID: ${emp.employeeDetails.employeeId}`);
                    console.log(`      Department: ${emp.employeeDetails.department}`);
                }
            });
        } else {
            const errorText = await planSquareEmployeesResponse.text();
            console.log(`❌ Failed to get Plan Square employees: ${errorText}`);
        }

        // Step 3: Login as Test Company Admin
        console.log(`\n3️⃣ Testing Test Company Admin Access`);
        console.log('-'.repeat(40));
        const testCompanyLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!testCompanyLoginResponse.ok) {
            throw new Error(`Test Company admin login failed: ${testCompanyLoginResponse.status}`);
        }

        const testCompanyData = await testCompanyLoginResponse.json();
        const testCompanyToken = testCompanyData.data.token;
        const testCompanyOrgId = testCompanyData.data.user.organization?.id;
        
        console.log(`✅ Test Company Admin Login Successful`);
        console.log(`   👤 Name: ${testCompanyData.data.user.name}`);
        console.log(`   📧 Email: ${testCompanyData.data.user.email}`);
        console.log(`   🏢 Organization: ${testCompanyData.data.user.organization?.name}`);
        console.log(`   🆔 Organization ID: ${testCompanyOrgId}`);

        // Step 4: Get Test Company employees
        console.log(`\n4️⃣ Getting Test Company Employees`);
        console.log('-'.repeat(40));
        const testCompanyEmployeesResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${testCompanyToken}`,
                'X-Organization-ID': testCompanyOrgId,
                'Content-Type': 'application/json'
            }
        });

        if (testCompanyEmployeesResponse.ok) {
            const testCompanyEmployeesData = await testCompanyEmployeesResponse.json();
            const employees = testCompanyEmployeesData.data?.items || testCompanyEmployeesData.data || [];
            console.log(`✅ Test Company Employees Retrieved: ${employees.length} employees`);
            
            employees.forEach((emp, index) => {
                console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - Org: ${emp.organization?.name || 'N/A'}`);
                if (emp.employeeDetails) {
                    console.log(`      Employee ID: ${emp.employeeDetails.employeeId}`);
                    console.log(`      Department: ${emp.employeeDetails.department}`);
                }
            });
        } else {
            const errorText = await testCompanyEmployeesResponse.text();
            console.log(`❌ Failed to get Test Company employees: ${errorText}`);
        }

        // Step 5: Analysis
        console.log(`\n5️⃣ Tenant Isolation Analysis`);
        console.log('-'.repeat(40));
        console.log(`Plan Square Organization ID: ${planSquareOrgId}`);
        console.log(`Test Company Organization ID: ${testCompanyOrgId}`);
        console.log(`Organizations are different: ${planSquareOrgId !== testCompanyOrgId ? '✅ YES' : '❌ NO'}`);
        
        if (planSquareOrgId === testCompanyOrgId) {
            console.log(`🚨 CRITICAL ISSUE: Both organizations have the same ID!`);
        }

        console.log(`\n📊 Expected Behavior:`);
        console.log(`   - Plan Square should only see Plan Square employees`);
        console.log(`   - Test Company should only see Test Company employees`);
        console.log(`   - No cross-organization data visibility`);

    } catch (error) {
        console.error('❌ Debug test failed:', error.message);
    }
}

// Run the debug test
debugTenantIsolation();
