// Simple script to check what users exist in the system
async function checkUsers() {
    const baseUrl = 'http://localhost:5020/api/v1';
    
    try {
        // Try to login as super admin first
        console.log('🔐 Trying to login as super admin...');
        const superAdminResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'SuperAdmin123!' // Try common password
            })
        });

        if (superAdminResponse.ok) {
            const superAdminData = await superAdminResponse.json();
            const token = superAdminData.data.token;
            
            console.log('✅ Super admin login successful!');
            
            // Get all users
            console.log('\n👥 Fetching all users...');
            const usersResponse = await fetch(`${baseUrl}/users`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            if (usersResponse.ok) {
                const usersData = await usersResponse.json();
                console.log(`Found ${usersData.data?.users?.length || 0} users:`);
                
                usersData.data?.users?.forEach((user, index) => {
                    console.log(`  ${index + 1}. ${user.name} (${user.email}) - Role: ${user.role}`);
                    if (user.organization) {
                        console.log(`     Organization: ${user.organization.name} (${user.organization.id})`);
                    }
                });
            } else {
                console.error('❌ Failed to fetch users:', usersResponse.status);
            }
        } else {
            console.log('❌ Super admin login failed, trying default admin...');
            
            // Try default admin with different passwords
            const passwords = ['Admin123!', 'TempPass123!', 'Password123!', 'admin123'];
            
            for (const password of passwords) {
                console.log(`🔐 Trying admin login with password: ${password}`);
                const adminResponse = await fetch(`${baseUrl}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: password
                    })
                });
                
                if (adminResponse.ok) {
                    const adminData = await adminResponse.json();
                    console.log('✅ Admin login successful!');
                    console.log(`   User: ${adminData.data.user.name}`);
                    console.log(`   Role: ${adminData.data.user.role}`);
                    console.log(`   Organization: ${adminData.data.user.organization?.name}`);
                    return; // Exit after successful login
                }
            }
            
            console.log('❌ All login attempts failed');
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

checkUsers();
