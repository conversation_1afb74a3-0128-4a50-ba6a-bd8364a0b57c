// Complete authentication flow test for HRMS application
const API_BASE = 'http://localhost:5020/api/v1';

async function testCompleteAuthFlow() {
    console.log('🔐 HRMS Complete Authentication Flow Test\n');
    console.log('=' .repeat(70));

    const testResults = {
        superAdmin: false,
        orgAdmin: false,
        employee: false,
        invalidCredentials: false,
        tokenValidation: false,
        logout: false
    };

    try {
        // Test 1: Super Admin Authentication
        console.log('\n1️⃣ SUPER ADMIN AUTHENTICATION TEST');
        console.log('-'.repeat(40));
        
        const superAdminLogin = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (superAdminLogin.ok) {
            const superAdminData = await superAdminLogin.json();
            testResults.superAdmin = true;
            console.log('✅ Super Admin Login: SUCCESS');
            console.log(`   👤 User: ${superAdminData.data.user.name}`);
            console.log(`   🎭 Role: ${superAdminData.data.user.role}`);
            console.log(`   🏢 Organization: ${superAdminData.data.user.organization?.name || 'System Level'}`);
            console.log(`   🔑 Token Generated: ${superAdminData.data.token ? 'YES' : 'NO'}`);
            console.log(`   ⏰ Expires In: ${superAdminData.data.expiresIn} seconds`);
            
            // Test token validation
            const tokenTest = await fetch(`${API_BASE}/users/me`, {
                headers: { 
                    'Authorization': `Bearer ${superAdminData.data.token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (tokenTest.ok) {
                testResults.tokenValidation = true;
                console.log('✅ Token Validation: SUCCESS');
            } else {
                console.log('❌ Token Validation: FAILED');
            }
        } else {
            console.log('❌ Super Admin Login: FAILED');
        }

        // Test 2: Organization Admin Authentication
        console.log('\n2️⃣ ORGANIZATION ADMIN AUTHENTICATION TEST');
        console.log('-'.repeat(40));
        
        const orgAdminLogin = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (orgAdminLogin.ok) {
            const orgAdminData = await orgAdminLogin.json();
            testResults.orgAdmin = true;
            console.log('✅ Organization Admin Login: SUCCESS');
            console.log(`   👤 User: ${orgAdminData.data.user.name}`);
            console.log(`   🎭 Role: ${orgAdminData.data.user.role}`);
            console.log(`   🏢 Organization: ${orgAdminData.data.user.organization?.name}`);
            console.log(`   🌐 Domain: ${orgAdminData.data.user.organization?.domain}`);
            console.log(`   🔑 Token Generated: ${orgAdminData.data.token ? 'YES' : 'NO'}`);
        } else {
            console.log('❌ Organization Admin Login: FAILED');
        }

        // Test 3: Employee Authentication
        console.log('\n3️⃣ EMPLOYEE AUTHENTICATION TEST');
        console.log('-'.repeat(40));
        
        const employeeLogin = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'ChangedPassword123!'
            })
        });

        if (employeeLogin.ok) {
            const employeeData = await employeeLogin.json();
            testResults.employee = true;
            console.log('✅ Employee Login: SUCCESS');
            console.log(`   👤 User: ${employeeData.data.user.name}`);
            console.log(`   🎭 Role: ${employeeData.data.user.role}`);
            console.log(`   🏢 Organization: ${employeeData.data.user.organization?.name}`);
            console.log(`   🔑 Token Generated: ${employeeData.data.token ? 'YES' : 'NO'}`);
        } else {
            console.log('❌ Employee Login: FAILED');
        }

        // Test 4: Invalid Credentials
        console.log('\n4️⃣ INVALID CREDENTIALS TEST');
        console.log('-'.repeat(40));
        
        const invalidLogin = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'wrongpassword'
            })
        });

        if (!invalidLogin.ok) {
            const errorData = await invalidLogin.json();
            testResults.invalidCredentials = true;
            console.log('✅ Invalid Credentials: PROPERLY REJECTED');
            console.log(`   ❌ Error: ${errorData.error?.message}`);
            console.log(`   📊 Status: ${invalidLogin.status}`);
        } else {
            console.log('❌ Invalid Credentials: IMPROPERLY ACCEPTED (Security Issue!)');
        }

        // Test 5: Logout Test
        console.log('\n5️⃣ LOGOUT FUNCTIONALITY TEST');
        console.log('-'.repeat(40));
        
        // First login to get a token
        const loginForLogout = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (loginForLogout.ok) {
            const loginData = await loginForLogout.json();
            
            // Test logout
            const logoutTest = await fetch(`${API_BASE}/auth/logout`, {
                method: 'POST',
                headers: { 
                    'Authorization': `Bearer ${loginData.data.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (logoutTest.ok) {
                testResults.logout = true;
                console.log('✅ Logout: SUCCESS');
            } else {
                console.log('❌ Logout: FAILED');
            }
        }

        // Summary
        console.log('\n' + '=' .repeat(70));
        console.log('📊 AUTHENTICATION TEST RESULTS');
        console.log('=' .repeat(70));
        
        const totalTests = Object.keys(testResults).length;
        const passedTests = Object.values(testResults).filter(result => result).length;
        
        console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed\n`);
        
        Object.entries(testResults).forEach(([test, passed]) => {
            const status = passed ? '✅ PASS' : '❌ FAIL';
            const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            console.log(`   ${status} - ${testName}`);
        });

        console.log('\n🔐 AUTHENTICATION CREDENTIALS:');
        console.log('   🔹 Super Admin: <EMAIL> / Admin123!');
        console.log('   🔹 Org Admin: <EMAIL> / Admin123!');
        console.log('   🔹 Employee: <EMAIL> / ChangedPassword123!');

        console.log('\n🌐 FRONTEND ROUTING URLS:');
        console.log('   🔹 Dashboard: http://localhost:8081/dashboard');
        console.log('   🔹 Super Admin - Organizations: http://localhost:8081/super-admin/organizations');
        console.log('   🔹 Org Admin - Employees: http://localhost:8081/org-admin/employee-management');
        console.log('   🔹 Employee - Attendance: http://localhost:8081/employee/attendance');

        if (passedTests === totalTests) {
            console.log('\n🎉 ALL TESTS PASSED! Authentication system is fully functional.');
        } else {
            console.log(`\n⚠️  ${totalTests - passedTests} test(s) failed. Please check the issues above.`);
        }

    } catch (error) {
        console.error('\n❌ Test suite failed with error:', error.message);
    }
}

// Run the complete test
testCompleteAuthFlow();
