# HRMS Database Configuration
# Multi-tenant database provisioning configuration for different environments

# Environment-specific database configurations
environments:
  development:
    master_db:
      server: "${SQL_SERVER_HOST}"
      port: 1433
      database: "${SQL_SERVER_DATABASE}"
      username: "${SQL_SERVER_USERNAME}"
      password: "${SQL_SERVER_PASSWORD}"
      trusted_connection: false
      trust_server_certificate: true
      max_connections: 20
      connection_timeout: 30
      idle_timeout: 300
    
    # Connection pool settings for development
    pool:
      max_open_connections: 25
      max_idle_connections: 5
      connection_max_lifetime: 3600
    
    # Schema provisioning settings
    provisioning:
      auto_provision: true
      schema_prefix: "org_"
      schema_suffix: ""
      timeout_seconds: 300
      retry_attempts: 3
      retry_delay_seconds: 5

  staging:
    master_db:
      host: "staging-db.hrms.internal"
      port: 5432
      database: "hrms_master_staging"
      username: "hrms_staging_user"
      password: "${DB_STAGING_PASSWORD}"
      ssl_mode: "require"
      max_connections: 50
      connection_timeout: 30
      idle_timeout: 600
    
    pool:
      max_open_connections: 75
      max_idle_connections: 10
      connection_max_lifetime: 3600
    
    provisioning:
      auto_provision: true
      schema_prefix: "org_"
      schema_suffix: "_staging"
      timeout_seconds: 600
      retry_attempts: 3
      retry_delay_seconds: 10

  production:
    master_db:
      host: "prod-db-cluster.hrms.com"
      port: 5432
      database: "hrms_master_prod"
      username: "hrms_prod_user"
      password: "${DB_PROD_PASSWORD}"
      ssl_mode: "require"
      max_connections: 100
      connection_timeout: 30
      idle_timeout: 900
    
    pool:
      max_open_connections: 200
      max_idle_connections: 20
      connection_max_lifetime: 3600
    
    provisioning:
      auto_provision: true
      schema_prefix: "org_"
      schema_suffix: ""
      timeout_seconds: 900
      retry_attempts: 5
      retry_delay_seconds: 15

# Schema naming conventions
schema_naming:
  # Available patterns: {org_id}, {org_domain}, {org_name_clean}
  pattern: "{schema_prefix}{org_id}{schema_suffix}"
  
  # Domain-based naming (alternative)
  # pattern: "{org_domain}_hrms{schema_suffix}"
  
  # Clean organization name (removes special chars, converts to lowercase)
  # pattern: "{schema_prefix}{org_name_clean}{schema_suffix}"
  
  # Maximum schema name length (SQL Server limit is 128 characters)
  max_length: 128
  
  # Characters to replace in organization names/domains
  char_replacements:
    "-": "_"
    ".": "_"
    " ": "_"
    "&": "and"

# Database schema template configuration
schema_template:
  # Base SQL files to execute in order
  migration_files:
    - "migrations/001_create_core_tables.sql"
    - "migrations/002_create_attendance_tables.sql"
    - "migrations/003_create_leave_tables.sql"
    - "migrations/004_create_task_tables.sql"
    - "migrations/005_create_performance_tables.sql"
    - "migrations/006_create_payroll_tables.sql"
    - "migrations/007_create_recruitment_tables.sql"
    - "migrations/008_create_system_tables.sql"
    - "migrations/009_create_indexes.sql"
    - "migrations/010_create_constraints.sql"
    - "migrations/011_add_missing_employee_detail_columns.sql"
  
  # Data seeding files
  seed_files:
    - "seeds/001_default_leave_types.sql"
    - "seeds/002_default_system_settings.sql"
    - "seeds/003_default_subscription_plans.sql"
    - "seeds/004_default_job_categories.sql"
  
  # Schema-specific replacements in SQL files
  replacements:
    "{SCHEMA_NAME}": "dynamic_schema_name"
    "{ORG_ID}": "organization_id"
    "{CREATED_DATE}": "current_timestamp"

# Security and permissions
security:
  # Database user for schema operations (should have CREATE SCHEMA privileges)
  provisioning_user:
    username: "hrms_provisioner"
    password: "${DB_PROVISIONER_PASSWORD}"
  
  # Default permissions for new schemas
  default_permissions:
    owner: "hrms_app_user"
    read_users: ["hrms_readonly", "hrms_backup"]
    write_users: ["hrms_app_user"]
  
  # Schema isolation settings
  isolation:
    enable_row_level_security: true
    default_search_path: "public"
    restrict_cross_schema_access: true

# Monitoring and logging
monitoring:
  # Enable detailed logging
  enable_logging: true
  log_level: "INFO"  # DEBUG, INFO, WARN, ERROR
  
  # Log file configuration
  log_file:
    path: "/var/log/hrms/database-provisioning.log"
    max_size_mb: 100
    max_files: 10
    compress: true
  
  # Metrics collection
  metrics:
    enable: true
    endpoint: "/metrics"
    include_timing: true
    include_error_rates: true
  
  # Alerting thresholds
  alerts:
    provisioning_timeout_minutes: 15
    failed_attempts_threshold: 3
    disk_usage_threshold_percent: 85

# Backup and recovery
backup:
  # Automatic backup after schema creation
  auto_backup_new_schemas: true
  backup_retention_days: 30
  backup_location: "/var/backups/hrms/schemas/"
  
  # Backup naming convention
  backup_naming: "{schema_name}_{timestamp}.sql"

# Performance optimization
performance:
  # Parallel schema creation (for multiple organizations)
  max_concurrent_provisions: 3
  
  # Connection pooling per schema
  per_schema_pool_size: 10
  
  # Query optimization
  enable_query_optimization: true
  analyze_after_creation: true
  vacuum_after_seeding: true

# Feature flags
features:
  # Enable/disable specific features
  enable_auto_provisioning: true
  enable_schema_validation: true
  enable_rollback_on_failure: true
  enable_concurrent_provisioning: true
  enable_schema_cleanup: false  # For development only
  
  # Experimental features
  experimental:
    enable_schema_templates: false
    enable_dynamic_scaling: false
    enable_cross_schema_queries: false

# Validation rules
validation:
  # Organization validation before schema creation
  required_fields: ["name", "domain", "industry"]
  
  # Domain validation
  domain_validation:
    allow_subdomains: false
    blocked_domains: ["test.com", "example.com", "localhost"]
    min_length: 4
    max_length: 253
  
  # Organization name validation
  name_validation:
    min_length: 2
    max_length: 100
    allowed_characters: "a-zA-Z0-9 .-_&"
  
  # Schema name validation
  schema_validation:
    min_length: 5
    max_length: 63
    allowed_characters: "a-z0-9_"
    reserved_names: ["public", "information_schema", "pg_catalog"]

# Error handling
error_handling:
  # Rollback strategy
  rollback:
    enable: true
    timeout_seconds: 300
    preserve_logs: true
  
  # Retry configuration
  retry:
    max_attempts: 3
    backoff_strategy: "exponential"  # linear, exponential
    base_delay_seconds: 5
    max_delay_seconds: 60
  
  # Notification settings
  notifications:
    enable_email_alerts: true
    admin_emails: ["<EMAIL>", "<EMAIL>"]
    webhook_url: "${WEBHOOK_URL}"
    
    # Notification triggers
    triggers:
      - "provisioning_failed"
      - "provisioning_timeout"
      - "rollback_executed"
      - "disk_space_low"

# Development and testing
development:
  # Enable development-specific features
  enable_debug_mode: false
  enable_sql_logging: false
  enable_performance_profiling: false
  
  # Test data generation
  generate_test_data: false
  test_data_size: "small"  # small, medium, large
  
  # Schema cleanup for testing
  auto_cleanup_test_schemas: true
  test_schema_prefix: "test_"
  cleanup_after_hours: 24
