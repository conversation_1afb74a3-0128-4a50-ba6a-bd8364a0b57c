-- Script to check current organizations and their data
USE dbHRMS;
GO

PRINT 'Current Organizations in Database:';
PRINT '==================================';

SELECT 
    Id,
    Name,
    Domain,
    Industry,
    Status,
    EmployeeCount,
    CreatedAt,
    UpdatedAt
FROM Organizations
ORDER BY Name;

PRINT '';
PRINT 'Users by Organization:';
PRINT '=====================';

SELECT 
    o.Name AS OrganizationName,
    COUNT(u.Id) AS UserCount,
    STRING_AGG(u.Name, ', ') AS UserNames
FROM Organizations o
LEFT JOIN Users u ON o.Id = u.OrganizationId
GROUP BY o.Id, o.Name
ORDER BY o.Name;

PRINT '';
PRINT 'Looking for TechCorp Solutions specifically:';
PRINT '==========================================';

DECLARE @TechCorpId UNIQUEIDENTIFIER;
SELECT @TechCorpId = Id FROM Organizations WHERE Name LIKE '%TechCorp%' OR Name LIKE '%Tech Corp%';

IF @TechCorpId IS NOT NULL
BEGIN
    PRINT 'Found TechCorp organization with ID: ' + CAST(@TechCorpId AS NVARCHAR(50));
    
    SELECT 
        'Organization Details' AS Section,
        Name,
        Domain,
        Industry,
        Status,
        EmployeeCount,
        MonthlyRevenue,
        CreatedAt
    FROM Organizations 
    WHERE Id = @TechCorpId;
    
    SELECT 
        'Users in TechCorp' AS Section,
        u.Name,
        u.Email,
        u.Role,
        u.IsActive,
        u.CreatedAt
    FROM Users u
    WHERE u.OrganizationId = @TechCorpId;
    
    SELECT 
        'Leave Types for TechCorp' AS Section,
        lt.Name,
        lt.Description,
        lt.MaxDaysPerYear,
        lt.IsActive
    FROM LeaveTypes lt
    WHERE lt.OrganizationId = @TechCorpId;
END
ELSE
BEGIN
    PRINT 'No TechCorp organization found in database';
END
