// Using built-in fetch (Node.js 18+)

async function debugEmployeeLeaveHistory() {
    const baseUrl = 'http://localhost:5020/api/v1';
    
    try {
        // Step 1: First login as admin to create an employee
        console.log('🔐 Step 1: Logging in as admin to create employee...');
        const adminLoginResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!' // Try common password first
            })
        });

        if (!adminLoginResponse.ok) {
            console.error('❌ Admin login failed:', adminLoginResponse.status, adminLoginResponse.statusText);
            console.log('   Trying to get admin credentials from logs...');
            return;
        }

        const adminLoginData = await adminLoginResponse.json();
        const adminToken = adminLoginData.data.token;
        const adminOrgId = adminLoginData.data.user.organization?.id;

        console.log(`✅ Admin login successful!`);
        console.log(`   Admin Organization ID: ${adminOrgId}`);

        // Step 2: Create a test employee
        console.log('\n👤 Step 2: Creating test employee...');
        const createEmployeeResponse = await fetch(`${baseUrl}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'X-Organization-ID': adminOrgId,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'Test Employee',
                email: '<EMAIL>',
                role: 'Employee',
                employeeDetails: {
                    employeeId: 'EMP_TEST_001',
                    jobTitle: 'Software Developer',
                    department: 'Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 75000,
                    annualCTC: 90000
                }
            })
        });

        if (!createEmployeeResponse.ok) {
            console.error('❌ Employee creation failed:', createEmployeeResponse.status);
            const errorText = await createEmployeeResponse.text();
            console.error('   Error details:', errorText);
            return;
        }

        const createEmployeeData = await createEmployeeResponse.json();
        const employeeCredentials = createEmployeeData.data.credentials;

        console.log(`✅ Employee created successfully!`);
        console.log(`   Email: ${employeeCredentials.email}`);
        console.log(`   Password: ${employeeCredentials.password}`);

        // Step 3: Login as the employee
        console.log('\n🔐 Step 3: Logging in as employee...');
        const loginResponse = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: employeeCredentials.email,
                password: employeeCredentials.password
            })
        });

        if (!loginResponse.ok) {
            console.error('❌ Login failed:', loginResponse.status, loginResponse.statusText);
            return;
        }

        const loginData = await loginResponse.json();
        const token = loginData.data.token;
        const userId = loginData.data.user.id;
        const userRole = loginData.data.user.role;
        const orgId = loginData.data.user.organization?.id;
        
        console.log(`✅ Login successful!`);
        console.log(`   User ID: ${userId}`);
        console.log(`   User Role: ${userRole}`);
        console.log(`   Organization ID: ${orgId}`);
        console.log(`   Token: ${token.substring(0, 20)}...`);

        // Step 4: Test leave requests API call
        console.log('\n📋 Step 4: Fetching employee leave requests...');
        const leaveRequestsResponse = await fetch(`${baseUrl}/leave/requests`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Response Status: ${leaveRequestsResponse.status}`);
        
        if (!leaveRequestsResponse.ok) {
            console.error('❌ Leave requests fetch failed:', leaveRequestsResponse.statusText);
            const errorText = await leaveRequestsResponse.text();
            console.error('   Error details:', errorText);
            return;
        }

        const leaveRequestsData = await leaveRequestsResponse.json();
        console.log(`✅ Leave requests response received`);
        console.log(`   Success: ${leaveRequestsData.success}`);
        console.log(`   Number of requests: ${leaveRequestsData.data?.requests?.length || 0}`);
        
        if (leaveRequestsData.data?.requests?.length > 0) {
            console.log('   Leave requests found:');
            leaveRequestsData.data.requests.forEach((request, index) => {
                console.log(`     ${index + 1}. ID: ${request.id}`);
                console.log(`        User: ${request.user?.name} (${request.user?.id})`);
                console.log(`        Status: ${request.status}`);
                console.log(`        From: ${request.fromDate} To: ${request.toDate}`);
                console.log(`        Applied: ${request.appliedDate}`);
            });
        } else {
            console.log('   ⚠️  No leave requests found for this employee');
        }

        // Step 5: Test with explicit userId parameter
        console.log('\n📋 Step 5: Fetching leave requests with explicit userId...');
        const explicitUserResponse = await fetch(`${baseUrl}/leave/requests?userId=${userId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Response Status: ${explicitUserResponse.status}`);
        
        if (explicitUserResponse.ok) {
            const explicitUserData = await explicitUserResponse.json();
            console.log(`✅ Explicit userId response received`);
            console.log(`   Success: ${explicitUserData.success}`);
            console.log(`   Number of requests: ${explicitUserData.data?.requests?.length || 0}`);
        } else {
            console.error('❌ Explicit userId fetch failed:', explicitUserResponse.statusText);
        }

        // Step 6: Check if there are any leave requests in the organization (admin view)
        console.log('\n📋 Step 6: Checking organization leave requests (should fail for employee)...');
        const orgLeaveResponse = await fetch(`${baseUrl}/organization-admin/leave/requests`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   Response Status: ${orgLeaveResponse.status}`);
        if (orgLeaveResponse.status === 403) {
            console.log('✅ Correctly forbidden for employee role');
        } else if (orgLeaveResponse.ok) {
            const orgLeaveData = await orgLeaveResponse.json();
            console.log(`   Organization requests: ${orgLeaveData.data?.requests?.length || 0}`);
        }

        // Step 7: Apply for a new leave to test the flow
        console.log('\n📝 Step 7: Applying for a new leave...');
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dayAfter = new Date();
        dayAfter.setDate(dayAfter.getDate() + 2);

        const applyLeaveResponse = await fetch(`${baseUrl}/leave/apply`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                fromDate: tomorrow.toISOString(),
                toDate: dayAfter.toISOString(),
                durationType: 'full-day',
                reason: 'Debug test leave application'
            })
        });

        console.log(`   Apply Leave Response Status: ${applyLeaveResponse.status}`);
        
        if (applyLeaveResponse.ok) {
            const applyLeaveData = await applyLeaveResponse.json();
            console.log(`✅ Leave application successful`);
            console.log(`   Leave ID: ${applyLeaveData.data?.id}`);
            console.log(`   Status: ${applyLeaveData.data?.status}`);

            // Step 8: Fetch leave requests again after applying
            console.log('\n📋 Step 8: Fetching leave requests after applying new leave...');
            const afterApplyResponse = await fetch(`${baseUrl}/leave/requests`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (afterApplyResponse.ok) {
                const afterApplyData = await afterApplyResponse.json();
                console.log(`✅ After apply response received`);
                console.log(`   Success: ${afterApplyData.success}`);
                console.log(`   Number of requests: ${afterApplyData.data?.requests?.length || 0}`);
                
                if (afterApplyData.data?.requests?.length > 0) {
                    console.log('   Updated leave requests:');
                    afterApplyData.data.requests.forEach((request, index) => {
                        console.log(`     ${index + 1}. ID: ${request.id}`);
                        console.log(`        Status: ${request.status}`);
                        console.log(`        Reason: ${request.reason}`);
                    });
                }
            }
        } else {
            const applyError = await applyLeaveResponse.text();
            console.error('❌ Leave application failed:', applyError);
        }

    } catch (error) {
        console.error('❌ Error during debugging:', error);
    }
}

// Run the debug function
debugEmployeeLeaveHistory();
