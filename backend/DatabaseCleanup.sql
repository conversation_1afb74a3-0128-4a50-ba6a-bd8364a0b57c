-- HRMS Database Cleanup Script
-- This script will drop all existing tables and recreate the database schema

USE dbHRMS;

-- Drop all foreign key constraints first
DECLARE @sql NVARCHAR(MAX) = '';
SELECT @sql = @sql + 'ALTER TABLE [' + SCHEMA_NAME(schema_id) + '].[' + OBJECT_NAME(parent_object_id) + '] DROP CONSTRAINT [' + name + '];' + CHAR(13)
FROM sys.foreign_keys;
EXEC sp_executesql @sql;

-- Drop all tables
DECLARE @tableSql NVARCHAR(MAX) = '';
SELECT @tableSql = @tableSql + 'DROP TABLE [' + SCHEMA_NAME(schema_id) + '].[' + name + '];' + CHAR(13)
FROM sys.tables
WHERE name NOT IN ('__EFMigrationsHistory');
EXEC sp_executesql @tableSql;

-- Clean up migration history (optional - uncomment if you want to start fresh)
-- DELETE FROM __EFMigrationsHistory;

PRINT 'Database cleanup completed successfully.';
