{"format": 1, "restore": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/HRMS.API.csproj": {}}, "projects": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/HRMS.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/HRMS.API.csproj", "projectName": "HRMS.API", "projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/HRMS.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Application/HRMS.Application.csproj": {"projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Application/HRMS.Application.csproj"}, "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.8, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Application/HRMS.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Application/HRMS.Application.csproj", "projectName": "HRMS.Application", "projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Application/HRMS.Application.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj": {"projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj"}, "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj", "projectName": "HRMS.Core", "projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj", "projectName": "HRMS.Infrastructure", "projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj": {"projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.13.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}