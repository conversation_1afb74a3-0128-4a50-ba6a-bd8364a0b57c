using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using HRMS.Core.Interfaces;

namespace HRMS.API.Authentication;

public class EnhancedJwtAuthenticationHandler : AuthenticationHandler<JwtBearerOptions>
{
    private readonly IJwtService _jwtService;
    private readonly ILogger<EnhancedJwtAuthenticationHandler> _logger;

    public EnhancedJwtAuthenticationHandler(
        IOptionsMonitor<JwtBearerOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        IJwtService jwtService)
        : base(options, logger, encoder)
    {
        _jwtService = jwtService;
        _logger = logger.CreateLogger<EnhancedJwtAuthenticationHandler>();
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // Check if Authorization header exists
            if (!Request.Headers.ContainsKey("Authorization"))
            {
                return AuthenticateResult.NoResult();
            }

            var authHeader = Request.Headers["Authorization"].ToString();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return AuthenticateResult.NoResult();
            }

            // Extract token
            var token = authHeader.Substring("Bearer ".Length).Trim();
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogDebug("Empty token provided");
                return AuthenticateResult.Fail("Invalid token");
            }

            // Use our enhanced token validation
            var isValid = await _jwtService.ValidateTokenAsync(token);
            if (!isValid)
            {
                _logger.LogDebug("Token validation failed for token: {TokenPrefix}...", token.Substring(0, Math.Min(20, token.Length)));
                return AuthenticateResult.Fail("Invalid or expired token");
            }

            // Extract claims from token
            var userId = _jwtService.GetUserIdFromToken(token);
            var userRole = _jwtService.GetUserRoleFromToken(token);
            var organizationId = _jwtService.GetOrganizationIdFromToken(token);

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogDebug("No user ID found in token");
                return AuthenticateResult.Fail("Invalid token claims");
            }

            // Create claims identity
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, userId),
                new("user_id", userId)
            };

            if (!string.IsNullOrEmpty(userRole))
            {
                claims.Add(new Claim(ClaimTypes.Role, userRole));
                claims.Add(new Claim("role", userRole));
            }

            if (!string.IsNullOrEmpty(organizationId))
            {
                claims.Add(new Claim("organization_id", organizationId));
            }

            // Add token to claims for potential future use
            claims.Add(new Claim("token", token));

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            _logger.LogDebug("Authentication successful for user {UserId} with role {Role}", userId, userRole);
            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token authentication");
            return AuthenticateResult.Fail("Authentication error");
        }
    }

    protected override async Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        Response.StatusCode = 401;
        Response.ContentType = "application/json";

        var errorResponse = new
        {
            success = false,
            error = new
            {
                code = "UNAUTHORIZED",
                message = "Authentication required. Please provide a valid JWT token.",
                details = new
                {
                    scheme = "Bearer",
                    header = "Authorization",
                    format = "Bearer <token>"
                }
            }
        };

        await Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(errorResponse));
    }

    protected override async Task HandleForbiddenAsync(AuthenticationProperties properties)
    {
        Response.StatusCode = 403;
        Response.ContentType = "application/json";

        var errorResponse = new
        {
            success = false,
            error = new
            {
                code = "FORBIDDEN",
                message = "Access denied. Insufficient permissions for this resource.",
                details = new
                {
                    requiredRole = properties.Items.ContainsKey("RequiredRole") ? properties.Items["RequiredRole"] : "Unknown",
                    currentRole = Context.User?.FindFirst(ClaimTypes.Role)?.Value ?? "None"
                }
            }
        };

        await Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(errorResponse));
    }
}
