using System.Text.Json;
using System.Text.RegularExpressions;

namespace HRMS.API.Middleware;

public class MobileDeviceBlockingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<MobileDeviceBlockingMiddleware> _logger;
    private readonly bool _enableMobileBlocking;

    // Comprehensive mobile device detection patterns
    private static readonly Regex MobileUserAgentPattern = new(
        @"(Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Windows Phone|webOS|Kindle|Silk|Mobile Safari|Opera Mobi|Fennec|Maemo|Symbian|Series60|Series40|PalmOS|Blazer|Compal|Elaine|Hipt|Hiptop|Smartphone|Midp|Windows CE|NetFront|UP\.Browser|UP\.Link|Mmp|DoCoMo|J-PHONE|Vodafone|SoftBank|KDDI|AU-MIC|Openwave|SEMC-Browser|portalmmm|Obigo|AvantGo|Danger|Palm|Plucker|ReqwirelessWeb|Eudoraweb|Minimo|wap|pda|pocket|mobile|phone|tablet)",
        RegexOptions.IgnoreCase | RegexOptions.Compiled
    );

    // Additional patterns for tablet detection
    private static readonly Regex TabletUserAgentPattern = new(
        @"(iPad|Android.*Tablet|Kindle|Silk|PlayBook|BB10.*Touch|RIM.*Tablet|Tab.*Build|Nexus [0-9]|Galaxy Tab|SM-T|GT-P|SCH-I800|SHW-M180S|GT-N8000|SGH-T849|SPH-P100|GT-P3100|SM-P|SHV-E230|EK-GC100|GT-P5200|SM-T2105|GT-P5210|SM-T310|GT-P5220|SM-T320|SM-T321|SM-T325|GT-P5113|SM-T116|SM-T210|SM-T211|SM-T217|SM-T230|SM-T231|SM-T235|GT-P3110|GT-P5100|GT-P7100|GT-P7300|GT-P7310|GT-P7320|GT-P7500|GT-P7510|SCH-I905|SGH-I957|SGH-I987|SGH-T849|SGH-T859|SGH-T869|SPH-P100|GT-P1000|GT-P1010|GT-P6200|GT-P6201|GT-P6210|GT-P6211|GT-P6800|GT-P6810|GT-P7100|SHV-E140K|SHV-E140L|SHV-E140S|SHV-E150S|SHV-E230K|SHV-E230L|SHV-E230S|SHW-M180K|SHW-M180L|SHW-M180S|SHW-M180W|SHW-M300W|SHW-M305W|SHW-M380K|SHW-M380S|SHW-M380W|SHW-M430W|SHW-M480K|SHW-M480S|SHW-M480W|SHW-M485W|SHW-M486W|SHW-M500W|GT-P1000L|GT-P1000N|GT-P1000R|GT-P1000V|GT-P1010N|GT-P3100B|GT-P3105|GT-P3108|GT-P3110N|GT-P5100N|GT-P5110|GT-P5113|GT-P5200|GT-P5210|GT-P5220|GT-P6200L|GT-P6201|GT-P6210|GT-P6211|GT-P6800|GT-P6810|GT-P7100|GT-P7300|GT-P7300B|GT-P7310|GT-P7320|GT-P7500|GT-P7500D|GT-P7500M|GT-P7500R|GT-P7500V|GT-P7510|GT-P7511|GT-N5100|GT-N5105|GT-N5110|GT-N5120|GT-N8000|GT-N8005|GT-N8010|GT-N8013|GT-N8020|SM-P600|SM-P601|SM-P605|SM-T210|SM-T210R|SM-T211|SM-T217A|SM-T217S|SM-T230|SM-T230NU|SM-T230NT|SM-T231|SM-T235|SM-T310|SM-T311|SM-T315|SM-T320|SM-T321|SM-T325|SM-T330|SM-T331|SM-T335|SM-T520|SM-T525|SM-T530|SM-T531|SM-T535|SM-T700|SM-T705|SM-T800|SM-T805|SM-T900|SM-T905)",
        RegexOptions.IgnoreCase | RegexOptions.Compiled
    );

    // Screen resolution patterns that typically indicate mobile devices
    private static readonly string[] MobileScreenResolutions = {
        "320x568", "375x667", "414x736", "375x812", "414x896", // iPhone resolutions
        "360x640", "412x732", "360x780", "393x851", // Android resolutions
        "768x1024", "834x1112", "1024x1366", // iPad resolutions
        "800x1280", "1200x1920", "1440x2560" // Android tablet resolutions
    };

    public MobileDeviceBlockingMiddleware(RequestDelegate next, ILogger<MobileDeviceBlockingMiddleware> logger, IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _enableMobileBlocking = configuration.GetValue<bool>("Security:EnableMobileBlocking", true);
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Skip mobile blocking if disabled in configuration
            if (!_enableMobileBlocking)
            {
                await _next(context);
                return;
            }

            // Skip mobile blocking for certain endpoints (health checks, swagger, etc.)
            if (ShouldSkipMobileBlocking(context))
            {
                await _next(context);
                return;
            }

            // Detect if the request is from a mobile device
            var isMobileDevice = IsMobileDevice(context);
            
            if (isMobileDevice)
            {
                _logger.LogWarning("Mobile device access blocked. Path: {Path}, UserAgent: {UserAgent}, IP: {IP}",
                    context.Request.Path,
                    context.Request.Headers["User-Agent"].ToString(),
                    GetClientIpAddress(context));

                // Return mobile blocking response
                await HandleMobileDeviceBlocked(context);
                return;
            }

            // Continue to next middleware if not a mobile device
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in mobile device blocking middleware");
            // Continue processing even if mobile detection fails
            await _next(context);
        }
    }

    private bool ShouldSkipMobileBlocking(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        if (path == null) return false;

        // Skip mobile blocking for these endpoints
        var skipEndpoints = new[]
        {
            "/api/v1/health",
            "/swagger",
            "/api/docs",
            "/.well-known",
            "/favicon.ico",
            "/robots.txt"
        };

        return skipEndpoints.Any(endpoint => path.StartsWith(endpoint));
    }

    private bool IsMobileDevice(HttpContext context)
    {
        // Method 1: User Agent Detection
        var userAgent = context.Request.Headers["User-Agent"].ToString();
        if (!string.IsNullOrEmpty(userAgent))
        {
            // Check for mobile patterns
            if (MobileUserAgentPattern.IsMatch(userAgent))
            {
                _logger.LogDebug("Mobile device detected via User-Agent: {UserAgent}", userAgent);
                return true;
            }

            // Check for tablet patterns
            if (TabletUserAgentPattern.IsMatch(userAgent))
            {
                _logger.LogDebug("Tablet device detected via User-Agent: {UserAgent}", userAgent);
                return true;
            }
        }

        // Method 2: Accept Header Detection
        var acceptHeader = context.Request.Headers["Accept"].ToString();
        if (!string.IsNullOrEmpty(acceptHeader) && acceptHeader.Contains("application/vnd.wap"))
        {
            _logger.LogDebug("Mobile device detected via Accept header: {Accept}", acceptHeader);
            return true;
        }

        // Method 3: Screen Resolution Detection (if provided)
        var screenResolution = context.Request.Headers["X-Screen-Resolution"].ToString();
        if (!string.IsNullOrEmpty(screenResolution) && MobileScreenResolutions.Contains(screenResolution))
        {
            _logger.LogDebug("Mobile device detected via screen resolution: {Resolution}", screenResolution);
            return true;
        }

        // Method 4: Touch Support Detection
        var touchSupport = context.Request.Headers["X-Touch-Support"].ToString();
        if (!string.IsNullOrEmpty(touchSupport) && touchSupport.ToLowerInvariant() == "true")
        {
            // Additional check to ensure it's not a touch-enabled desktop
            if (!string.IsNullOrEmpty(userAgent) && 
                (userAgent.Contains("Windows NT") || userAgent.Contains("Macintosh")) &&
                !userAgent.Contains("Mobile"))
            {
                return false; // Touch-enabled desktop
            }
            
            _logger.LogDebug("Mobile device detected via touch support");
            return true;
        }

        return false;
    }

    private async Task HandleMobileDeviceBlocked(HttpContext context)
    {
        context.Response.StatusCode = 403;
        context.Response.ContentType = "application/json";

        var errorResponse = new
        {
            success = false,
            error = new
            {
                code = "MOBILE_ACCESS_BLOCKED",
                message = "Access from mobile devices is not allowed",
                details = new
                {
                    reason = "This HRMS application is designed for desktop/laptop computers only",
                    supportedDevices = new[] { "Desktop computers", "Laptop computers" },
                    unsupportedDevices = new[] { "Smartphones", "Tablets", "Mobile devices" },
                    recommendation = "Please access the application from a desktop or laptop computer",
                    userAgent = context.Request.Headers["User-Agent"].ToString(),
                    timestamp = DateTime.UtcNow.ToString("O")
                }
            }
        };

        var jsonResponse = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (in case of proxy/load balancer)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }
}
