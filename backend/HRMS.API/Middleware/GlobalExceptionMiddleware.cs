using System.Net;
using System.Text.Json;
using FluentValidation;
using HRMS.Application.Common;

namespace HRMS.API.Middleware;

public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred: {Message}", ex.Message);
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = exception switch
        {
            ValidationException validationEx => CreateValidationErrorResponse(validationEx),
            UnauthorizedAccessException => CreateErrorResponse("UNAUTHORIZED", "Access denied", HttpStatusCode.Unauthorized),
            ArgumentException argEx => CreateErrorResponse("INVALID_ARGUMENT", argEx.Message, HttpStatusCode.BadRequest),
            KeyNotFoundException => CreateErrorResponse("NOT_FOUND", "Resource not found", HttpStatusCode.NotFound),
            InvalidOperationException invalidOpEx => CreateErrorResponse("INVALID_OPERATION", invalidOpEx.Message, HttpStatusCode.BadRequest),
            TimeoutException => CreateErrorResponse("TIMEOUT", "Request timeout", HttpStatusCode.RequestTimeout),
            _ => CreateErrorResponse("INTERNAL_SERVER_ERROR", "An internal server error occurred", HttpStatusCode.InternalServerError)
        };

        context.Response.StatusCode = (int)response.StatusCode;

        var jsonResponse = JsonSerializer.Serialize(response.ApiResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private static (ApiResponse ApiResponse, HttpStatusCode StatusCode) CreateErrorResponse(string code, string message, HttpStatusCode statusCode)
    {
        var apiResponse = ApiResponse.ErrorResult(code, message);
        return (apiResponse, statusCode);
    }

    private static (ApiResponse ApiResponse, HttpStatusCode StatusCode) CreateValidationErrorResponse(ValidationException validationException)
    {
        var errors = validationException.Errors
            .GroupBy(e => e.PropertyName)
            .ToDictionary(
                g => g.Key,
                g => g.Select(e => e.ErrorMessage).ToArray()
            );

        var apiResponse = ApiResponse.ErrorResult("VALIDATION_ERROR", "One or more validation errors occurred", errors);
        return (apiResponse, HttpStatusCode.BadRequest);
    }
}

public class ValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ValidationMiddleware> _logger;

    public ValidationMiddleware(RequestDelegate next, ILogger<ValidationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Log request details for debugging
        if (context.Request.Method != "GET")
        {
            _logger.LogDebug("Processing {Method} request to {Path}", 
                context.Request.Method, context.Request.Path);
        }

        await _next(context);

        // Log response status for non-success responses
        if (context.Response.StatusCode >= 400)
        {
            _logger.LogWarning("Request to {Path} returned status code {StatusCode}", 
                context.Request.Path, context.Response.StatusCode);
        }
    }
}

public class SecurityHeadersMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SecurityHeadersMiddleware> _logger;

    public SecurityHeadersMiddleware(RequestDelegate next, ILogger<SecurityHeadersMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Content Security Policy - Prevent XSS attacks
            var csp = "default-src 'self'; " +
                     "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                     "style-src 'self' 'unsafe-inline'; " +
                     "img-src 'self' data: https:; " +
                     "font-src 'self' data:; " +
                     "connect-src 'self'; " +
                     "frame-ancestors 'none'; " +
                     "base-uri 'self'; " +
                     "form-action 'self'";

            context.Response.Headers.Add("Content-Security-Policy", csp);

            // Prevent MIME type sniffing
            context.Response.Headers.Add("X-Content-Type-Options", "nosniff");

            // Prevent clickjacking attacks
            context.Response.Headers.Add("X-Frame-Options", "DENY");

            // Enable XSS protection (legacy browsers)
            context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");

            // Control referrer information
            context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");

            // Prevent browsers from inferring content types
            context.Response.Headers.Add("X-Download-Options", "noopen");

            // Prevent Adobe Flash and PDF files from loading content
            context.Response.Headers.Add("X-Permitted-Cross-Domain-Policies", "none");

            // HTTP Strict Transport Security (HSTS) - Force HTTPS
            if (context.Request.IsHttps)
            {
                context.Response.Headers.Add("Strict-Transport-Security",
                    "max-age=31536000; includeSubDomains; preload");
            }

            // Permissions Policy - Control browser features
            var permissionsPolicy = "camera=(), " +
                                  "microphone=(), " +
                                  "geolocation=(), " +
                                  "payment=(), " +
                                  "usb=(), " +
                                  "magnetometer=(), " +
                                  "gyroscope=(), " +
                                  "accelerometer=()";

            context.Response.Headers.Add("Permissions-Policy", permissionsPolicy);

            // Remove server information headers
            context.Response.Headers.Remove("Server");
            context.Response.Headers.Remove("X-Powered-By");
            context.Response.Headers.Remove("X-AspNet-Version");
            context.Response.Headers.Remove("X-AspNetMvc-Version");

            // Add custom security headers
            context.Response.Headers.Add("X-Security-Headers", "enabled");
            context.Response.Headers.Add("X-API-Version", "v1.0");

            await _next(context);

            // Log security violations if any
            if (context.Response.StatusCode >= 400)
            {
                var userAgent = context.Request.Headers["User-Agent"].ToString();
                var ipAddress = GetClientIpAddress(context);

                _logger.LogWarning("Security-related response {StatusCode} for {Method} {Path} from {IP} - {UserAgent}",
                    context.Response.StatusCode, context.Request.Method, context.Request.Path, ipAddress, userAgent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in security headers middleware");
            // Continue processing even if security headers fail
            await _next(context);
        }
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        // Check for real IP header
        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to connection remote IP
        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}
