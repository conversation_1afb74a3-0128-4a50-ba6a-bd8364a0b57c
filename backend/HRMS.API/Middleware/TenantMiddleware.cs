using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Data;
using System.Text.Json;

namespace HRMS.API.Middleware;

public class TenantMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantMiddleware> _logger;

    public TenantMiddleware(RequestDelegate next, ILogger<TenantMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantService tenantService, IJwtService jwtService)
    {
        try
        {
            // Skip tenant validation for public endpoints
            if (IsPublicEndpoint(context))
            {
                await _next(context);
                return;
            }

            // Extract tenant information from various sources
            var organizationId = GetOrganizationId(context, jwtService);
            var userId = GetUserId(context, jwtService);
            var userRole = GetUserRole(context, jwtService);

            // Validate tenant access for organization-specific endpoints
            if (IsOrganizationEndpoint(context) && !ValidateTenantAccess(context, organizationId, userRole))
            {
                _logger.LogWarning("Unauthorized tenant access attempt. Path: {Path}, OrganizationId: {OrganizationId}, Role: {Role}",
                    context.Request.Path, organizationId, userRole);

                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Access denied: Insufficient permissions for this organization");
                return;
            }

            if (!string.IsNullOrEmpty(organizationId))
            {
                tenantService.SetCurrentTenant(organizationId, userId, userRole);
                _logger.LogDebug("Set tenant context: OrganizationId={OrganizationId}, UserId={UserId}, Role={Role}",
                    organizationId, userId, userRole);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to set tenant context");
        }

        await _next(context);
    }

    private string? GetOrganizationId(HttpContext context, IJwtService jwtService)
    {
        // Try to get organization ID from header first
        if (context.Request.Headers.TryGetValue("X-Organization-ID", out var headerValue))
        {
            return headerValue.FirstOrDefault();
        }

        // Try to get from JWT token
        var token = GetTokenFromRequest(context);
        if (!string.IsNullOrEmpty(token))
        {
            return jwtService.GetOrganizationIdFromToken(token);
        }

        return null;
    }

    private string? GetUserId(HttpContext context, IJwtService jwtService)
    {
        var token = GetTokenFromRequest(context);
        if (!string.IsNullOrEmpty(token))
        {
            return jwtService.GetUserIdFromToken(token);
        }

        return null;
    }

    private string? GetUserRole(HttpContext context, IJwtService jwtService)
    {
        var token = GetTokenFromRequest(context);
        if (!string.IsNullOrEmpty(token))
        {
            return jwtService.GetUserRoleFromToken(token);
        }

        return null;
    }

    private string? GetTokenFromRequest(HttpContext context)
    {
        var authHeader = context.Request.Headers.Authorization.FirstOrDefault();
        if (authHeader != null && authHeader.StartsWith("Bearer "))
        {
            return authHeader.Substring("Bearer ".Length).Trim();
        }

        return null;
    }

    private bool IsPublicEndpoint(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        if (path == null) return false;

        // List of public endpoints that don't require tenant validation
        var publicEndpoints = new[]
        {
            "/api/v1/auth/login",
            "/api/v1/auth/register-organization",
            "/api/v1/auth/refresh",
            "/api/v1/health",
            "/swagger",
            "/api/v1/super-admin" // Super admin endpoints don't need tenant validation
        };

        return publicEndpoints.Any(endpoint => path.StartsWith(endpoint));
    }

    private bool IsOrganizationEndpoint(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        if (path == null) return false;

        // Organization-specific endpoints that require tenant validation
        var organizationEndpoints = new[]
        {
            "/api/v1/organization-admin",
            "/api/v1/employee-management",
            "/api/v1/attendance",
            "/api/v1/leave",
            "/api/v1/payroll",
            "/api/v1/performance",
            "/api/v1/tasks",
            "/api/v1/employee/tasks",
            "/api/v1/manager/tasks"
        };

        return organizationEndpoints.Any(endpoint => path.StartsWith(endpoint));
    }

    private bool ValidateTenantAccess(HttpContext context, string? organizationId, string? userRole)
    {
        // Super admins can access any organization
        if (userRole?.ToLowerInvariant() == "superadmin")
        {
            return true;
        }

        // Organization admins and employees must have an organization ID
        if (string.IsNullOrEmpty(organizationId))
        {
            _logger.LogWarning("Access denied: No organization ID found for user with role {Role}", userRole);
            return false;
        }

        // Allow access for organization admins and employees
        var allowedRoles = new[] { "orgadmin", "employee" };
        if (allowedRoles.Contains(userRole?.ToLowerInvariant()))
        {
            return true;
        }

        _logger.LogWarning("Access denied: Invalid role {Role} for organization access", userRole);
        return false;
    }
}

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionMiddleware> _logger;

    public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            success = false,
            error = new
            {
                code = "INTERNAL_SERVER_ERROR",
                message = "An internal server error occurred",
                details = exception.Message
            }
        };

        context.Response.StatusCode = exception switch
        {
            UnauthorizedAccessException => 401,
            ArgumentException => 400,
            KeyNotFoundException => 404,
            InvalidOperationException => 400,
            _ => 500
        };

        var jsonResponse = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}

public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var startTime = DateTime.UtcNow;
        
        _logger.LogInformation("Request started: {Method} {Path} from {RemoteIpAddress}",
            context.Request.Method,
            context.Request.Path,
            context.Connection.RemoteIpAddress);

        await _next(context);

        var duration = DateTime.UtcNow - startTime;
        
        _logger.LogInformation("Request completed: {Method} {Path} responded {StatusCode} in {Duration}ms",
            context.Request.Method,
            context.Request.Path,
            context.Response.StatusCode,
            duration.TotalMilliseconds);
    }
}

public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RateLimitingMiddleware> _logger;

    public RateLimitingMiddleware(RequestDelegate next, ILogger<RateLimitingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IRateLimitingService rateLimitingService, IJwtService jwtService)
    {
        try
        {
            // Skip rate limiting for certain endpoints if needed
            if (ShouldSkipRateLimit(context))
            {
                await _next(context);
                return;
            }

            // Get rate limit key (IP address or user ID)
            var rateLimitKey = GetRateLimitKey(context, jwtService);
            var endpoint = context.Request.Path.Value ?? string.Empty;

            // Get rate limit configuration for this endpoint
            var config = rateLimitingService.GetRateLimitConfig(endpoint);

            if (!config.Enabled)
            {
                await _next(context);
                return;
            }

            // Check if request is allowed
            var isAllowed = await rateLimitingService.IsRequestAllowedAsync(
                rateLimitKey, endpoint, config.MaxRequests, config.TimeWindowSeconds);

            if (!isAllowed)
            {
                // Rate limit exceeded
                var remainingRequests = await rateLimitingService.GetRemainingRequestsAsync(
                    rateLimitKey, endpoint, config.MaxRequests, config.TimeWindowSeconds);
                var resetTime = await rateLimitingService.GetResetTimeAsync(
                    rateLimitKey, endpoint, config.TimeWindowSeconds);

                _logger.LogWarning("Rate limit exceeded for {Key} on {Endpoint}. Config: {MaxRequests}/{TimeWindow}s",
                    rateLimitKey, endpoint, config.MaxRequests, config.TimeWindowSeconds);

                // Set rate limit headers
                context.Response.Headers.Add("X-RateLimit-Limit", config.MaxRequests.ToString());
                context.Response.Headers.Add("X-RateLimit-Remaining", remainingRequests.ToString());
                context.Response.Headers.Add("X-RateLimit-Reset", ((DateTimeOffset)resetTime).ToUnixTimeSeconds().ToString());
                context.Response.Headers.Add("Retry-After", ((int)(resetTime - DateTime.UtcNow).TotalSeconds).ToString());

                // Return 429 Too Many Requests
                context.Response.StatusCode = 429;
                context.Response.ContentType = "application/json";

                var errorResponse = new
                {
                    success = false,
                    error = new
                    {
                        code = "RATE_LIMIT_EXCEEDED",
                        message = $"Rate limit exceeded. Maximum {config.MaxRequests} requests per {config.TimeWindowSeconds} seconds.",
                        details = new
                        {
                            limit = config.MaxRequests,
                            windowSeconds = config.TimeWindowSeconds,
                            resetTime = resetTime,
                            retryAfter = (int)(resetTime - DateTime.UtcNow).TotalSeconds
                        }
                    }
                };

                await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
                return;
            }

            // Add rate limit headers for successful requests
            var remaining = await rateLimitingService.GetRemainingRequestsAsync(
                rateLimitKey, endpoint, config.MaxRequests, config.TimeWindowSeconds);
            var reset = await rateLimitingService.GetResetTimeAsync(
                rateLimitKey, endpoint, config.TimeWindowSeconds);

            context.Response.Headers.Add("X-RateLimit-Limit", config.MaxRequests.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", remaining.ToString());
            context.Response.Headers.Add("X-RateLimit-Reset", ((DateTimeOffset)reset).ToUnixTimeSeconds().ToString());

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in rate limiting middleware");
            // Continue processing even if rate limiting fails
            await _next(context);
        }
    }

    private bool ShouldSkipRateLimit(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        if (path == null) return false;

        // Skip rate limiting for static files, swagger, etc.
        var skipPaths = new[]
        {
            "/swagger",
            "/favicon.ico",
            "/.well-known"
        };

        return skipPaths.Any(skipPath => path.StartsWith(skipPath));
    }

    private string GetRateLimitKey(HttpContext context, IJwtService jwtService)
    {
        try
        {
            // Try to get user ID from JWT token first
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                var userId = jwtService.GetUserIdFromToken(token);
                if (!string.IsNullOrEmpty(userId))
                {
                    return $"user:{userId}";
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Could not extract user ID from token for rate limiting");
        }

        // Fall back to IP address
        var ipAddress = GetClientIpAddress(context);
        return $"ip:{ipAddress}";
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        // Check for real IP header
        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to connection remote IP
        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}
