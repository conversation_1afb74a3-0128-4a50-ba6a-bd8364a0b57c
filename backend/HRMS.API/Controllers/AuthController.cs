using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/auth")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// User Login
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>JWT token and user information</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<LoginResponseDto>>> Login([FromBody] LoginRequestDto request)
    {
        try
        {
            // Validate model state
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Login attempt with invalid model state: {Errors}", string.Join(", ", errors));

                return BadRequest(ApiResponse<LoginResponseDto>.ErrorResult(
                    "VALIDATION_ERROR",
                    "Invalid input data",
                    string.Join("; ", errors)));
            }

            _logger.LogInformation("Login attempt for email: {Email}", request.Email);

            var result = await _authService.LoginAsync(request);

            if (!result.Success)
            {
                _logger.LogWarning("Login failed for email: {Email}. Error: {Error}",
                    request.Email, result.Error?.Message);

                return result.Error?.Code switch
                {
                    "INVALID_CREDENTIALS" => Unauthorized(result),
                    "ACCOUNT_DISABLED" => Unauthorized(result),
                    "ORGANIZATION_INACTIVE" => Unauthorized(result),
                    "VALIDATION_ERROR" => BadRequest(result),
                    _ => StatusCode(500, result)
                };
            }

            _logger.LogInformation("Login successful for email: {Email}", request.Email);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during login for email: {Email}", request.Email);

            return StatusCode(500, ApiResponse<LoginResponseDto>.ErrorResult(
                "INTERNAL_SERVER_ERROR",
                "An unexpected error occurred during login"));
        }
    }

    /// <summary>
    /// Organization Registration
    /// </summary>
    /// <param name="request">Organization and admin user details</param>
    /// <returns>Organization information</returns>
    [HttpPost("register-organization")]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<OrganizationDto>>> RegisterOrganization([FromBody] OrganizationRegistrationDto request)
    {
        _logger.LogInformation("Organization registration attempt for domain: {Domain}", request.Organization.Domain);

        var result = await _authService.RegisterOrganizationAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Organization registration failed for domain: {Domain}. Error: {Error}", 
                request.Organization.Domain, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "DOMAIN_EXISTS" => Conflict(result),
                "EMAIL_EXISTS" => Conflict(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Organization registration successful for domain: {Domain}", request.Organization.Domain);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Refresh JWT Token
    /// </summary>
    /// <param name="request">Refresh token</param>
    /// <returns>New JWT token</returns>
    [HttpPost("refresh")]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<RefreshTokenResponseDto>>> RefreshToken([FromBody] RefreshTokenRequestDto request)
    {
        _logger.LogInformation("Token refresh attempt");

        var result = await _authService.RefreshTokenAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Token refresh failed. Error: {Error}", result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "INVALID_TOKEN" => Unauthorized(result),
                "USER_NOT_FOUND" => Unauthorized(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Token refresh successful");
        return Ok(result);
    }

    /// <summary>
    /// User Logout
    /// </summary>
    /// <returns>Success message</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<ActionResult<ApiResponse>> Logout()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Logout attempt for user: {UserId}", userId);

        var result = await _authService.LogoutAsync(userId);

        if (!result.Success)
        {
            _logger.LogWarning("Logout failed for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            return StatusCode(500, result);
        }

        _logger.LogInformation("Logout successful for user: {UserId}", userId);
        return Ok(result);
    }

    /// <summary>
    /// Change Password
    /// </summary>
    /// <param name="request">Password change request</param>
    /// <returns>Password change result</returns>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<PasswordResetResponseDto>>> ChangePassword([FromBody] ChangePasswordRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(ApiResponse<PasswordResetResponseDto>.ErrorResult(
                    "UNAUTHORIZED", "User not authenticated"));
            }

            _logger.LogInformation("Password change attempt for user: {UserId}", userId);

            var result = await _authService.ChangePasswordAsync(userId, request);

            if (!result.Success)
            {
                _logger.LogWarning("Password change failed for user: {UserId}. Error: {Error}",
                    userId, result.Error?.Message);

                return result.Error?.Code switch
                {
                    "INVALID_CURRENT_PASSWORD" => BadRequest(result),
                    "INVALID_PASSWORD" => BadRequest(result),
                    "USER_NOT_FOUND" => NotFound(result),
                    _ => StatusCode(500, result)
                };
            }

            _logger.LogInformation("Password change successful for user: {UserId}", userId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during password change");
            return StatusCode(500, ApiResponse<PasswordResetResponseDto>.ErrorResult(
                "INTERNAL_SERVER_ERROR", "An unexpected error occurred during password change"));
        }
    }

    /// <summary>
    /// Reset User Password (Admin Only)
    /// </summary>
    /// <param name="request">Password reset request</param>
    /// <returns>Password reset result</returns>
    [HttpPost("reset-password")]
    [Authorize(Roles = "SuperAdmin,OrgAdmin")]
    public async Task<ActionResult<ApiResponse<PasswordResetResponseDto>>> ResetUserPassword([FromBody] ResetPasswordRequestDto request)
    {
        try
        {
            var adminUserId = GetCurrentUserId();
            if (string.IsNullOrEmpty(adminUserId))
            {
                return Unauthorized(ApiResponse<PasswordResetResponseDto>.ErrorResult(
                    "UNAUTHORIZED", "Admin not authenticated"));
            }

            _logger.LogInformation("Password reset attempt by admin: {AdminUserId} for user: {UserId}",
                adminUserId, request.UserId);

            var result = await _authService.ResetUserPasswordAsync(adminUserId, request);

            if (!result.Success)
            {
                _logger.LogWarning("Password reset failed by admin: {AdminUserId} for user: {UserId}. Error: {Error}",
                    adminUserId, request.UserId, result.Error?.Message);

                return result.Error?.Code switch
                {
                    "INSUFFICIENT_PERMISSIONS" => Forbid(),
                    "INVALID_PASSWORD" => BadRequest(result),
                    "USER_NOT_FOUND" => NotFound(result),
                    "ADMIN_NOT_FOUND" => NotFound(result),
                    _ => StatusCode(500, result)
                };
            }

            _logger.LogInformation("Password reset successful by admin: {AdminUserId} for user: {UserId}",
                adminUserId, request.UserId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during password reset");
            return StatusCode(500, ApiResponse<PasswordResetResponseDto>.ErrorResult(
                "INTERNAL_SERVER_ERROR", "An unexpected error occurred during password reset"));
        }
    }

    private string GetCurrentUserId()
    {
        return User.FindFirst("user_id")?.Value ??
               User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ??
               string.Empty;
    }
}
