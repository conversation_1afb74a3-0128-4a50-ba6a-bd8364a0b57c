using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/attendance")]
[Authorize]
public class AttendanceController : BaseController
{
    private readonly IAttendanceService _attendanceService;
    private readonly ILogger<AttendanceController> _logger;

    public AttendanceController(IAttendanceService attendanceService, ILogger<AttendanceController> logger)
    {
        _attendanceService = attendanceService;
        _logger = logger;
    }

    /// <summary>
    /// Check In
    /// </summary>
    /// <param name="request">Check-in details</param>
    /// <returns>Attendance record with check-in information</returns>
    [HttpPost("checkin")]
    public async Task<ActionResult<ApiResponse<AttendanceResponseDto>>> CheckIn([FromBody] CheckInRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Check-in attempt for user: {UserId}", userId);

        var result = await _attendanceService.CheckInAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Check-in failed for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "ALREADY_CHECKED_IN" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Check-in successful for user: {UserId}", userId);
        return Ok(result);
    }

    /// <summary>
    /// Check Out
    /// </summary>
    /// <param name="request">Check-out details</param>
    /// <returns>Attendance record with check-out information</returns>
    [HttpPost("checkout")]
    public async Task<ActionResult<ApiResponse<AttendanceResponseDto>>> CheckOut([FromBody] CheckOutRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Check-out attempt for user: {UserId}", userId);

        var result = await _attendanceService.CheckOutAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Check-out failed for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "NOT_CHECKED_IN" => BadRequest(result),
                "ALREADY_CHECKED_OUT" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Check-out successful for user: {UserId}", userId);
        return Ok(result);
    }

    /// <summary>
    /// Get Attendance Records
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of attendance records with summary</returns>
    [HttpGet("records")]
    public async Task<ActionResult<ApiResponse<AttendanceRecordsResponseDto>>> GetAttendanceRecords([FromQuery] GetAttendanceRequestDto request)
    {
        var currentUserId = GetCurrentUserId();
        var currentUserRole = GetCurrentUserRole();

        // If not admin and no specific user requested, default to current user
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() && !request.UserId.HasValue)
        {
            request.UserId = Guid.Parse(currentUserId);
        }

        // Non-admin users can only see their own records
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() && 
            request.UserId.HasValue && request.UserId.Value.ToString() != currentUserId)
        {
            return Forbid();
        }

        _logger.LogInformation("Getting attendance records for user: {UserId}, Role: {Role}", 
            request.UserId, currentUserRole);

        var result = await _attendanceService.GetAttendanceRecordsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get attendance records. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get organization-wide attendance data (Admin only)
    /// </summary>
    /// <param name="request">Filter parameters for organization attendance</param>
    /// <returns>Organization attendance data with employee details and statistics</returns>
    [HttpGet("organization")]
    [Authorize(Roles = "OrgAdmin")]
    public async Task<ActionResult<ApiResponse<OrganizationAttendanceResponseDto>>> GetOrganizationAttendance([FromQuery] GetOrganizationAttendanceRequestDto request)
    {
        _logger.LogInformation("Organization attendance request from admin: {UserId}", GetCurrentUserId());

        var result = await _attendanceService.GetOrganizationAttendanceAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Organization attendance request failed. Error: {Error}", result.Error?.Message);
            return result.Error?.Code switch
            {
                "INVALID_TENANT" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }
}
