using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/leave")]
[Authorize]
public class LeaveController : BaseController
{
    private readonly ILeaveService _leaveService;
    private readonly ILogger<LeaveController> _logger;

    public LeaveController(ILeaveService leaveService, ILogger<LeaveController> logger)
    {
        _leaveService = leaveService;
        _logger = logger;
    }

    /// <summary>
    /// Get Leave Types for current organization
    /// </summary>
    /// <returns>List of leave types</returns>
    [HttpGet("types")]
    public async Task<ActionResult<ApiResponse<LeaveTypesResponseDto>>> GetLeaveTypes()
    {
        var orgId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(orgId))
        {
            _logger.LogWarning("Organization context missing for user {UserId}", GetCurrentUserId());
            return BadRequest(ApiResponse<LeaveTypesResponseDto>.ErrorResult("MISSING_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting leave types for organization {OrganizationId}", orgId);

        var result = await _leaveService.GetLeaveTypesAsync(orgId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get leave types for org {OrganizationId}. Error: {Error}", orgId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }


    /// <summary>
    /// Get Leave Balance
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>Leave balance information</returns>
    [HttpGet("balance")]
    public async Task<ActionResult<ApiResponse<LeaveBalanceResponseDto>>> GetLeaveBalance([FromQuery] GetLeaveBalanceRequestDto request)
    {
        var currentUserId = GetCurrentUserId();

        // If no specific user requested, default to current user
        if (!request.UserId.HasValue)
        {
            request.UserId = Guid.Parse(currentUserId);
        }

        // Non-admin users can only see their own balance
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() &&
            request.UserId.Value.ToString() != currentUserId)
        {
            return Forbid();
        }

        _logger.LogInformation("Getting leave balance for user: {UserId}", request.UserId);

        var result = await _leaveService.GetLeaveBalanceAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get leave balance for user: {UserId}. Error: {Error}",
                request.UserId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Apply for Leave
    /// </summary>
    /// <param name="request">Leave application details</param>
    /// <returns>Leave request information</returns>
    [HttpPost("apply")]
    public async Task<ActionResult<ApiResponse<LeaveRequestResponseDto>>> ApplyLeave([FromBody] ApplyLeaveRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Leave application for user: {UserId}", userId);

        var result = await _leaveService.ApplyLeaveAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Leave application failed for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "INSUFFICIENT_BALANCE" => BadRequest(result),
                "OVERLAPPING_LEAVE" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Leave application successful for user: {UserId}", userId);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Get Leave Requests
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of leave requests</returns>
    [HttpGet("requests")]
    public async Task<ActionResult<ApiResponse<LeaveRequestsResponseDto>>> GetLeaveRequests([FromQuery] GetLeaveRequestsDto request)
    {
        var currentUserId = GetCurrentUserId();
        var currentUserRole = GetCurrentUserRole();
        var currentOrgId = GetCurrentOrganizationId();

        _logger.LogInformation("Leave requests called by user: {UserId}, Role: {Role}, OrgId: {OrgId}",
            currentUserId, currentUserRole, currentOrgId);

        // If not admin and no specific user requested, default to current user
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() && !request.UserId.HasValue)
        {
            request.UserId = Guid.Parse(currentUserId);
            _logger.LogInformation("Setting request.UserId to current user: {UserId}", request.UserId);
        }

        // Non-admin users can only see their own requests
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() &&
            request.UserId.HasValue && request.UserId.Value.ToString() != currentUserId)
        {
            _logger.LogWarning("Access denied: User {CurrentUserId} trying to access requests for {RequestedUserId}",
                currentUserId, request.UserId);
            return Forbid();
        }

        _logger.LogInformation("Getting leave requests for user: {UserId}", request.UserId);

        var result = await _leaveService.GetLeaveRequestsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get leave requests. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Update Leave Request Status
    /// </summary>
    /// <param name="id">Leave request ID</param>
    /// <param name="request">Status update details</param>
    /// <returns>Updated leave request</returns>
    [HttpPut("requests/{id}/status")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<LeaveRequestDto>>> UpdateLeaveStatus(Guid id, [FromBody] UpdateLeaveStatusDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Updating leave request status for request: {LeaveRequestId} by user: {UserId}", id, userId);

        var result = await _leaveService.UpdateLeaveStatusAsync(id, userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update leave request status for request: {LeaveRequestId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "LEAVE_REQUEST_NOT_FOUND" => NotFound(result),
                "INVALID_STATUS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Leave request status updated successfully for request: {LeaveRequestId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Get organization-wide leave requests (Admin only)
    /// </summary>
    /// <param name="request">Filter parameters for organization leave requests</param>
    /// <returns>Organization leave requests with pagination</returns>
    [HttpGet("organization")]
    [Authorize(Roles = "OrgAdmin")]
    public async Task<ActionResult<ApiResponse<OrganizationLeaveRequestsResponseDto>>> GetOrganizationLeaveRequests([FromQuery] GetOrganizationLeaveRequestsDto request)
    {
        var orgId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(orgId))
        {
            _logger.LogWarning("Organization context missing for user {UserId}", GetCurrentUserId());
            return BadRequest(ApiResponse<OrganizationLeaveRequestsResponseDto>.ErrorResult("MISSING_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Organization leave requests request from admin: {UserId}", GetCurrentUserId());

        var result = await _leaveService.GetOrganizationLeaveRequestsAsync(orgId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Organization leave requests request failed. Error: {Error}", result.Error?.Message);
            return result.Error?.Code switch
            {
                "INVALID_ORGANIZATION" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Approve or Reject Leave Request (Admin only)
    /// </summary>
    /// <param name="id">Leave request ID</param>
    /// <param name="request">Approval/rejection details</param>
    /// <returns>Updated leave request</returns>
    [HttpPut("requests/{id}/approve-reject")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<LeaveRequestDto>>> ApproveRejectLeave(Guid id, [FromBody] ApproveRejectLeaveDto request)
    {
        var adminUserId = GetCurrentUserId();
        _logger.LogInformation("Approving/rejecting leave request {LeaveRequestId} by admin: {AdminUserId}", id, adminUserId);

        var result = await _leaveService.ApproveRejectLeaveAsync(id, adminUserId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to approve/reject leave request {LeaveRequestId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "LEAVE_REQUEST_NOT_FOUND" => NotFound(result),
                "INVALID_STATUS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Leave request {LeaveRequestId} processed successfully", id);
        return Ok(result);
    }

    /// <summary>
    /// Get leave statistics for organization (Admin only)
    /// </summary>
    /// <param name="request">Filter parameters for statistics</param>
    /// <returns>Leave statistics</returns>
    [HttpGet("statistics")]
    [Authorize(Roles = "OrgAdmin")]
    public async Task<ActionResult<ApiResponse<LeaveStatisticsResponseDto>>> GetLeaveStatistics([FromQuery] GetLeaveStatisticsRequestDto request)
    {
        var orgId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(orgId))
        {
            _logger.LogWarning("Organization context missing for user {UserId}", GetCurrentUserId());
            return BadRequest(ApiResponse<LeaveStatisticsResponseDto>.ErrorResult("MISSING_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting leave statistics for organization {OrganizationId}", orgId);

        var result = await _leaveService.GetLeaveStatisticsAsync(orgId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get leave statistics for org {OrganizationId}. Error: {Error}", orgId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }
}
