using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/performance")]
[Authorize]
public class PerformanceController : BaseController
{
    private readonly IPerformanceService _performanceService;
    private readonly ILogger<PerformanceController> _logger;

    public PerformanceController(IPerformanceService performanceService, ILogger<PerformanceController> logger)
    {
        _performanceService = performanceService;
        _logger = logger;
    }

    /// <summary>
    /// Get Performance Reviews
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of performance reviews</returns>
    [HttpGet("reviews")]
    public async Task<ActionResult<ApiResponse<PerformanceReviewsResponseDto>>> GetPerformanceReviews([FromQuery] GetPerformanceReviewsRequestDto request)
    {
        var currentUserId = GetCurrentUserId();

        // If not admin and no specific employee/reviewer requested, default to current user as employee
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() && 
            !request.EmployeeId.HasValue && !request.ReviewerId.HasValue)
        {
            request.EmployeeId = Guid.Parse(currentUserId);
        }

        // Non-admin users can only see their own reviews (as employee or reviewer)
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin())
        {
            var currentUserGuid = Guid.Parse(currentUserId);
            if (request.EmployeeId.HasValue && request.EmployeeId.Value != currentUserGuid &&
                request.ReviewerId.HasValue && request.ReviewerId.Value != currentUserGuid)
            {
                return Forbid();
            }
        }

        _logger.LogInformation("Getting performance reviews for employee: {EmployeeId}, reviewer: {ReviewerId}", 
            request.EmployeeId, request.ReviewerId);

        var result = await _performanceService.GetPerformanceReviewsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get performance reviews. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create Performance Review
    /// </summary>
    /// <param name="request">Performance review creation details</param>
    /// <returns>Created performance review information</returns>
    [HttpPost("reviews")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<CreatePerformanceReviewResponseDto>>> CreatePerformanceReview([FromBody] CreatePerformanceReviewRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Creating performance review for employee: {EmployeeId} by user: {UserId}", 
            request.EmployeeId, userId);

        var result = await _performanceService.CreatePerformanceReviewAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create performance review for employee: {EmployeeId}. Error: {Error}", 
                request.EmployeeId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => BadRequest(result),
                "INVALID_GOAL_WEIGHTS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Performance review created successfully for employee: {EmployeeId}", request.EmployeeId);
        return StatusCode(201, result);
    }
}
