using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using System.Security.Claims;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/dashboard")]
[Authorize]
public class DashboardController : BaseController
{
    private readonly ISuperAdminService _superAdminService;
    private readonly IOrganizationAdminService _organizationAdminService;
    private readonly IEmployeeService _employeeService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(
        ISuperAdminService superAdminService,
        IOrganizationAdminService organizationAdminService,
        IEmployeeService employeeService,
        ILogger<DashboardController> logger)
    {
        _superAdminService = superAdminService;
        _organizationAdminService = organizationAdminService;
        _employeeService = employeeService;
        _logger = logger;
    }

    /// <summary>
    /// Get Super Admin Dashboard Data
    /// </summary>
    /// <returns>Super admin dashboard statistics and metrics</returns>
    [HttpGet("super-admin")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<ApiResponse<SuperAdminDashboardDto>>> GetSuperAdminDashboard()
    {
        _logger.LogInformation("Getting super admin dashboard data");

        var result = await _superAdminService.GetDashboardAsync();

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get super admin dashboard data. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Organization Admin Dashboard Data
    /// </summary>
    /// <returns>Organization admin dashboard statistics and metrics</returns>
    [HttpGet("organization-admin")]
    [Authorize(Roles = "OrgAdmin")]
    public async Task<ActionResult<ApiResponse<OrganizationDashboardDto>>> GetOrganizationAdminDashboard()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Getting organization admin dashboard data for user: {UserId}", userId);

        var result = await _organizationAdminService.GetOrganizationDashboardAsync(userId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization admin dashboard data for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee Dashboard Data
    /// </summary>
    /// <returns>Employee dashboard statistics and metrics</returns>
    [HttpGet("employee")]
    [Authorize(Roles = "Employee")]
    public async Task<ActionResult<ApiResponse<EmployeeDashboardDto>>> GetEmployeeDashboard()
    {
        _logger.LogInformation("Getting employee dashboard data");

        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var userGuid))
        {
            return BadRequest(ApiResponse<EmployeeDashboardDto>.ErrorResult("INVALID_USER", "Invalid user ID"));
        }

        var result = await _employeeService.GetDashboardAsync(userGuid);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee dashboard data for user {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Dashboard Statistics for Current User
    /// </summary>
    /// <returns>Role-specific dashboard statistics</returns>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetDashboardStats()
    {
        var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        _logger.LogInformation("Getting dashboard stats for user {UserId} with role {Role}", userId, userRole);

        try
        {
            return userRole?.ToLower() switch
            {
                "superadmin" => Ok(await _superAdminService.GetDashboardAsync()),
                "orgadmin" => Ok(await _organizationAdminService.GetOrganizationDashboardAsync(userId)),
                "employee" when Guid.TryParse(userId, out var userGuid) =>
                    Ok(await _employeeService.GetDashboardAsync(userGuid)),
                _ => BadRequest(ApiResponse<object>.ErrorResult("INVALID_ROLE", "Invalid user role"))
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard stats for user {UserId}", userId);
            return StatusCode(500, ApiResponse<object>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting dashboard statistics"));
        }
    }
}
