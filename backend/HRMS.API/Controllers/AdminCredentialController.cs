using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using HRMS.Core.Services;
using HRMS.Application.Common;

namespace HRMS.API.Controllers;

/// <summary>
/// Controller for managing admin credentials (Development/Testing only)
/// </summary>
[ApiController]
[Route("api/v1/admin-credentials")]
[Authorize(Roles = "SuperAdmin")]
public class AdminCredentialController : ControllerBase
{
    private readonly IAdminCredentialService _adminCredentialService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AdminCredentialController> _logger;

    public AdminCredentialController(
        IAdminCredentialService adminCredentialService,
        IConfiguration configuration,
        ILogger<AdminCredentialController> logger)
    {
        _adminCredentialService = adminCredentialService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Generate new admin credentials (Development only)
    /// </summary>
    /// <param name="adminEmail">Admin email address</param>
    /// <param name="adminName">Admin full name</param>
    /// <returns>Generated credentials</returns>
    [HttpPost("generate")]
    public async Task<ActionResult<ApiResponse<AdminCredentialDisplayDto>>> GenerateAdminCredentials(
        [FromQuery] string adminEmail,
        [FromQuery] string adminName)
    {
        // Only allow in development environment
        if (_configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT") != "Development")
        {
            return Forbid("This endpoint is only available in development environment");
        }

        try
        {
            _logger.LogInformation("Generating admin credentials for {AdminEmail}", adminEmail);

            var credentials = await _adminCredentialService.GenerateAdminCredentialsAsync(adminEmail, adminName);

            var response = new AdminCredentialDisplayDto
            {
                Email = credentials.Email,
                Name = credentials.Name,
                Password = credentials.PlainTextPassword,
                Source = credentials.Source.ToString(),
                GeneratedAt = credentials.GeneratedAt,
                RequirePasswordReset = credentials.RequirePasswordReset
            };

            return Ok(ApiResponse<AdminCredentialDisplayDto>.SuccessResult(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating admin credentials for {AdminEmail}", adminEmail);
            return StatusCode(500, ApiResponse<AdminCredentialDisplayDto>.ErrorResult(
                "INTERNAL_SERVER_ERROR", "An error occurred while generating admin credentials"));
        }
    }

    /// <summary>
    /// Get admin credential information (without password)
    /// </summary>
    /// <param name="adminEmail">Admin email address</param>
    /// <returns>Credential information</returns>
    [HttpGet("info")]
    public async Task<ActionResult<ApiResponse<AdminCredentialInfoDto>>> GetAdminCredentialInfo(
        [FromQuery] string adminEmail)
    {
        try
        {
            _logger.LogInformation("Getting admin credential info for {AdminEmail}", adminEmail);

            var configCredentials = await _adminCredentialService.GetAdminCredentialsFromConfigAsync(adminEmail);

            var response = new AdminCredentialInfoDto
            {
                Email = adminEmail,
                HasConfiguredCredentials = configCredentials != null,
                CredentialSource = configCredentials?.Source.ToString() ?? "None",
                LastChecked = DateTime.UtcNow
            };

            return Ok(ApiResponse<AdminCredentialInfoDto>.SuccessResult(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin credential info for {AdminEmail}", adminEmail);
            return StatusCode(500, ApiResponse<AdminCredentialInfoDto>.ErrorResult(
                "INTERNAL_SERVER_ERROR", "An error occurred while getting admin credential info"));
        }
    }
}

/// <summary>
/// DTO for displaying admin credentials (Development only)
/// </summary>
public class AdminCredentialDisplayDto
{
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public bool RequirePasswordReset { get; set; }
}

/// <summary>
/// DTO for admin credential information (without sensitive data)
/// </summary>
public class AdminCredentialInfoDto
{
    public string Email { get; set; } = string.Empty;
    public bool HasConfiguredCredentials { get; set; }
    public string CredentialSource { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
}
