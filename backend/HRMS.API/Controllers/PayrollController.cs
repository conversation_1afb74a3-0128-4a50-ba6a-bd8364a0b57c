using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/payroll")]
[Authorize]
public class PayrollController : BaseController
{
    private readonly IPayrollService _payrollService;
    private readonly ILogger<PayrollController> _logger;

    public PayrollController(IPayrollService payrollService, ILogger<PayrollController> logger)
    {
        _payrollService = payrollService;
        _logger = logger;
    }

    /// <summary>
    /// Get Employee Payroll
    /// </summary>
    /// <param name="request">Payroll filter parameters</param>
    /// <returns>Employee payroll information</returns>
    [HttpGet("employee")]
    public async Task<ActionResult<ApiResponse<EmployeePayrollDto>>> GetEmployeePayroll([FromQuery] GetEmployeePayrollRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Getting payroll information for user: {UserId}", userId);

        var result = await _payrollService.GetEmployeePayrollAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get payroll information for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee Benefits
    /// </summary>
    /// <returns>Employee benefits information</returns>
    [HttpGet("benefits")]
    public async Task<ActionResult<ApiResponse<EmployeeBenefitsDto>>> GetEmployeeBenefits()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Getting benefits information for user: {UserId}", userId);

        var result = await _payrollService.GetEmployeeBenefitsAsync(userId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get benefits information for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }
}
