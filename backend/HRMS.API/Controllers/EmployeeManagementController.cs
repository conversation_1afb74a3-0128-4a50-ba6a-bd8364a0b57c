using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/employee-management")]
[Authorize]
public class EmployeeManagementController : BaseController
{
    private readonly IEmployeeManagementService _employeeManagementService;
    private readonly IDatabaseUpdateService _databaseUpdateService;
    private readonly ILogger<EmployeeManagementController> _logger;

    public EmployeeManagementController(
        IEmployeeManagementService employeeManagementService,
        IDatabaseUpdateService databaseUpdateService,
        ILogger<EmployeeManagementController> logger)
    {
        _employeeManagementService = employeeManagementService;
        _databaseUpdateService = databaseUpdateService;
        _logger = logger;
    }

    /// <summary>
    /// Get All Employees
    /// </summary>
    /// <param name="request">Filter and pagination parameters</param>
    /// <returns>Paginated list of employees</returns>
    [HttpGet("employees")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<PaginatedResponse<EmployeeDto>>>> GetEmployees([FromQuery] GetEmployeesRequestDto request)
    {
        _logger.LogInformation("Getting employees with filters: {@Request}", request);

        var result = await _employeeManagementService.GetEmployeesAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employees. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee by ID
    /// </summary>
    /// <param name="id">Employee ID</param>
    /// <returns>Employee information</returns>
    [HttpGet("employees/{id}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin,Employee")]
    public async Task<ActionResult<ApiResponse<EmployeeDto>>> GetEmployee(Guid id)
    {
        _logger.LogInformation("Getting employee by ID: {EmployeeId}", id);

        // Employees can only access their own data
        if (IsCurrentUserEmployee() && !string.Equals(GetCurrentUserId(), id.ToString(), StringComparison.OrdinalIgnoreCase))
        {
            return Forbid();
        }

        var result = await _employeeManagementService.GetEmployeeByIdAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee by ID: {EmployeeId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Create New Employee
    /// </summary>
    /// <param name="request">Employee creation data</param>
    /// <returns>Created employee information</returns>
    [HttpPost("employees")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<EmployeeDto>>> CreateEmployee([FromBody] CreateEmployeeRequestDto request)
    {
        _logger.LogInformation("Creating new employee with email: {Email}", request.Email);

        var result = await _employeeManagementService.CreateEmployeeAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create employee with email: {Email}. Error: {Error}", 
                request.Email, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMAIL_EXISTS" => Conflict(result),
                "EMPLOYEE_ID_EXISTS" => Conflict(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Successfully created employee with email: {Email}", request.Email);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Update Employee
    /// </summary>
    /// <param name="id">Employee ID</param>
    /// <param name="request">Employee update data</param>
    /// <returns>Updated employee information</returns>
    [HttpPut("employees/{id}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<EmployeeDto>>> UpdateEmployee(Guid id, [FromBody] UpdateEmployeeRequestDto request)
    {
        _logger.LogInformation("Updating employee: {EmployeeId}", id);

        var result = await _employeeManagementService.UpdateEmployeeAsync(id, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update employee: {EmployeeId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Delete Employee
    /// </summary>
    /// <param name="id">Employee ID</param>
    /// <returns>Success message</returns>
    [HttpDelete("employees/{id}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> DeleteEmployee(Guid id)
    {
        _logger.LogInformation("Deleting employee: {EmployeeId}", id);

        var result = await _employeeManagementService.DeleteEmployeeAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to delete employee: {EmployeeId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Organization Hierarchy
    /// </summary>
    /// <returns>Organization structure with departments and employees</returns>
    [HttpGet("hierarchy")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<OrganizationHierarchyDto>>> GetOrganizationHierarchy()
    {
        _logger.LogInformation("Getting organization hierarchy");

        var result = await _employeeManagementService.GetOrganizationHierarchyAsync();

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization hierarchy. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employees by Manager
    /// </summary>
    /// <param name="managerId">Manager ID</param>
    /// <returns>List of employees reporting to the manager</returns>
    [HttpGet("employees/by-manager/{managerId}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin,Employee")]
    public async Task<ActionResult<ApiResponse<List<EmployeeDto>>>> GetEmployeesByManager(Guid managerId)
    {
        _logger.LogInformation("Getting employees by manager: {ManagerId}", managerId);

        // Employees can only see their own subordinates
        if (IsCurrentUserEmployee() && !string.Equals(GetCurrentUserId(), managerId.ToString(), StringComparison.OrdinalIgnoreCase))
        {
            return Forbid();
        }

        var result = await _employeeManagementService.GetEmployeesByManagerAsync(managerId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employees by manager: {ManagerId}. Error: {Error}", 
                managerId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employees by Department
    /// </summary>
    /// <param name="department">Department name</param>
    /// <returns>List of employees in the department</returns>
    [HttpGet("employees/by-department/{department}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<List<EmployeeDto>>>> GetEmployeesByDepartment(string department)
    {
        _logger.LogInformation("Getting employees by department: {Department}", department);

        var result = await _employeeManagementService.GetEmployeesByDepartmentAsync(department);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employees by department: {Department}. Error: {Error}", 
                department, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee Statistics
    /// </summary>
    /// <returns>Employee statistics and metrics</returns>
    [HttpGet("stats")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<EmployeeStatsDto>>> GetEmployeeStats()
    {
        _logger.LogInformation("Getting employee statistics");

        var result = await _employeeManagementService.GetEmployeeStatsAsync();

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee statistics. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Validate Hierarchy Assignment
    /// </summary>
    /// <param name="employeeId">Employee ID to be assigned</param>
    /// <param name="managerId">Manager ID to assign to</param>
    /// <returns>Validation result</returns>
    [HttpPost("validate-hierarchy")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<bool>>> ValidateHierarchyAssignment([FromBody] ValidateHierarchyRequestDto request)
    {
        _logger.LogInformation("Validating hierarchy assignment: Employee {EmployeeId} to Manager {ManagerId}",
            request.EmployeeId, request.ManagerId);

        var result = await _employeeManagementService.ValidateHierarchyAssignmentAsync(request.EmployeeId, request.ManagerId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to validate hierarchy assignment. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Update Database Schema - Add missing EmployeeDetails columns
    /// </summary>
    /// <returns>Success status</returns>
    [HttpPost("update-schema")]
    public async Task<IActionResult> UpdateDatabaseSchema()
    {
        try
        {
            _logger.LogInformation("Updating database schema for organization");

            var organizationId = GetCurrentOrganizationId() ?? "1";
            var result = await _databaseUpdateService.AddMissingEmployeeDetailColumnsAsync(organizationId);

            if (result)
            {
                return Ok(new { success = true, message = "Database schema updated successfully" });
            }
            else
            {
                return StatusCode(500, new { success = false, error = new { message = "Failed to update database schema" } });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating database schema");
            return StatusCode(500, new { success = false, error = new { message = "An error occurred while updating database schema" } });
        }
    }

    /// <summary>
    /// Generate Login Credentials for Existing Employee
    /// </summary>
    /// <param name="id">Employee ID</param>
    /// <param name="request">Optional custom password request</param>
    /// <returns>Employee information with generated credentials</returns>
    [HttpPost("employees/{id}/generate-credentials")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<EmployeeDto>>> GenerateCredentials(Guid id, [FromBody] GenerateCredentialsRequestDto? request = null)
    {
        _logger.LogInformation("Generating credentials for employee: {EmployeeId}", id);

        // Use empty request if none provided (for backward compatibility)
        request ??= new GenerateCredentialsRequestDto();

        var result = await _employeeManagementService.GenerateCredentialsAsync(id, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to generate credentials for employee: {EmployeeId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                "CREDENTIALS_ALREADY_EXIST" => Conflict(result),
                "INVALID_PASSWORD" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Successfully generated credentials for employee: {EmployeeId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Change Password for Employee
    /// </summary>
    /// <param name="id">Employee ID</param>
    /// <param name="request">Password change request with new password</param>
    /// <returns>Employee information with updated password</returns>
    [HttpPut("employees/{id}/change-password")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<EmployeeDto>>> ChangePassword(Guid id, [FromBody] ChangePasswordRequestDto request)
    {
        _logger.LogInformation("Changing password for employee: {EmployeeId}", id);

        if (request == null || string.IsNullOrWhiteSpace(request.NewPassword))
        {
            return BadRequest(ApiResponse<EmployeeDto>.ErrorResult("INVALID_REQUEST", "New password is required"));
        }

        var result = await _employeeManagementService.ChangePasswordAsync(id, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to change password for employee: {EmployeeId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                "INVALID_PASSWORD" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Successfully changed password for employee: {EmployeeId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Update Master Database Schema - Add missing EmployeeDetails columns
    /// </summary>
    /// <returns>Success status</returns>
    [HttpPost("update-master-schema")]
    public async Task<IActionResult> UpdateMasterDatabaseSchema()
    {
        try
        {
            _logger.LogInformation("Updating master database schema");

            var result = await _databaseUpdateService.AddLoginCredentialColumnsToMasterDatabaseAsync();

            if (result)
            {
                return Ok(new { success = true, message = "Master database schema updated successfully" });
            }
            else
            {
                return StatusCode(500, new { success = false, error = new { message = "Failed to update master database schema" } });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating master database schema: {Message}", ex.Message);
            return StatusCode(500, new { success = false, error = new { message = ex.Message } });
        }
    }
}
