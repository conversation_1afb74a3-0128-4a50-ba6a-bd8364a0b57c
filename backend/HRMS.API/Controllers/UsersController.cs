using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/users")]
[Authorize]
public class UsersController : BaseController
{
    private readonly IAuthService _authService;
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(
        IAuthService authService, 
        IUserService userService,
        ILogger<UsersController> logger)
    {
        _authService = authService;
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// Get Current User Profile
    /// </summary>
    /// <returns>Current user's profile information</returns>
    [HttpGet("me")]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetCurrentUser()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Getting current user profile for user: {UserId}", userId);

        var result = await _authService.GetCurrentUserAsync(userId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get current user profile for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Update Current User Profile
    /// </summary>
    /// <param name="request">Profile update data</param>
    /// <returns>Updated user profile</returns>
    [HttpPut("me")]
    public async Task<ActionResult<ApiResponse<UserDto>>> UpdateCurrentUser([FromBody] UpdateUserProfileDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Updating user profile for user: {UserId}", userId);

        var result = await _authService.UpdateUserProfileAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update user profile for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Organization Users
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of users in organization</returns>
    [HttpGet]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<PaginatedResponse<UserDto>>>> GetUsers([FromQuery] GetUsersRequestDto request)
    {
        _logger.LogInformation("Getting organization users with filters: {@Request}", request);

        var result = await _userService.GetUsersAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization users. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create New Employee
    /// </summary>
    /// <param name="request">Employee creation data</param>
    /// <returns>Created employee information</returns>
    [HttpPost]
    [Authorize(Roles = "OrgAdmin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> CreateEmployee([FromBody] CreateEmployeeDto request)
    {
        _logger.LogInformation("Creating new employee with email: {Email}", request.Email);

        var result = await _userService.CreateEmployeeAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create employee with email: {Email}. Error: {Error}", 
                request.Email, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "EMAIL_EXISTS" => Conflict(result),
                "EMPLOYEE_ID_EXISTS" => Conflict(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Successfully created employee with email: {Email}", request.Email);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Get User by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User information</returns>
    [HttpGet("{id}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(Guid id)
    {
        _logger.LogInformation("Getting user by ID: {UserId}", id);

        var result = await _userService.GetUserByIdAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get user by ID: {UserId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Update User
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="request">User update data</param>
    /// <returns>Updated user information</returns>
    [HttpPut("{id}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> UpdateUser(Guid id, [FromBody] UpdateUserDto request)
    {
        _logger.LogInformation("Updating user: {UserId}", id);

        var result = await _userService.UpdateUserAsync(id, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update user: {UserId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Delete User
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success message</returns>
    [HttpDelete("{id}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> DeleteUser(Guid id)
    {
        _logger.LogInformation("Deleting user: {UserId}", id);

        var result = await _userService.DeleteUserAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to delete user: {UserId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }
}
