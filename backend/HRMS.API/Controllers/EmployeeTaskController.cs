using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.API.Controllers;

namespace HRMS.API.Controllers;

/// <summary>
/// Employee Task Management Controller
/// Handles employee self-task creation, daily updates, and task management
/// </summary>
[ApiController]
[Route("api/v1/employee/tasks")]
[Authorize]
public class EmployeeTaskController : BaseController
{
    private readonly ITaskService _taskService;
    private readonly ILogger<EmployeeTaskController> _logger;

    public EmployeeTaskController(ITaskService taskService, ILogger<EmployeeTaskController> logger)
    {
        _taskService = taskService;
        _logger = logger;
    }

    /// <summary>
    /// Create a self-task for the authenticated employee
    /// </summary>
    /// <param name="request">Task creation request</param>
    /// <returns>Created task details</returns>
    [HttpPost("self")]
    public async Task<ActionResult<ApiResponse<CreateSelfTaskResponseDto>>> CreateSelfTask([FromBody] CreateSelfTaskRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _taskService.CreateSelfTaskAsync(userId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get all tasks for the authenticated employee (both self-created and assigned)
    /// </summary>
    /// <param name="status">Filter by status</param>
    /// <param name="priority">Filter by priority</param>
    /// <param name="taskType">Filter by task type</param>
    /// <param name="isSelfCreated">Filter by self-created flag</param>
    /// <param name="dueDateFrom">Filter by due date from</param>
    /// <param name="dueDateTo">Filter by due date to</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of employee tasks</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<TasksResponseDto>>> GetEmployeeTasks(
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? taskType = null,
        [FromQuery] bool? isSelfCreated = null,
        [FromQuery] DateTime? dueDateFrom = null,
        [FromQuery] DateTime? dueDateTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(ApiResponse<TasksResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var request = new GetEmployeeTasksRequestDto
        {
            Status = status,
            Priority = priority,
            TaskType = taskType,
            IsSelfCreated = isSelfCreated,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetEmployeeTasksAsync(userId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create a daily update for a task
    /// </summary>
    /// <param name="request">Daily update request</param>
    /// <returns>Created daily update details</returns>
    [HttpPost("updates")]
    public async Task<ActionResult<ApiResponse<CreateDailyUpdateResponseDto>>> CreateDailyUpdate([FromBody] CreateDailyUpdateRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _taskService.CreateDailyUpdateAsync(userId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get daily updates for the authenticated employee's tasks
    /// </summary>
    /// <param name="taskId">Filter by specific task</param>
    /// <param name="dateFrom">Filter by date from</param>
    /// <param name="dateTo">Filter by date to</param>
    /// <param name="updateType">Filter by update type</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of daily updates</returns>
    [HttpGet("updates")]
    public async Task<ActionResult<ApiResponse<TaskUpdatesResponseDto>>> GetEmployeeDailyUpdates(
        [FromQuery] Guid? taskId = null,
        [FromQuery] DateTime? dateFrom = null,
        [FromQuery] DateTime? dateTo = null,
        [FromQuery] string? updateType = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(ApiResponse<TaskUpdatesResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var request = new GetDailyUpdatesRequestDto
        {
            TaskId = taskId,
            DateFrom = dateFrom,
            DateTo = dateTo,
            UpdateType = updateType,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetEmployeeDailyUpdatesAsync(userId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Check if the current user can view a specific task
    /// </summary>
    /// <param name="taskId">Task ID to check</param>
    /// <returns>Boolean indicating view permission</returns>
    [HttpGet("{taskId}/can-view")]
    public async Task<ActionResult<ApiResponse<bool>>> CanViewTask(Guid taskId)
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var canView = await _taskService.CanUserViewTaskAsync(userId, taskId);
        
        return Ok(ApiResponse<bool>.SuccessResult(canView, "Permission check completed"));
    }

    /// <summary>
    /// Check if the current user can edit a specific task
    /// </summary>
    /// <param name="taskId">Task ID to check</param>
    /// <returns>Boolean indicating edit permission</returns>
    [HttpGet("{taskId}/can-edit")]
    public async Task<ActionResult<ApiResponse<bool>>> CanEditTask(Guid taskId)
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var canEdit = await _taskService.CanUserEditTaskAsync(userId, taskId);
        
        return Ok(ApiResponse<bool>.SuccessResult(canEdit, "Permission check completed"));
    }
}
