using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/super-admin")]
[Authorize(Roles = "SuperAdmin")]
public class SuperAdminController : BaseController
{
    private readonly ISuperAdminService _superAdminService;
    private readonly ILogger<SuperAdminController> _logger;

    public SuperAdminController(
        ISuperAdminService superAdminService,
        ILogger<SuperAdminController> logger)
    {
        _superAdminService = superAdminService;
        _logger = logger;
    }

    /// <summary>
    /// Get Organizations
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of organizations with summary</returns>
    [HttpGet("organizations")]
    public async Task<ActionResult<ApiResponse<OrganizationsResponseDto>>> GetOrganizations([FromQuery] GetOrganizationsRequestDto request)
    {
        _logger.LogInformation("Getting organizations with filters: {@Request}", request);

        var result = await _superAdminService.GetOrganizationsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organizations. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create Organization
    /// </summary>
    /// <param name="request">Organization creation details</param>
    /// <returns>Created organization with admin credentials</returns>
    [HttpPost("organizations")]
    public async Task<ActionResult<ApiResponse<CreateOrganizationResponseDto>>> CreateOrganization([FromBody] CreateOrganizationRequestDto request)
    {
        _logger.LogInformation("Creating organization: {OrganizationName} with domain: {Domain}",
            request.Name, request.Domain);

        var result = await _superAdminService.CreateOrganizationAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create organization: {OrganizationName}. Error: {Error}",
                request.Name, result.Error?.Message);

            return result.Error?.Code switch
            {
                "DOMAIN_EXISTS" => Conflict(result),
                "EMAIL_EXISTS" => Conflict(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Organization created successfully: {OrganizationId}", result.Data?.OrganizationId);
        return CreatedAtAction(nameof(GetOrganizationDetails), new { id = result.Data?.OrganizationId }, result);
    }

    /// <summary>
    /// Get Organization Details
    /// </summary>
    /// <param name="id">Organization ID</param>
    /// <returns>Organization details</returns>
    [HttpGet("organizations/{id}")]
    public async Task<ActionResult<ApiResponse<OrganizationDetailsDto>>> GetOrganizationDetails(Guid id)
    {
        _logger.LogInformation("Getting organization details for organization: {OrganizationId}", id);

        var result = await _superAdminService.GetOrganizationDetailsAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization details for organization: {OrganizationId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "ORGANIZATION_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Organization Admin Credentials
    /// </summary>
    /// <param name="id">Organization ID</param>
    /// <returns>Admin credentials information</returns>
    [HttpGet("organizations/{id}/admin-credentials")]
    public async Task<ActionResult<ApiResponse<AdminCredentialsDto>>> GetOrganizationAdminCredentials(Guid id)
    {
        _logger.LogInformation("Getting admin credentials for organization: {OrganizationId}", id);

        var result = await _superAdminService.GetOrganizationAdminCredentialsAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get admin credentials for organization: {OrganizationId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "ORGANIZATION_NOT_FOUND" => NotFound(result),
                "ADMIN_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Reset Organization Admin Password
    /// </summary>
    /// <param name="id">Organization ID</param>
    /// <returns>New admin credentials</returns>
    [HttpPost("organizations/{id}/reset-admin-password")]
    public async Task<ActionResult<ApiResponse<AdminCredentialsDto>>> ResetOrganizationAdminPassword(Guid id)
    {
        _logger.LogInformation("Resetting admin password for organization: {OrganizationId}", id);

        var result = await _superAdminService.ResetOrganizationAdminPasswordAsync(id);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to reset admin password for organization: {OrganizationId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "ORGANIZATION_NOT_FOUND" => NotFound(result),
                "ADMIN_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Update User Password
    /// </summary>
    /// <param name="request">Password update request</param>
    /// <returns>Success response</returns>
    [HttpPost("update-user-password")]
    public async Task<ActionResult<ApiResponse>> UpdateUserPassword([FromBody] UpdateUserPasswordRequest request)
    {
        _logger.LogInformation("Updating password for user: {UserId}", request.UserId);

        var result = await _superAdminService.UpdateUserPasswordAsync(request.UserId, request.NewPassword);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update password for user: {UserId}. Error: {Error}",
                request.UserId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Update Organization Details
    /// </summary>
    /// <param name="id">Organization ID</param>
    /// <param name="request">Organization update details</param>
    /// <returns>Updated organization details</returns>
    [HttpPut("organizations/{id}")]
    public async Task<ActionResult<ApiResponse<OrganizationDetailsDto>>> UpdateOrganizationDetails(Guid id, [FromBody] UpdateOrganizationDetailsRequestDto request)
    {
        _logger.LogInformation("Updating organization details for organization: {OrganizationId}", id);

        var result = await _superAdminService.UpdateOrganizationDetailsAsync(id, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update organization details for organization: {OrganizationId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "ORGANIZATION_NOT_FOUND" => NotFound(result),
                "INVALID_DATA" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Organization details updated successfully for organization: {OrganizationId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Update Organization Status
    /// </summary>
    /// <param name="id">Organization ID</param>
    /// <param name="request">Status update details</param>
    /// <returns>Updated organization status</returns>
    [HttpPut("organizations/{id}/status")]
    public async Task<ActionResult<ApiResponse<UpdateOrganizationStatusResponseDto>>> UpdateOrganizationStatus(Guid id, [FromBody] UpdateOrganizationStatusRequestDto request)
    {
        _logger.LogInformation("Updating organization status for organization: {OrganizationId} to status: {Status}",
            id, request.Status);

        var result = await _superAdminService.UpdateOrganizationStatusAsync(id, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update organization status for organization: {OrganizationId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "ORGANIZATION_NOT_FOUND" => NotFound(result),
                "INVALID_STATUS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Organization status updated successfully for organization: {OrganizationId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Delete Organization
    /// </summary>
    /// <param name="id">Organization ID</param>
    /// <param name="force">Force delete even if organization has users</param>
    /// <returns>Success message</returns>
    [HttpDelete("organizations/{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteOrganization(Guid id, [FromQuery] bool force = false)
    {
        _logger.LogInformation("Deleting organization: {OrganizationId}, Force: {Force}", id, force);

        var result = await _superAdminService.DeleteOrganizationAsync(id, force);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to delete organization: {OrganizationId}. Error: {Error}",
                id, result.Error?.Message);

            return result.Error?.Code switch
            {
                "ORGANIZATION_NOT_FOUND" => NotFound(result),
                "ORGANIZATION_HAS_USERS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Organization deleted successfully: {OrganizationId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Delete Organization by Name (Complete Deletion)
    /// </summary>
    /// <param name="organizationName">Organization name to delete</param>
    /// <param name="force">Force delete even if organization has users</param>
    /// <returns>Success message</returns>
    [HttpDelete("organizations/by-name/{organizationName}")]
    public async Task<ActionResult<ApiResponse>> DeleteOrganizationByName(string organizationName, [FromQuery] bool force = false)
    {
        _logger.LogInformation("Deleting organization by name: {OrganizationName}, Force: {Force}", organizationName, force);

        if (string.IsNullOrWhiteSpace(organizationName))
        {
            return BadRequest(ApiResponse.ErrorResult("INVALID_ORGANIZATION_NAME", "Organization name cannot be empty"));
        }

        try
        {
            // Find organization by name
            var organizationsRequest = new GetOrganizationsRequestDto();
            var organizations = await _superAdminService.GetOrganizationsAsync(organizationsRequest);
            if (!organizations.Success || organizations.Data?.Organizations == null)
            {
                return StatusCode(500, ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", "Failed to retrieve organizations"));
            }

            var organization = organizations.Data.Organizations.FirstOrDefault(o =>
                string.Equals(o.Name, organizationName, StringComparison.OrdinalIgnoreCase));

            if (organization == null)
            {
                _logger.LogWarning("Organization not found: {OrganizationName}", organizationName);
                return NotFound(ApiResponse.ErrorResult("ORGANIZATION_NOT_FOUND", $"Organization '{organizationName}' not found"));
            }

            // Use existing delete method
            var result = await _superAdminService.DeleteOrganizationAsync(organization.Id, force);

            if (!result.Success)
            {
                _logger.LogWarning("Failed to delete organization: {OrganizationName}. Error: {Error}",
                    organizationName, result.Error?.Message);

                return result.Error?.Code switch
                {
                    "ORGANIZATION_NOT_FOUND" => NotFound(result),
                    "ORGANIZATION_HAS_USERS" => BadRequest(result),
                    _ => StatusCode(500, result)
                };
            }

            _logger.LogInformation("Organization deleted successfully: {OrganizationName}", organizationName);
            return Ok(ApiResponse.SuccessResult($"Organization '{organizationName}' and all associated data deleted successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting organization by name: {OrganizationName}", organizationName);
            return StatusCode(500, ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while deleting the organization"));
        }
    }

    /// <summary>
    /// Get System Analytics
    /// </summary>
    /// <param name="request">Analytics filter parameters</param>
    /// <returns>System analytics data</returns>
    [HttpGet("analytics")]
    public async Task<ActionResult<ApiResponse<SystemAnalyticsDto>>> GetSystemAnalytics([FromQuery] GetSystemAnalyticsRequestDto request)
    {
        _logger.LogInformation("Getting system analytics for period: {Period}", request.Period);

        var result = await _superAdminService.GetSystemAnalyticsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get system analytics. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get System Settings
    /// </summary>
    /// <returns>System settings</returns>
    [HttpGet("settings")]
    public async Task<ActionResult<ApiResponse<SystemSettingsDto>>> GetSystemSettings()
    {
        _logger.LogInformation("Getting system settings");

        var result = await _superAdminService.GetSystemSettingsAsync();

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get system settings. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Update System Settings
    /// </summary>
    /// <param name="request">Settings update details</param>
    /// <returns>Updated system settings</returns>
    [HttpPut("settings")]
    public async Task<ActionResult<ApiResponse<SystemSettingsDto>>> UpdateSystemSettings([FromBody] UpdateSystemSettingsRequestDto request)
    {
        _logger.LogInformation("Updating system settings");

        var result = await _superAdminService.UpdateSystemSettingsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update system settings. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        _logger.LogInformation("System settings updated successfully");
        return Ok(result);
    }
}
