using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/recruitment")]
[Authorize]
public class RecruitmentController : BaseController
{
    private readonly IRecruitmentService _recruitmentService;
    private readonly ILogger<RecruitmentController> _logger;

    public RecruitmentController(IRecruitmentService recruitmentService, ILogger<RecruitmentController> logger)
    {
        _recruitmentService = recruitmentService;
        _logger = logger;
    }

    /// <summary>
    /// Get Job Postings
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of job postings</returns>
    [HttpGet("jobs")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<JobPostingsResponseDto>>> GetJobPostings([FromQuery] GetJobPostingsRequestDto request)
    {
        _logger.LogInformation("Getting job postings with filters: {@Request}", request);

        var result = await _recruitmentService.GetJobPostingsAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get job postings. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create Job Posting
    /// </summary>
    /// <param name="request">Job posting creation details</param>
    /// <returns>Created job posting information</returns>
    [HttpPost("jobs")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<CreateJobPostingResponseDto>>> CreateJobPosting([FromBody] CreateJobPostingRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Creating job posting: {Title} by user: {UserId}", request.Title, userId);

        var result = await _recruitmentService.CreateJobPostingAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create job posting: {Title}. Error: {Error}", 
                request.Title, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Job posting created successfully: {Title}", request.Title);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Get Job Applications
    /// </summary>
    /// <param name="jobId">Job posting ID</param>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of job applications with pipeline summary</returns>
    [HttpGet("jobs/{jobId}/applications")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<JobApplicationsResponseDto>>> GetJobApplications(Guid jobId, [FromQuery] GetJobApplicationsRequestDto request)
    {
        _logger.LogInformation("Getting job applications for job: {JobId}", jobId);

        var result = await _recruitmentService.GetJobApplicationsAsync(jobId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get job applications for job: {JobId}. Error: {Error}", 
                jobId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }
}
