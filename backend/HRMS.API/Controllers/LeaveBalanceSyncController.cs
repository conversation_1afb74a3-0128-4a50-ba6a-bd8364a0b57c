using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/leave-balance-sync")]
[Authorize]
public class LeaveBalanceSyncController : BaseController
{
    private readonly ILeaveBalanceSyncService _leaveBalanceSyncService;
    private readonly ILogger<LeaveBalanceSyncController> _logger;

    public LeaveBalanceSyncController(
        ILeaveBalanceSyncService leaveBalanceSyncService,
        ILogger<LeaveBalanceSyncController> logger)
    {
        _leaveBalanceSyncService = leaveBalanceSyncService;
        _logger = logger;
    }

    /// <summary>
    /// Sync leave balance for current employee (DISABLED)
    /// </summary>
    /// <returns>Sync result</returns>
    [HttpPost("sync-my-balance")]
    public async Task<ActionResult<ApiResponse>> SyncMyLeaveBalance()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Leave balance sync requested for user {UserId} - feature disabled", userId);

        return Ok(ApiResponse.SuccessResult("Leave balance synchronization is disabled. Employees can now apply for leave without balance restrictions."));
    }

    /// <summary>
    /// Sync leave balance for a specific employee (DISABLED)
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Sync result</returns>
    [HttpPost("sync-employee/{employeeId}")]
    [Authorize(Roles = "SuperAdmin,OrgAdmin")]
    public async Task<ActionResult<ApiResponse>> SyncEmployeeLeaveBalance(Guid employeeId)
    {
        var currentUserId = GetCurrentUserId();
        _logger.LogInformation("Admin {AdminId} requested leave balance sync for employee {EmployeeId} - feature disabled",
            currentUserId, employeeId);

        return Ok(ApiResponse.SuccessResult("Leave balance synchronization is disabled. Employees can now apply for leave without balance restrictions."));
    }

    /// <summary>
    /// Sync leave balances for all employees in current organization (DISABLED)
    /// </summary>
    /// <returns>Sync result</returns>
    [HttpPost("sync-organization")]
    [Authorize(Roles = "SuperAdmin,OrgAdmin")]
    public async Task<ActionResult<ApiResponse>> SyncOrganizationLeaveBalances()
    {
        var currentUserId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();

        _logger.LogInformation("Admin {AdminId} requested organization leave balance sync for org {OrganizationId} - feature disabled",
            currentUserId, organizationId);

        return Ok(ApiResponse.SuccessResult("Leave balance synchronization is disabled. Employees can now apply for leave without balance restrictions."));
    }

    /// <summary>
    /// Sync leave balances for all employees in the system (DISABLED)
    /// </summary>
    /// <returns>Sync result</returns>
    [HttpPost("sync-all")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> SyncAllLeaveBalances()
    {
        var currentUserId = GetCurrentUserId();
        _logger.LogInformation("SuperAdmin {AdminId} requested system-wide leave balance sync - feature disabled", currentUserId);

        return Ok(ApiResponse.SuccessResult("Leave balance synchronization is disabled. Employees can now apply for leave without balance restrictions."));
    }

    /// <summary>
    /// Get sync status information
    /// </summary>
    /// <returns>Sync status</returns>
    [HttpGet("status")]
    public async Task<ActionResult<ApiResponse<object>>> GetSyncStatus()
    {
        var currentUserId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();
        
        _logger.LogInformation("Getting sync status for user {UserId} with role {Role}", currentUserId, userRole);

        try
        {
            var status = new
            {
                Message = "Leave balance sync service is active",
                LastSyncTime = DateTime.UtcNow,
                UserRole = userRole,
                AvailableActions = userRole?.ToLower() switch
                {
                    "superadmin" => new[] { "sync-my-balance", "sync-employee", "sync-organization", "sync-all" },
                    "orgadmin" => new[] { "sync-my-balance", "sync-employee", "sync-organization" },
                    "employee" => new[] { "sync-my-balance" },
                    _ => new[] { "sync-my-balance" }
                }
            };

            return Ok(ApiResponse<object>.SuccessResult(status));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync status for user {UserId}", currentUserId);
            return StatusCode(500, ApiResponse<object>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting sync status"));
        }
    }
}
