using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using HRMS.Core.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
public abstract class BaseController : ControllerBase
{
    protected string GetCurrentUserId()
    {
        return User.FindFirst("user_id")?.Value ?? 
               User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
               string.Empty;
    }

    protected string? GetCurrentUserEmail()
    {
        return User.FindFirst("email")?.Value ?? 
               User.FindFirst(ClaimTypes.Email)?.Value;
    }

    protected string? GetCurrentUserName()
    {
        return User.FindFirst("name")?.Value ?? 
               User.FindFirst(ClaimTypes.Name)?.Value;
    }

    protected string? GetCurrentUserRole()
    {
        return User.FindFirst("role")?.Value ?? 
               User.FindFirst(ClaimTypes.Role)?.Value;
    }

    protected string? GetCurrentOrganizationId()
    {
        // First try to get from JWT token
        var orgIdFromToken = User.FindFirst("organization_id")?.Value;
        if (!string.IsNullOrEmpty(orgIdFromToken))
        {
            return orgIdFromToken;
        }

        // If not in JWT token, try to get from tenant context
        var tenantService = HttpContext.RequestServices.GetService<ITenantService>();
        if (tenantService != null)
        {
            var tenant = tenantService.GetCurrentTenant();
            return tenant.OrganizationId;
        }

        return null;
    }

    protected bool IsCurrentUserSuperAdmin()
    {
        var role = GetCurrentUserRole();
        return string.Equals(role, "SuperAdmin", StringComparison.OrdinalIgnoreCase);
    }

    protected bool IsCurrentUserOrgAdmin()
    {
        var role = GetCurrentUserRole();
        return string.Equals(role, "OrgAdmin", StringComparison.OrdinalIgnoreCase);
    }

    protected bool IsCurrentUserEmployee()
    {
        var role = GetCurrentUserRole();
        return string.Equals(role, "Employee", StringComparison.OrdinalIgnoreCase);
    }

    protected bool CanAccessOrganization(string organizationId)
    {
        // Super admin can access any organization
        if (IsCurrentUserSuperAdmin())
            return true;

        // Other users can only access their own organization
        var currentOrgId = GetCurrentOrganizationId();
        return string.Equals(currentOrgId, organizationId, StringComparison.OrdinalIgnoreCase);
    }

    protected bool CanAccessUser(string userId)
    {
        // Super admin can access any user
        if (IsCurrentUserSuperAdmin())
            return true;

        // Users can access their own data
        var currentUserId = GetCurrentUserId();
        if (string.Equals(currentUserId, userId, StringComparison.OrdinalIgnoreCase))
            return true;

        // Org admin can access users in their organization
        if (IsCurrentUserOrgAdmin())
        {
            // This would need additional logic to check if the target user 
            // belongs to the same organization as the current user
            return true; // Simplified for now
        }

        return false;
    }
}
