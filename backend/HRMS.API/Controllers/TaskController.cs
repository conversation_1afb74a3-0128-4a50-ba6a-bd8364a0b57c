using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/tasks")]
[Authorize]
public class TaskController : BaseController
{
    private readonly ITaskService _taskService;
    private readonly ILogger<TaskController> _logger;

    public TaskController(ITaskService taskService, ILogger<TaskController> logger)
    {
        _taskService = taskService;
        _logger = logger;
    }

    /// <summary>
    /// Get Tasks
    /// </summary>
    /// <param name="request">Filter parameters</param>
    /// <returns>List of tasks</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<TasksResponseDto>>> GetTasks([FromQuery] GetTasksRequestDto request)
    {
        var currentUserId = GetCurrentUserId();

        // If not admin and no specific user requested, default to current user
        if (!IsCurrentUserOrgAdmin() && !IsCurrentUserSuperAdmin() && !request.AssignedTo.HasValue)
        {
            request.AssignedTo = Guid.Parse(currentUserId);
        }

        _logger.LogInformation("Getting tasks for user: {UserId}", request.AssignedTo);

        var result = await _taskService.GetTasksAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get tasks. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create Task
    /// </summary>
    /// <param name="request">Task creation details</param>
    /// <returns>Created task information</returns>
    [HttpPost]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<CreateTaskResponseDto>>> CreateTask([FromBody] CreateTaskRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Creating task for user: {UserId}", userId);

        var result = await _taskService.CreateTaskAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create task. Error: {Error}", result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Task created successfully by user: {UserId}", userId);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Update Task Progress
    /// </summary>
    /// <param name="id">Task ID</param>
    /// <param name="request">Progress update details</param>
    /// <returns>Updated task progress</returns>
    [HttpPut("{id}/progress")]
    public async Task<ActionResult<ApiResponse<UpdateTaskProgressResponseDto>>> UpdateTaskProgress(Guid id, [FromBody] UpdateTaskProgressRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Updating task progress for task: {TaskId} by user: {UserId}", id, userId);

        var result = await _taskService.UpdateTaskProgressAsync(id, userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update task progress for task: {TaskId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "TASK_NOT_FOUND" => NotFound(result),
                "UNAUTHORIZED" => Forbid(),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Task progress updated successfully for task: {TaskId}", id);
        return Ok(result);
    }

    /// <summary>
    /// Add Task Comment
    /// </summary>
    /// <param name="id">Task ID</param>
    /// <param name="request">Comment details</param>
    /// <returns>Added comment information</returns>
    [HttpPost("{id}/comments")]
    public async Task<ActionResult<ApiResponse<AddTaskCommentResponseDto>>> AddTaskComment(Guid id, [FromBody] AddTaskCommentRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Adding comment to task: {TaskId} by user: {UserId}", id, userId);

        var result = await _taskService.AddTaskCommentAsync(id, userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to add comment to task: {TaskId}. Error: {Error}", 
                id, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "TASK_NOT_FOUND" => NotFound(result),
                "USER_NOT_FOUND" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Comment added successfully to task: {TaskId}", id);
        return StatusCode(201, result);
    }
}
