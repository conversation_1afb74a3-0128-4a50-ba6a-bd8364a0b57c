using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/organization-admin")]
[Authorize(Roles = "OrgAdmin")]
public class OrganizationAdminController : BaseController
{
    private readonly IOrganizationAdminService _organizationAdminService;
    private readonly ILeaveService _leaveService;
    private readonly ITaskService _taskService;
    private readonly ILogger<OrganizationAdminController> _logger;

    public OrganizationAdminController(
        IOrganizationAdminService organizationAdminService,
        ILeaveService leaveService,
        ITaskService taskService,
        ILogger<OrganizationAdminController> logger)
    {
        _organizationAdminService = organizationAdminService;
        _leaveService = leaveService;
        _taskService = taskService;
        _logger = logger;
    }

    /// <summary>
    /// Get Organization Dashboard
    /// </summary>
    /// <returns>Organization dashboard with metrics and activities</returns>
    [HttpGet("dashboard")]
    public async Task<ActionResult<ApiResponse<OrganizationDashboardDto>>> GetOrganizationDashboard()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Getting organization dashboard for user: {UserId}", userId);

        var result = await _organizationAdminService.GetOrganizationDashboardAsync(userId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization dashboard for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Billing Information
    /// </summary>
    /// <returns>Organization billing and subscription information</returns>
    [HttpGet("billing")]
    public async Task<ActionResult<ApiResponse<BillingInformationDto>>> GetBillingInformation()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Getting billing information for user: {UserId}", userId);

        var result = await _organizationAdminService.GetBillingInformationAsync(userId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get billing information for user: {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Organization Leave Requests
    /// </summary>
    /// <param name="status">Filter by status (optional)</param>
    /// <param name="startDate">Filter from date (optional)</param>
    /// <param name="endDate">Filter to date (optional)</param>
    /// <param name="department">Filter by department (optional)</param>
    /// <param name="employeeId">Filter by employee (optional)</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <returns>Paginated list of leave requests for the organization</returns>
    [HttpGet("leave/requests")]
    public async Task<ActionResult<ApiResponse<OrganizationLeaveRequestsResponseDto>>> GetOrganizationLeaveRequests(
        [FromQuery] string? status = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string? department = null,
        [FromQuery] Guid? employeeId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<OrganizationLeaveRequestsResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting organization leave requests for user: {UserId}, organization: {OrganizationId}", userId, organizationId);

        var request = new GetOrganizationLeaveRequestsDto
        {
            Status = status,
            StartDate = startDate,
            EndDate = endDate,
            Department = department,
            EmployeeId = employeeId,
            Page = page,
            PageSize = pageSize
        };

        var result = await _leaveService.GetOrganizationLeaveRequestsAsync(organizationId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization leave requests for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "INVALID_ORGANIZATION" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Leave Statistics
    /// </summary>
    /// <param name="startDate">Statistics from date (optional)</param>
    /// <param name="endDate">Statistics to date (optional)</param>
    /// <param name="department">Filter by department (optional)</param>
    /// <returns>Leave statistics for the organization</returns>
    [HttpGet("leave/statistics")]
    public async Task<ActionResult<ApiResponse<LeaveStatisticsResponseDto>>> GetLeaveStatistics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string? department = null)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<LeaveStatisticsResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting leave statistics for user: {UserId}, organization: {OrganizationId}", userId, organizationId);

        var request = new GetLeaveStatisticsRequestDto
        {
            StartDate = startDate,
            EndDate = endDate,
            Department = department
        };

        var result = await _leaveService.GetLeaveStatisticsAsync(organizationId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get leave statistics for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "INVALID_ORGANIZATION" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Approve or Reject Leave Request
    /// </summary>
    /// <param name="leaveRequestId">Leave request ID</param>
    /// <param name="request">Approval/rejection details</param>
    /// <returns>Updated leave request information</returns>
    [HttpPut("leave/requests/{leaveRequestId}/approve-reject")]
    public async Task<ActionResult<ApiResponse<LeaveRequestDto>>> ApproveRejectLeave(
        Guid leaveRequestId,
        [FromBody] ApproveRejectLeaveDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Approving/rejecting leave request {LeaveRequestId} by user: {UserId}", leaveRequestId, userId);

        var result = await _leaveService.ApproveRejectLeaveAsync(leaveRequestId, userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to approve/reject leave request {LeaveRequestId} by user: {UserId}. Error: {Error}",
                leaveRequestId, userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "LEAVE_REQUEST_NOT_FOUND" => NotFound(result),
                "INVALID_STATUS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Leave request {LeaveRequestId} processed successfully by user: {UserId}", leaveRequestId, userId);
        return Ok(result);
    }

    /// <summary>
    /// Get Organization Tasks
    /// </summary>
    /// <param name="status">Filter by status (optional)</param>
    /// <param name="priority">Filter by priority (optional)</param>
    /// <param name="category">Filter by category (optional)</param>
    /// <param name="department">Filter by department (optional)</param>
    /// <param name="assignedTo">Filter by assigned employee (optional)</param>
    /// <param name="assignedBy">Filter by assigner (optional)</param>
    /// <param name="dueDateFrom">Filter from due date (optional)</param>
    /// <param name="dueDateTo">Filter to due date (optional)</param>
    /// <param name="overdue">Filter overdue tasks (optional)</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <returns>Paginated list of tasks for the organization</returns>
    [HttpGet("tasks")]
    public async Task<ActionResult<ApiResponse<OrganizationTasksResponseDto>>> GetOrganizationTasks(
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? category = null,
        [FromQuery] string? department = null,
        [FromQuery] Guid? assignedTo = null,
        [FromQuery] Guid? assignedBy = null,
        [FromQuery] DateTime? dueDateFrom = null,
        [FromQuery] DateTime? dueDateTo = null,
        [FromQuery] bool? overdue = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<OrganizationTasksResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting organization tasks for user: {UserId}, organization: {OrganizationId}", userId, organizationId);

        var request = new GetOrganizationTasksDto
        {
            Status = status,
            Priority = priority,
            Category = category,
            Department = department,
            AssignedTo = assignedTo,
            AssignedBy = assignedBy,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            Overdue = overdue,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetOrganizationTasksAsync(organizationId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get organization tasks for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "INVALID_ORGANIZATION" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Task Statistics
    /// </summary>
    /// <param name="startDate">Statistics from date (optional)</param>
    /// <param name="endDate">Statistics to date (optional)</param>
    /// <param name="department">Filter by department (optional)</param>
    /// <param name="category">Filter by category (optional)</param>
    /// <returns>Task statistics for the organization</returns>
    [HttpGet("tasks/statistics")]
    public async Task<ActionResult<ApiResponse<TaskStatisticsResponseDto>>> GetTaskStatistics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string? department = null,
        [FromQuery] string? category = null)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<TaskStatisticsResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting task statistics for user: {UserId}, organization: {OrganizationId}", userId, organizationId);

        var request = new GetTaskStatisticsRequestDto
        {
            DateFrom = startDate,
            DateTo = endDate,
            Department = department
        };

        var result = await _taskService.GetTaskStatisticsAsync(organizationId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get task statistics for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "INVALID_ORGANIZATION" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Assign Task to Employee
    /// </summary>
    /// <param name="request">Task assignment details</param>
    /// <returns>Created task information</returns>
    [HttpPost("tasks/assign")]
    public async Task<ActionResult<ApiResponse<CreateTaskResponseDto>>> AssignTask([FromBody] AssignTaskRequestDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Assigning task by admin user: {UserId}", userId);

        var result = await _taskService.AssignTaskAsync(userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to assign task by user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "USER_NOT_FOUND" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Task assigned successfully by user: {UserId}", userId);
        return Ok(result);
    }

    /// <summary>
    /// Get Employee Dashboard Data (Admin Access)
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Employee dashboard data</returns>
    [HttpGet("employees/{employeeId}/dashboard")]
    public async Task<ActionResult<ApiResponse<EmployeeDashboardDto>>> GetEmployeeDashboard(Guid employeeId)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();

        _logger.LogInformation("Admin {UserId} requesting dashboard for employee {EmployeeId}", userId, employeeId);

        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<EmployeeDashboardDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context"));
        }

        var result = await _organizationAdminService.GetEmployeeDashboardAsync(employeeId, organizationId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee dashboard for admin {UserId}, employee {EmployeeId}. Error: {Error}",
                userId, employeeId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                "ACCESS_DENIED" => Forbid(),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee Profile Summary (Admin Access)
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Employee profile summary</returns>
    [HttpGet("employees/{employeeId}/profile")]
    public async Task<ActionResult<ApiResponse<EmployeeProfileSummaryDto>>> GetEmployeeProfile(Guid employeeId)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();

        _logger.LogInformation("Admin {UserId} requesting profile for employee {EmployeeId}", userId, employeeId);

        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context"));
        }

        var result = await _organizationAdminService.GetEmployeeProfileAsync(employeeId, organizationId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee profile for admin {UserId}, employee {EmployeeId}. Error: {Error}",
                userId, employeeId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                "ACCESS_DENIED" => Forbid(),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee Attendance Summary (Admin Access)
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Employee attendance summary</returns>
    [HttpGet("employees/{employeeId}/attendance")]
    public async Task<ActionResult<ApiResponse<EmployeeAttendanceSummaryDto>>> GetEmployeeAttendance(Guid employeeId)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();

        _logger.LogInformation("Admin {UserId} requesting attendance for employee {EmployeeId}", userId, employeeId);

        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<EmployeeAttendanceSummaryDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context"));
        }

        var result = await _organizationAdminService.GetEmployeeAttendanceAsync(employeeId, organizationId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee attendance for admin {UserId}, employee {EmployeeId}. Error: {Error}",
                userId, employeeId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "EMPLOYEE_NOT_FOUND" => NotFound(result),
                "ACCESS_DENIED" => Forbid(),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Task Updates Feed
    /// </summary>
    /// <param name="startDate">Updates from date (optional)</param>
    /// <param name="endDate">Updates to date (optional)</param>
    /// <param name="employeeId">Filter by employee (optional)</param>
    /// <param name="department">Filter by department (optional)</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <returns>Paginated list of task updates</returns>
    [HttpGet("tasks/updates")]
    public async Task<ActionResult<ApiResponse<TaskUpdatesResponseDto>>> GetTaskUpdates(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] Guid? employeeId = null,
        [FromQuery] string? department = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();
        if (string.IsNullOrEmpty(organizationId))
        {
            return BadRequest(ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Organization context not found"));
        }

        _logger.LogInformation("Getting task updates for user: {UserId}, organization: {OrganizationId}", userId, organizationId);

        var request = new GetTaskUpdatesRequestDto
        {
            DateFrom = startDate,
            DateTo = endDate,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetTaskUpdatesAsync(organizationId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get task updates for user: {UserId}. Error: {Error}",
                userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "INVALID_ORGANIZATION" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Update Task Status
    /// </summary>
    /// <param name="taskId">Task ID</param>
    /// <param name="request">Status update details</param>
    /// <returns>Updated task information</returns>
    [HttpPut("tasks/{taskId}/status")]
    public async Task<ActionResult<ApiResponse<TaskDto>>> UpdateTaskStatus(
        Guid taskId,
        [FromBody] UpdateTaskStatusDto request)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Updating task status {TaskId} by user: {UserId}", taskId, userId);

        var result = await _taskService.UpdateTaskStatusAsync(taskId, userId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update task status {TaskId} by user: {UserId}. Error: {Error}",
                taskId, userId, result.Error?.Message);

            return result.Error?.Code switch
            {
                "TASK_NOT_FOUND" => NotFound(result),
                "INVALID_STATUS" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Task status {TaskId} updated successfully by user: {UserId}", taskId, userId);
        return Ok(result);
    }
}
