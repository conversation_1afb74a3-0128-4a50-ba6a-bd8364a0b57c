using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.API.Controllers;

namespace HRMS.API.Controllers;

/// <summary>
/// Manager Task Visibility Controller
/// Handles manager access to direct reports' tasks and updates
/// </summary>
[ApiController]
[Route("api/v1/manager/tasks")]
[Authorize]
public class ManagerTaskController : BaseController
{
    private readonly ITaskService _taskService;
    private readonly ILogger<ManagerTaskController> _logger;

    public ManagerTaskController(ITaskService taskService, ILogger<ManagerTaskController> logger)
    {
        _taskService = taskService;
        _logger = logger;
    }

    /// <summary>
    /// Get tasks visible to the manager (direct reports' tasks)
    /// </summary>
    /// <param name="employeeId">Specific employee ID (optional, defaults to all direct reports)</param>
    /// <param name="status">Filter by status</param>
    /// <param name="priority">Filter by priority</param>
    /// <param name="taskType">Filter by task type</param>
    /// <param name="isSelfCreated">Filter by self-created flag</param>
    /// <param name="dueDateFrom">Filter by due date from</param>
    /// <param name="dueDateTo">Filter by due date to</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of visible tasks</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<TasksResponseDto>>> GetManagerVisibleTasks(
        [FromQuery] Guid? employeeId = null,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? taskType = null,
        [FromQuery] bool? isSelfCreated = null,
        [FromQuery] DateTime? dueDateFrom = null,
        [FromQuery] DateTime? dueDateTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var managerId = GetCurrentUserId();
        if (string.IsNullOrEmpty(managerId))
        {
            return Unauthorized(ApiResponse<TasksResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var request = new GetManagerTasksRequestDto
        {
            EmployeeId = employeeId,
            Status = status,
            Priority = priority,
            TaskType = taskType,
            IsSelfCreated = isSelfCreated,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetManagerVisibleTasksAsync(managerId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get daily updates visible to the manager (direct reports' updates)
    /// </summary>
    /// <param name="employeeId">Specific employee ID (optional, defaults to all direct reports)</param>
    /// <param name="taskId">Filter by specific task</param>
    /// <param name="dateFrom">Filter by date from</param>
    /// <param name="dateTo">Filter by date to</param>
    /// <param name="updateType">Filter by update type</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of visible daily updates</returns>
    [HttpGet("updates")]
    public async Task<ActionResult<ApiResponse<TaskUpdatesResponseDto>>> GetManagerVisibleUpdates(
        [FromQuery] Guid? employeeId = null,
        [FromQuery] Guid? taskId = null,
        [FromQuery] DateTime? dateFrom = null,
        [FromQuery] DateTime? dateTo = null,
        [FromQuery] string? updateType = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var managerId = GetCurrentUserId();
        if (string.IsNullOrEmpty(managerId))
        {
            return Unauthorized(ApiResponse<TaskUpdatesResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var request = new GetDailyUpdatesRequestDto
        {
            EmployeeId = employeeId,
            TaskId = taskId,
            DateFrom = dateFrom,
            DateTo = dateTo,
            UpdateType = updateType,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetManagerVisibleUpdatesAsync(managerId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get tasks for a specific direct report employee
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <param name="status">Filter by status</param>
    /// <param name="priority">Filter by priority</param>
    /// <param name="taskType">Filter by task type</param>
    /// <param name="isSelfCreated">Filter by self-created flag</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of employee tasks</returns>
    [HttpGet("employee/{employeeId}")]
    public async Task<ActionResult<ApiResponse<TasksResponseDto>>> GetEmployeeTasks(
        Guid employeeId,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? taskType = null,
        [FromQuery] bool? isSelfCreated = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var managerId = GetCurrentUserId();
        if (string.IsNullOrEmpty(managerId))
        {
            return Unauthorized(ApiResponse<TasksResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var request = new GetManagerTasksRequestDto
        {
            EmployeeId = employeeId,
            Status = status,
            Priority = priority,
            TaskType = taskType,
            IsSelfCreated = isSelfCreated,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetManagerVisibleTasksAsync(managerId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get daily updates for a specific direct report employee
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <param name="taskId">Filter by specific task</param>
    /// <param name="dateFrom">Filter by date from</param>
    /// <param name="dateTo">Filter by date to</param>
    /// <param name="updateType">Filter by update type</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of employee daily updates</returns>
    [HttpGet("employee/{employeeId}/updates")]
    public async Task<ActionResult<ApiResponse<TaskUpdatesResponseDto>>> GetEmployeeDailyUpdates(
        Guid employeeId,
        [FromQuery] Guid? taskId = null,
        [FromQuery] DateTime? dateFrom = null,
        [FromQuery] DateTime? dateTo = null,
        [FromQuery] string? updateType = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var managerId = GetCurrentUserId();
        if (string.IsNullOrEmpty(managerId))
        {
            return Unauthorized(ApiResponse<TaskUpdatesResponseDto>.ErrorResult("UNAUTHORIZED", "User not authenticated"));
        }

        var request = new GetDailyUpdatesRequestDto
        {
            EmployeeId = employeeId,
            TaskId = taskId,
            DateFrom = dateFrom,
            DateTo = dateTo,
            UpdateType = updateType,
            Page = page,
            PageSize = pageSize
        };

        var result = await _taskService.GetManagerVisibleUpdatesAsync(managerId, request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }
}
