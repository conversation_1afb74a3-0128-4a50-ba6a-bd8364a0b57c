using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/permissions")]
[Authorize]
public class PermissionController : BaseController
{
    private readonly IPermissionService _permissionService;
    private readonly ILogger<PermissionController> _logger;

    public PermissionController(
        IPermissionService permissionService,
        ILogger<PermissionController> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }

    /// <summary>
    /// Get All Permissions
    /// </summary>
    /// <returns>List of all available permissions</returns>
    [HttpGet]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<List<PermissionDto>>>> GetAllPermissions()
    {
        _logger.LogInformation("Getting all permissions");

        var result = await _permissionService.GetAllPermissionsAsync();

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get permissions. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Roles by Organization
    /// </summary>
    /// <param name="organizationId">Organization ID</param>
    /// <returns>List of roles for the organization</returns>
    [HttpGet("roles")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<List<RoleDto>>>> GetRolesByOrganization([FromQuery] Guid organizationId)
    {
        _logger.LogInformation("Getting roles for organization: {OrganizationId}", organizationId);

        var result = await _permissionService.GetRolesByOrganizationAsync(organizationId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get roles for organization: {OrganizationId}. Error: {Error}", 
                organizationId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Create New Role
    /// </summary>
    /// <param name="request">Role creation data</param>
    /// <returns>Created role information</returns>
    [HttpPost("roles")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<RoleDto>>> CreateRole([FromBody] CreateRoleRequestDto request)
    {
        _logger.LogInformation("Creating new role: {RoleName}", request.Name);

        var result = await _permissionService.CreateRoleAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to create role: {RoleName}. Error: {Error}", 
                request.Name, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "ROLE_EXISTS" => Conflict(result),
                _ => StatusCode(500, result)
            };
        }

        _logger.LogInformation("Successfully created role: {RoleName}", request.Name);
        return StatusCode(201, result);
    }

    /// <summary>
    /// Update Role
    /// </summary>
    /// <param name="roleId">Role ID</param>
    /// <param name="request">Role update data</param>
    /// <returns>Updated role information</returns>
    [HttpPut("roles/{roleId}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<RoleDto>>> UpdateRole(Guid roleId, [FromBody] UpdateRoleRequestDto request)
    {
        _logger.LogInformation("Updating role: {RoleId}", roleId);

        var result = await _permissionService.UpdateRoleAsync(roleId, request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to update role: {RoleId}. Error: {Error}", 
                roleId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "ROLE_NOT_FOUND" => NotFound(result),
                "SYSTEM_ROLE" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Delete Role
    /// </summary>
    /// <param name="roleId">Role ID</param>
    /// <returns>Success message</returns>
    [HttpDelete("roles/{roleId}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> DeleteRole(Guid roleId)
    {
        _logger.LogInformation("Deleting role: {RoleId}", roleId);

        var result = await _permissionService.DeleteRoleAsync(roleId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to delete role: {RoleId}. Error: {Error}", 
                roleId, result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "ROLE_NOT_FOUND" => NotFound(result),
                "SYSTEM_ROLE" => BadRequest(result),
                "ROLE_IN_USE" => BadRequest(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Employee Roles
    /// </summary>
    /// <param name="employeeDetailId">Employee Detail ID</param>
    /// <returns>List of roles assigned to the employee</returns>
    [HttpGet("employee-roles/{employeeDetailId}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<List<EmployeeRoleDto>>>> GetEmployeeRoles(Guid employeeDetailId)
    {
        _logger.LogInformation("Getting roles for employee: {EmployeeDetailId}", employeeDetailId);

        var result = await _permissionService.GetEmployeeRolesAsync(employeeDetailId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to get employee roles for: {EmployeeDetailId}. Error: {Error}", 
                employeeDetailId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Assign Role to Employee
    /// </summary>
    /// <param name="request">Role assignment data</param>
    /// <returns>Success message</returns>
    [HttpPost("assign-role")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> AssignRoleToEmployee([FromBody] AssignRoleRequestDto request)
    {
        _logger.LogInformation("Assigning role {RoleId} to employee {EmployeeDetailId}", 
            request.RoleId, request.EmployeeDetailId);

        var result = await _permissionService.AssignRoleToEmployeeAsync(request);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to assign role. Error: {Error}", result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "ROLE_ALREADY_ASSIGNED" => Conflict(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Revoke Role from Employee
    /// </summary>
    /// <param name="employeeDetailId">Employee Detail ID</param>
    /// <param name="roleId">Role ID</param>
    /// <returns>Success message</returns>
    [HttpDelete("revoke-role/{employeeDetailId}/{roleId}")]
    [Authorize(Roles = "OrgAdmin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> RevokeRoleFromEmployee(Guid employeeDetailId, Guid roleId)
    {
        _logger.LogInformation("Revoking role {RoleId} from employee {EmployeeDetailId}", 
            roleId, employeeDetailId);

        var result = await _permissionService.RevokeRoleFromEmployeeAsync(employeeDetailId, roleId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to revoke role. Error: {Error}", result.Error?.Message);
            
            return result.Error?.Code switch
            {
                "ROLE_NOT_ASSIGNED" => NotFound(result),
                _ => StatusCode(500, result)
            };
        }

        return Ok(result);
    }

    /// <summary>
    /// Check Permission
    /// </summary>
    /// <param name="employeeDetailId">Employee Detail ID</param>
    /// <param name="module">Module name</param>
    /// <param name="action">Action name</param>
    /// <returns>Permission check result</returns>
    [HttpGet("check/{employeeDetailId}/{module}/{action}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> CheckPermission(Guid employeeDetailId, string module, string action)
    {
        // Employees can only check their own permissions
        if (IsCurrentUserEmployee())
        {
            var currentUserId = GetCurrentUserId();
            if (string.IsNullOrEmpty(currentUserId) || !Guid.TryParse(currentUserId, out var userGuid) || userGuid != employeeDetailId)
            {
                return Forbid();
            }
        }

        _logger.LogInformation("Checking permission for employee {EmployeeDetailId}: {Module}.{Action}", 
            employeeDetailId, module, action);

        var result = await _permissionService.CheckPermissionAsync(employeeDetailId, module, action);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to check permission. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }
}
