{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=103.145.50.203,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;"}, "Jwt": {"SecretKey": "development-super-secret-jwt-key-that-is-at-least-32-characters-long-for-development-only", "Issuer": "HRMS.API.Dev", "Audience": "HRMS.Client.Dev", "ExpirationMinutes": 1440}, "Database": {"SchemaPrefix": "org_", "SchemaSuffix": "_dev", "AutoProvision": true, "TimeoutSeconds": 300, "ConnectionRetryCount": 3, "ConnectionRetryDelay": 5}, "AdminCredentials": {"GenerateOnStartup": true, "RequirePasswordReset": false, "SuperAdmin": {"Email": "<EMAIL>", "Name": "System Administrator"}, "DefaultOrganization": {"Name": "Plan Square", "Domain": "plan2intl.com", "Industry": "Technology", "AdminEmail": "<EMAIL>", "AdminName": "<PERSON><PERSON><PERSON>"}}, "CORS": {"AllowedOrigins": ["http://localhost:8080", "http://localhost:3000"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}}