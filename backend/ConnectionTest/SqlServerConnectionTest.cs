using System;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace ConnectionTest
{
    public class SqlServerConnectionTest
    {
        private const string ConnectionString = "Server=103.145.50.203,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;";

        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                Console.WriteLine("Testing SQL Server connection...");
                Console.WriteLine($"Server: 103.145.50.203:2829");
                Console.WriteLine($"Database: dbHRMS");
                Console.WriteLine($"User: userHRMS");
                Console.WriteLine();

                using var connection = new SqlConnection(ConnectionString);
                await connection.OpenAsync();
                
                Console.WriteLine("✅ Connection successful!");
                
                // Test basic query
                using var command = new SqlCommand("SELECT @@VERSION", connection);
                var version = await command.ExecuteScalarAsync();
                Console.WriteLine($"SQL Server Version: {version}");
                
                // Check if database exists
                using var dbCheckCommand = new SqlCommand("SELECT DB_NAME()", connection);
                var currentDb = await dbCheckCommand.ExecuteScalarAsync();
                Console.WriteLine($"Current Database: {currentDb}");
                
                // Check permissions
                using var permissionCommand = new SqlCommand("SELECT IS_SRVROLEMEMBER('sysadmin') as IsSysAdmin, IS_MEMBER('db_owner') as IsDbOwner", connection);
                using var reader = await permissionCommand.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    var isSysAdmin = reader.IsDBNull(0) ? false : reader.GetInt32(0) == 1;
                    var isDbOwner = reader.IsDBNull(1) ? false : reader.GetInt32(1) == 1;
                    Console.WriteLine($"SysAdmin: {isSysAdmin}, DbOwner: {isDbOwner}");
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Connection failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        public static async Task Main(string[] args)
        {
            Console.WriteLine("HRMS SQL Server Connection Test");
            Console.WriteLine("================================");
            
            var success = await TestConnectionAsync();
            
            if (success)
            {
                Console.WriteLine("\n✅ Connection test passed! Ready to proceed with database setup.");
            }
            else
            {
                Console.WriteLine("\n❌ Connection test failed! Please check the connection details and network connectivity.");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
