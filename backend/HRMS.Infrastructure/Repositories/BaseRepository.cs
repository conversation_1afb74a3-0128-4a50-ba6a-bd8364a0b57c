using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using HRMS.Core.Common;
using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Data;
using HRMS.Core.Entities;
using Task = System.Threading.Tasks.Task;

namespace HRMS.Infrastructure.Repositories;

public class BaseRepository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly HRMSDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public BaseRepository(HRMSDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(Guid id)
    {
        return await _dbSet.FindAsync(id);
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.Where(predicate).ToListAsync();
    }

    public virtual async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.FirstOrDefaultAsync(predicate);
    }

    public virtual async Task<T> AddAsync(T entity)
    {
        await _dbSet.AddAsync(entity);
        return entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
    {
        await _dbSet.AddRangeAsync(entities);
        return entities;
    }

    public virtual Task UpdateAsync(T entity)
    {
        _dbSet.Update(entity);
        return Task.CompletedTask;
    }

    public virtual Task DeleteAsync(T entity)
    {
        _dbSet.Remove(entity);
        return Task.CompletedTask;
    }

    public virtual Task DeleteRangeAsync(IEnumerable<T> entities)
    {
        _dbSet.RemoveRange(entities);
        return Task.CompletedTask;
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null)
    {
        if (predicate == null)
            return await _dbSet.CountAsync();

        return await _dbSet.CountAsync(predicate);
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.AnyAsync(predicate);
    }
}

public class UserRepository : BaseRepository<HRMS.Core.Entities.User>, IUserRepository
{
    public UserRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<HRMS.Core.Entities.User?> GetByEmailAsync(string email)
    {
        return await _dbSet
            .Include(u => u.Organization)
            .FirstOrDefaultAsync(u => u.Email == email);
    }

    public async Task<IEnumerable<HRMS.Core.Entities.User>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Include(u => u.Organization)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Education)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Skills)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Manager)
            .Where(u => u.OrganizationId == organizationId && u.IsActive && !u.IsDeleted)
            .ToListAsync();
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        return await _dbSet.AnyAsync(u => u.Email == email);
    }

    public override async Task<IEnumerable<HRMS.Core.Entities.User>> GetAllAsync()
    {
        return await _dbSet
            .Include(u => u.Organization)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Education)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Skills)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Manager)
            .Where(u => u.IsActive && !u.IsDeleted)
            .ToListAsync();
    }

    public override async Task<HRMS.Core.Entities.User?> GetByIdAsync(Guid id)
    {
        return await _dbSet
            .Include(u => u.Organization)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Education)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Skills)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Manager)
            .FirstOrDefaultAsync(u => u.Id == id && u.IsActive && !u.IsDeleted);
    }

    /// <summary>
    /// Get user by ID including soft-deleted records (for admin operations)
    /// </summary>
    public async Task<HRMS.Core.Entities.User?> GetByIdIncludingDeletedAsync(Guid id)
    {
        return await _dbSet
            .Include(u => u.Organization)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Education)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Skills)
            .Include(u => u.EmployeeDetail)
                .ThenInclude(ed => ed.Manager)
            .FirstOrDefaultAsync(u => u.Id == id);
    }
}

public class OrganizationRepository : BaseRepository<HRMS.Core.Entities.Organization>, IOrganizationRepository
{
    public OrganizationRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<HRMS.Core.Entities.Organization?> GetByDomainAsync(string domain)
    {
        return await _dbSet.FirstOrDefaultAsync(o => o.Domain == domain);
    }

    public async Task<bool> DomainExistsAsync(string domain)
    {
        return await _dbSet.AnyAsync(o => o.Domain == domain);
    }
}

public class EmployeeDetailRepository : BaseRepository<HRMS.Core.Entities.EmployeeDetail>, IEmployeeDetailRepository
{
    public EmployeeDetailRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<HRMS.Core.Entities.EmployeeDetail?> GetByUserIdAsync(Guid userId)
    {
        return await _dbSet
            .Include(ed => ed.Education)
            .Include(ed => ed.Skills)
            .Include(ed => ed.Manager)
            .FirstOrDefaultAsync(ed => ed.UserId == userId);
    }

    public async Task<HRMS.Core.Entities.EmployeeDetail?> GetByEmployeeIdAsync(string employeeId)
    {
        return await _dbSet
            .Include(ed => ed.User)
            .Include(ed => ed.Education)
            .Include(ed => ed.Skills)
            .FirstOrDefaultAsync(ed => ed.EmployeeId == employeeId);
    }

    public async Task<IEnumerable<HRMS.Core.Entities.EmployeeDetail>> GetByManagerIdAsync(Guid managerId)
    {
        return await _dbSet
            .Include(ed => ed.User)
            .Include(ed => ed.Education)
            .Include(ed => ed.Skills)
            .Where(ed => ed.ManagerId == managerId)
            .ToListAsync();
    }
}

public class AttendanceRepository : BaseRepository<HRMS.Core.Entities.AttendanceRecord>, IAttendanceRepository
{
    public AttendanceRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.AttendanceRecord>> GetByUserAndDateRangeAsync(Guid userId, DateTime startDate, DateTime endDate)
    {
        return await _dbSet
            .Where(a => a.UserId == userId && a.Date >= startDate && a.Date <= endDate)
            .OrderBy(a => a.Date)
            .ToListAsync();
    }

    public async Task<HRMS.Core.Entities.AttendanceRecord?> GetByUserAndDateAsync(Guid userId, DateTime date)
    {
        return await _dbSet
            .FirstOrDefaultAsync(a => a.UserId == userId && a.Date.Date == date.Date);
    }
}

public class LeaveRepository : BaseRepository<HRMS.Core.Entities.LeaveRequest>, ILeaveRepository
{
    public LeaveRepository(HRMSDbContext context) : base(context)
    {
    }

    public override async Task<IEnumerable<HRMS.Core.Entities.LeaveRequest>> GetAllAsync()
    {
        return await _dbSet
            .Include(lr => lr.User)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(lr => lr.LeaveType)
            .Include(lr => lr.Approver)
            .OrderByDescending(lr => lr.CreatedAt)
            .ToListAsync();
    }

    public override async Task<HRMS.Core.Entities.LeaveRequest?> GetByIdAsync(Guid id)
    {
        return await _dbSet
            .Include(lr => lr.User)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(lr => lr.LeaveType)
            .Include(lr => lr.Approver)
            .FirstOrDefaultAsync(lr => lr.Id == id);
    }

    public async Task<IEnumerable<HRMS.Core.Entities.LeaveRequest>> GetByUserAsync(Guid userId)
    {
        return await _dbSet
            .Include(lr => lr.User)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(lr => lr.LeaveType)
            .Include(lr => lr.Approver)
            .Where(lr => lr.UserId == userId)
            .OrderByDescending(lr => lr.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.LeaveRequest>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Include(lr => lr.User)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(lr => lr.LeaveType)
            .Include(lr => lr.Approver)
            .Where(lr => lr.User.OrganizationId == organizationId)
            .OrderByDescending(lr => lr.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.LeaveBalance>> GetLeaveBalancesByUserAsync(Guid userId, int year)
    {
        // Leave balance functionality disabled - return empty list
        return await Task.FromResult(new List<HRMS.Core.Entities.LeaveBalance>());
    }
}



public class LeaveTypeRepository : BaseRepository<HRMS.Core.Entities.LeaveType>, ILeaveTypeRepository
{
    public LeaveTypeRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.LeaveType>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Where(lt => lt.OrganizationId == organizationId && lt.IsActive)
            .OrderBy(lt => lt.Name)
            .ToListAsync();
    }
}


public class TaskRepository : BaseRepository<HRMS.Core.Entities.Task>, ITaskRepository
{
    public TaskRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.Task>> GetByAssignedUserAsync(Guid userId)
    {
        return await _dbSet
            .Include(t => t.AssignedTo)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(t => t.AssignedBy)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(t => t.TaskUpdates)
                .ThenInclude(tu => tu.UpdatedBy)
                    .ThenInclude(u => u.EmployeeDetail)
            .Where(t => t.AssignedToId == userId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.Task>> GetByCreatedUserAsync(Guid userId)
    {
        return await _dbSet
            .Include(t => t.AssignedTo)
            .Include(t => t.TaskUpdates)
            .Where(t => t.AssignedById == userId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.Task>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Include(t => t.AssignedTo)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(t => t.AssignedBy)
                .ThenInclude(u => u.EmployeeDetail)
            .Include(t => t.TaskUpdates)
            .Where(t => t.AssignedTo.OrganizationId == organizationId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }
}

public class TaskUpdateRepository : BaseRepository<HRMS.Core.Entities.TaskUpdate>, ITaskUpdateRepository
{
    public TaskUpdateRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.TaskUpdate>> GetByTaskAsync(Guid taskId)
    {
        return await _dbSet
            .Include(tu => tu.Task)
                .ThenInclude(t => t.AssignedTo)
                    .ThenInclude(u => u.EmployeeDetail)
            .Include(tu => tu.UpdatedBy)
                .ThenInclude(u => u.EmployeeDetail)
            .Where(tu => tu.TaskId == taskId)
            .OrderByDescending(tu => tu.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.TaskUpdate>> GetByTaskIdsAsync(IEnumerable<Guid> taskIds)
    {
        return await _dbSet
            .Include(tu => tu.Task)
                .ThenInclude(t => t.AssignedTo)
                    .ThenInclude(u => u.EmployeeDetail)
            .Include(tu => tu.UpdatedBy)
                .ThenInclude(u => u.EmployeeDetail)
            .Where(tu => taskIds.Contains(tu.TaskId))
            .OrderByDescending(tu => tu.UpdateDate)
            .ThenByDescending(tu => tu.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.TaskUpdate>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Include(tu => tu.Task)
                .ThenInclude(t => t.AssignedTo)
                    .ThenInclude(u => u.EmployeeDetail)
            .Where(tu => tu.Task.AssignedTo.OrganizationId == organizationId)
            .OrderByDescending(tu => tu.CreatedAt)
            .ToListAsync();
    }
}

public class PerformanceRepository : BaseRepository<HRMS.Core.Entities.PerformanceReview>, IPerformanceRepository
{
    public PerformanceRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.PerformanceReview>> GetByEmployeeAsync(Guid employeeId)
    {
        return await _dbSet
            .Include(pr => pr.Reviewer)
            .Include(pr => pr.Goals)
            .Where(pr => pr.EmployeeId == employeeId)
            .OrderByDescending(pr => pr.ReviewDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<HRMS.Core.Entities.PerformanceReview>> GetByReviewerAsync(Guid reviewerId)
    {
        return await _dbSet
            .Include(pr => pr.Employee)
            .Include(pr => pr.Goals)
            .Where(pr => pr.ReviewerId == reviewerId)
            .OrderByDescending(pr => pr.ReviewDate)
            .ToListAsync();
    }
}

public class LeaveBalanceRepository : BaseRepository<HRMS.Core.Entities.LeaveBalance>, ILeaveBalanceRepository
{
    public LeaveBalanceRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.LeaveBalance>> GetByUserAsync(Guid userId, int year)
    {
        return await _dbSet
            .Include(lb => lb.LeaveType)
            .Where(lb => lb.UserId == userId && lb.Year == year)
            .ToListAsync();
    }
}

public class RoleRepository : BaseRepository<HRMS.Core.Entities.Role>, IRoleRepository
{
    public RoleRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.Role>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Include(r => r.RolePermissions)
            .ThenInclude(rp => rp.Permission)
            .Where(r => r.OrganizationId == organizationId && r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync();
    }

    public async Task<HRMS.Core.Entities.Role?> GetByNameAsync(Guid organizationId, string name)
    {
        return await _dbSet
            .FirstOrDefaultAsync(r => r.OrganizationId == organizationId && r.Name == name);
    }
}

public class PermissionRepository : BaseRepository<HRMS.Core.Entities.Permission>, IPermissionRepository
{
    public PermissionRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.Permission>> GetByModuleAsync(string module)
    {
        return await _dbSet
            .Where(p => p.Module == module && p.IsActive)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }
}

public class RolePermissionRepository : BaseRepository<HRMS.Core.Entities.RolePermission>, IRolePermissionRepository
{
    public RolePermissionRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.RolePermission>> GetByRoleAsync(Guid roleId)
    {
        return await _dbSet
            .Include(rp => rp.Permission)
            .Where(rp => rp.RoleId == roleId)
            .ToListAsync();
    }
}

public class EmployeeRoleRepository : BaseRepository<HRMS.Core.Entities.EmployeeRole>, IEmployeeRoleRepository
{
    public EmployeeRoleRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.EmployeeRole>> GetByEmployeeAsync(Guid employeeDetailId)
    {
        return await _dbSet
            .Include(er => er.Role)
            .Where(er => er.EmployeeDetailId == employeeDetailId)
            .OrderByDescending(er => er.AssignedDate)
            .ToListAsync();
    }
}

public class EmployeePermissionRepository : BaseRepository<HRMS.Core.Entities.EmployeePermission>, IEmployeePermissionRepository
{
    public EmployeePermissionRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.EmployeePermission>> GetByEmployeeAsync(Guid employeeDetailId)
    {
        return await _dbSet
            .Include(ep => ep.Permission)
            .Where(ep => ep.EmployeeDetailId == employeeDetailId)
            .OrderByDescending(ep => ep.AssignedDate)
            .ToListAsync();
    }
}

public class SalaryComponentRepository : BaseRepository<HRMS.Core.Entities.SalaryComponent>, ISalaryComponentRepository
{
    public SalaryComponentRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.SalaryComponent>> GetByOrganizationAsync(Guid organizationId)
    {
        return await _dbSet
            .Where(sc => sc.OrganizationId == organizationId && sc.IsActive)
            .OrderBy(sc => sc.DisplayOrder)
            .ThenBy(sc => sc.Name)
            .ToListAsync();
    }
}

public class EmployeeSalaryStructureRepository : BaseRepository<HRMS.Core.Entities.EmployeeSalaryStructure>, IEmployeeSalaryStructureRepository
{
    public EmployeeSalaryStructureRepository(HRMSDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<HRMS.Core.Entities.EmployeeSalaryStructure>> GetByEmployeeAsync(Guid employeeId)
    {
        return await _dbSet
            .Include(ess => ess.Component)
            .Where(ess => ess.EmployeeId == employeeId && ess.IsActive)
            .OrderBy(ess => ess.Component.DisplayOrder)
            .ToListAsync();
    }
}
