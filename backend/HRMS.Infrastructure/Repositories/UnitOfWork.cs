using Microsoft.EntityFrameworkCore.Storage;
using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Data;

namespace HRMS.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly HRMSDbContext _context;
    private IDbContextTransaction? _transaction;

    private IUserRepository? _users;
    private IOrganizationRepository? _organizations;
    private IEmployeeDetailRepository? _employeeDetails;
    private IAttendanceRepository? _attendance;
    private ILeaveRepository? _leaves;
    private ILeaveTypeRepository? _leaveTypes;
    private ILeaveBalanceRepository? _leaveBalances;
    private IMonthlyLeaveAllowanceRepository? _monthlyLeaveAllowances;
    private IMonthlyLeaveUsageRepository? _monthlyLeaveUsages;
    private ITaskRepository? _tasks;
    private ITaskUpdateRepository? _taskUpdates;
    private IPerformanceRepository? _performance;
    private IRoleRepository? _roles;
    private IPermissionRepository? _permissions;
    private IRolePermissionRepository? _rolePermissions;
    private IEmployeeRoleRepository? _employeeRoles;
    private IEmployeePermissionRepository? _employeePermissions;
    private ISalaryComponentRepository? _salaryComponents;
    private IEmployeeSalaryStructureRepository? _employeeSalaryStructures;

    public UnitOfWork(HRMSDbContext context)
    {
        _context = context;
    }

    public IUserRepository Users => _users ??= new UserRepository(_context);

    public IOrganizationRepository Organizations => _organizations ??= new OrganizationRepository(_context);

    public IEmployeeDetailRepository EmployeeDetails => _employeeDetails ??= new EmployeeDetailRepository(_context);

    public IAttendanceRepository Attendance => _attendance ??= new AttendanceRepository(_context);

    public ILeaveRepository Leaves => _leaves ??= new LeaveRepository(_context);
    public ILeaveTypeRepository LeaveTypes => _leaveTypes ??= new LeaveTypeRepository(_context);
    public ILeaveBalanceRepository LeaveBalances => _leaveBalances ??= new LeaveBalanceRepository(_context);
    public IMonthlyLeaveAllowanceRepository MonthlyLeaveAllowances => _monthlyLeaveAllowances ??= new MonthlyLeaveAllowanceRepository(_context);
    public IMonthlyLeaveUsageRepository MonthlyLeaveUsages => _monthlyLeaveUsages ??= new MonthlyLeaveUsageRepository(_context);

    public ITaskRepository Tasks => _tasks ??= new TaskRepository(_context);
    public ITaskUpdateRepository TaskUpdates => _taskUpdates ??= new TaskUpdateRepository(_context);

    public IPerformanceRepository Performance => _performance ??= new PerformanceRepository(_context);

    public IRoleRepository Roles => _roles ??= new RoleRepository(_context);
    public IPermissionRepository Permissions => _permissions ??= new PermissionRepository(_context);
    public IRolePermissionRepository RolePermissions => _rolePermissions ??= new RolePermissionRepository(_context);
    public IEmployeeRoleRepository EmployeeRoles => _employeeRoles ??= new EmployeeRoleRepository(_context);
    public IEmployeePermissionRepository EmployeePermissions => _employeePermissions ??= new EmployeePermissionRepository(_context);
    public ISalaryComponentRepository SalaryComponents => _salaryComponents ??= new SalaryComponentRepository(_context);
    public IEmployeeSalaryStructureRepository EmployeeSalaryStructures => _employeeSalaryStructures ??= new EmployeeSalaryStructureRepository(_context);

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}

public class TenantUnitOfWork : IUnitOfWork
{
    private readonly ITenantDbContextFactory _contextFactory;
    private readonly ITenantService _tenantService;
    private HRMSDbContext? _context;
    private IDbContextTransaction? _transaction;

    private IUserRepository? _users;
    private IOrganizationRepository? _organizations;
    private IEmployeeDetailRepository? _employeeDetails;
    private IAttendanceRepository? _attendance;
    private ILeaveRepository? _leaves;
    private ILeaveTypeRepository? _leaveTypes;
    private ILeaveBalanceRepository? _leaveBalances;
    private IMonthlyLeaveAllowanceRepository? _monthlyLeaveAllowances;
    private IMonthlyLeaveUsageRepository? _monthlyLeaveUsages;
    private ITaskRepository? _tasks;
    private ITaskUpdateRepository? _taskUpdates;
    private IPerformanceRepository? _performance;
    private IRoleRepository? _roles;
    private IPermissionRepository? _permissions;
    private IRolePermissionRepository? _rolePermissions;
    private IEmployeeRoleRepository? _employeeRoles;
    private IEmployeePermissionRepository? _employeePermissions;
    private ISalaryComponentRepository? _salaryComponents;
    private IEmployeeSalaryStructureRepository? _employeeSalaryStructures;

    public TenantUnitOfWork(ITenantDbContextFactory contextFactory, ITenantService tenantService)
    {
        _contextFactory = contextFactory;
        _tenantService = tenantService;
    }

    private HRMSDbContext GetContext()
    {
        if (_context == null)
        {
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                throw new InvalidOperationException("No tenant context set. Cannot create database context.");
            }

            _context = _contextFactory.CreateDbContext(tenant.OrganizationId);
        }

        return _context;
    }

    public IUserRepository Users => _users ??= new UserRepository(GetContext());

    public IOrganizationRepository Organizations => _organizations ??= new OrganizationRepository(GetContext());

    public IEmployeeDetailRepository EmployeeDetails => _employeeDetails ??= new EmployeeDetailRepository(GetContext());

    public IAttendanceRepository Attendance => _attendance ??= new AttendanceRepository(GetContext());

    public ILeaveRepository Leaves => _leaves ??= new LeaveRepository(GetContext());
    public ILeaveTypeRepository LeaveTypes => _leaveTypes ??= new LeaveTypeRepository(GetContext());
    public ILeaveBalanceRepository LeaveBalances => _leaveBalances ??= new LeaveBalanceRepository(GetContext());
    public IMonthlyLeaveAllowanceRepository MonthlyLeaveAllowances => _monthlyLeaveAllowances ??= new MonthlyLeaveAllowanceRepository(GetContext());
    public IMonthlyLeaveUsageRepository MonthlyLeaveUsages => _monthlyLeaveUsages ??= new MonthlyLeaveUsageRepository(GetContext());

    public ITaskRepository Tasks => _tasks ??= new TaskRepository(GetContext());
    public ITaskUpdateRepository TaskUpdates => _taskUpdates ??= new TaskUpdateRepository(GetContext());

    public IPerformanceRepository Performance => _performance ??= new PerformanceRepository(GetContext());

    public IRoleRepository Roles => _roles ??= new RoleRepository(GetContext());
    public IPermissionRepository Permissions => _permissions ??= new PermissionRepository(GetContext());
    public IRolePermissionRepository RolePermissions => _rolePermissions ??= new RolePermissionRepository(GetContext());
    public IEmployeeRoleRepository EmployeeRoles => _employeeRoles ??= new EmployeeRoleRepository(GetContext());
    public IEmployeePermissionRepository EmployeePermissions => _employeePermissions ??= new EmployeePermissionRepository(GetContext());
    public ISalaryComponentRepository SalaryComponents => _salaryComponents ??= new SalaryComponentRepository(GetContext());
    public IEmployeeSalaryStructureRepository EmployeeSalaryStructures => _employeeSalaryStructures ??= new EmployeeSalaryStructureRepository(GetContext());

    public async Task<int> SaveChangesAsync()
    {
        return await GetContext().SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await GetContext().Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context?.Dispose();
    }
}
