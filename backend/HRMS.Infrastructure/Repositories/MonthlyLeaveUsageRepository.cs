using Microsoft.EntityFrameworkCore;
using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Data;
using HRMS.Core.Entities;

namespace HRMS.Infrastructure.Repositories;

public class MonthlyLeaveUsageRepository : BaseRepository<MonthlyLeaveUsage>, IMonthlyLeaveUsageRepository
{
    public MonthlyLeaveUsageRepository(HRMSDbContext context) : base(context)
    {
    }

    public async System.Threading.Tasks.Task<MonthlyLeaveUsage?> GetByLeaveRequestAsync(Guid leaveRequestId)
    {
        return await _dbSet
            .Include(mlu => mlu.LeaveRequest)
            .Include(mlu => mlu.MonthlyLeaveAllowance)
                .ThenInclude(mla => mla.User)
            .FirstOrDefaultAsync(mlu => mlu.LeaveRequestId == leaveRequestId);
    }

    public async System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveUsage>> GetByAllowanceAsync(Guid allowanceId)
    {
        return await _dbSet
            .Include(mlu => mlu.LeaveRequest)
            .Where(mlu => mlu.MonthlyLeaveAllowanceId == allowanceId)
            .OrderByDescending(mlu => mlu.UsageDate)
            .ToListAsync();
    }

    public async System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveUsage>> GetByUserAndMonthAsync(Guid userId, int year, int month)
    {
        return await _dbSet
            .Include(mlu => mlu.LeaveRequest)
            .Include(mlu => mlu.MonthlyLeaveAllowance)
            .Where(mlu => mlu.MonthlyLeaveAllowance.UserId == userId && 
                         mlu.MonthlyLeaveAllowance.Year == year && 
                         mlu.MonthlyLeaveAllowance.Month == month)
            .OrderByDescending(mlu => mlu.UsageDate)
            .ToListAsync();
    }
}
