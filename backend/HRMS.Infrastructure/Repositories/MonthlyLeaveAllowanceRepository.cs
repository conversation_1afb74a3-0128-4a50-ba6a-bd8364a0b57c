using Microsoft.EntityFrameworkCore;
using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Data;
using HRMS.Core.Entities;

namespace HRMS.Infrastructure.Repositories;

public class MonthlyLeaveAllowanceRepository : BaseRepository<MonthlyLeaveAllowance>, IMonthlyLeaveAllowanceRepository
{
    public MonthlyLeaveAllowanceRepository(HRMSDbContext context) : base(context)
    {
    }

    public async System.Threading.Tasks.Task<MonthlyLeaveAllowance?> GetByUserAndMonthAsync(Guid userId, int year, int month)
    {
        return await _dbSet
            .Include(mla => mla.User)
            .FirstOrDefaultAsync(mla => mla.UserId == userId && mla.Year == year && mla.Month == month);
    }

    public async System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveAllowance>> GetByUserAndYearAsync(Guid userId, int year)
    {
        return await _dbSet
            .Include(mla => mla.User)
            .Where(mla => mla.UserId == userId && mla.Year == year)
            .OrderBy(mla => mla.Month)
            .ToListAsync();
    }

    public async System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveAllowance>> GetUnprocessedAllowancesAsync(int year, int month)
    {
        return await _dbSet
            .Include(mla => mla.User)
            .Where(mla => mla.Year == year && mla.Month == month && !mla.IsProcessed)
            .ToListAsync();
    }

    public async System.Threading.Tasks.Task<MonthlyLeaveAllowance> CreateOrUpdateAllowanceAsync(Guid userId, int year, int month, int baseAllowance = 1, int carriedForward = 0)
    {
        var existing = await GetByUserAndMonthAsync(userId, year, month);
        
        if (existing != null)
        {
            existing.BaseAllowance = baseAllowance;
            existing.CarriedForward = carriedForward;
            existing.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return existing;
        }

        var newAllowance = new MonthlyLeaveAllowance
        {
            UserId = userId,
            Year = year,
            Month = month,
            BaseAllowance = baseAllowance,
            CarriedForward = carriedForward,
            UsedLeaves = 0,
            IsProcessed = false
        };

        await _dbSet.AddAsync(newAllowance);
        await _context.SaveChangesAsync();
        return newAllowance;
    }

    public async System.Threading.Tasks.Task<bool> HasAvailableLeaveAsync(Guid userId, int year, int month)
    {
        var allowance = await GetByUserAndMonthAsync(userId, year, month);
        return allowance != null && allowance.RemainingLeaves > 0;
    }

    public async System.Threading.Tasks.Task<int> GetRemainingLeavesAsync(Guid userId, int year, int month)
    {
        var allowance = await GetByUserAndMonthAsync(userId, year, month);
        return allowance?.RemainingLeaves ?? 0;
    }

    public async System.Threading.Tasks.Task<bool> DeductLeaveAsync(Guid userId, Guid leaveRequestId, int year, int month, int daysToDeduct)
    {
        var allowance = await GetByUserAndMonthAsync(userId, year, month);
        
        if (allowance == null || allowance.RemainingLeaves < daysToDeduct)
        {
            return false;
        }

        // Monthly leave usage tracking disabled - balance functionality removed

        // Update allowance
        allowance.UsedLeaves += daysToDeduct;
        allowance.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async System.Threading.Tasks.Task<bool> RestoreLeaveAsync(Guid leaveRequestId)
    {
        // Monthly leave usage tracking disabled - balance functionality removed
        return await System.Threading.Tasks.Task.FromResult(true); // Always return success since balance is disabled
    }

    public async System.Threading.Tasks.Task ProcessMonthlyCarryoverAsync(int year, int month)
    {
        var unprocessedAllowances = await GetUnprocessedAllowancesAsync(year, month);

        foreach (var allowance in unprocessedAllowances)
        {
            // Calculate next month
            var nextMonth = month == 12 ? 1 : month + 1;
            var nextYear = month == 12 ? year + 1 : year;

            // Create or update next month's allowance with carryover
            var remainingLeaves = allowance.RemainingLeaves;
            if (remainingLeaves > 0)
            {
                await CreateOrUpdateAllowanceAsync(allowance.UserId, nextYear, nextMonth, 1, remainingLeaves);
            }

            // Mark current month as processed
            allowance.IsProcessed = true;
            allowance.UpdatedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
    }
}
