{"format": 1, "restore": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj": {}}, "projects": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj", "projectName": "HRMS.Core", "projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj", "projectName": "HRMS.Infrastructure", "projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj": {"projectPath": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.Core/HRMS.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.13.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}