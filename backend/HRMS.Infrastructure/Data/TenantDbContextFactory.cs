using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using HRMS.Core.Interfaces;

namespace HRMS.Infrastructure.Data;

public interface ITenantDbContextFactory
{
    HRMSDbContext CreateDbContext(string organizationId);
    HRMSDbContext CreateMasterDbContext();
}

public class TenantDbContextFactory : ITenantDbContextFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TenantDbContextFactory> _logger;

    public TenantDbContextFactory(
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        ILogger<TenantDbContextFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
    }

    public HRMSDbContext CreateDbContext(string organizationId)
    {
        var connectionString = GetConnectionString();
        var schema = GetSchemaName(organizationId);

        var optionsBuilder = new DbContextOptionsBuilder<HRMSDbContext>();

        // Detect database provider
        if (IsSqlite(connectionString))
        {
            // For SQLite, we'll use separate database files per organization
            var sqliteConnectionString = connectionString.Replace(".db", $"_{organizationId}.db");
            optionsBuilder.UseSqlite(sqliteConnectionString);
        }
        else
        {
            optionsBuilder.UseSqlServer(connectionString, options =>
            {
                options.MigrationsHistoryTable("__EFMigrationsHistory", schema);
            });
        }

        _logger.LogDebug("Creating DbContext for organization {OrganizationId} with schema {Schema}",
            organizationId, schema);

        return new HRMSDbContext(optionsBuilder.Options, schema);
    }

    public HRMSDbContext CreateMasterDbContext()
    {
        var connectionString = GetConnectionString();
        var schema = "dbo"; // Master schema

        var optionsBuilder = new DbContextOptionsBuilder<HRMSDbContext>();

        // Detect database provider
        if (IsSqlite(connectionString))
        {
            optionsBuilder.UseSqlite(connectionString);
        }
        else
        {
            optionsBuilder.UseSqlServer(connectionString, options =>
            {
                options.MigrationsHistoryTable("__EFMigrationsHistory", schema);
            });
        }

        _logger.LogDebug("Creating master DbContext with schema {Schema}", schema);

        return new HRMSDbContext(optionsBuilder.Options, schema);
    }

    private string GetConnectionString()
    {
        var connectionString = _configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("Database connection string 'DefaultConnection' not found.");
        }

        // Only process SQL Server placeholders if it's not SQLite
        if (!IsSqlite(connectionString))
        {
            // Replace placeholders with environment variables if they exist
            connectionString = connectionString
                .Replace("{SQL_SERVER_HOST}", Environment.GetEnvironmentVariable("SQL_SERVER_HOST") ?? "{SQL_SERVER_HOST}")
                .Replace("{SQL_SERVER_DATABASE}", Environment.GetEnvironmentVariable("SQL_SERVER_DATABASE") ?? "{SQL_SERVER_DATABASE}")
                .Replace("{SQL_SERVER_USERNAME}", Environment.GetEnvironmentVariable("SQL_SERVER_USERNAME") ?? "{SQL_SERVER_USERNAME}")
                .Replace("{SQL_SERVER_PASSWORD}", Environment.GetEnvironmentVariable("SQL_SERVER_PASSWORD") ?? "{SQL_SERVER_PASSWORD}");

            // Validate that placeholders have been replaced
            if (connectionString.Contains("{SQL_SERVER_"))
            {
                throw new InvalidOperationException("SQL Server connection string contains unresolved placeholders. Please set the required environment variables: SQL_SERVER_HOST, SQL_SERVER_DATABASE, SQL_SERVER_USERNAME, SQL_SERVER_PASSWORD");
            }
        }

        return connectionString;
    }

    private static bool IsSqlite(string connectionString)
    {
        return connectionString.Contains(".db");
    }

    private string GetSchemaName(string organizationId)
    {
        // Convert organization ID to schema name
        // Format: org_<organizationId>
        var schemaPrefix = _configuration.GetValue<string>("Database:SchemaPrefix") ?? "org_";
        var schemaSuffix = _configuration.GetValue<string>("Database:SchemaSuffix") ?? "";
        
        return $"{schemaPrefix}{organizationId.Replace("-", "")}{schemaSuffix}".ToLowerInvariant();
    }
}

public class TenantService : ITenantService
{
    private string? _organizationId;
    private string? _userId;
    private string? _userRole;

    public TenantContext GetCurrentTenant()
    {
        return new TenantContext
        {
            OrganizationId = _organizationId,
            UserId = _userId,
            UserRole = _userRole
        };
    }

    public void SetCurrentTenant(string organizationId, string? userId = null, string? userRole = null)
    {
        _organizationId = organizationId;
        _userId = userId;
        _userRole = userRole;
    }
}
