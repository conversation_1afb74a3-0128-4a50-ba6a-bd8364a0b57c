using Microsoft.EntityFrameworkCore.Storage;
using HRMS.Core.Interfaces;
using HRMS.Core.Entities;
using HRMS.Infrastructure.Data;
using HRMS.Infrastructure.Repositories;

namespace HRMS.Infrastructure.Services;

public class MasterDatabaseService : IMasterDatabaseService
{
    private readonly ITenantDbContextFactory _contextFactory;
    private HRMSDbContext? _context;
    private IDbContextTransaction? _transaction;

    public MasterDatabaseService(ITenantDbContextFactory contextFactory)
    {
        _contextFactory = contextFactory;
    }

    private HRMSDbContext GetContext()
    {
        return _context ??= _contextFactory.CreateMasterDbContext();
    }

    public async System.Threading.Tasks.Task<bool> DomainExistsAsync(string domain)
    {
        var context = GetContext();
        var organizationRepo = new OrganizationRepository(context);
        return await organizationRepo.DomainExistsAsync(domain);
    }

    public async System.Threading.Tasks.Task<bool> EmailExistsAsync(string email)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        return await userRepo.EmailExistsAsync(email);
    }

    public async System.Threading.Tasks.Task<User?> GetUserByEmailAsync(string email)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        return await userRepo.GetByEmailAsync(email);
    }

    public async System.Threading.Tasks.Task<User?> GetUserByIdAsync(Guid userId)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        return await userRepo.GetByIdAsync(userId);
    }

    public async System.Threading.Tasks.Task<List<User>> GetUsersByOrganizationAsync(Guid organizationId)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        var users = await userRepo.GetByOrganizationAsync(organizationId);
        return users.ToList();
    }

    public async System.Threading.Tasks.Task<Organization?> GetOrganizationByIdAsync(Guid organizationId)
    {
        var context = GetContext();
        var organizationRepo = new OrganizationRepository(context);
        return await organizationRepo.GetByIdAsync(organizationId);
    }

    public async System.Threading.Tasks.Task<List<Organization>> GetAllOrganizationsAsync()
    {
        var context = GetContext();
        var organizationRepo = new OrganizationRepository(context);
        var organizations = await organizationRepo.GetAllAsync();
        return organizations.ToList();
    }

    public async System.Threading.Tasks.Task<List<User>> GetAllUsersAsync()
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        var users = await userRepo.GetAllAsync();
        return users.ToList();
    }

    public async System.Threading.Tasks.Task<Organization> CreateOrganizationAsync(Organization organization)
    {
        var context = GetContext();
        var organizationRepo = new OrganizationRepository(context);
        await organizationRepo.AddAsync(organization);
        await context.SaveChangesAsync();
        return organization;
    }

    public async System.Threading.Tasks.Task<User> CreateUserAsync(User user)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        await userRepo.AddAsync(user);
        await context.SaveChangesAsync();
        return user;
    }

    public async System.Threading.Tasks.Task<User> UpdateUserAsync(User user)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        await userRepo.UpdateAsync(user);
        await context.SaveChangesAsync();
        return user;
    }

    public async System.Threading.Tasks.Task<EmployeeDetail> CreateEmployeeDetailAsync(EmployeeDetail employeeDetail)
    {
        var context = GetContext();
        var employeeDetailRepo = new EmployeeDetailRepository(context);
        await employeeDetailRepo.AddAsync(employeeDetail);
        await context.SaveChangesAsync();
        return employeeDetail;
    }

    public async System.Threading.Tasks.Task<EmployeeDetail> UpdateEmployeeDetailAsync(EmployeeDetail employeeDetail)
    {
        var context = GetContext();
        var employeeDetailRepo = new EmployeeDetailRepository(context);
        await employeeDetailRepo.UpdateAsync(employeeDetail);
        await context.SaveChangesAsync();
        return employeeDetail;
    }

    public async System.Threading.Tasks.Task DeleteUserAsync(Guid userId)
    {
        var context = GetContext();
        var userRepo = new UserRepository(context);
        var user = await userRepo.GetByIdAsync(userId);
        if (user != null)
        {
            await userRepo.DeleteAsync(user);
            await context.SaveChangesAsync();
        }
    }

    public async System.Threading.Tasks.Task<Organization> UpdateOrganizationAsync(Organization organization)
    {
        var context = GetContext();
        var organizationRepo = new OrganizationRepository(context);
        await organizationRepo.UpdateAsync(organization);
        await context.SaveChangesAsync();
        return organization;
    }

    public async System.Threading.Tasks.Task DeleteOrganizationAsync(Guid organizationId)
    {
        var context = GetContext();
        var organizationRepo = new OrganizationRepository(context);
        var organization = await organizationRepo.GetByIdAsync(organizationId);
        if (organization != null)
        {
            await organizationRepo.DeleteAsync(organization);
            await context.SaveChangesAsync();
        }
    }

    public async System.Threading.Tasks.Task BeginTransactionAsync()
    {
        var context = GetContext();
        _transaction = await context.Database.BeginTransactionAsync();
    }

    public async System.Threading.Tasks.Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async System.Threading.Tasks.Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context?.Dispose();
    }
}
