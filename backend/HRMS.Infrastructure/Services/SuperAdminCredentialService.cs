using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HRMS.Core.Configuration;
using HRMS.Core.Services;
using BCrypt.Net;

namespace HRMS.Infrastructure.Services;

/// <summary>
/// Service for managing super admin credentials
/// </summary>
public class SuperAdminCredentialService : ISuperAdminCredentialService
{
    private readonly IConfiguration _configuration;
    private readonly ICredentialGenerationService _credentialGenerationService;
    private readonly ILogger<SuperAdminCredentialService> _logger;
    private readonly AdminCredentialsConfiguration _adminConfig;

    public SuperAdminCredentialService(
        IConfiguration configuration,
        ICredentialGenerationService credentialGenerationService,
        ILogger<SuperAdminCredentialService> logger)
    {
        _configuration = configuration;
        _credentialGenerationService = credentialGenerationService;
        _logger = logger;
        _adminConfig = _configuration.GetSection("AdminCredentials").Get<AdminCredentialsConfiguration>() ?? new AdminCredentialsConfiguration();
    }

    public async Task<AdminCredentialsResult> GetOrGenerateSuperAdminCredentialsAsync(string superAdminEmail, string superAdminName)
    {
        _logger.LogInformation("Getting or generating super admin credentials for {SuperAdminEmail}", superAdminEmail);

        // First, try to get credentials from environment variables or configuration
        var configCredentials = await GetSuperAdminCredentialsFromConfigAsync(superAdminEmail);
        if (configCredentials != null)
        {
            _logger.LogInformation("Using super admin credentials from configuration for {SuperAdminEmail}", superAdminEmail);
            return configCredentials;
        }

        // If not found in config, generate new credentials
        _logger.LogInformation("Generating new super admin credentials for {SuperAdminEmail}", superAdminEmail);
        
        // Generate a secure password for super admin
        var plainTextPassword = _credentialGenerationService.GenerateSecurePassword(20); // Extra long password for super admin
        var hashedPassword = _credentialGenerationService.HashPassword(plainTextPassword);

        var result = new AdminCredentialsResult
        {
            Email = superAdminEmail,
            Name = superAdminName,
            PlainTextPassword = plainTextPassword,
            HashedPassword = hashedPassword,
            RequirePasswordReset = false, // Super admin doesn't require password reset by default
            GeneratedAt = DateTime.UtcNow,
            Source = CredentialSource.Generated
        };

        _logger.LogInformation("Generated super admin credentials for {SuperAdminEmail}. Password length: {PasswordLength}", 
            superAdminEmail, plainTextPassword.Length);

        return result;
    }

    public async Task<AdminCredentialsResult?> GetSuperAdminCredentialsFromConfigAsync(string superAdminEmail)
    {
        _logger.LogDebug("Checking for super admin credentials in configuration for {SuperAdminEmail}", superAdminEmail);

        // Check environment variables first (highest priority)
        var envPassword = Environment.GetEnvironmentVariable("HRMS_SUPER_ADMIN_PASSWORD");
        if (!string.IsNullOrEmpty(envPassword))
        {
            _logger.LogInformation("Found super admin password in environment variables");
            
            var hashedPassword = _credentialGenerationService.HashPassword(envPassword);
            return new AdminCredentialsResult
            {
                Email = superAdminEmail,
                Name = _adminConfig.SuperAdmin.Name,
                PlainTextPassword = envPassword,
                HashedPassword = hashedPassword,
                RequirePasswordReset = false, // Environment passwords don't require reset
                GeneratedAt = DateTime.UtcNow,
                Source = CredentialSource.Environment
            };
        }

        // Check for specific super admin password environment variable
        var specificEnvPassword = Environment.GetEnvironmentVariable($"HRMS_SUPER_ADMIN_PASSWORD_{superAdminEmail.Replace("@", "_").Replace(".", "_").ToUpperInvariant()}");
        if (!string.IsNullOrEmpty(specificEnvPassword))
        {
            _logger.LogInformation("Found specific super admin password in environment variables for {SuperAdminEmail}", superAdminEmail);
            
            var hashedPassword = _credentialGenerationService.HashPassword(specificEnvPassword);
            return new AdminCredentialsResult
            {
                Email = superAdminEmail,
                Name = _adminConfig.SuperAdmin.Name,
                PlainTextPassword = specificEnvPassword,
                HashedPassword = hashedPassword,
                RequirePasswordReset = false,
                GeneratedAt = DateTime.UtcNow,
                Source = CredentialSource.Environment
            };
        }

        // Check configuration files (lower priority)
        var configPassword = _configuration[$"AdminCredentials:SuperAdmin:Password"];
        if (!string.IsNullOrEmpty(configPassword))
        {
            _logger.LogInformation("Found super admin password in configuration");

            var hashedPassword = _credentialGenerationService.HashPassword(configPassword);
            return new AdminCredentialsResult
            {
                Email = superAdminEmail,
                Name = _adminConfig.SuperAdmin.Name,
                PlainTextPassword = configPassword,
                HashedPassword = hashedPassword,
                RequirePasswordReset = _adminConfig.RequirePasswordReset,
                GeneratedAt = DateTime.UtcNow,
                Source = CredentialSource.Configuration
            };
        }

        _logger.LogDebug("No super admin credentials found in configuration for {SuperAdminEmail}", superAdminEmail);
        return null;
    }

    public async Task<bool> ValidateExistingSuperAdminCredentialsAsync(string superAdminEmail, string passwordHash)
    {
        if (string.IsNullOrEmpty(passwordHash))
        {
            _logger.LogDebug("No existing password hash for super admin {SuperAdminEmail}", superAdminEmail);
            return false;
        }

        // Check if we have a configured password that should override the existing one
        var configCredentials = await GetSuperAdminCredentialsFromConfigAsync(superAdminEmail);
        if (configCredentials != null)
        {
            // If we have config credentials, check if they match the existing hash
            var configMatches = BCrypt.Net.BCrypt.Verify(configCredentials.PlainTextPassword, passwordHash);
            if (!configMatches)
            {
                _logger.LogInformation("Existing super admin credentials for {SuperAdminEmail} don't match configuration, will update", superAdminEmail);
                return false;
            }
            
            _logger.LogDebug("Existing super admin credentials for {SuperAdminEmail} match configuration", superAdminEmail);
            return true;
        }

        // If no config credentials, existing credentials are valid
        _logger.LogDebug("No configuration override for super admin {SuperAdminEmail}, existing credentials are valid", superAdminEmail);
        return true;
    }
}
