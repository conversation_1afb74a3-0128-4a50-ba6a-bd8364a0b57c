using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

using HRMS.Core.Services;
using Microsoft.Extensions.Logging;

namespace HRMS.Infrastructure.Services;

public class CredentialGenerationService : ICredentialGenerationService
{
    private readonly ILogger<CredentialGenerationService> _logger;
    private static readonly char[] PasswordChars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%&*".ToCharArray();
    private static readonly char[] UsernameChars = "abcdefghijklmnopqrstuvwxyz0123456789".ToCharArray();

    public CredentialGenerationService(ILogger<CredentialGenerationService> logger)
    {
        _logger = logger;
    }

    public async Task<EmployeeCredentialsDto> GenerateCredentialsAsync(string employeeName, string organizationDomain)
    {
        try
        {
            var username = GenerateUsername(employeeName, organizationDomain);
            var temporaryPassword = GenerateSecurePassword();
            var hashedPassword = HashPassword(temporaryPassword);

            var credentials = new EmployeeCredentialsDto
            {
                Username = username,
                TemporaryPassword = temporaryPassword,
                HashedPassword = hashedPassword,
                GeneratedAt = DateTime.UtcNow,
                RequirePasswordReset = true
            };

            _logger.LogInformation("Generated credentials for employee: {EmployeeName}, Username: {Username}", 
                employeeName, username);

            return credentials;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate credentials for employee: {EmployeeName}", employeeName);
            throw;
        }
    }

    public async Task<EmployeeCredentialsDto> GenerateCredentialsWithEmailAsync(string employeeEmail)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(employeeEmail))
                throw new ArgumentException("Employee email cannot be empty", nameof(employeeEmail));

            // Use the existing email as the username (no generation needed)
            var temporaryPassword = GenerateSecurePassword();
            var hashedPassword = HashPassword(temporaryPassword);

            var credentials = new EmployeeCredentialsDto
            {
                Username = employeeEmail, // Use existing email as username
                TemporaryPassword = temporaryPassword,
                HashedPassword = hashedPassword,
                GeneratedAt = DateTime.UtcNow,
                RequirePasswordReset = true
            };

            _logger.LogInformation("Generated credentials for employee with existing email: {Email}", employeeEmail);

            return credentials;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating credentials for employee email: {Email}", employeeEmail);
            throw;
        }
    }

    public async Task<EmployeeCredentialsDto> GenerateCredentialsWithCustomPasswordAsync(string employeeEmail, string customPassword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(employeeEmail))
                throw new ArgumentException("Employee email cannot be empty", nameof(employeeEmail));

            if (string.IsNullOrWhiteSpace(customPassword))
                throw new ArgumentException("Custom password cannot be empty", nameof(customPassword));

            // Hash the custom password
            var hashedPassword = HashPassword(customPassword);

            var credentials = new EmployeeCredentialsDto
            {
                Username = employeeEmail, // Use existing email as username
                TemporaryPassword = customPassword, // Store the custom password for admin display
                HashedPassword = hashedPassword,
                GeneratedAt = DateTime.UtcNow,
                RequirePasswordReset = true // Still require password reset on first login
            };

            _logger.LogInformation("Generated credentials with custom password for employee email: {Email}", employeeEmail);

            return credentials;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating credentials with custom password for employee email: {Email}", employeeEmail);
            throw;
        }
    }

    public string GenerateSecurePassword(int length = 12)
    {
        if (length < 8)
            length = 8; // Minimum secure length

        using var rng = RandomNumberGenerator.Create();
        var password = new StringBuilder(length);
        var buffer = new byte[4];

        // Ensure at least one character from each category
        var categories = new[]
        {
            "ABCDEFGHJKLMNPQRSTUVWXYZ", // Uppercase (excluding I, O for clarity)
            "abcdefghijkmnpqrstuvwxyz", // Lowercase (excluding l, o for clarity)
            "23456789", // Numbers (excluding 0, 1 for clarity)
            "!@#$%&*" // Special characters
        };

        // Add one character from each category
        foreach (var category in categories)
        {
            rng.GetBytes(buffer);
            var randomIndex = Math.Abs(BitConverter.ToInt32(buffer, 0)) % category.Length;
            password.Append(category[randomIndex]);
        }

        // Fill the rest with random characters from all categories
        var allChars = string.Join("", categories);
        for (int i = password.Length; i < length; i++)
        {
            rng.GetBytes(buffer);
            var randomIndex = Math.Abs(BitConverter.ToInt32(buffer, 0)) % allChars.Length;
            password.Append(allChars[randomIndex]);
        }

        // Shuffle the password to avoid predictable patterns
        return ShuffleString(password.ToString());
    }

    public string GenerateUsername(string employeeName, string organizationDomain)
    {
        if (string.IsNullOrWhiteSpace(employeeName))
            throw new ArgumentException("Employee name cannot be empty", nameof(employeeName));

        if (string.IsNullOrWhiteSpace(organizationDomain))
            throw new ArgumentException("Organization domain cannot be empty", nameof(organizationDomain));

        // Clean and normalize the name
        var cleanName = Regex.Replace(employeeName.ToLowerInvariant(), @"[^a-z\s]", "");
        var nameParts = cleanName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (nameParts.Length == 0)
            throw new ArgumentException("Invalid employee name", nameof(employeeName));

        string baseUsername;
        
        if (nameParts.Length == 1)
        {
            // Single name: use the name + random number
            baseUsername = nameParts[0];
        }
        else
        {
            // Multiple names: use first name + first letter of last name
            var firstName = nameParts[0];
            var lastNameInitial = nameParts[nameParts.Length - 1][0];
            baseUsername = $"{firstName}{lastNameInitial}";
        }

        // Ensure minimum length and add random suffix for uniqueness
        if (baseUsername.Length < 3)
        {
            baseUsername = baseUsername.PadRight(3, 'x');
        }

        // Add random number suffix for uniqueness
        using var rng = RandomNumberGenerator.Create();
        var buffer = new byte[4];
        rng.GetBytes(buffer);
        var randomSuffix = Math.Abs(BitConverter.ToInt32(buffer, 0)) % 1000;

        var username = $"{baseUsername}{randomSuffix:D3}@{organizationDomain}";
        
        _logger.LogDebug("Generated username: {Username} for employee: {EmployeeName}", username, employeeName);
        
        return username;
    }

    public string HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be empty", nameof(password));

        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
    }

    public bool VerifyPassword(string password, string hash)
    {
        if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hash))
            return false;

        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify password");
            return false;
        }
    }

    private static string ShuffleString(string input)
    {
        using var rng = RandomNumberGenerator.Create();
        var array = input.ToCharArray();
        var buffer = new byte[4];

        for (int i = array.Length - 1; i > 0; i--)
        {
            rng.GetBytes(buffer);
            var randomIndex = Math.Abs(BitConverter.ToInt32(buffer, 0)) % (i + 1);
            (array[i], array[randomIndex]) = (array[randomIndex], array[i]);
        }

        return new string(array);
    }
}
