using System.Text.RegularExpressions;
using HRMS.Core.Services;
using Microsoft.Extensions.Logging;

namespace HRMS.Infrastructure.Services;

public class PasswordValidationService : IPasswordValidationService
{
    private readonly ILogger<PasswordValidationService> _logger;

    // Password requirements configuration
    private const int MinLength = 8;
    private const int MaxLength = 128;
    private const int MinUppercase = 1;
    private const int MinLowercase = 1;
    private const int MinDigits = 1;
    private const int MinSpecialChars = 1;

    private readonly string[] CommonPasswords = {
        "password", "123456", "password123", "admin", "qwerty", "letmein",
        "welcome", "monkey", "1234567890", "abc123", "Password1", "password1"
    };

    private readonly string[] WeakPatterns = {
        @"^(.)\1+$", // All same character
        @"^(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)", // Sequential numbers
        @"^(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)", // Sequential letters
        @"^(qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)" // Keyboard patterns
    };

    public PasswordValidationService(ILogger<PasswordValidationService> logger)
    {
        _logger = logger;
    }

    public PasswordValidationResult ValidatePassword(string password)
    {
        var result = new PasswordValidationResult();

        if (string.IsNullOrEmpty(password))
        {
            result.Errors.Add("Password cannot be empty");
            result.IsValid = false;
            result.Strength = PasswordStrength.VeryWeak;
            return result;
        }

        // Length validation
        if (password.Length < MinLength)
        {
            result.Errors.Add($"Password must be at least {MinLength} characters long");
        }

        if (password.Length > MaxLength)
        {
            result.Errors.Add($"Password must not exceed {MaxLength} characters");
        }

        // Character type validation
        var uppercaseCount = password.Count(char.IsUpper);
        var lowercaseCount = password.Count(char.IsLower);
        var digitCount = password.Count(char.IsDigit);
        var specialCharCount = password.Count(c => !char.IsLetterOrDigit(c));

        if (uppercaseCount < MinUppercase)
        {
            result.Errors.Add($"Password must contain at least {MinUppercase} uppercase letter(s)");
        }

        if (lowercaseCount < MinLowercase)
        {
            result.Errors.Add($"Password must contain at least {MinLowercase} lowercase letter(s)");
        }

        if (digitCount < MinDigits)
        {
            result.Errors.Add($"Password must contain at least {MinDigits} digit(s)");
        }

        if (specialCharCount < MinSpecialChars)
        {
            result.Errors.Add("Password must contain at least 1 special character (!@#$%^&*()_+-=[]{}|;:,.<>?)");
        }

        // Common password check
        if (CommonPasswords.Contains(password.ToLowerInvariant()))
        {
            result.Errors.Add("Password is too common and easily guessable");
        }

        // Weak pattern check
        foreach (var pattern in WeakPatterns)
        {
            if (Regex.IsMatch(password.ToLowerInvariant(), pattern))
            {
                result.Errors.Add("Password contains predictable patterns");
                break;
            }
        }

        // Calculate strength and score
        result.Score = CalculatePasswordScore(password, uppercaseCount, lowercaseCount, digitCount, specialCharCount);
        result.Strength = GetPasswordStrength(result.Score);
        result.IsValid = result.Errors.Count == 0;

        _logger.LogDebug("Password validation completed. Score: {Score}, Strength: {Strength}, Errors: {ErrorCount}",
            result.Score, result.Strength, result.Errors.Count);

        return result;
    }

    public List<string> GetPasswordRequirements()
    {
        return new List<string>
        {
            $"At least {MinLength} characters long",
            $"At least {MinUppercase} uppercase letter",
            $"At least {MinLowercase} lowercase letter",
            $"At least {MinDigits} number",
            $"At least {MinSpecialChars} special character (!@#$%^&*()_+-=[]{{}}|;:,.<>?)",
            "Not a common or easily guessable password",
            "No predictable patterns (e.g., 123, abc, qwerty)"
        };
    }

    public bool MeetsMinimumRequirements(string password)
    {
        var result = ValidatePassword(password);
        return result.IsValid;
    }

    private int CalculatePasswordScore(string password, int uppercaseCount, int lowercaseCount, int digitCount, int specialCharCount)
    {
        int score = 0;

        // Length scoring (0-25 points)
        score += Math.Min(25, password.Length * 2);

        // Character variety scoring (0-40 points)
        if (uppercaseCount > 0) score += 10;
        if (lowercaseCount > 0) score += 10;
        if (digitCount > 0) score += 10;
        if (specialCharCount > 0) score += 10;

        // Bonus for character variety (0-20 points)
        var uniqueChars = password.Distinct().Count();
        score += Math.Min(20, uniqueChars * 2);

        // Bonus for length beyond minimum (0-15 points)
        if (password.Length > MinLength)
        {
            score += Math.Min(15, (password.Length - MinLength) * 2);
        }

        // Penalty for common patterns
        if (CommonPasswords.Contains(password.ToLowerInvariant()))
        {
            score -= 30;
        }

        foreach (var pattern in WeakPatterns)
        {
            if (Regex.IsMatch(password.ToLowerInvariant(), pattern))
            {
                score -= 20;
                break;
            }
        }

        return Math.Max(0, Math.Min(100, score));
    }

    private PasswordStrength GetPasswordStrength(int score)
    {
        return score switch
        {
            >= 90 => PasswordStrength.VeryStrong,
            >= 75 => PasswordStrength.Strong,
            >= 60 => PasswordStrength.Good,
            >= 40 => PasswordStrength.Fair,
            >= 20 => PasswordStrength.Weak,
            _ => PasswordStrength.VeryWeak
        };
    }
}
