using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HRMS.Core.Interfaces;
using System.Collections.Concurrent;

namespace HRMS.Infrastructure.Services;

public class RateLimitingService : IRateLimitingService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<RateLimitingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, RateLimitConfig> _rateLimitConfigs;

    public RateLimitingService(
        IMemoryCache cache, 
        ILogger<RateLimitingService> logger,
        IConfiguration configuration)
    {
        _cache = cache;
        _logger = logger;
        _configuration = configuration;
        _rateLimitConfigs = new ConcurrentDictionary<string, RateLimitConfig>();
        InitializeRateLimitConfigs();
    }

    public async Task<bool> IsRequestAllowedAsync(string key, string endpoint, int maxRequests, int timeWindow)
    {
        var cacheKey = GetCacheKey(key, endpoint);
        var now = DateTime.UtcNow;
        
        var requestData = _cache.Get<RequestData>(cacheKey);
        
        if (requestData == null)
        {
            // First request for this key/endpoint combination
            requestData = new RequestData
            {
                Count = 1,
                WindowStart = now,
                LastRequest = now
            };
            
            var expiry = TimeSpan.FromSeconds(timeWindow);
            _cache.Set(cacheKey, requestData, expiry);
            
            _logger.LogDebug("First request for {Key} on {Endpoint}", key, endpoint);
            return true;
        }

        // Check if we're still within the time window
        var windowEnd = requestData.WindowStart.AddSeconds(timeWindow);
        
        if (now > windowEnd)
        {
            // Time window has expired, reset the counter
            requestData.Count = 1;
            requestData.WindowStart = now;
            requestData.LastRequest = now;
            
            var expiry = TimeSpan.FromSeconds(timeWindow);
            _cache.Set(cacheKey, requestData, expiry);
            
            _logger.LogDebug("Time window reset for {Key} on {Endpoint}", key, endpoint);
            return true;
        }

        // We're within the time window, check if we've exceeded the limit
        if (requestData.Count >= maxRequests)
        {
            _logger.LogWarning("Rate limit exceeded for {Key} on {Endpoint}. Count: {Count}, Limit: {Limit}", 
                key, endpoint, requestData.Count, maxRequests);
            return false;
        }

        // Increment the counter
        requestData.Count++;
        requestData.LastRequest = now;
        
        var remainingTime = windowEnd - now;
        _cache.Set(cacheKey, requestData, remainingTime);
        
        _logger.LogDebug("Request allowed for {Key} on {Endpoint}. Count: {Count}/{Limit}", 
            key, endpoint, requestData.Count, maxRequests);
        
        return true;
    }

    public async Task<int> GetRemainingRequestsAsync(string key, string endpoint, int maxRequests, int timeWindow)
    {
        var cacheKey = GetCacheKey(key, endpoint);
        var requestData = _cache.Get<RequestData>(cacheKey);
        
        if (requestData == null)
        {
            return maxRequests;
        }

        var now = DateTime.UtcNow;
        var windowEnd = requestData.WindowStart.AddSeconds(timeWindow);
        
        if (now > windowEnd)
        {
            return maxRequests;
        }

        return Math.Max(0, maxRequests - requestData.Count);
    }

    public async Task<DateTime> GetResetTimeAsync(string key, string endpoint, int timeWindow)
    {
        var cacheKey = GetCacheKey(key, endpoint);
        var requestData = _cache.Get<RequestData>(cacheKey);
        
        if (requestData == null)
        {
            return DateTime.UtcNow.AddSeconds(timeWindow);
        }

        return requestData.WindowStart.AddSeconds(timeWindow);
    }

    public async Task ClearRateLimitAsync(string key, string endpoint)
    {
        var cacheKey = GetCacheKey(key, endpoint);
        _cache.Remove(cacheKey);
        _logger.LogInformation("Rate limit cleared for {Key} on {Endpoint}", key, endpoint);
    }

    public RateLimitConfig GetRateLimitConfig(string endpoint)
    {
        // Normalize endpoint path
        var normalizedEndpoint = NormalizeEndpoint(endpoint);
        
        // Check for specific endpoint configuration
        if (_rateLimitConfigs.TryGetValue(normalizedEndpoint, out var config))
        {
            return config;
        }

        // Check for pattern matches
        foreach (var kvp in _rateLimitConfigs)
        {
            if (IsEndpointMatch(normalizedEndpoint, kvp.Key))
            {
                return kvp.Value;
            }
        }

        // Return default configuration
        return GetDefaultRateLimitConfig();
    }

    private void InitializeRateLimitConfigs()
    {
        // Authentication endpoints - stricter limits
        _rateLimitConfigs["/api/v1/auth/login"] = new RateLimitConfig
        {
            MaxRequests = 5,
            TimeWindowSeconds = 300, // 5 minutes
            Description = "Login attempts",
            Enabled = true
        };

        _rateLimitConfigs["/api/v1/auth/register-organization"] = new RateLimitConfig
        {
            MaxRequests = 3,
            TimeWindowSeconds = 3600, // 1 hour
            Description = "Organization registration",
            Enabled = true
        };

        // General API endpoints - moderate limits
        _rateLimitConfigs["/api/v1/*"] = new RateLimitConfig
        {
            MaxRequests = 100,
            TimeWindowSeconds = 300, // 5 minutes
            Description = "General API access",
            Enabled = true
        };

        // Super admin endpoints - higher limits
        _rateLimitConfigs["/api/v1/super-admin/*"] = new RateLimitConfig
        {
            MaxRequests = 200,
            TimeWindowSeconds = 300, // 5 minutes
            Description = "Super admin operations",
            Enabled = true
        };

        // Health check endpoints - very high limits
        _rateLimitConfigs["/api/v1/health/*"] = new RateLimitConfig
        {
            MaxRequests = 1000,
            TimeWindowSeconds = 60, // 1 minute
            Description = "Health checks",
            Enabled = true
        };

        _logger.LogInformation("Rate limiting configurations initialized with {Count} rules", _rateLimitConfigs.Count);
    }

    private RateLimitConfig GetDefaultRateLimitConfig()
    {
        return new RateLimitConfig
        {
            MaxRequests = 60,
            TimeWindowSeconds = 60, // 1 minute
            Description = "Default rate limit",
            Enabled = true
        };
    }

    private string GetCacheKey(string key, string endpoint)
    {
        return $"rate_limit:{key}:{NormalizeEndpoint(endpoint)}";
    }

    private string NormalizeEndpoint(string endpoint)
    {
        return endpoint?.ToLowerInvariant().Trim('/') ?? string.Empty;
    }

    private bool IsEndpointMatch(string endpoint, string pattern)
    {
        if (pattern.EndsWith("*"))
        {
            var prefix = pattern.TrimEnd('*');
            return endpoint.StartsWith(prefix, StringComparison.OrdinalIgnoreCase);
        }

        return string.Equals(endpoint, pattern, StringComparison.OrdinalIgnoreCase);
    }

    private class RequestData
    {
        public int Count { get; set; }
        public DateTime WindowStart { get; set; }
        public DateTime LastRequest { get; set; }
    }
}
