using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HRMS.Core.Configuration;
using HRMS.Core.Services;
using BCrypt.Net;

namespace HRMS.Infrastructure.Services;

/// <summary>
/// Service for managing admin credentials dynamically
/// </summary>
public class AdminCredentialService : IAdminCredentialService
{
    private readonly IConfiguration _configuration;
    private readonly ICredentialGenerationService _credentialGenerationService;
    private readonly ILogger<AdminCredentialService> _logger;
    private readonly AdminCredentialsConfiguration _adminConfig;

    public AdminCredentialService(
        IConfiguration configuration,
        ICredentialGenerationService credentialGenerationService,
        ILogger<AdminCredentialService> logger)
    {
        _configuration = configuration;
        _credentialGenerationService = credentialGenerationService;
        _logger = logger;
        _adminConfig = _configuration.GetSection("AdminCredentials").Get<AdminCredentialsConfiguration>() ?? new AdminCredentialsConfiguration();
    }

    public async Task<AdminCredentialsResult> GetOrGenerateAdminCredentialsAsync(Guid organizationId, string adminEmail, string adminName)
    {
        _logger.LogInformation("Getting or generating admin credentials for {AdminEmail} in organization {OrganizationId}", adminEmail, organizationId);

        // First, try to get credentials from environment variables or configuration
        var configCredentials = await GetAdminCredentialsFromConfigAsync(adminEmail);
        if (configCredentials != null)
        {
            _logger.LogInformation("Using admin credentials from configuration for {AdminEmail}", adminEmail);
            return configCredentials;
        }

        // If not found in config, generate new credentials
        _logger.LogInformation("Generating new admin credentials for {AdminEmail}", adminEmail);
        return await GenerateAdminCredentialsAsync(adminEmail, adminName);
    }

    public async Task<AdminCredentialsResult> GenerateAdminCredentialsAsync(string adminEmail, string adminName)
    {
        _logger.LogInformation("Generating admin credentials for {AdminEmail}", adminEmail);

        // Generate a secure password
        var plainTextPassword = _credentialGenerationService.GenerateSecurePassword(16); // Longer password for admin
        var hashedPassword = _credentialGenerationService.HashPassword(plainTextPassword);

        var result = new AdminCredentialsResult
        {
            Email = adminEmail,
            Name = adminName,
            PlainTextPassword = plainTextPassword,
            HashedPassword = hashedPassword,
            RequirePasswordReset = _adminConfig.RequirePasswordReset,
            GeneratedAt = DateTime.UtcNow,
            Source = CredentialSource.Generated
        };

        _logger.LogInformation("Generated admin credentials for {AdminEmail}. Password length: {PasswordLength}, Requires reset: {RequireReset}", 
            adminEmail, plainTextPassword.Length, result.RequirePasswordReset);

        return result;
    }

    public async Task<AdminCredentialsResult?> GetAdminCredentialsFromConfigAsync(string adminEmail)
    {
        _logger.LogDebug("Checking for admin credentials in configuration for {AdminEmail}", adminEmail);

        // Check environment variables first (highest priority)
        var envPassword = Environment.GetEnvironmentVariable($"HRMS_ADMIN_PASSWORD_{adminEmail.Replace("@", "_").Replace(".", "_").ToUpperInvariant()}");
        if (!string.IsNullOrEmpty(envPassword))
        {
            _logger.LogInformation("Found admin password in environment variables for {AdminEmail}", adminEmail);
            
            var hashedPassword = _credentialGenerationService.HashPassword(envPassword);
            return new AdminCredentialsResult
            {
                Email = adminEmail,
                Name = _adminConfig.DefaultOrganization.AdminName,
                PlainTextPassword = envPassword,
                HashedPassword = hashedPassword,
                RequirePasswordReset = false, // Environment passwords don't require reset
                GeneratedAt = DateTime.UtcNow,
                Source = CredentialSource.Environment
            };
        }

        // Check for generic admin password environment variable
        var genericEnvPassword = Environment.GetEnvironmentVariable("HRMS_ADMIN_PASSWORD");
        if (!string.IsNullOrEmpty(genericEnvPassword))
        {
            _logger.LogInformation("Found generic admin password in environment variables");
            
            var hashedPassword = _credentialGenerationService.HashPassword(genericEnvPassword);
            return new AdminCredentialsResult
            {
                Email = adminEmail,
                Name = _adminConfig.DefaultOrganization.AdminName,
                PlainTextPassword = genericEnvPassword,
                HashedPassword = hashedPassword,
                RequirePasswordReset = false,
                GeneratedAt = DateTime.UtcNow,
                Source = CredentialSource.Environment
            };
        }

        // Check configuration files (lower priority)
        var configPassword = _configuration[$"AdminCredentials:Passwords:{adminEmail}"];
        if (!string.IsNullOrEmpty(configPassword))
        {
            _logger.LogInformation("Found admin password in configuration for {AdminEmail}", adminEmail);

            var hashedPassword = _credentialGenerationService.HashPassword(configPassword);
            return new AdminCredentialsResult
            {
                Email = adminEmail,
                Name = _adminConfig.DefaultOrganization.AdminName,
                PlainTextPassword = configPassword,
                HashedPassword = hashedPassword,
                RequirePasswordReset = _adminConfig.RequirePasswordReset,
                GeneratedAt = DateTime.UtcNow,
                Source = CredentialSource.Configuration
            };
        }

        _logger.LogDebug("No admin credentials found in configuration for {AdminEmail}", adminEmail);
        return null;
    }

    public async Task<bool> ValidateExistingCredentialsAsync(string adminEmail, string passwordHash)
    {
        if (string.IsNullOrEmpty(passwordHash))
        {
            _logger.LogDebug("No existing password hash for {AdminEmail}", adminEmail);
            return false;
        }

        // Check if we have a configured password that should override the existing one
        var configCredentials = await GetAdminCredentialsFromConfigAsync(adminEmail);
        if (configCredentials != null)
        {
            // If we have config credentials, check if they match the existing hash
            var configMatches = BCrypt.Net.BCrypt.Verify(configCredentials.PlainTextPassword, passwordHash);
            if (!configMatches)
            {
                _logger.LogInformation("Existing credentials for {AdminEmail} don't match configuration, will update", adminEmail);
                return false;
            }
            
            _logger.LogDebug("Existing credentials for {AdminEmail} match configuration", adminEmail);
            return true;
        }

        // If no config credentials, existing credentials are valid
        _logger.LogDebug("No configuration override for {AdminEmail}, existing credentials are valid", adminEmail);
        return true;
    }
}
