using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using HRMS.Core.Interfaces;

namespace HRMS.Infrastructure.Services;

public class JwtService : IJwtService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<JwtService> _logger;
    private readonly ITokenBlacklistService _tokenBlacklistService;
    private readonly string _secretKey;
    private readonly string _issuer;
    private readonly string _audience;
    private readonly int _expirationMinutes;

    public JwtService(
        IConfiguration configuration,
        ILogger<JwtService> logger,
        ITokenBlacklistService tokenBlacklistService)
    {
        _configuration = configuration;
        _logger = logger;
        _tokenBlacklistService = tokenBlacklistService;
        _secretKey = _configuration["Jwt:SecretKey"] ?? throw new InvalidOperationException("JWT <PERSON> not configured");
        _issuer = _configuration["Jwt:Issuer"] ?? "HRMS.API";
        _audience = _configuration["Jwt:Audience"] ?? "HRMS.Client";
        _expirationMinutes = _configuration.GetValue<int>("Jwt:ExpirationMinutes", 1440); // Default 24 hours
    }

    public string GenerateToken(HRMS.Core.Entities.User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_secretKey);
        var tokenId = Guid.NewGuid().ToString(); // Unique token identifier
        var issuedAt = DateTime.UtcNow;
        var expiresAt = issuedAt.AddMinutes(_expirationMinutes);

        var claims = new List<Claim>
        {
            // Standard JWT claims
            new(JwtRegisteredClaimNames.Jti, tokenId), // JWT ID for blacklisting
            new(JwtRegisteredClaimNames.Iat, ((DateTimeOffset)issuedAt).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new(JwtRegisteredClaimNames.Nbf, ((DateTimeOffset)issuedAt).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new(JwtRegisteredClaimNames.Exp, ((DateTimeOffset)expiresAt).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Email, user.Email),
            new(ClaimTypes.Name, user.Name),
            new(ClaimTypes.Role, user.Role.ToString()),

            // Custom claims for easier access
            new("user_id", user.Id.ToString()),
            new("email", user.Email),
            new("name", user.Name),
            new("role", user.Role.ToString()),
            new("is_active", user.IsActive.ToString()),
            new("email_verified", user.EmailVerified.ToString()),

            // Enhanced security claims
            new("token_version", "2.0"), // Token version for future compatibility
            new("last_login", user.LastLogin?.ToString("O") ?? ""),
            new("token_issued_at", issuedAt.ToString("O")),
            new("security_stamp", GenerateSecurityStamp(user)) // Security stamp for invalidation
        };

        // Add organization-specific claims
        if (user.OrganizationId.HasValue)
        {
            claims.Add(new Claim("organization_id", user.OrganizationId.Value.ToString()));

            // Add organization details if available
            if (user.Organization != null)
            {
                claims.Add(new Claim("organization_name", user.Organization.Name));
                claims.Add(new Claim("organization_domain", user.Organization.Domain));
                claims.Add(new Claim("organization_status", user.Organization.Status.ToString()));
            }
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = expiresAt,
            IssuedAt = issuedAt,
            NotBefore = issuedAt,
            Issuer = _issuer,
            Audience = _audience,
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        _logger.LogDebug("Generated JWT token for user {UserId} with JTI {TokenId}, expires at {ExpiresAt}",
            user.Id, tokenId, expiresAt);

        return tokenString;
    }

    public string GenerateRefreshToken()
    {
        var randomNumber = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }

    public bool ValidateToken(string token)
    {
        return ValidateTokenAsync(token).GetAwaiter().GetResult();
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(token))
            {
                _logger.LogDebug("Token validation failed: Token is null or empty");
                return false;
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _issuer,
                ValidateAudience = true,
                ValidAudience = _audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero, // No clock skew for enhanced security
                RequireExpirationTime = true,
                RequireSignedTokens = true
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

            // Additional validation - check if token is a JWT
            if (validatedToken is not JwtSecurityToken jwtToken)
            {
                _logger.LogDebug("Token validation failed: Not a valid JWT token");
                return false;
            }

            // Validate algorithm
            if (!jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                _logger.LogDebug("Token validation failed: Invalid algorithm {Algorithm}", jwtToken.Header.Alg);
                return false;
            }

            // Check token version for compatibility
            var tokenVersion = GetClaimFromToken(token, "token_version");
            if (string.IsNullOrEmpty(tokenVersion) || !IsTokenVersionSupported(tokenVersion))
            {
                _logger.LogDebug("Token validation failed: Unsupported token version {Version}", tokenVersion);
                return false;
            }

            // Check if token is blacklisted
            var tokenId = GetClaimFromToken(token, JwtRegisteredClaimNames.Jti);
            if (!string.IsNullOrEmpty(tokenId))
            {
                var isBlacklisted = await _tokenBlacklistService.IsTokenBlacklistedAsync(tokenId);
                if (isBlacklisted)
                {
                    _logger.LogDebug("Token validation failed: Token {TokenId} is blacklisted", tokenId);
                    return false;
                }
            }

            // Additional security checks
            var userId = GetClaimFromToken(token, "user_id");
            var securityStamp = GetClaimFromToken(token, "security_stamp");

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(securityStamp))
            {
                // In a production system, you might want to validate the security stamp
                // against the current user's security stamp in the database
                // This allows for immediate token invalidation when user security changes
            }

            _logger.LogDebug("Token validation successful for token {TokenId}", tokenId);
            return true;
        }
        catch (SecurityTokenExpiredException ex)
        {
            _logger.LogDebug("Token validation failed: Token has expired - {Message}", ex.Message);
            return false;
        }
        catch (SecurityTokenInvalidSignatureException ex)
        {
            _logger.LogWarning("Token validation failed: Invalid signature - {Message}", ex.Message);
            return false;
        }
        catch (SecurityTokenValidationException ex)
        {
            _logger.LogDebug("Token validation failed: General validation failure - {Message}", ex.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during token validation");
            return false;
        }
    }

    public string? GetUserIdFromToken(string token)
    {
        return GetClaimFromToken(token, "user_id");
    }

    public string? GetOrganizationIdFromToken(string token)
    {
        return GetClaimFromToken(token, "organization_id");
    }

    public string? GetUserRoleFromToken(string token)
    {
        return GetClaimFromToken(token, "role");
    }

    public async Task<bool> InvalidateTokenAsync(string token, string reason = "logout")
    {
        try
        {
            var tokenId = GetClaimFromToken(token, JwtRegisteredClaimNames.Jti);
            var userId = GetClaimFromToken(token, "user_id");

            if (string.IsNullOrEmpty(tokenId) || string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Cannot invalidate token: Missing token ID or user ID");
                return false;
            }

            // Get token expiry time
            var expiryTime = GetTokenExpiryTime(token);
            if (expiryTime == null)
            {
                _logger.LogWarning("Cannot invalidate token: Unable to determine expiry time");
                return false;
            }

            await _tokenBlacklistService.BlacklistTokenAsync(tokenId, userId, expiryTime.Value, reason);
            _logger.LogInformation("Token {TokenId} invalidated for user {UserId}, reason: {Reason}",
                tokenId, userId, reason);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating token");
            return false;
        }
    }

    public async Task<bool> InvalidateAllUserTokensAsync(string userId, string reason = "security")
    {
        try
        {
            await _tokenBlacklistService.BlacklistAllUserTokensAsync(userId, reason);
            _logger.LogInformation("All tokens invalidated for user {UserId}, reason: {Reason}", userId, reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating all tokens for user {UserId}", userId);
            return false;
        }
    }

    private string? GetClaimFromToken(string token, string claimType)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwt = tokenHandler.ReadJwtToken(token);

            var claim = jwt.Claims.FirstOrDefault(c => c.Type == claimType);
            return claim?.Value;
        }
        catch
        {
            return null;
        }
    }

    private DateTime? GetTokenExpiryTime(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwt = tokenHandler.ReadJwtToken(token);

            var expClaim = jwt.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Exp);
            if (expClaim != null && long.TryParse(expClaim.Value, out var exp))
            {
                return DateTimeOffset.FromUnixTimeSeconds(exp).DateTime;
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    private string GenerateSecurityStamp(HRMS.Core.Entities.User user)
    {
        // Generate a security stamp based on user properties
        // This can be used to invalidate tokens when user security changes
        var data = $"{user.Id}:{user.Email}:{user.PasswordHash}:{user.UpdatedAt:O}";
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToBase64String(hash)[..16]; // Take first 16 characters
    }

    private bool IsTokenVersionSupported(string version)
    {
        // Define supported token versions
        var supportedVersions = new[] { "2.0", "1.0" };
        return supportedVersions.Contains(version);
    }
}

public class PasswordService : IPasswordService
{
    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt());
    }

    public bool VerifyPassword(string password, string hash)
    {
        return BCrypt.Net.BCrypt.Verify(password, hash);
    }

    public string GenerateTemporaryPassword()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 12)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }
}
