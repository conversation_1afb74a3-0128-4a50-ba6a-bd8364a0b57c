using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Configuration;
using HRMS.Core.Services;
using HRMS.Infrastructure.Data;
using HRMS.Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace HRMS.Infrastructure.Services;

public class DataSeedingService : IDataSeedingService
{
    private readonly ITenantDbContextFactory _contextFactory;
    private readonly ILogger<DataSeedingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAdminCredentialService _adminCredentialService;
    private readonly ISuperAdminCredentialService _superAdminCredentialService;
    private readonly AdminCredentialsConfiguration _adminConfig;

    public DataSeedingService(
        ITenantDbContextFactory contextFactory,
        ILogger<DataSeedingService> logger,
        IConfiguration configuration,
        IAdminCredentialService adminCredentialService,
        ISuperAdminCredentialService superAdminCredentialService)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _configuration = configuration;
        _adminCredentialService = adminCredentialService;
        _superAdminCredentialService = superAdminCredentialService;
        _adminConfig = _configuration.GetSection("AdminCredentials").Get<AdminCredentialsConfiguration>() ?? new AdminCredentialsConfiguration();
    }

    public async System.Threading.Tasks.Task SeedInitialDataAsync()
    {
        _logger.LogInformation("Starting data seeding...");

        try
        {
            await SeedMasterDataAsync();
            await SeedOrganizationDataAsync();
            _logger.LogInformation("Data seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to seed initial data");
            throw;
        }
    }

    private async System.Threading.Tasks.Task SeedMasterDataAsync()
    {
        using var context = _contextFactory.CreateMasterDbContext();

        // Seed super admin first
        await SeedSuperAdminAsync(context);

        // Seed default organization from configuration
        var orgConfig = _adminConfig.DefaultOrganization;
        var defaultOrg = await context.Organizations.FirstOrDefaultAsync(o => o.Domain == orgConfig.Domain);
        if (defaultOrg == null)
        {
            defaultOrg = new Organization
            {
                Id = Guid.NewGuid(),
                Name = orgConfig.Name,
                Domain = orgConfig.Domain,
                Industry = orgConfig.Industry,
                EmployeeCount = 1,
                SubscriptionPlan = "Enterprise",
                Status = OrganizationStatus.Active,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Organizations.Add(defaultOrg);
            _logger.LogInformation("Added default organization: {OrganizationName} ({Domain})", orgConfig.Name, orgConfig.Domain);
        }

        // Create or update default organization admin user with dynamic credentials
        var existingUser = await context.Users.FirstOrDefaultAsync(u => u.Email == orgConfig.AdminEmail);
        User defaultAdmin;

        if (existingUser == null)
        {
            // Generate or retrieve admin credentials dynamically
            var adminCredentials = await _adminCredentialService.GetOrGenerateAdminCredentialsAsync(
                defaultOrg.Id, orgConfig.AdminEmail, orgConfig.AdminName);

            defaultAdmin = new User
            {
                Id = Guid.NewGuid(),
                Email = orgConfig.AdminEmail,
                Name = orgConfig.AdminName,
                Role = UserRole.OrgAdmin,
                PasswordHash = adminCredentials.HashedPassword,
                IsActive = true,
                OrganizationId = defaultOrg.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Users.Add(defaultAdmin);
            _logger.LogInformation("Added default admin user to master database: {AdminEmail} (Source: {CredentialSource})",
                orgConfig.AdminEmail, adminCredentials.Source);

            // Log the generated password for admin reference (only in development)
            if (_configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT") == "Development" &&
                adminCredentials.Source == CredentialSource.Generated)
            {
                _logger.LogWarning("DEVELOPMENT ONLY - Generated admin password for {AdminEmail}: {Password}",
                    orgConfig.AdminEmail, adminCredentials.PlainTextPassword);
            }
        }
        else
        {
            // Update existing user to ensure it's active and has correct settings
            existingUser.IsActive = true;
            existingUser.Name = orgConfig.AdminName;
            existingUser.OrganizationId = defaultOrg.Id;
            existingUser.Role = UserRole.OrgAdmin;
            existingUser.UpdatedAt = DateTime.UtcNow;

            // Check if credentials need to be updated based on configuration
            var credentialsValid = await _adminCredentialService.ValidateExistingCredentialsAsync(
                orgConfig.AdminEmail, existingUser.PasswordHash);

            if (!credentialsValid)
            {
                var adminCredentials = await _adminCredentialService.GetOrGenerateAdminCredentialsAsync(
                    defaultOrg.Id, orgConfig.AdminEmail, orgConfig.AdminName);

                existingUser.PasswordHash = adminCredentials.HashedPassword;
                _logger.LogInformation("Updated admin credentials for {AdminEmail} (Source: {CredentialSource})",
                    orgConfig.AdminEmail, adminCredentials.Source);

                // Log the updated password for admin reference (only in development)
                if (_configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT") == "Development" &&
                    adminCredentials.Source == CredentialSource.Generated)
                {
                    _logger.LogWarning("DEVELOPMENT ONLY - Updated admin password for {AdminEmail}: {Password}",
                        orgConfig.AdminEmail, adminCredentials.PlainTextPassword);
                }
            }

            context.Users.Update(existingUser);
            defaultAdmin = existingUser;
            _logger.LogInformation("Updated existing default admin user to ensure it's active with correct settings");
        }

        // Create or update EmployeeDetail for the admin user
        var existingEmployeeDetail = await context.EmployeeDetails.FirstOrDefaultAsync(ed => ed.UserId == defaultAdmin.Id);
        if (existingEmployeeDetail == null)
        {
            var adminEmployeeDetail = new EmployeeDetail
            {
                UserId = defaultAdmin.Id,
                EmployeeId = $"{orgConfig.Domain.Split('.')[0].ToUpperInvariant()}_ADMIN_{DateTime.UtcNow:yyyyMMdd}",
                JobTitle = "Organization Administrator",
                Department = "Administration",
                JoinDate = DateTime.UtcNow,
                EmploymentType = EmploymentType.FullTime,
                EmploymentStatus = EmploymentStatus.Active,
                WorkLocation = "Head Office",
                BaseSalary = 120000,
                AnnualCTC = 150000,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.EmployeeDetails.Add(adminEmployeeDetail);
            _logger.LogInformation("Added EmployeeDetail for default admin user");
        }
        else
        {
            // Update existing employee detail to ensure it's active
            existingEmployeeDetail.EmploymentStatus = EmploymentStatus.Active;
            existingEmployeeDetail.UpdatedAt = DateTime.UtcNow;
            context.EmployeeDetails.Update(existingEmployeeDetail);
            _logger.LogInformation("Updated existing EmployeeDetail for default admin user");
        }

        // Skip creating Test Company organization - removed for single organization setup
        // await SeedTestOrganizationAdminUsersAsync(context);

        await context.SaveChangesAsync();
    }

    private async System.Threading.Tasks.Task SeedSuperAdminAsync(HRMSDbContext context)
    {
        var superAdminConfig = _adminConfig.SuperAdmin;

        // Check if super admin already exists
        var existingSuperAdmin = await context.Users.FirstOrDefaultAsync(u => u.Email == superAdminConfig.Email);

        if (existingSuperAdmin == null)
        {
            // Generate or retrieve super admin credentials dynamically
            var superAdminCredentials = await _superAdminCredentialService.GetOrGenerateSuperAdminCredentialsAsync(
                superAdminConfig.Email, superAdminConfig.Name);

            var superAdmin = new User
            {
                Id = Guid.NewGuid(),
                Email = superAdminConfig.Email,
                Name = superAdminConfig.Name,
                Role = UserRole.SuperAdmin,
                PasswordHash = superAdminCredentials.HashedPassword,
                IsActive = true,
                OrganizationId = null, // Super admin is not tied to any organization
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Users.Add(superAdmin);
            _logger.LogInformation("Added super admin user: {SuperAdminEmail} (Source: {CredentialSource})",
                superAdminConfig.Email, superAdminCredentials.Source);

            // Log the generated password for admin reference (only in development)
            if (_configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT") == "Development" &&
                superAdminCredentials.Source == CredentialSource.Generated)
            {
                _logger.LogWarning("DEVELOPMENT ONLY - Generated super admin password for {SuperAdminEmail}: {Password}",
                    superAdminConfig.Email, superAdminCredentials.PlainTextPassword);
            }
        }
        else
        {
            // Update existing super admin to ensure it's active and has correct settings
            existingSuperAdmin.IsActive = true;
            existingSuperAdmin.Name = superAdminConfig.Name;
            existingSuperAdmin.Role = UserRole.SuperAdmin;
            existingSuperAdmin.OrganizationId = null; // Ensure super admin is not tied to any organization
            existingSuperAdmin.UpdatedAt = DateTime.UtcNow;

            // Check if credentials need to be updated based on configuration
            var credentialsValid = await _superAdminCredentialService.ValidateExistingSuperAdminCredentialsAsync(
                superAdminConfig.Email, existingSuperAdmin.PasswordHash);

            if (!credentialsValid)
            {
                var superAdminCredentials = await _superAdminCredentialService.GetOrGenerateSuperAdminCredentialsAsync(
                    superAdminConfig.Email, superAdminConfig.Name);

                existingSuperAdmin.PasswordHash = superAdminCredentials.HashedPassword;
                _logger.LogInformation("Updated super admin credentials for {SuperAdminEmail} (Source: {CredentialSource})",
                    superAdminConfig.Email, superAdminCredentials.Source);

                // Log the updated password for admin reference (only in development)
                if (_configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT") == "Development" &&
                    superAdminCredentials.Source == CredentialSource.Generated)
                {
                    _logger.LogWarning("DEVELOPMENT ONLY - Updated super admin password for {SuperAdminEmail}: {Password}",
                        superAdminConfig.Email, superAdminCredentials.PlainTextPassword);
                }
            }

            context.Users.Update(existingSuperAdmin);
            _logger.LogInformation("Updated existing super admin user to ensure it's active with correct settings");
        }
    }

    private async System.Threading.Tasks.Task SeedTestOrganizationAdminUsersAsync(HRMSDbContext context)
    {
        // Create a test organization for demo purposes
        var testOrg = await context.Organizations.FirstOrDefaultAsync(o => o.Domain == "testcompany.com");
        if (testOrg == null)
        {
            testOrg = new Organization
            {
                Id = Guid.NewGuid(),
                Name = "Test Company",
                Domain = "testcompany.com",
                Industry = "Software",
                EmployeeCount = 1,
                SubscriptionPlan = "Premium",
                Status = OrganizationStatus.Active,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Organizations.Add(testOrg);
            _logger.LogInformation("Added Test Company organization");
        }

        // Create or update test admin user
        var testAdminUser = await context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
        if (testAdminUser == null)
        {
            testAdminUser = new User
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                Name = "Test Admin",
                Role = UserRole.OrgAdmin,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!"),
                IsActive = true,
                OrganizationId = testOrg.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Users.Add(testAdminUser);
            _logger.LogInformation("Added Test Company admin user to master database");
        }
        else
        {
            // Update existing user to ensure it's active and has correct settings
            testAdminUser.IsActive = true;
            testAdminUser.Name = "Test Admin"; // Ensure correct name
            testAdminUser.OrganizationId = testOrg.Id;
            testAdminUser.Role = UserRole.OrgAdmin;
            testAdminUser.UpdatedAt = DateTime.UtcNow;

            // Update password if needed
            if (!BCrypt.Net.BCrypt.Verify("Admin123!", testAdminUser.PasswordHash))
            {
                testAdminUser.PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!");
            }

            context.Users.Update(testAdminUser);
            _logger.LogInformation("Updated existing Test Company admin user to ensure it's active with correct settings");
        }
    }

    private async System.Threading.Tasks.Task SeedOrganizationDataAsync()
    {
        // Seed leave types for existing organizations that don't have them
        await SeedLeaveTypesForExistingOrganizationsAsync();

        // Organization-specific data seeding can be added here if needed
        // For now, all users are stored in the master database
        _logger.LogInformation("Organization data seeding completed (users stored in master database)");
    }

    private async System.Threading.Tasks.Task SeedLeaveTypesForExistingOrganizationsAsync()
    {
        try
        {
            using var masterContext = _contextFactory.CreateMasterDbContext();
            var organizations = await masterContext.Organizations
                .Where(o => o.Status == OrganizationStatus.Active)
                .ToListAsync();

            foreach (var org in organizations)
            {
                try
                {
                    // Check if organization already has leave types
                    using var orgContext = _contextFactory.CreateDbContext(org.Id.ToString());
                    var existingLeaveTypes = await orgContext.LeaveTypes
                        .Where(lt => lt.OrganizationId == org.Id)
                        .CountAsync();

                    if (existingLeaveTypes == 0)
                    {
                        _logger.LogInformation("Seeding leave types for organization {OrganizationId} ({OrganizationName})",
                            org.Id, org.Name);

                        // Seed default leave types
                        var defaultLeaveTypes = new[]
                        {
                            new HRMS.Core.Entities.LeaveType
                            {
                                OrganizationId = org.Id,
                                Name = "Annual Leave",
                                Description = "Yearly vacation leave",
                                MaxDaysPerYear = 25,
                                IsCarryForward = true,
                                MaxCarryForwardDays = 5,
                                IsActive = true
                            },
                            new HRMS.Core.Entities.LeaveType
                            {
                                OrganizationId = org.Id,
                                Name = "Sick Leave",
                                Description = "Medical leave",
                                MaxDaysPerYear = 12,
                                IsCarryForward = false,
                                MaxCarryForwardDays = 0,
                                IsActive = true
                            },
                            new HRMS.Core.Entities.LeaveType
                            {
                                OrganizationId = org.Id,
                                Name = "Personal Leave",
                                Description = "Personal time off",
                                MaxDaysPerYear = 5,
                                IsCarryForward = false,
                                MaxCarryForwardDays = 0,
                                IsActive = true
                            },
                            new HRMS.Core.Entities.LeaveType
                            {
                                OrganizationId = org.Id,
                                Name = "Emergency Leave",
                                Description = "Emergency situations",
                                MaxDaysPerYear = 3,
                                IsCarryForward = false,
                                MaxCarryForwardDays = 0,
                                IsActive = true
                            }
                        };

                        await orgContext.LeaveTypes.AddRangeAsync(defaultLeaveTypes);
                        await orgContext.SaveChangesAsync();

                        _logger.LogInformation("Successfully seeded {Count} leave types for organization {OrganizationId}",
                            defaultLeaveTypes.Length, org.Id);

                        // Create leave balances for existing employees
                        var employees = await orgContext.Users
                            .Where(u => u.OrganizationId == org.Id && u.Role == UserRole.Employee)
                            .ToListAsync();

                        // Leave balance creation removed - balance functionality disabled

                        await orgContext.SaveChangesAsync();
                        _logger.LogInformation("Created leave balances for {EmployeeCount} employees in organization {OrganizationId}",
                            employees.Count, org.Id);
                    }
                    else
                    {
                        _logger.LogInformation("Organization {OrganizationId} already has {Count} leave types, skipping",
                            org.Id, existingLeaveTypes);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to seed leave types for organization {OrganizationId} ({OrganizationName})",
                        org.Id, org.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to seed leave types for existing organizations");
        }
    }
}
