using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using HRMS.Core.Interfaces;
using System.Collections.Concurrent;

namespace HRMS.Infrastructure.Services;

public class TokenBlacklistService : ITokenBlacklistService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<TokenBlacklistService> _logger;
    private readonly ConcurrentDictionary<string, BlacklistedToken> _blacklistedTokens;
    private readonly ConcurrentDictionary<string, HashSet<string>> _userTokens;
    private readonly Timer _cleanupTimer;

    public TokenBlacklistService(IMemoryCache cache, ILogger<TokenBlacklistService> logger)
    {
        _cache = cache;
        _logger = logger;
        _blacklistedTokens = new ConcurrentDictionary<string, BlacklistedToken>();
        _userTokens = new ConcurrentDictionary<string, HashSet<string>>();
        
        // Setup cleanup timer to run every hour
        _cleanupTimer = new Timer(async _ => await CleanupExpiredTokensAsync(), 
            null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
    }

    public async Task BlacklistTokenAsync(string tokenId, string userId, DateTime expiryTime, string reason = "logout")
    {
        try
        {
            var blacklistedToken = new BlacklistedToken
            {
                TokenId = tokenId,
                UserId = userId,
                BlacklistedAt = DateTime.UtcNow,
                ExpiryTime = expiryTime,
                Reason = reason
            };

            // Add to in-memory storage
            _blacklistedTokens.TryAdd(tokenId, blacklistedToken);

            // Track user tokens
            _userTokens.AddOrUpdate(userId, 
                new HashSet<string> { tokenId },
                (key, existing) => 
                {
                    existing.Add(tokenId);
                    return existing;
                });

            // Add to cache with expiration
            var cacheKey = GetCacheKey(tokenId);
            var cacheExpiry = expiryTime > DateTime.UtcNow ? expiryTime : DateTime.UtcNow.AddMinutes(5);
            _cache.Set(cacheKey, blacklistedToken, cacheExpiry);

            _logger.LogInformation("Token blacklisted: {TokenId} for user {UserId}, reason: {Reason}", 
                tokenId, userId, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error blacklisting token {TokenId} for user {UserId}", tokenId, userId);
            throw;
        }
    }

    public async Task<bool> IsTokenBlacklistedAsync(string tokenId)
    {
        try
        {
            // Check in-memory storage first
            if (_blacklistedTokens.ContainsKey(tokenId))
            {
                var token = _blacklistedTokens[tokenId];
                
                // Check if token has expired
                if (DateTime.UtcNow > token.ExpiryTime)
                {
                    // Remove expired token
                    _blacklistedTokens.TryRemove(tokenId, out _);
                    _cache.Remove(GetCacheKey(tokenId));
                    return false;
                }
                
                return true;
            }

            // Check cache as fallback
            var cacheKey = GetCacheKey(tokenId);
            var cachedToken = _cache.Get<BlacklistedToken>(cacheKey);
            
            if (cachedToken != null)
            {
                // Check if token has expired
                if (DateTime.UtcNow > cachedToken.ExpiryTime)
                {
                    _cache.Remove(cacheKey);
                    return false;
                }
                
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if token {TokenId} is blacklisted", tokenId);
            // In case of error, assume token is not blacklisted to avoid blocking valid requests
            return false;
        }
    }

    public async Task BlacklistAllUserTokensAsync(string userId, string reason = "security")
    {
        try
        {
            if (_userTokens.TryGetValue(userId, out var userTokens))
            {
                var tasks = userTokens.Select(tokenId =>
                {
                    if (_blacklistedTokens.TryGetValue(tokenId, out var existingToken))
                    {
                        return BlacklistTokenAsync(tokenId, userId, existingToken.ExpiryTime, reason);
                    }
                    
                    // If we don't have the token details, blacklist for a reasonable duration
                    var defaultExpiry = DateTime.UtcNow.AddDays(1);
                    return BlacklistTokenAsync(tokenId, userId, defaultExpiry, reason);
                });

                await Task.WhenAll(tasks);
            }

            _logger.LogInformation("All tokens blacklisted for user {UserId}, reason: {Reason}", userId, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error blacklisting all tokens for user {UserId}", userId);
            throw;
        }
    }

    public async Task CleanupExpiredTokensAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredTokens = _blacklistedTokens
                .Where(kvp => now > kvp.Value.ExpiryTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var tokenId in expiredTokens)
            {
                if (_blacklistedTokens.TryRemove(tokenId, out var removedToken))
                {
                    // Remove from user tokens tracking
                    if (_userTokens.TryGetValue(removedToken.UserId, out var userTokens))
                    {
                        userTokens.Remove(tokenId);
                        if (userTokens.Count == 0)
                        {
                            _userTokens.TryRemove(removedToken.UserId, out _);
                        }
                    }

                    // Remove from cache
                    _cache.Remove(GetCacheKey(tokenId));
                }
            }

            if (expiredTokens.Count > 0)
            {
                _logger.LogInformation("Cleaned up {Count} expired blacklisted tokens", expiredTokens.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token cleanup");
        }
    }

    public async Task<List<BlacklistedToken>> GetUserBlacklistedTokensAsync(string userId)
    {
        try
        {
            var userTokens = new List<BlacklistedToken>();

            if (_userTokens.TryGetValue(userId, out var tokenIds))
            {
                foreach (var tokenId in tokenIds)
                {
                    if (_blacklistedTokens.TryGetValue(tokenId, out var token))
                    {
                        // Only return non-expired tokens
                        if (DateTime.UtcNow <= token.ExpiryTime)
                        {
                            userTokens.Add(token);
                        }
                    }
                }
            }

            return userTokens;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting blacklisted tokens for user {UserId}", userId);
            return new List<BlacklistedToken>();
        }
    }

    private string GetCacheKey(string tokenId)
    {
        return $"blacklisted_token:{tokenId}";
    }

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
    }
}
