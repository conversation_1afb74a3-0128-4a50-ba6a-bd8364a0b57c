using Microsoft.Extensions.Logging;
using HRMS.Infrastructure.Data;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using HRMS.Core.Interfaces;

namespace HRMS.Infrastructure.Services;

public class DatabaseUpdateService : IDatabaseUpdateService
{
    private readonly ITenantDbContextFactory _contextFactory;
    private readonly ILogger<DatabaseUpdateService> _logger;

    public DatabaseUpdateService(ITenantDbContextFactory contextFactory, ILogger<DatabaseUpdateService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<bool> AddMissingEmployeeDetailColumnsAsync(string organizationId)
    {
        try
        {
            _logger.LogInformation("Adding missing EmployeeDetails columns for organization {OrganizationId}", organizationId);

            using var context = _contextFactory.CreateDbContext(organizationId);
            var connectionString = context.Database.GetConnectionString();

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            // First, ensure Roles table exists
            await EnsureRolesTableExistsAsync(connection);

            var columnsToAdd = new Dictionary<string, string>
            {
                { "EmergencyContactName", "NVARCHAR(100) NULL" },
                { "EmergencyContactPhone", "NVARCHAR(20) NULL" },
                { "EmergencyContactRelation", "NVARCHAR(100) NULL" },
                { "BloodGroup", "NVARCHAR(50) NULL" },
                { "MaritalStatus", "NVARCHAR(20) NULL" },
                { "Gender", "NVARCHAR(10) NULL" },
                { "Nationality", "NVARCHAR(50) NULL" },
                { "BankAccountNumber", "NVARCHAR(100) NULL" },
                { "BankIFSC", "NVARCHAR(20) NULL" },
                { "BankName", "NVARCHAR(100) NULL" },
                { "PAN", "NVARCHAR(50) NULL" },
                { "Aadhar", "NVARCHAR(20) NULL" },
                { "ProbationEndDate", "DATETIME2 NULL" },
                { "ConfirmationDate", "DATETIME2 NULL" },
                { "ResignationDate", "DATETIME2 NULL" },
                { "LastWorkingDate", "DATETIME2 NULL" },
                { "ResignationReason", "NVARCHAR(MAX) NULL" }
            };

            foreach (var column in columnsToAdd)
            {
                var checkColumnSql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'EmployeeDetails' 
                    AND COLUMN_NAME = @ColumnName";

                using var checkCmd = new SqlCommand(checkColumnSql, connection);
                checkCmd.Parameters.AddWithValue("@ColumnName", column.Key);
                
                var columnExists = (int)await checkCmd.ExecuteScalarAsync() > 0;

                if (!columnExists)
                {
                    var addColumnSql = $"ALTER TABLE EmployeeDetails ADD {column.Key} {column.Value}";
                    using var addCmd = new SqlCommand(addColumnSql, connection);
                    await addCmd.ExecuteNonQueryAsync();
                    
                    _logger.LogInformation("Added column {ColumnName} to EmployeeDetails table", column.Key);
                }
                else
                {
                    _logger.LogDebug("Column {ColumnName} already exists in EmployeeDetails table", column.Key);
                }
            }

            _logger.LogInformation("Successfully added missing EmployeeDetails columns for organization {OrganizationId}", organizationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add missing EmployeeDetails columns for organization {OrganizationId}", organizationId);
            return false;
        }
    }

    private async Task EnsureRolesTableExistsAsync(SqlConnection connection)
    {
        try
        {
            // Check if Roles table exists
            var checkTableSql = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = 'Roles'";

            using var checkCmd = new SqlCommand(checkTableSql, connection);
            var tableExists = (int)await checkCmd.ExecuteScalarAsync() > 0;

            if (!tableExists)
            {
                // Create Roles table
                var createTableSql = @"
                    CREATE TABLE [dbo].[Roles] (
                        [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
                        [Name] NVARCHAR(100) NOT NULL,
                        [Description] NVARCHAR(500) NULL,
                        [IsActive] BIT NOT NULL DEFAULT 1,
                        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                        [UpdatedAt] DATETIME2 NULL,
                        [CreatedBy] UNIQUEIDENTIFIER NULL,
                        [UpdatedBy] UNIQUEIDENTIFIER NULL
                    )";

                using var createCmd = new SqlCommand(createTableSql, connection);
                await createCmd.ExecuteNonQueryAsync();

                _logger.LogInformation("Created Roles table");

                // Insert default roles
                var insertRolesSql = @"
                    INSERT INTO [dbo].[Roles] ([Name], [Description]) VALUES
                    ('Employee', 'Standard employee role with basic permissions'),
                    ('Manager', 'Manager role with team management permissions'),
                    ('HR', 'Human Resources role with employee management permissions'),
                    ('Admin', 'Administrator role with full system permissions')";

                using var insertCmd = new SqlCommand(insertRolesSql, connection);
                await insertCmd.ExecuteNonQueryAsync();

                _logger.LogInformation("Inserted default roles");
            }
            else
            {
                _logger.LogDebug("Roles table already exists");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure Roles table exists");
            throw;
        }
    }

    public async Task<bool> AddLoginCredentialColumnsAsync(string organizationId)
    {
        try
        {
            _logger.LogInformation("Adding login credential columns for organization {OrganizationId}", organizationId);

            using var context = _contextFactory.CreateDbContext(organizationId);
            var connection = context.Database.GetDbConnection();
            await connection.OpenAsync();

            // Check if columns already exist
            var checkColumnsSql = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'EmployeeDetails'
                AND COLUMN_NAME IN ('LoginUsername', 'TemporaryPassword', 'RequirePasswordReset', 'PasswordGeneratedAt')";

            using var checkCmd = new SqlCommand(checkColumnsSql, (SqlConnection)connection);
            var existingColumns = (int)await checkCmd.ExecuteScalarAsync();

            if (existingColumns == 0)
            {
                // Add the new columns
                var addColumnsSql = @"
                    ALTER TABLE [dbo].[EmployeeDetails]
                    ADD [LoginUsername] NVARCHAR(255) NULL,
                        [TemporaryPassword] NVARCHAR(255) NULL,
                        [RequirePasswordReset] BIT NOT NULL DEFAULT 1,
                        [PasswordGeneratedAt] DATETIME2 NULL;

                    -- Add indexes for performance
                    CREATE INDEX IX_EmployeeDetails_LoginUsername ON [dbo].[EmployeeDetails] ([LoginUsername]);
                    CREATE INDEX IX_EmployeeDetails_RequirePasswordReset ON [dbo].[EmployeeDetails] ([RequirePasswordReset]);";

                using var addCmd = new SqlCommand(addColumnsSql, (SqlConnection)connection);
                await addCmd.ExecuteNonQueryAsync();

                _logger.LogInformation("Successfully added login credential columns");
                return true;
            }
            else
            {
                _logger.LogInformation("Login credential columns already exist");
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add login credential columns for organization {OrganizationId}", organizationId);
            return false;
        }
    }

    public async Task<bool> AddLoginCredentialColumnsToMasterDatabaseAsync()
    {
        try
        {
            _logger.LogInformation("Adding login credential columns to master database");

            using var context = _contextFactory.CreateMasterDbContext();
            var connection = context.Database.GetDbConnection();
            await connection.OpenAsync();

            // Check if columns already exist in master database
            var checkColumnsSql = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'EmployeeDetails'
                AND COLUMN_NAME IN ('LoginUsername', 'TemporaryPassword', 'RequirePasswordReset', 'PasswordGeneratedAt')";

            using var checkCmd = new SqlCommand(checkColumnsSql, (SqlConnection)connection);
            var existingColumns = (int)await checkCmd.ExecuteScalarAsync();

            if (existingColumns == 0)
            {
                // Add the new columns to master database
                var addColumnsSql = @"
                    ALTER TABLE [dbo].[EmployeeDetails]
                    ADD [LoginUsername] NVARCHAR(255) NULL,
                        [TemporaryPassword] NVARCHAR(255) NULL,
                        [RequirePasswordReset] BIT NOT NULL DEFAULT 1,
                        [PasswordGeneratedAt] DATETIME2 NULL;

                    -- Add indexes for performance
                    CREATE INDEX IX_EmployeeDetails_LoginUsername ON [dbo].[EmployeeDetails] ([LoginUsername]);
                    CREATE INDEX IX_EmployeeDetails_RequirePasswordReset ON [dbo].[EmployeeDetails] ([RequirePasswordReset]);";

                using var addCmd = new SqlCommand(addColumnsSql, (SqlConnection)connection);
                await addCmd.ExecuteNonQueryAsync();

                _logger.LogInformation("Successfully added login credential columns to master database");
                return true;
            }
            else
            {
                _logger.LogInformation("Login credential columns already exist in master database");
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add login credential columns to master database");
            return false;
        }
    }
}
