-- Make LeaveTypeId nullable in LeaveRequests table for simplified monthly leave system
-- This allows leave requests to exist without being tied to specific leave types

-- First, drop the foreign key constraint
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_LeaveRequests_LeaveTypes_LeaveTypeId')
BEGIN
    ALTER TABLE [dbo].[LeaveRequests] DROP CONSTRAINT [FK_LeaveRequests_LeaveTypes_LeaveTypeId];
    PRINT 'Dropped FK_LeaveRequests_LeaveTypes_LeaveTypeId constraint';
END

-- Make the LeaveTypeId column nullable
ALTER TABLE [dbo].[LeaveRequests] ALTER COLUMN [LeaveTypeId] uniqueidentifier NULL;
PRINT 'Made LeaveTypeId column nullable';

-- Re-add the foreign key constraint as optional
ALTER TABLE [dbo].[LeaveRequests] 
ADD CONSTRAINT [FK_LeaveRequests_LeaveTypes_LeaveTypeId] 
FOREIGN KEY ([LeaveTypeId]) REFERENCES [dbo].[LeaveTypes] ([Id]) ON DELETE SET NULL;
PRINT 'Re-added FK_LeaveRequests_LeaveTypes_LeaveTypeId constraint as optional';

PRINT 'LeaveTypeId column successfully made nullable for simplified monthly leave system';
