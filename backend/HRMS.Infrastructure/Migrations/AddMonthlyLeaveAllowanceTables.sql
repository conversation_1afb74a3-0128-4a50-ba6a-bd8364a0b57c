-- Add Monthly Leave Allowance Tables
-- This script adds the new monthly leave allowance system tables

-- Create MonthlyLeaveAllowances table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MonthlyLeaveAllowances' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    CREATE TABLE [dbo].[MonthlyLeaveAllowances] (
        [Id] uniqueidentifier NOT NULL,
        [UserId] uniqueidentifier NOT NULL,
        [Year] int NOT NULL,
        [Month] int NOT NULL,
        [BaseAllowance] int NOT NULL DEFAULT 1,
        [CarriedForward] int NOT NULL DEFAULT 0,
        [UsedLeaves] int NOT NULL DEFAULT 0,
        [IsProcessed] bit NOT NULL DEFAULT 0,
        [CreatedAt] datetime2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] datetime2 NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] uniqueidentifier NULL,
        [UpdatedBy] uniqueidentifier NULL,
        [IsDeleted] bit NOT NULL DEFAULT 0,
        [DeletedAt] datetime2 NULL,
        [DeletedBy] uniqueidentifier NULL,
        CONSTRAINT [PK_MonthlyLeaveAllowances] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_MonthlyLeaveAllowances_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [CK_MonthlyLeaveAllowance_Month] CHECK ([Month] >= 1 AND [Month] <= 12),
        CONSTRAINT [CK_MonthlyLeaveAllowance_Year] CHECK ([Year] >= 2020 AND [Year] <= 2100)
    );

    -- Create unique index for user, year, month combination
    CREATE UNIQUE INDEX [IX_MonthlyLeaveAllowances_UserId_Year_Month] ON [dbo].[MonthlyLeaveAllowances] ([UserId], [Year], [Month]);
END

-- Create MonthlyLeaveUsages table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MonthlyLeaveUsages' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    CREATE TABLE [dbo].[MonthlyLeaveUsages] (
        [Id] uniqueidentifier NOT NULL,
        [LeaveRequestId] uniqueidentifier NOT NULL,
        [MonthlyLeaveAllowanceId] uniqueidentifier NOT NULL,
        [DaysUsed] int NOT NULL,
        [UsageDate] datetime2 NOT NULL,
        [Status] nvarchar(max) NOT NULL DEFAULT 'Active',
        [CreatedAt] datetime2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] datetime2 NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] uniqueidentifier NULL,
        [UpdatedBy] uniqueidentifier NULL,
        [IsDeleted] bit NOT NULL DEFAULT 0,
        [DeletedAt] datetime2 NULL,
        [DeletedBy] uniqueidentifier NULL,
        CONSTRAINT [PK_MonthlyLeaveUsages] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_MonthlyLeaveUsages_LeaveRequests_LeaveRequestId] FOREIGN KEY ([LeaveRequestId]) REFERENCES [dbo].[LeaveRequests] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MonthlyLeaveUsages_MonthlyLeaveAllowances_MonthlyLeaveAllowanceId] FOREIGN KEY ([MonthlyLeaveAllowanceId]) REFERENCES [dbo].[MonthlyLeaveAllowances] ([Id]) ON DELETE CASCADE
    );

    -- Create unique index for leave request (one usage record per leave request)
    CREATE UNIQUE INDEX [IX_MonthlyLeaveUsages_LeaveRequestId] ON [dbo].[MonthlyLeaveUsages] ([LeaveRequestId]);
    
    -- Create index for monthly leave allowance
    CREATE INDEX [IX_MonthlyLeaveUsages_MonthlyLeaveAllowanceId] ON [dbo].[MonthlyLeaveUsages] ([MonthlyLeaveAllowanceId]);
END

PRINT 'Monthly Leave Allowance tables created successfully';
