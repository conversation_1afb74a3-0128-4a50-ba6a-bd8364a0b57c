-- Migration: Add Login Credential Columns to EmployeeDetails
-- Date: 2024-12-11
-- Description: Add columns for automatic login credential generation

-- Add new columns to EmployeeDetails table
ALTER TABLE [dbo].[EmployeeDetails] 
ADD [LoginUsername] NVARCHAR(255) NULL,
    [TemporaryPassword] NVARCHAR(255) NULL,
    [RequirePasswordReset] BIT NOT NULL DEFAULT 1,
    [PasswordGeneratedAt] DATETIME2 NULL;

-- Add indexes for performance
CREATE INDEX IX_EmployeeDetails_LoginUsername ON [dbo].[EmployeeDetails] ([LoginUsername]);
CREATE INDEX IX_EmployeeDetails_RequirePasswordReset ON [dbo].[EmployeeDetails] ([RequirePasswordReset]);

-- Add comments for documentation
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Username/email for employee login credentials', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'EmployeeDetails', 
    @level2type = N'COLUMN', @level2name = N'LoginUsername';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Temporary password for first-time login (hashed)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'EmployeeDetails', 
    @level2type = N'COLUMN', @level2name = N'TemporaryPassword';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Flag indicating if password reset is required on first login', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'EmployeeDetails', 
    @level2type = N'COLUMN', @level2name = N'RequirePasswordReset';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Timestamp when the temporary password was generated', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'EmployeeDetails', 
    @level2type = N'COLUMN', @level2name = N'PasswordGeneratedAt';

PRINT 'Successfully added login credential columns to EmployeeDetails table';
