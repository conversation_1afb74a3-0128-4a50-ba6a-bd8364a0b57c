-- Migration: Remove Leave Balance and Monthly Allowance Tables
-- Description: Remove balance-related tables while preserving core leave management functionality
-- Date: 2024-01-XX
-- Author: System Migration

-- This migration removes the leave balance functionality while preserving:
-- 1. leave_requests table (core functionality)
-- 2. leave_types table (core functionality)
-- 3. All foreign key relationships for leave requests

BEGIN TRANSACTION;

PRINT 'Starting removal of leave balance tables...';

-- Step 1: Drop foreign key constraints that reference tables we're removing
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_MonthlyLeaveUsages_LeaveRequests_LeaveRequestId')
BEGIN
    ALTER TABLE [dbo].[MonthlyLeaveUsages] DROP CONSTRAINT [FK_MonthlyLeaveUsages_LeaveRequests_LeaveRequestId];
    PRINT 'Dropped FK_MonthlyLeaveUsages_LeaveRequests_LeaveRequestId';
END

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_MonthlyLeaveUsages_MonthlyLeaveAllowances_MonthlyLeaveAllowanceId')
BEGIN
    ALTER TABLE [dbo].[MonthlyLeaveUsages] DROP CONSTRAINT [FK_MonthlyLeaveUsages_MonthlyLeaveAllowances_MonthlyLeaveAllowanceId];
    PRINT 'Dropped FK_MonthlyLeaveUsages_MonthlyLeaveAllowances_MonthlyLeaveAllowanceId';
END

-- Step 2: Drop indexes
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MonthlyLeaveUsages_LeaveRequestId')
BEGIN
    DROP INDEX [IX_MonthlyLeaveUsages_LeaveRequestId] ON [dbo].[MonthlyLeaveUsages];
    PRINT 'Dropped IX_MonthlyLeaveUsages_LeaveRequestId';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MonthlyLeaveUsages_MonthlyLeaveAllowanceId')
BEGIN
    DROP INDEX [IX_MonthlyLeaveUsages_MonthlyLeaveAllowanceId] ON [dbo].[MonthlyLeaveUsages];
    PRINT 'Dropped IX_MonthlyLeaveUsages_MonthlyLeaveAllowanceId';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MonthlyLeaveAllowances_UserId_Year_Month')
BEGIN
    DROP INDEX [IX_MonthlyLeaveAllowances_UserId_Year_Month] ON [dbo].[MonthlyLeaveAllowances];
    PRINT 'Dropped IX_MonthlyLeaveAllowances_UserId_Year_Month';
END

-- Step 3: Drop tables (order matters due to foreign keys)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MonthlyLeaveUsages' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    DROP TABLE [dbo].[MonthlyLeaveUsages];
    PRINT 'Dropped MonthlyLeaveUsages table';
END

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MonthlyLeaveAllowances' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    DROP TABLE [dbo].[MonthlyLeaveAllowances];
    PRINT 'Dropped MonthlyLeaveAllowances table';
END

-- Step 4: Drop leave_balances table (if it exists)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'LeaveBalances' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    DROP TABLE [dbo].[LeaveBalances];
    PRINT 'Dropped LeaveBalances table';
END

-- Step 5: Verify core tables still exist
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'LeaveRequests' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    RAISERROR('ERROR: LeaveRequests table is missing! This table is required for core functionality.', 16, 1);
    ROLLBACK TRANSACTION;
    RETURN;
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'LeaveTypes' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    RAISERROR('ERROR: LeaveTypes table is missing! This table is required for core functionality.', 16, 1);
    ROLLBACK TRANSACTION;
    RETURN;
END

-- Step 6: Add comment to document the change
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Leave balance functionality removed. Only core leave request/approval workflow remains.', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'LeaveRequests';

PRINT 'Leave balance tables removed successfully!';
PRINT 'Core leave management functionality preserved:';
PRINT '- LeaveRequests table: ✓ Available';
PRINT '- LeaveTypes table: ✓ Available';
PRINT '- Leave application/approval workflow: ✓ Functional';
PRINT '';
PRINT 'Removed functionality:';
PRINT '- Leave balance tracking: ✗ Removed';
PRINT '- Monthly allowance system: ✗ Removed';
PRINT '- Balance validation: ✗ Removed';

COMMIT TRANSACTION;
