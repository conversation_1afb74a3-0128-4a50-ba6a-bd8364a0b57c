using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;
using System.Text;

namespace DataMigration
{
    public class DataMigrationService
    {
        private readonly ILogger<DataMigrationService> _logger;
        private const string SqliteConnectionString = "Data Source=../HRMS.API/hrms_dev.db";
        private const string SqlServerConnectionString = "Server=103.145.50.203,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;";

        public DataMigrationService(ILogger<DataMigrationService> logger)
        {
            _logger = logger;
        }

        public async Task<bool> MigrateAllDataAsync()
        {
            try
            {
                _logger.LogInformation("Starting data migration from SQLite to SQL Server...");

                // Migrate in order of dependencies
                await MigrateOrganizationsAsync();
                await MigrateUsersAsync();
                await MigrateEmployeeDetailsAsync();
                await MigrateLeaveTypesAsync();
                await MigrateOrganizationSettingsAsync();
                await MigrateAttendanceRecordsAsync();
                await MigrateLeaveRequestsAsync();
                await MigrateTasksAsync();
                await MigratePerformanceReviewsAsync();
                await MigrateJobPostingsAsync();

                _logger.LogInformation("Data migration completed successfully!");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Data migration failed");
                return false;
            }
        }

        private async Task MigrateOrganizationsAsync()
        {
            _logger.LogInformation("Migrating Organizations...");
            
            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);
            
            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM Organizations", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO Organizations (Id, Name, Domain, Industry, EmployeeCount, Status, SubscriptionPlan, MonthlyRevenue, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @Name, @Domain, @Industry, @EmployeeCount, @Status, @SubscriptionPlan, @MonthlyRevenue, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);
                
                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@Name", reader["Name"]);
                insertCmd.Parameters.AddWithValue("@Domain", reader["Domain"]);
                insertCmd.Parameters.AddWithValue("@Industry", reader["Industry"]);
                insertCmd.Parameters.AddWithValue("@EmployeeCount", reader["EmployeeCount"]);
                insertCmd.Parameters.AddWithValue("@Status", reader["Status"]);
                insertCmd.Parameters.AddWithValue("@SubscriptionPlan", reader["SubscriptionPlan"]);
                insertCmd.Parameters.AddWithValue("@MonthlyRevenue", reader["MonthlyRevenue"]);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Organizations migration completed");
        }

        private async Task MigrateUsersAsync()
        {
            _logger.LogInformation("Migrating Users...");
            
            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);
            
            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM Users", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO Users (Id, Email, PasswordHash, Name, Role, OrganizationId, AvatarUrl, IsActive, EmailVerified, LastLogin, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @Email, @PasswordHash, @Name, @Role, @OrganizationId, @AvatarUrl, @IsActive, @EmailVerified, @LastLogin, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);
                
                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@Email", reader["Email"]);
                insertCmd.Parameters.AddWithValue("@PasswordHash", reader["PasswordHash"]);
                insertCmd.Parameters.AddWithValue("@Name", reader["Name"]);
                insertCmd.Parameters.AddWithValue("@Role", reader["Role"]);
                insertCmd.Parameters.AddWithValue("@OrganizationId", reader["OrganizationId"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["OrganizationId"].ToString()));
                insertCmd.Parameters.AddWithValue("@AvatarUrl", reader["AvatarUrl"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@IsActive", Convert.ToBoolean(reader["IsActive"]));
                insertCmd.Parameters.AddWithValue("@EmailVerified", Convert.ToBoolean(reader["EmailVerified"]));
                insertCmd.Parameters.AddWithValue("@LastLogin", reader["LastLogin"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["LastLogin"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Users migration completed");
        }

        private async Task MigrateEmployeeDetailsAsync()
        {
            _logger.LogInformation("Migrating Employee Details...");
            
            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);
            
            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM EmployeeDetails", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO EmployeeDetails (Id, UserId, EmployeeId, JobTitle, Department, ManagerId, JoinDate, EmploymentType, EmploymentStatus, WorkLocation, Phone, Address, DateOfBirth, BaseSalary, AnnualCTC, NextSalaryReview, PerformanceRating, TotalExperience, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @UserId, @EmployeeId, @JobTitle, @Department, @ManagerId, @JoinDate, @EmploymentType, @EmploymentStatus, @WorkLocation, @Phone, @Address, @DateOfBirth, @BaseSalary, @AnnualCTC, @NextSalaryReview, @PerformanceRating, @TotalExperience, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);
                
                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@UserId", Guid.Parse(reader["UserId"].ToString()));
                insertCmd.Parameters.AddWithValue("@EmployeeId", reader["EmployeeId"]);
                insertCmd.Parameters.AddWithValue("@JobTitle", reader["JobTitle"]);
                insertCmd.Parameters.AddWithValue("@Department", reader["Department"]);
                insertCmd.Parameters.AddWithValue("@ManagerId", reader["ManagerId"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["ManagerId"].ToString()));
                insertCmd.Parameters.AddWithValue("@JoinDate", DateTime.Parse(reader["JoinDate"].ToString()));
                insertCmd.Parameters.AddWithValue("@EmploymentType", reader["EmploymentType"]);
                insertCmd.Parameters.AddWithValue("@EmploymentStatus", reader["EmploymentStatus"]);
                insertCmd.Parameters.AddWithValue("@WorkLocation", reader["WorkLocation"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Phone", reader["Phone"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Address", reader["Address"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@DateOfBirth", reader["DateOfBirth"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DateOfBirth"].ToString()));
                insertCmd.Parameters.AddWithValue("@BaseSalary", reader["BaseSalary"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@AnnualCTC", reader["AnnualCTC"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@NextSalaryReview", reader["NextSalaryReview"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["NextSalaryReview"].ToString()));
                insertCmd.Parameters.AddWithValue("@PerformanceRating", reader["PerformanceRating"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@TotalExperience", reader["TotalExperience"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Employee Details migration completed");
        }

        private async Task MigrateLeaveTypesAsync()
        {
            _logger.LogInformation("Migrating Leave Types...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM LeaveTypes", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO LeaveTypes (Id, OrganizationId, Name, Description, MaxDaysPerYear, IsCarryForward, MaxCarryForwardDays, IsActive, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @OrganizationId, @Name, @Description, @MaxDaysPerYear, @IsCarryForward, @MaxCarryForwardDays, @IsActive, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@OrganizationId", Guid.Parse(reader["OrganizationId"].ToString()));
                insertCmd.Parameters.AddWithValue("@Name", reader["Name"]);
                insertCmd.Parameters.AddWithValue("@Description", reader["Description"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@MaxDaysPerYear", reader["MaxDaysPerYear"]);
                insertCmd.Parameters.AddWithValue("@IsCarryForward", Convert.ToBoolean(reader["IsCarryForward"]));
                insertCmd.Parameters.AddWithValue("@MaxCarryForwardDays", reader["MaxCarryForwardDays"]);
                insertCmd.Parameters.AddWithValue("@IsActive", Convert.ToBoolean(reader["IsActive"]));
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Leave Types migration completed");
        }

        private async Task MigrateOrganizationSettingsAsync()
        {
            _logger.LogInformation("Migrating Organization Settings...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM OrganizationSettings", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO OrganizationSettings (Id, OrganizationId, [Key], Value, Description, DataType, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @OrganizationId, @Key, @Value, @Description, @DataType, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@OrganizationId", Guid.Parse(reader["OrganizationId"].ToString()));
                insertCmd.Parameters.AddWithValue("@Key", reader["Key"]);
                insertCmd.Parameters.AddWithValue("@Value", reader["Value"]);
                insertCmd.Parameters.AddWithValue("@Description", reader["Description"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@DataType", reader["DataType"]);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Organization Settings migration completed");
        }

        private async Task MigrateAttendanceRecordsAsync()
        {
            _logger.LogInformation("Migrating Attendance Records...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM AttendanceRecords", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO AttendanceRecords (Id, UserId, Date, CheckInTime, CheckOutTime, TotalHours, Status, Location, Notes, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @UserId, @Date, @CheckInTime, @CheckOutTime, @TotalHours, @Status, @Location, @Notes, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@UserId", Guid.Parse(reader["UserId"].ToString()));
                insertCmd.Parameters.AddWithValue("@Date", DateTime.Parse(reader["Date"].ToString()));
                insertCmd.Parameters.AddWithValue("@CheckInTime", reader["CheckInTime"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["CheckInTime"].ToString()));
                insertCmd.Parameters.AddWithValue("@CheckOutTime", reader["CheckOutTime"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["CheckOutTime"].ToString()));
                insertCmd.Parameters.AddWithValue("@TotalHours", reader["TotalHours"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Status", reader["Status"]);
                insertCmd.Parameters.AddWithValue("@Location", reader["Location"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Notes", reader["Notes"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Attendance Records migration completed");
        }

        private async Task MigrateLeaveRequestsAsync()
        {
            _logger.LogInformation("Migrating Leave Requests...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM LeaveRequests", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO LeaveRequests (Id, UserId, LeaveTypeId, FromDate, ToDate, TotalDays, DurationType, Reason, Status, ApprovedBy, ApprovedAt, Comments, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @UserId, @LeaveTypeId, @FromDate, @ToDate, @TotalDays, @DurationType, @Reason, @Status, @ApprovedBy, @ApprovedAt, @Comments, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@UserId", Guid.Parse(reader["UserId"].ToString()));
                insertCmd.Parameters.AddWithValue("@LeaveTypeId", Guid.Parse(reader["LeaveTypeId"].ToString()));
                insertCmd.Parameters.AddWithValue("@FromDate", DateTime.Parse(reader["FromDate"].ToString()));
                insertCmd.Parameters.AddWithValue("@ToDate", DateTime.Parse(reader["ToDate"].ToString()));
                insertCmd.Parameters.AddWithValue("@TotalDays", reader["TotalDays"]);
                insertCmd.Parameters.AddWithValue("@DurationType", reader["DurationType"]);
                insertCmd.Parameters.AddWithValue("@Reason", reader["Reason"]);
                insertCmd.Parameters.AddWithValue("@Status", reader["Status"]);
                insertCmd.Parameters.AddWithValue("@ApprovedBy", reader["ApprovedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["ApprovedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@ApprovedAt", reader["ApprovedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["ApprovedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@Comments", reader["Comments"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Leave Requests migration completed");
        }

        private async Task MigrateTasksAsync()
        {
            _logger.LogInformation("Migrating Tasks...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM Tasks", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO Tasks (Id, Title, Description, Priority, Status, ProgressPercentage, AssignedToId, AssignedById, DueDate, EstimatedHours, ActualHours, Category, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @Title, @Description, @Priority, @Status, @ProgressPercentage, @AssignedToId, @AssignedById, @DueDate, @EstimatedHours, @ActualHours, @Category, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@Title", reader["Title"]);
                insertCmd.Parameters.AddWithValue("@Description", reader["Description"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Priority", reader["Priority"]);
                insertCmd.Parameters.AddWithValue("@Status", reader["Status"]);
                insertCmd.Parameters.AddWithValue("@ProgressPercentage", reader["ProgressPercentage"]);
                insertCmd.Parameters.AddWithValue("@AssignedToId", Guid.Parse(reader["AssignedToId"].ToString()));
                insertCmd.Parameters.AddWithValue("@AssignedById", Guid.Parse(reader["AssignedById"].ToString()));
                insertCmd.Parameters.AddWithValue("@DueDate", reader["DueDate"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DueDate"].ToString()));
                insertCmd.Parameters.AddWithValue("@EstimatedHours", reader["EstimatedHours"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@ActualHours", reader["ActualHours"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Category", reader["Category"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Tasks migration completed");
        }

        private async Task MigratePerformanceReviewsAsync()
        {
            _logger.LogInformation("Migrating Performance Reviews...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM PerformanceReviews", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO PerformanceReviews (Id, EmployeeId, ReviewerId, ReviewPeriod, ReviewType, OverallRating, Status, ReviewDate, Feedback, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @EmployeeId, @ReviewerId, @ReviewPeriod, @ReviewType, @OverallRating, @Status, @ReviewDate, @Feedback, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@EmployeeId", Guid.Parse(reader["EmployeeId"].ToString()));
                insertCmd.Parameters.AddWithValue("@ReviewerId", Guid.Parse(reader["ReviewerId"].ToString()));
                insertCmd.Parameters.AddWithValue("@ReviewPeriod", reader["ReviewPeriod"]);
                insertCmd.Parameters.AddWithValue("@ReviewType", reader["ReviewType"]);
                insertCmd.Parameters.AddWithValue("@OverallRating", reader["OverallRating"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Status", reader["Status"]);
                insertCmd.Parameters.AddWithValue("@ReviewDate", DateTime.Parse(reader["ReviewDate"].ToString()));
                insertCmd.Parameters.AddWithValue("@Feedback", reader["Feedback"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Performance Reviews migration completed");
        }

        private async Task MigrateJobPostingsAsync()
        {
            _logger.LogInformation("Migrating Job Postings...");

            using var sqliteConn = new SqliteConnection(SqliteConnectionString);
            using var sqlServerConn = new SqlConnection(SqlServerConnectionString);

            await sqliteConn.OpenAsync();
            await sqlServerConn.OpenAsync();

            var selectCmd = new SqliteCommand("SELECT * FROM JobPostings", sqliteConn);
            using var reader = await selectCmd.ExecuteReaderAsync();

            var insertSql = @"
                INSERT INTO JobPostings (Id, Title, Department, Description, Requirements, Location, EmploymentType, ExperienceLevel, SalaryRangeMin, SalaryRangeMax, Status, ApplicationDeadline, PostedById, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy)
                VALUES (@Id, @Title, @Department, @Description, @Requirements, @Location, @EmploymentType, @ExperienceLevel, @SalaryRangeMin, @SalaryRangeMax, @Status, @ApplicationDeadline, @PostedById, @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted, @DeletedAt, @DeletedBy)";

            while (await reader.ReadAsync())
            {
                using var insertCmd = new SqlCommand(insertSql, sqlServerConn);

                insertCmd.Parameters.AddWithValue("@Id", Guid.Parse(reader["Id"].ToString()));
                insertCmd.Parameters.AddWithValue("@Title", reader["Title"]);
                insertCmd.Parameters.AddWithValue("@Department", reader["Department"]);
                insertCmd.Parameters.AddWithValue("@Description", reader["Description"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Requirements", reader["Requirements"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Location", reader["Location"]);
                insertCmd.Parameters.AddWithValue("@EmploymentType", reader["EmploymentType"]);
                insertCmd.Parameters.AddWithValue("@ExperienceLevel", reader["ExperienceLevel"]);
                insertCmd.Parameters.AddWithValue("@SalaryRangeMin", reader["SalaryRangeMin"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@SalaryRangeMax", reader["SalaryRangeMax"] ?? (object)DBNull.Value);
                insertCmd.Parameters.AddWithValue("@Status", reader["Status"]);
                insertCmd.Parameters.AddWithValue("@ApplicationDeadline", reader["ApplicationDeadline"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["ApplicationDeadline"].ToString()));
                insertCmd.Parameters.AddWithValue("@PostedById", Guid.Parse(reader["PostedById"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedAt", DateTime.Parse(reader["CreatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Parse(reader["UpdatedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@CreatedBy", reader["CreatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["CreatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@UpdatedBy", reader["UpdatedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["UpdatedBy"].ToString()));
                insertCmd.Parameters.AddWithValue("@IsDeleted", Convert.ToBoolean(reader["IsDeleted"]));
                insertCmd.Parameters.AddWithValue("@DeletedAt", reader["DeletedAt"] == DBNull.Value ? (object)DBNull.Value : DateTime.Parse(reader["DeletedAt"].ToString()));
                insertCmd.Parameters.AddWithValue("@DeletedBy", reader["DeletedBy"] == DBNull.Value ? (object)DBNull.Value : Guid.Parse(reader["DeletedBy"].ToString()));

                await insertCmd.ExecuteNonQueryAsync();
            }

            _logger.LogInformation("Job Postings migration completed");
        }
    }
}
