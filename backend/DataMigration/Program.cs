using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace DataMigration
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("HRMS Data Migration Tool");
            Console.WriteLine("========================");
            Console.WriteLine("This tool will migrate data from SQLite to SQL Server");
            Console.WriteLine();

            // Setup dependency injection
            var services = new ServiceCollection();
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });
            services.AddTransient<DataMigrationService>();

            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            var migrationService = serviceProvider.GetRequiredService<DataMigrationService>();

            try
            {
                logger.LogInformation("Starting HRMS data migration...");
                
                Console.WriteLine("⚠️  WARNING: This will migrate data from SQLite to SQL Server.");
                Console.WriteLine("Make sure you have backed up your data before proceeding.");
                Console.WriteLine();
                Console.Write("Do you want to continue? (y/N): ");
                
                var response = Console.ReadLine();
                if (response?.ToLower() != "y" && response?.ToLower() != "yes")
                {
                    Console.WriteLine("Migration cancelled by user.");
                    return;
                }

                Console.WriteLine();
                logger.LogInformation("User confirmed migration. Starting process...");

                var success = await migrationService.MigrateAllDataAsync();

                if (success)
                {
                    Console.WriteLine();
                    Console.WriteLine("✅ Data migration completed successfully!");
                    Console.WriteLine("All data has been transferred from SQLite to SQL Server.");
                    logger.LogInformation("Data migration completed successfully");
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("❌ Data migration failed!");
                    Console.WriteLine("Please check the logs for more details.");
                    logger.LogError("Data migration failed");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An unexpected error occurred during migration");
                Console.WriteLine($"❌ An unexpected error occurred: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
