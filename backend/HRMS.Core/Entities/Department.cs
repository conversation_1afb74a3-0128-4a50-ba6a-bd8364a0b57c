using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;

namespace HRMS.Core.Entities;

public class Department : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? Description { get; set; }
    
    public Guid? ParentDepartmentId { get; set; }
    
    public Guid? HeadOfDepartmentId { get; set; } // User ID of department head
    
    [MaxLength(255)]
    public string? Location { get; set; }
    
    public decimal? Budget { get; set; }
    
    public int EmployeeCount { get; set; } = 0;
    
    public bool IsActive { get; set; } = true;
    
    public int Level { get; set; } = 1; // Hierarchy level (1 = top level)
    
    [MaxLength(500)]
    public string? HierarchyPath { get; set; } // e.g., "Engineering/Backend/API Team"
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
    public virtual Department? ParentDepartment { get; set; }
    public virtual User? HeadOfDepartment { get; set; }
    public virtual ICollection<Department> SubDepartments { get; set; } = new List<Department>();
    public virtual ICollection<DepartmentEmployee> DepartmentEmployees { get; set; } = new List<DepartmentEmployee>();
}

public class DepartmentEmployee : BaseAuditableEntity
{
    public Guid DepartmentId { get; set; }
    
    public Guid EmployeeDetailId { get; set; }
    
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? TransferDate { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsPrimary { get; set; } = true; // Primary department assignment
    
    [MaxLength(255)]
    public string? TransferReason { get; set; }
    
    // Navigation properties
    public virtual Department Department { get; set; } = null!;
    public virtual EmployeeDetail EmployeeDetail { get; set; } = null!;
}

public class Position : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    public Guid DepartmentId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? Description { get; set; }
    
    public int Level { get; set; } = 1; // Position level in hierarchy
    
    public decimal? MinSalary { get; set; }
    
    public decimal? MaxSalary { get; set; }
    
    public string? RequiredSkills { get; set; } // JSON array of skills
    
    public string? Responsibilities { get; set; }
    
    public int MaxPositions { get; set; } = 1; // Maximum number of employees for this position
    
    public int CurrentPositions { get; set; } = 0; // Current number of employees
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
    public virtual Department Department { get; set; } = null!;
    public virtual ICollection<EmployeePosition> EmployeePositions { get; set; } = new List<EmployeePosition>();
}

public class EmployeePosition : BaseAuditableEntity
{
    public Guid EmployeeDetailId { get; set; }
    
    public Guid PositionId { get; set; }
    
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? EndDate { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsPrimary { get; set; } = true; // Primary position
    
    [MaxLength(255)]
    public string? AssignmentReason { get; set; }
    
    // Navigation properties
    public virtual EmployeeDetail EmployeeDetail { get; set; } = null!;
    public virtual Position Position { get; set; } = null!;
}
