using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class Task : BaseAuditableEntity
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;

    public string? Description { get; set; }

    public TaskPriority Priority { get; set; } = TaskPriority.Medium;

    public HRMS.Core.Enums.TaskStatus Status { get; set; } = HRMS.Core.Enums.TaskStatus.Pending;

    public int ProgressPercentage { get; set; } = 0;

    public Guid AssignedToId { get; set; }

    public Guid AssignedById { get; set; }

    public DateTime? DueDate { get; set; }

    public decimal? EstimatedHours { get; set; }

    public decimal? ActualHours { get; set; }

    [MaxLength(100)]
    public string? Category { get; set; }

    // New field to distinguish between admin-assigned and employee self-created tasks
    public bool IsSelfCreated { get; set; } = false;

    // Task type to categorize different kinds of tasks
    public TaskType TaskType { get; set; } = TaskType.Regular;

    // Navigation properties
    public virtual User AssignedTo { get; set; } = null!;
    public virtual User AssignedBy { get; set; } = null!;
    public virtual ICollection<TaskUpdate> TaskUpdates { get; set; } = new List<TaskUpdate>();
    public virtual ICollection<TaskComment> TaskComments { get; set; } = new List<TaskComment>();
}

public class TaskUpdate : BaseAuditableEntity
{
    public Guid TaskId { get; set; }

    public Guid UpdatedById { get; set; }

    public int ProgressPercentage { get; set; }

    public HRMS.Core.Enums.TaskStatus Status { get; set; }

    public decimal? HoursSpent { get; set; }

    [Required]
    public string UpdateNotes { get; set; } = string.Empty;

    public string? Blockers { get; set; }

    public string? NextSteps { get; set; }

    // Date of the update (for daily updates tracking)
    public DateTime UpdateDate { get; set; }

    // Type of update (daily, milestone, completion, etc.)
    public TaskUpdateType UpdateType { get; set; } = TaskUpdateType.Daily;

    // Navigation properties
    public virtual Task Task { get; set; } = null!;
    public virtual User UpdatedBy { get; set; } = null!;
}

public class TaskComment : BaseAuditableEntity
{
    public Guid TaskId { get; set; }
    
    public Guid AuthorId { get; set; }
    
    [Required]
    public string Comment { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual Task Task { get; set; } = null!;
    public virtual User Author { get; set; } = null!;
}
