using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class JobPosting : BaseAuditableEntity
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Department { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public string? Requirements { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Location { get; set; } = string.Empty;
    
    [Required]
    public EmploymentType EmploymentType { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string ExperienceLevel { get; set; } = string.Empty;
    
    public decimal? SalaryRangeMin { get; set; }
    
    public decimal? SalaryRangeMax { get; set; }
    
    public JobStatus Status { get; set; } = JobStatus.Draft;
    
    public DateTime? ApplicationDeadline { get; set; }
    
    public Guid PostedById { get; set; }
    
    // Navigation properties
    public virtual User PostedBy { get; set; } = null!;
    public virtual ICollection<JobApplication> Applications { get; set; } = new List<JobApplication>();
}

public class JobApplication : BaseAuditableEntity
{
    public Guid JobPostingId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string CandidateName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    [EmailAddress]
    public string CandidateEmail { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string? CandidatePhone { get; set; }
    
    public int? ExperienceYears { get; set; }
    
    public decimal? CurrentSalary { get; set; }
    
    public decimal? ExpectedSalary { get; set; }
    
    public ApplicationStatus Status { get; set; } = ApplicationStatus.Applied;
    
    [MaxLength(500)]
    public string? ResumeUrl { get; set; }
    
    public string? CoverLetter { get; set; }
    
    // Navigation properties
    public virtual JobPosting JobPosting { get; set; } = null!;
}
