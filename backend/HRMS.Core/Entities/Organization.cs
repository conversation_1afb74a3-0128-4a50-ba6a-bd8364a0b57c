using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class Organization : BaseAuditableEntity
{
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string Domain { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Industry { get; set; } = string.Empty;
    
    public int EmployeeCount { get; set; } = 0;
    
    public OrganizationStatus Status { get; set; } = OrganizationStatus.Pending;
    
    [MaxLength(50)]
    public string SubscriptionPlan { get; set; } = "standard";
    
    public decimal MonthlyRevenue { get; set; } = 0;
    
    // Navigation properties
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<OrganizationSetting> Settings { get; set; } = new List<OrganizationSetting>();
}
