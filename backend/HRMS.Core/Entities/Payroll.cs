using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class PayrollCycle : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string CycleName { get; set; } = string.Empty;
    
    public DateTime PayPeriodStart { get; set; }
    
    public DateTime PayPeriodEnd { get; set; }
    
    public DateTime PayDate { get; set; }
    
    public PayrollStatus Status { get; set; } = PayrollStatus.Draft;
    
    public decimal TotalGrossAmount { get; set; } = 0;
    
    public decimal TotalNetAmount { get; set; } = 0;
    
    public decimal TotalDeductions { get; set; } = 0;
    
    public Guid? ProcessedBy { get; set; }
    
    public DateTime? ProcessedAt { get; set; }
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
    public virtual User? ProcessedByUser { get; set; }
    public virtual ICollection<EmployeePayroll> EmployeePayrolls { get; set; } = new List<EmployeePayroll>();
}

public class EmployeePayroll : BaseAuditableEntity
{
    public Guid PayrollCycleId { get; set; }
    
    public Guid EmployeeId { get; set; }
    
    public decimal GrossSalary { get; set; }
    
    public decimal BasicSalary { get; set; }
    
    public decimal Allowances { get; set; } = 0;
    
    public decimal OvertimeAmount { get; set; } = 0;
    
    public decimal BonusAmount { get; set; } = 0;
    
    public decimal TotalDeductions { get; set; } = 0;
    
    public decimal TaxDeductions { get; set; } = 0;
    
    public decimal InsuranceDeductions { get; set; } = 0;
    
    public decimal ProvidentFund { get; set; } = 0;
    
    public decimal OtherDeductions { get; set; } = 0;
    
    public decimal NetSalary { get; set; }
    
    public PayrollStatus Status { get; set; } = PayrollStatus.Draft;
    
    public int WorkingDays { get; set; } = 0;
    
    public int PresentDays { get; set; } = 0;
    
    public decimal LeaveDays { get; set; } = 0;
    
    public decimal OvertimeHours { get; set; } = 0;
    
    public string? PayslipPath { get; set; }
    
    public DateTime? PaidDate { get; set; }
    
    public string? PaymentReference { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual PayrollCycle PayrollCycle { get; set; } = null!;
    public virtual User Employee { get; set; } = null!;
    public virtual ICollection<PayrollComponent> PayrollComponents { get; set; } = new List<PayrollComponent>();
}

public class SalaryComponent : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string ComponentType { get; set; } = string.Empty; // earning, deduction
    
    [Required]
    [MaxLength(20)]
    public string CalculationType { get; set; } = string.Empty; // fixed, percentage, formula
    
    public decimal? DefaultValue { get; set; }
    
    public decimal? MinValue { get; set; }
    
    public decimal? MaxValue { get; set; }
    
    public string? Formula { get; set; } // For complex calculations
    
    public bool IsTaxable { get; set; } = true;
    
    public bool IsStatutory { get; set; } = false; // PF, ESI, etc.
    
    public bool IsActive { get; set; } = true;
    
    public int DisplayOrder { get; set; } = 0;
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
    public virtual ICollection<EmployeeSalaryStructure> EmployeeSalaryStructures { get; set; } = new List<EmployeeSalaryStructure>();
    public virtual ICollection<PayrollComponent> PayrollComponents { get; set; } = new List<PayrollComponent>();
}

public class EmployeeSalaryStructure : BaseAuditableEntity
{
    public Guid EmployeeId { get; set; }
    
    public Guid ComponentId { get; set; }
    
    public decimal Amount { get; set; }
    
    public DateTime EffectiveFrom { get; set; }
    
    public DateTime? EffectiveTo { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual User Employee { get; set; } = null!;
    public virtual SalaryComponent Component { get; set; } = null!;
}

public class PayrollComponent : BaseAuditableEntity
{
    public Guid EmployeePayrollId { get; set; }
    
    public Guid ComponentId { get; set; }
    
    public decimal Amount { get; set; }
    
    public decimal? CalculatedValue { get; set; }
    
    public string? Remarks { get; set; }
    
    // Navigation properties
    public virtual EmployeePayroll EmployeePayroll { get; set; } = null!;
    public virtual SalaryComponent Component { get; set; } = null!;
}
