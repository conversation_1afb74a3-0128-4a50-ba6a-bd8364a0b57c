using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;

namespace HRMS.Core.Entities;

public class Permission : BaseAuditableEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Code { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Module { get; set; } = string.Empty; // attendance, leave, tasks, payroll, etc.
    
    [Required]
    [MaxLength(50)]
    public string Action { get; set; } = string.Empty; // create, read, update, delete, approve, etc.
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    public virtual ICollection<EmployeePermission> EmployeePermissions { get; set; } = new List<EmployeePermission>();
}

public class Role : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? Description { get; set; }
    
    public bool IsSystemRole { get; set; } = false; // true for built-in roles like Admin, Employee
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    public virtual ICollection<EmployeeRole> EmployeeRoles { get; set; } = new List<EmployeeRole>();
}

public class RolePermission : BaseAuditableEntity
{
    public Guid RoleId { get; set; }
    
    public Guid PermissionId { get; set; }
    
    // Navigation properties
    public virtual Role Role { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
}

public class EmployeeRole : BaseAuditableEntity
{
    public Guid EmployeeDetailId { get; set; }
    
    public Guid RoleId { get; set; }
    
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ExpiryDate { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual EmployeeDetail EmployeeDetail { get; set; } = null!;
    public virtual Role Role { get; set; } = null!;
}

public class EmployeePermission : BaseAuditableEntity
{
    public Guid EmployeeDetailId { get; set; }
    
    public Guid PermissionId { get; set; }
    
    public bool IsGranted { get; set; } = true; // true = granted, false = explicitly denied
    
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ExpiryDate { get; set; }
    
    public string? Reason { get; set; }
    
    // Navigation properties
    public virtual EmployeeDetail EmployeeDetail { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
}
