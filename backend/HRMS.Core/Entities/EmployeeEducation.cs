using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;

namespace HRMS.Core.Entities;

public class EmployeeEducation : BaseAuditableEntity
{
    public Guid EmployeeDetailId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Degree { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string Institution { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(10)]
    public string Year { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? FieldOfStudy { get; set; }
    
    [MaxLength(20)]
    public string? Grade { get; set; }
    
    // Navigation properties
    public virtual EmployeeDetail EmployeeDetail { get; set; } = null!;
}

public class EmployeeSkill : BaseAuditableEntity
{
    public Guid EmployeeDetailId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    public int Level { get; set; } // 1-100 scale
    
    [MaxLength(50)]
    public string? Category { get; set; }
    
    public int? YearsOfExperience { get; set; }
    
    // Navigation properties
    public virtual EmployeeDetail EmployeeDetail { get; set; } = null!;
}

public class OrganizationSetting : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Key { get; set; } = string.Empty;
    
    [Required]
    public string Value { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? Description { get; set; }
    
    [MaxLength(50)]
    public string DataType { get; set; } = "string"; // string, number, boolean, json
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
}
