using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class EmployeeDetail : BaseAuditableEntity
{
    public Guid UserId { get; set; }

    [Required]
    [MaxLength(50)]
    public string EmployeeId { get; set; } = string.Empty;

    [Required]
    [MaxLength(255)]
    public string JobTitle { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string Department { get; set; } = string.Empty;

    public Guid? ManagerId { get; set; }

    public DateTime JoinDate { get; set; }

    public EmploymentType EmploymentType { get; set; } = EmploymentType.FullTime;

    public EmploymentStatus EmploymentStatus { get; set; } = EmploymentStatus.Active;

    [MaxLength(255)]
    public string? WorkLocation { get; set; }

    [MaxLength(20)]
    public string? Phone { get; set; }

    public string? Address { get; set; }

    public DateTime? DateOfBirth { get; set; }

    public decimal? BaseSalary { get; set; }

    public decimal? AnnualCTC { get; set; }

    public DateTime? NextSalaryReview { get; set; }

    public decimal? PerformanceRating { get; set; }

    [MaxLength(50)]
    public string? TotalExperience { get; set; }

    // Additional fields for comprehensive employee management
    [MaxLength(100)]
    public string? EmergencyContactName { get; set; }

    [MaxLength(20)]
    public string? EmergencyContactPhone { get; set; }

    [MaxLength(100)]
    public string? EmergencyContactRelation { get; set; }

    [MaxLength(50)]
    public string? BloodGroup { get; set; }

    [MaxLength(20)]
    public string? MaritalStatus { get; set; }

    [MaxLength(10)]
    public string? Gender { get; set; }

    [MaxLength(50)]
    public string? Nationality { get; set; }

    [MaxLength(100)]
    public string? BankAccountNumber { get; set; }

    [MaxLength(20)]
    public string? BankIFSC { get; set; }

    [MaxLength(100)]
    public string? BankName { get; set; }

    [MaxLength(50)]
    public string? PAN { get; set; }

    [MaxLength(20)]
    public string? Aadhar { get; set; }

    public DateTime? ProbationEndDate { get; set; }

    public DateTime? ConfirmationDate { get; set; }

    public DateTime? ResignationDate { get; set; }

    public DateTime? LastWorkingDate { get; set; }

    public string? ResignationReason { get; set; }

    // Login credential fields for automatic credential generation
    [MaxLength(255)]
    public string? LoginUsername { get; set; }

    [MaxLength(255)]
    public string? TemporaryPassword { get; set; }

    public bool RequirePasswordReset { get; set; } = true;

    public DateTime? PasswordGeneratedAt { get; set; }

    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual User? Manager { get; set; }
    public virtual ICollection<EmployeeEducation> Education { get; set; } = new List<EmployeeEducation>();
    public virtual ICollection<EmployeeSkill> Skills { get; set; } = new List<EmployeeSkill>();
    public virtual ICollection<LeaveBalance> LeaveBalances { get; set; } = new List<LeaveBalance>();
    public virtual ICollection<EmployeeRole> EmployeeRoles { get; set; } = new List<EmployeeRole>();
}
