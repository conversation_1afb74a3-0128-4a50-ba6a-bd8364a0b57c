using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class PerformanceReview : BaseAuditableEntity
{
    public Guid EmployeeId { get; set; }
    
    public Guid ReviewerId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string ReviewPeriod { get; set; } = string.Empty;
    
    [MaxLength(50)]
    public string ReviewType { get; set; } = "quarterly";
    
    public decimal? OverallRating { get; set; }
    
    public ReviewStatus Status { get; set; } = ReviewStatus.Scheduled;
    
    public DateTime ReviewDate { get; set; }
    
    public string? Feedback { get; set; }
    
    // Navigation properties
    public virtual User Employee { get; set; } = null!;
    public virtual User Reviewer { get; set; } = null!;
    public virtual ICollection<PerformanceGoal> Goals { get; set; } = new List<PerformanceGoal>();
}

public class PerformanceGoal : BaseAuditableEntity
{
    public Guid ReviewId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public int WeightPercentage { get; set; }
    
    public decimal? AchievedRating { get; set; }
    
    public string? Comments { get; set; }
    
    // Navigation properties
    public virtual PerformanceReview Review { get; set; } = null!;
}
