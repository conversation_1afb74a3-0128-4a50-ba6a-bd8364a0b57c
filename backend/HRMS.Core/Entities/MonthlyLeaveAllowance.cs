using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;

namespace HRMS.Core.Entities;

/// <summary>
/// Tracks monthly leave allowances for employees with carryover functionality
/// Implements the 1-leave-per-month policy across all leave types
/// </summary>
public class MonthlyLeaveAllowance : BaseAuditableEntity
{
    public Guid UserId { get; set; }
    
    public int Year { get; set; }
    
    public int Month { get; set; } // 1-12
    
    /// <summary>
    /// Base monthly allowance (typically 1 for the new policy)
    /// </summary>
    public int BaseAllowance { get; set; } = 1;
    
    /// <summary>
    /// Carried forward from previous month
    /// </summary>
    public int CarriedForward { get; set; } = 0;
    
    /// <summary>
    /// Total available for this month (BaseAllowance + CarriedForward)
    /// </summary>
    public int TotalAvailable => BaseAllowance + CarriedForward;
    
    /// <summary>
    /// Number of leaves used in this month
    /// </summary>
    public int UsedLeaves { get; set; } = 0;
    
    /// <summary>
    /// Remaining leaves for this month
    /// </summary>
    public int RemainingLeaves => TotalAvailable - UsedLeaves;
    
    /// <summary>
    /// Whether this month's allowance has been processed for carryover
    /// </summary>
    public bool IsProcessed { get; set; } = false;
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
}

/// <summary>
/// Tracks individual leave requests against monthly allowances
/// Links leave requests to the monthly allowance system
/// </summary>
public class MonthlyLeaveUsage : BaseAuditableEntity
{
    public Guid LeaveRequestId { get; set; }
    
    public Guid MonthlyLeaveAllowanceId { get; set; }
    
    /// <summary>
    /// Number of days deducted from monthly allowance
    /// </summary>
    public int DaysUsed { get; set; }
    
    /// <summary>
    /// Date when this usage was recorded
    /// </summary>
    public DateTime UsageDate { get; set; }
    
    /// <summary>
    /// Status of this usage (active, cancelled, etc.)
    /// </summary>
    public MonthlyLeaveUsageStatus Status { get; set; } = MonthlyLeaveUsageStatus.Active;
    
    // Navigation properties
    public virtual LeaveRequest LeaveRequest { get; set; } = null!;
    public virtual MonthlyLeaveAllowance MonthlyLeaveAllowance { get; set; } = null!;
}

public enum MonthlyLeaveUsageStatus
{
    Active,
    Cancelled,
    Reverted
}
