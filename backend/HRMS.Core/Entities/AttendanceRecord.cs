using System.ComponentModel.DataAnnotations;
using HRMS.Core.Common;
using HRMS.Core.Enums;

namespace HRMS.Core.Entities;

public class AttendanceRecord : BaseAuditableEntity
{
    public Guid UserId { get; set; }
    
    public DateTime Date { get; set; }
    
    public DateTime? CheckInTime { get; set; }
    
    public DateTime? CheckOutTime { get; set; }
    
    public decimal? TotalHours { get; set; }
    
    public AttendanceStatus Status { get; set; }
    
    [MaxLength(255)]
    public string? Location { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
}

public class LeaveType : BaseAuditableEntity
{
    public Guid OrganizationId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public int MaxDaysPerYear { get; set; }
    
    public bool IsCarryForward { get; set; } = false;
    
    public int MaxCarryForwardDays { get; set; } = 0;
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Organization Organization { get; set; } = null!;
    public virtual ICollection<LeaveBalance> LeaveBalances { get; set; } = new List<LeaveBalance>();
    public virtual ICollection<LeaveRequest> LeaveRequests { get; set; } = new List<LeaveRequest>();
}

public class LeaveBalance : BaseAuditableEntity
{
    public Guid UserId { get; set; }
    
    public Guid LeaveTypeId { get; set; }
    
    public int Year { get; set; }
    
    public int TotalAllocated { get; set; }
    
    public int UsedLeaves { get; set; }
    
    public int CarriedForward { get; set; } = 0;
    
    public int RemainingLeaves => TotalAllocated + CarriedForward - UsedLeaves;
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual LeaveType LeaveType { get; set; } = null!;
}

public class LeaveRequest : BaseAuditableEntity
{
    public Guid UserId { get; set; }

    public Guid? LeaveTypeId { get; set; } // Nullable for simplified monthly leave system
    
    public DateTime FromDate { get; set; }
    
    public DateTime ToDate { get; set; }
    
    public int TotalDays { get; set; }
    
    [MaxLength(50)]
    public string DurationType { get; set; } = "full-day"; // full-day, half-day
    
    public string Reason { get; set; } = string.Empty;
    
    public LeaveStatus Status { get; set; } = LeaveStatus.Pending;
    
    public Guid? ApprovedBy { get; set; }
    
    public DateTime? ApprovedAt { get; set; }
    
    public string? Comments { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual LeaveType? LeaveType { get; set; } // Nullable for simplified monthly leave system
    public virtual User? Approver { get; set; }
}
