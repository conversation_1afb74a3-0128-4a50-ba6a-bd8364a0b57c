namespace HRMS.Core.Enums;

public enum UserRole
{
    SuperAdmin = 1,
    OrgAdmin = 2,
    Employee = 3
}

public enum OrganizationStatus
{
    Pending = 1,
    Active = 2,
    Suspended = 3,
    Inactive = 4
}

public enum EmploymentType
{
    FullTime = 1,
    PartTime = 2,
    Contract = 3,
    Intern = 4
}

public enum EmploymentStatus
{
    Active = 1,
    Inactive = 2,
    Terminated = 3,
    OnLeave = 4
}

public enum AttendanceStatus
{
    Present = 1,
    Absent = 2,
    Late = 3,
    HalfDay = 4
}

public enum LeaveStatus
{
    Pending = 1,
    Approved = 2,
    Rejected = 3,
    Cancelled = 4
}

public enum TaskStatus
{
    Pending = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4
}

public enum TaskPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Urgent = 4
}

public enum TaskType
{
    Regular = 1,        // Admin-assigned tasks
    SelfCreated = 2,    // Employee self-created tasks
    Personal = 3,       // Personal development tasks
    Project = 4         // Project-related tasks
}

public enum TaskUpdateType
{
    Daily = 1,          // Daily progress updates
    Milestone = 2,      // Milestone completion updates
    Completion = 3,     // Task completion updates
    Blocker = 4,        // Blocker/issue updates
    StatusChange = 5    // Status change updates
}

public enum ReviewStatus
{
    Scheduled = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4
}

public enum PayrollStatus
{
    Draft = 1,
    Processed = 2,
    Paid = 3,
    Failed = 4
}

public enum ApplicationStatus
{
    Applied = 1,
    Screening = 2,
    Interview = 3,
    Selected = 4,
    Rejected = 5,
    Withdrawn = 6
}

public enum JobStatus
{
    Draft = 1,
    Active = 2,
    Closed = 3,
    OnHold = 4
}
