namespace HRMS.Core.Configuration;

/// <summary>
/// Configuration for admin credential management
/// </summary>
public class AdminCredentialsConfiguration
{
    /// <summary>
    /// Whether to generate admin credentials automatically on startup
    /// </summary>
    public bool GenerateOnStartup { get; set; } = true;

    /// <summary>
    /// Whether generated credentials require password reset on first login
    /// </summary>
    public bool RequirePasswordReset { get; set; } = true;

    /// <summary>
    /// Super admin configuration
    /// </summary>
    public SuperAdminConfiguration SuperAdmin { get; set; } = new();

    /// <summary>
    /// Default organization configuration for initial setup
    /// </summary>
    public DefaultOrganizationConfiguration DefaultOrganization { get; set; } = new();
}

/// <summary>
/// Configuration for the super admin user
/// </summary>
public class SuperAdminConfiguration
{
    /// <summary>
    /// Super admin email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Super admin full name
    /// </summary>
    public string Name { get; set; } = string.Empty;
}

/// <summary>
/// Configuration for the default organization and admin user
/// </summary>
public class DefaultOrganizationConfiguration
{
    /// <summary>
    /// Organization name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Organization domain
    /// </summary>
    public string Domain { get; set; } = string.Empty;

    /// <summary>
    /// Organization industry
    /// </summary>
    public string Industry { get; set; } = string.Empty;

    /// <summary>
    /// Admin user email address
    /// </summary>
    public string AdminEmail { get; set; } = string.Empty;

    /// <summary>
    /// Admin user full name
    /// </summary>
    public string AdminName { get; set; } = string.Empty;
}
