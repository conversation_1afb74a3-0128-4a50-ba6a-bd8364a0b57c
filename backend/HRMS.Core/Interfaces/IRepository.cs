using System.Linq.Expressions;
using HRMS.Core.Common;

namespace HRMS.Core.Interfaces;

public interface IRepository<T> where T : BaseEntity
{
    Task<T?> GetByIdAsync(Guid id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task UpdateAsync(T entity);
    Task DeleteAsync(T entity);
    Task DeleteRangeAsync(IEnumerable<T> entities);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
}

public interface IUnitOfWork : IDisposable
{
    IUserRepository Users { get; }
    IOrganizationRepository Organizations { get; }
    IEmployeeDetailRepository EmployeeDetails { get; }
    IAttendanceRepository Attendance { get; }
    ILeaveRepository Leaves { get; }
    ILeaveTypeRepository LeaveTypes { get; }
    ILeaveBalanceRepository LeaveBalances { get; }
    IMonthlyLeaveAllowanceRepository MonthlyLeaveAllowances { get; }
    IMonthlyLeaveUsageRepository MonthlyLeaveUsages { get; }
    ITaskRepository Tasks { get; }
    ITaskUpdateRepository TaskUpdates { get; }
    IPerformanceRepository Performance { get; }
    IRoleRepository Roles { get; }
    IPermissionRepository Permissions { get; }
    IRolePermissionRepository RolePermissions { get; }
    IEmployeeRoleRepository EmployeeRoles { get; }
    IEmployeePermissionRepository EmployeePermissions { get; }
    ISalaryComponentRepository SalaryComponents { get; }
    IEmployeeSalaryStructureRepository EmployeeSalaryStructures { get; }

    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}

public interface IUserRepository : IRepository<HRMS.Core.Entities.User>
{
    Task<HRMS.Core.Entities.User?> GetByEmailAsync(string email);
    Task<IEnumerable<HRMS.Core.Entities.User>> GetByOrganizationAsync(Guid organizationId);
    Task<bool> EmailExistsAsync(string email);
    Task<HRMS.Core.Entities.User?> GetByIdIncludingDeletedAsync(Guid id);
}

public interface IOrganizationRepository : IRepository<HRMS.Core.Entities.Organization>
{
    Task<HRMS.Core.Entities.Organization?> GetByDomainAsync(string domain);
    Task<bool> DomainExistsAsync(string domain);
}

public interface IEmployeeDetailRepository : IRepository<HRMS.Core.Entities.EmployeeDetail>
{
    Task<HRMS.Core.Entities.EmployeeDetail?> GetByUserIdAsync(Guid userId);
    Task<HRMS.Core.Entities.EmployeeDetail?> GetByEmployeeIdAsync(string employeeId);
    Task<IEnumerable<HRMS.Core.Entities.EmployeeDetail>> GetByManagerIdAsync(Guid managerId);
}

public interface IAttendanceRepository : IRepository<HRMS.Core.Entities.AttendanceRecord>
{
    Task<IEnumerable<HRMS.Core.Entities.AttendanceRecord>> GetByUserAndDateRangeAsync(Guid userId, DateTime startDate, DateTime endDate);
    Task<HRMS.Core.Entities.AttendanceRecord?> GetByUserAndDateAsync(Guid userId, DateTime date);
}

public interface ILeaveRepository : IRepository<HRMS.Core.Entities.LeaveRequest>
{
    Task<IEnumerable<HRMS.Core.Entities.LeaveRequest>> GetByUserAsync(Guid userId);
    Task<IEnumerable<HRMS.Core.Entities.LeaveRequest>> GetByOrganizationAsync(Guid organizationId);
    Task<IEnumerable<HRMS.Core.Entities.LeaveBalance>> GetLeaveBalancesByUserAsync(Guid userId, int year);
}

public interface ILeaveTypeRepository : IRepository<HRMS.Core.Entities.LeaveType>
{
    Task<IEnumerable<HRMS.Core.Entities.LeaveType>> GetByOrganizationAsync(Guid organizationId);
}

public interface ITaskRepository : IRepository<HRMS.Core.Entities.Task>
{
    Task<IEnumerable<HRMS.Core.Entities.Task>> GetByAssignedUserAsync(Guid userId);
    Task<IEnumerable<HRMS.Core.Entities.Task>> GetByCreatedUserAsync(Guid userId);
    Task<IEnumerable<HRMS.Core.Entities.Task>> GetByOrganizationAsync(Guid organizationId);
}

public interface ITaskUpdateRepository : IRepository<HRMS.Core.Entities.TaskUpdate>
{
    Task<IEnumerable<HRMS.Core.Entities.TaskUpdate>> GetByTaskAsync(Guid taskId);
    Task<IEnumerable<HRMS.Core.Entities.TaskUpdate>> GetByOrganizationAsync(Guid organizationId);
    Task<IEnumerable<HRMS.Core.Entities.TaskUpdate>> GetByTaskIdsAsync(IEnumerable<Guid> taskIds);
}

public interface IPerformanceRepository : IRepository<HRMS.Core.Entities.PerformanceReview>
{
    Task<IEnumerable<HRMS.Core.Entities.PerformanceReview>> GetByEmployeeAsync(Guid employeeId);
    Task<IEnumerable<HRMS.Core.Entities.PerformanceReview>> GetByReviewerAsync(Guid reviewerId);
}

public interface ILeaveBalanceRepository : IRepository<HRMS.Core.Entities.LeaveBalance>
{
    Task<IEnumerable<HRMS.Core.Entities.LeaveBalance>> GetByUserAsync(Guid userId, int year);
}

public interface IRoleRepository : IRepository<HRMS.Core.Entities.Role>
{
    Task<IEnumerable<HRMS.Core.Entities.Role>> GetByOrganizationAsync(Guid organizationId);
    Task<HRMS.Core.Entities.Role?> GetByNameAsync(Guid organizationId, string name);
}

public interface IPermissionRepository : IRepository<HRMS.Core.Entities.Permission>
{
    Task<IEnumerable<HRMS.Core.Entities.Permission>> GetByModuleAsync(string module);
}

public interface IRolePermissionRepository : IRepository<HRMS.Core.Entities.RolePermission>
{
    Task<IEnumerable<HRMS.Core.Entities.RolePermission>> GetByRoleAsync(Guid roleId);
}

public interface IEmployeeRoleRepository : IRepository<HRMS.Core.Entities.EmployeeRole>
{
    Task<IEnumerable<HRMS.Core.Entities.EmployeeRole>> GetByEmployeeAsync(Guid employeeDetailId);
}

public interface IEmployeePermissionRepository : IRepository<HRMS.Core.Entities.EmployeePermission>
{
    Task<IEnumerable<HRMS.Core.Entities.EmployeePermission>> GetByEmployeeAsync(Guid employeeDetailId);
}

public interface ISalaryComponentRepository : IRepository<HRMS.Core.Entities.SalaryComponent>
{
    Task<IEnumerable<HRMS.Core.Entities.SalaryComponent>> GetByOrganizationAsync(Guid organizationId);
}

public interface IEmployeeSalaryStructureRepository : IRepository<HRMS.Core.Entities.EmployeeSalaryStructure>
{
    Task<IEnumerable<HRMS.Core.Entities.EmployeeSalaryStructure>> GetByEmployeeAsync(Guid employeeId);
}

public interface IJwtService
{
    string GenerateToken(HRMS.Core.Entities.User user);
    string GenerateRefreshToken();
    bool ValidateToken(string token);
    Task<bool> ValidateTokenAsync(string token);
    Task<bool> InvalidateTokenAsync(string token, string reason = "logout");
    Task<bool> InvalidateAllUserTokensAsync(string userId, string reason = "security");
    string? GetUserIdFromToken(string token);
    string? GetOrganizationIdFromToken(string token);
    string? GetUserRoleFromToken(string token);
}

public interface IPasswordService
{
    string HashPassword(string password);
    bool VerifyPassword(string password, string hash);
    string GenerateTemporaryPassword();
}

public interface IDatabaseProvisioningService
{
    Task<bool> ProvisionOrganizationSchemaAsync(string organizationId);
    Task<bool> SchemaExistsAsync(string organizationId);
    Task<bool> DeleteOrganizationSchemaAsync(string organizationId);
    Task<bool> MigrateSchemaAsync(string organizationId);
}
