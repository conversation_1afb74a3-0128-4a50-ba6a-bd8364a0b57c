using HRMS.Core.Entities;

namespace HRMS.Core.Interfaces;

public interface IMonthlyLeaveAllowanceRepository : IRepository<MonthlyLeaveAllowance>
{
    System.Threading.Tasks.Task<MonthlyLeaveAllowance?> GetByUserAndMonthAsync(Guid userId, int year, int month);
    System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveAllowance>> GetByUserAndYearAsync(Guid userId, int year);
    System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveAllowance>> GetUnprocessedAllowancesAsync(int year, int month);
    System.Threading.Tasks.Task<MonthlyLeaveAllowance> CreateOrUpdateAllowanceAsync(Guid userId, int year, int month, int baseAllowance = 1, int carriedForward = 0);
    System.Threading.Tasks.Task<bool> HasAvailableLeaveAsync(Guid userId, int year, int month);
    System.Threading.Tasks.Task<int> GetRemainingLeavesAsync(Guid userId, int year, int month);
    System.Threading.Tasks.Task<bool> DeductLeaveAsync(Guid userId, Guid leaveRequestId, int year, int month, int daysToDeduct);
    System.Threading.Tasks.Task<bool> RestoreLeaveAsync(Guid leaveRequestId);
    System.Threading.Tasks.Task ProcessMonthlyCarryoverAsync(int year, int month);
}

public interface IMonthlyLeaveUsageRepository : IRepository<MonthlyLeaveUsage>
{
    System.Threading.Tasks.Task<MonthlyLeaveUsage?> GetByLeaveRequestAsync(Guid leaveRequestId);
    System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveUsage>> GetByAllowanceAsync(Guid allowanceId);
    System.Threading.Tasks.Task<IEnumerable<MonthlyLeaveUsage>> GetByUserAndMonthAsync(Guid userId, int year, int month);
}
