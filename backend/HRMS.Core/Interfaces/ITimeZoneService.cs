namespace HRMS.Core.Interfaces;

public interface ITimeZoneService
{
    /// <summary>
    /// Gets the current time in Indian Standard Time (IST)
    /// </summary>
    DateTime GetCurrentIstTime();

    /// <summary>
    /// Converts UTC datetime to IST
    /// </summary>
    DateTime ConvertUtcToIst(DateTime utcDateTime);

    /// <summary>
    /// Converts IST datetime to UTC
    /// </summary>
    DateTime ConvertIstToUtc(DateTime istDateTime);

    /// <summary>
    /// Gets the start of day (00:00:00) in IST for the given date
    /// </summary>
    DateTime GetIstStartOfDay(DateTime? date = null);

    /// <summary>
    /// Gets the end of day (23:59:59) in IST for the given date
    /// </summary>
    DateTime GetIstEndOfDay(DateTime? date = null);

    /// <summary>
    /// Formats datetime in IST with the specified format
    /// </summary>
    string FormatIstDateTime(DateTime dateTime, string format = "dd/MM/yyyy HH:mm:ss");

    /// <summary>
    /// Formats date in IST with the specified format
    /// </summary>
    string FormatIstDate(DateTime dateTime, string format = "dd/MM/yyyy");

    /// <summary>
    /// Formats time in IST with the specified format
    /// </summary>
    string FormatIstTime(DateTime dateTime, string format = "HH:mm:ss");

    /// <summary>
    /// Determines if a check-in time is late based on IST
    /// </summary>
    bool IsLateCheckIn(DateTime checkInTime, TimeSpan lateThreshold);

    /// <summary>
    /// Calculates working hours between check-in and check-out in IST
    /// </summary>
    decimal CalculateWorkingHours(DateTime checkInTime, DateTime? checkOutTime);

    /// <summary>
    /// Gets the IST TimeZoneInfo object
    /// </summary>
    TimeZoneInfo GetIstTimeZone();

    /// <summary>
    /// Gets the display name for the timezone
    /// </summary>
    string GetTimeZoneDisplayName();

    /// <summary>
    /// Gets the timezone abbreviation
    /// </summary>
    string GetTimeZoneAbbreviation();
}
