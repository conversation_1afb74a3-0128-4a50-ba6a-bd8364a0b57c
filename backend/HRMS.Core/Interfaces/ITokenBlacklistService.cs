namespace HRMS.Core.Interfaces;

public interface ITokenBlacklistService
{
    /// <summary>
    /// Add a token to the blacklist
    /// </summary>
    /// <param name="tokenId">Unique identifier for the token (JTI claim)</param>
    /// <param name="userId">User ID associated with the token</param>
    /// <param name="expiryTime">When the token expires</param>
    /// <param name="reason">Reason for blacklisting (logout, security, etc.)</param>
    Task BlacklistTokenAsync(string tokenId, string userId, DateTime expiryTime, string reason = "logout");

    /// <summary>
    /// Check if a token is blacklisted
    /// </summary>
    /// <param name="tokenId">Unique identifier for the token (JTI claim)</param>
    /// <returns>True if token is blacklisted, false otherwise</returns>
    Task<bool> IsTokenBlacklistedAsync(string tokenId);

    /// <summary>
    /// Blacklist all tokens for a specific user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="reason">Reason for blacklisting</param>
    Task BlacklistAllUserTokensAsync(string userId, string reason = "security");

    /// <summary>
    /// Clean up expired blacklisted tokens
    /// </summary>
    Task CleanupExpiredTokensAsync();

    /// <summary>
    /// Get blacklisted tokens for a user (for admin purposes)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of blacklisted tokens</returns>
    Task<List<BlacklistedToken>> GetUserBlacklistedTokensAsync(string userId);
}

public class BlacklistedToken
{
    public string TokenId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime BlacklistedAt { get; set; }
    public DateTime ExpiryTime { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
}
