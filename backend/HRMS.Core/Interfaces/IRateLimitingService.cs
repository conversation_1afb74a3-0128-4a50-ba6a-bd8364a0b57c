namespace HRMS.Core.Interfaces;

public interface IRateLimitingService
{
    /// <summary>
    /// Check if a request is allowed based on rate limiting rules
    /// </summary>
    /// <param name="key">Unique identifier for the rate limit (e.g., IP address, user ID)</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <param name="maxRequests">Maximum number of requests allowed</param>
    /// <param name="timeWindow">Time window in seconds</param>
    /// <returns>True if request is allowed, false if rate limited</returns>
    Task<bool> IsRequestAllowedAsync(string key, string endpoint, int maxRequests, int timeWindow);

    /// <summary>
    /// Get remaining requests for a given key and endpoint
    /// </summary>
    /// <param name="key">Unique identifier for the rate limit</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <param name="maxRequests">Maximum number of requests allowed</param>
    /// <param name="timeWindow">Time window in seconds</param>
    /// <returns>Number of remaining requests</returns>
    Task<int> GetRemainingRequestsAsync(string key, string endpoint, int maxRequests, int timeWindow);

    /// <summary>
    /// Get the time when the rate limit will reset
    /// </summary>
    /// <param name="key">Unique identifier for the rate limit</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <param name="timeWindow">Time window in seconds</param>
    /// <returns>DateTime when the rate limit resets</returns>
    Task<DateTime> GetResetTimeAsync(string key, string endpoint, int timeWindow);

    /// <summary>
    /// Clear rate limit data for a specific key and endpoint
    /// </summary>
    /// <param name="key">Unique identifier for the rate limit</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    Task ClearRateLimitAsync(string key, string endpoint);

    /// <summary>
    /// Get rate limit configuration for a specific endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint path</param>
    /// <returns>Rate limit configuration</returns>
    RateLimitConfig GetRateLimitConfig(string endpoint);
}

public class RateLimitConfig
{
    public int MaxRequests { get; set; }
    public int TimeWindowSeconds { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool Enabled { get; set; } = true;
}

public class RateLimitResult
{
    public bool IsAllowed { get; set; }
    public int RemainingRequests { get; set; }
    public DateTime ResetTime { get; set; }
    public int RetryAfterSeconds { get; set; }
    public string Message { get; set; } = string.Empty;
}
