using HRMS.Core.Configuration;
using HRMS.Core.Entities;

namespace HRMS.Core.Services;

/// <summary>
/// Service for managing admin credentials dynamically
/// </summary>
public interface IAdminCredentialService
{
    /// <summary>
    /// Generates or retrieves admin credentials for an organization
    /// </summary>
    /// <param name="organizationId">Organization ID</param>
    /// <param name="adminEmail">Admin email address</param>
    /// <param name="adminName">Admin full name</param>
    /// <returns>Admin credentials with generated or configured password</returns>
    Task<AdminCredentialsResult> GetOrGenerateAdminCredentialsAsync(Guid organizationId, string adminEmail, string adminName);

    /// <summary>
    /// Generates admin credentials using environment variables or configuration
    /// </summary>
    /// <param name="adminEmail">Admin email address</param>
    /// <param name="adminName">Admin full name</param>
    /// <returns>Admin credentials</returns>
    Task<AdminCredentialsResult> GenerateAdminCredentialsAsync(string adminEmail, string adminName);

    /// <summary>
    /// Sets admin credentials from environment variables or configuration
    /// </summary>
    /// <param name="adminEmail">Admin email address</param>
    /// <returns>Admin credentials if found in environment/config, null otherwise</returns>
    Task<AdminCredentialsResult?> GetAdminCredentialsFromConfigAsync(string adminEmail);

    /// <summary>
    /// Validates if admin credentials exist and are valid
    /// </summary>
    /// <param name="adminEmail">Admin email address</param>
    /// <param name="passwordHash">Current password hash</param>
    /// <returns>True if credentials are valid and don't need regeneration</returns>
    Task<bool> ValidateExistingCredentialsAsync(string adminEmail, string passwordHash);
}

/// <summary>
/// Result of admin credential generation or retrieval
/// </summary>
public class AdminCredentialsResult
{
    /// <summary>
    /// Admin email address (username)
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Admin full name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Plain text password (for display/logging purposes only)
    /// </summary>
    public string PlainTextPassword { get; set; } = string.Empty;

    /// <summary>
    /// Hashed password for database storage
    /// </summary>
    public string HashedPassword { get; set; } = string.Empty;

    /// <summary>
    /// Whether this password requires reset on first login
    /// </summary>
    public bool RequirePasswordReset { get; set; } = true;

    /// <summary>
    /// When the credentials were generated
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Source of the credentials (Generated, Environment, Configuration)
    /// </summary>
    public CredentialSource Source { get; set; } = CredentialSource.Generated;
}

/// <summary>
/// Source of admin credentials
/// </summary>
public enum CredentialSource
{
    /// <summary>
    /// Credentials were automatically generated
    /// </summary>
    Generated,

    /// <summary>
    /// Credentials were read from environment variables
    /// </summary>
    Environment,

    /// <summary>
    /// Credentials were read from configuration files
    /// </summary>
    Configuration,

    /// <summary>
    /// Credentials already exist in database
    /// </summary>
    Existing
}
