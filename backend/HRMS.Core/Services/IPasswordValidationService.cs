namespace HRMS.Core.Services;

public class PasswordValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public PasswordStrength Strength { get; set; }
    public int Score { get; set; } // 0-100
}

public enum PasswordStrength
{
    VeryWeak = 0,
    Weak = 1,
    Fair = 2,
    Good = 3,
    Strong = 4,
    VeryStrong = 5
}

public interface IPasswordValidationService
{
    /// <summary>
    /// Validates a password against security requirements
    /// </summary>
    /// <param name="password">Password to validate</param>
    /// <returns>Validation result with errors and strength assessment</returns>
    PasswordValidationResult ValidatePassword(string password);

    /// <summary>
    /// Gets password requirements as a list of rules
    /// </summary>
    /// <returns>List of password requirements</returns>
    List<string> GetPasswordRequirements();

    /// <summary>
    /// Checks if password meets minimum security requirements
    /// </summary>
    /// <param name="password">Password to check</param>
    /// <returns>True if password meets minimum requirements</returns>
    bool MeetsMinimumRequirements(string password);
}
