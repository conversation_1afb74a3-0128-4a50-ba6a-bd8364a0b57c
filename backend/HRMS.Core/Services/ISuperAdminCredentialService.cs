using HRMS.Core.Services;

namespace HRMS.Core.Services;

/// <summary>
/// Service for managing super admin credentials
/// </summary>
public interface ISuperAdminCredentialService
{
    /// <summary>
    /// Gets or generates super admin credentials
    /// </summary>
    /// <param name="superAdminEmail">Super admin email address</param>
    /// <param name="superAdminName">Super admin full name</param>
    /// <returns>Super admin credentials</returns>
    Task<AdminCredentialsResult> GetOrGenerateSuperAdminCredentialsAsync(string superAdminEmail, string superAdminName);

    /// <summary>
    /// Gets super admin credentials from environment variables or configuration
    /// </summary>
    /// <param name="superAdminEmail">Super admin email address</param>
    /// <returns>Super admin credentials if found, null otherwise</returns>
    Task<AdminCredentialsResult?> GetSuperAdminCredentialsFromConfigAsync(string superAdminEmail);

    /// <summary>
    /// Validates if super admin credentials exist and are valid
    /// </summary>
    /// <param name="superAdminEmail">Super admin email address</param>
    /// <param name="passwordHash">Current password hash</param>
    /// <returns>True if credentials are valid and don't need regeneration</returns>
    Task<bool> ValidateExistingSuperAdminCredentialsAsync(string superAdminEmail, string passwordHash);
}
