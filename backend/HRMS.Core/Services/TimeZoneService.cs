using HRMS.Core.Interfaces;

namespace HRMS.Core.Services;

public class TimeZoneService : ITimeZoneService
{
    private static readonly TimeZoneInfo IstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata");

    public DateTime GetCurrentIstTime()
    {
        return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, IstTimeZone);
    }

    public DateTime ConvertUtcToIst(DateTime utcDateTime)
    {
        if (utcDateTime.Kind != DateTimeKind.Utc)
        {
            // If the datetime is not explicitly UTC, treat it as UTC for conversion
            utcDateTime = DateTime.SpecifyKind(utcDateTime, DateTimeKind.Utc);
        }
        return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, IstTimeZone);
    }

    public DateTime ConvertIstToUtc(DateTime istDateTime)
    {
        // Ensure the datetime is treated as IST
        if (istDateTime.Kind == DateTimeKind.Utc)
        {
            // If it's already UTC, return as is
            return istDateTime;
        }

        // Treat as IST and convert to UTC
        var unspecifiedIst = DateTime.SpecifyKind(istDateTime, DateTimeKind.Unspecified);
        return TimeZoneInfo.ConvertTimeToUtc(unspecifiedIst, IstTimeZone);
    }

    public DateTime GetIstStartOfDay(DateTime? date = null)
    {
        var targetDate = date ?? GetCurrentIstTime();
        var istDate = ConvertUtcToIst(targetDate.Kind == DateTimeKind.Utc ? targetDate : ConvertIstToUtc(targetDate));
        return new DateTime(istDate.Year, istDate.Month, istDate.Day, 0, 0, 0, DateTimeKind.Unspecified);
    }

    public DateTime GetIstEndOfDay(DateTime? date = null)
    {
        var targetDate = date ?? GetCurrentIstTime();
        var istDate = ConvertUtcToIst(targetDate.Kind == DateTimeKind.Utc ? targetDate : ConvertIstToUtc(targetDate));
        return new DateTime(istDate.Year, istDate.Month, istDate.Day, 23, 59, 59, 999, DateTimeKind.Unspecified);
    }

    public string FormatIstDateTime(DateTime dateTime, string format = "dd/MM/yyyy HH:mm:ss")
    {
        var istDateTime = dateTime.Kind == DateTimeKind.Utc 
            ? ConvertUtcToIst(dateTime) 
            : dateTime;
        return istDateTime.ToString(format);
    }

    public string FormatIstDate(DateTime dateTime, string format = "dd/MM/yyyy")
    {
        var istDateTime = dateTime.Kind == DateTimeKind.Utc 
            ? ConvertUtcToIst(dateTime) 
            : dateTime;
        return istDateTime.ToString(format);
    }

    public string FormatIstTime(DateTime dateTime, string format = "HH:mm:ss")
    {
        var istDateTime = dateTime.Kind == DateTimeKind.Utc 
            ? ConvertUtcToIst(dateTime) 
            : dateTime;
        return istDateTime.ToString(format);
    }

    public bool IsLateCheckIn(DateTime checkInTime, TimeSpan lateThreshold)
    {
        var istCheckIn = checkInTime.Kind == DateTimeKind.Utc 
            ? ConvertUtcToIst(checkInTime) 
            : checkInTime;
        
        var checkInTimeOnly = istCheckIn.TimeOfDay;
        return checkInTimeOnly > lateThreshold;
    }

    public decimal CalculateWorkingHours(DateTime checkInTime, DateTime? checkOutTime)
    {
        if (!checkOutTime.HasValue)
            return 0;

        var istCheckIn = checkInTime.Kind == DateTimeKind.Utc 
            ? ConvertUtcToIst(checkInTime) 
            : checkInTime;
        
        var istCheckOut = checkOutTime.Value.Kind == DateTimeKind.Utc 
            ? ConvertUtcToIst(checkOutTime.Value) 
            : checkOutTime.Value;

        var duration = istCheckOut - istCheckIn;
        return (decimal)duration.TotalHours;
    }

    public TimeZoneInfo GetIstTimeZone()
    {
        return IstTimeZone;
    }

    public string GetTimeZoneDisplayName()
    {
        return "Indian Standard Time (IST)";
    }

    public string GetTimeZoneAbbreviation()
    {
        return "IST";
    }
}
