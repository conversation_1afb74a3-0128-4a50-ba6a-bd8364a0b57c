namespace HRMS.Core.Services;

public interface ICredentialGenerationService
{
    /// <summary>
    /// Generates secure login credentials for a new employee
    /// </summary>
    /// <param name="employeeName">Full name of the employee</param>
    /// <param name="organizationDomain">Organization domain for email generation</param>
    /// <returns>Generated credentials with username and temporary password</returns>
    Task<EmployeeCredentialsDto> GenerateCredentialsAsync(string employeeName, string organizationDomain);

    /// <summary>
    /// Generates secure login credentials using existing employee email
    /// </summary>
    /// <param name="employeeEmail">Employee's existing email address</param>
    /// <returns>Generated credentials with existing email and temporary password</returns>
    Task<EmployeeCredentialsDto> GenerateCredentialsWithEmailAsync(string employeeEmail);

    /// <summary>
    /// Generates login credentials using existing employee email and custom password
    /// </summary>
    /// <param name="employeeEmail">Employee's existing email address</param>
    /// <param name="customPassword">Custom password provided by admin</param>
    /// <returns>Generated credentials with existing email and custom password</returns>
    Task<EmployeeCredentialsDto> GenerateCredentialsWithCustomPasswordAsync(string employeeEmail, string customPassword);

    /// <summary>
    /// Generates a secure temporary password
    /// </summary>
    /// <param name="length">Length of the password (default: 12)</param>
    /// <returns>Generated password</returns>
    string GenerateSecurePassword(int length = 12);

    /// <summary>
    /// Generates a username based on employee name and organization domain
    /// </summary>
    /// <param name="employeeName">Full name of the employee</param>
    /// <param name="organizationDomain">Organization domain</param>
    /// <returns>Generated username/email</returns>
    string GenerateUsername(string employeeName, string organizationDomain);

    /// <summary>
    /// Hashes a password using secure hashing algorithm
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <returns>Hashed password</returns>
    string HashPassword(string password);

    /// <summary>
    /// Verifies a password against its hash
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <param name="hash">Hashed password</param>
    /// <returns>True if password matches hash</returns>
    bool VerifyPassword(string password, string hash);
}

public class EmployeeCredentialsDto
{
    public string Username { get; set; } = string.Empty;
    public string TemporaryPassword { get; set; } = string.Empty;
    public string HashedPassword { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public bool RequirePasswordReset { get; set; } = true;
}
