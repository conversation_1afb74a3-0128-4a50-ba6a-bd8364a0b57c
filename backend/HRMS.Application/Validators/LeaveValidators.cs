using FluentValidation;
using HRMS.Application.DTOs;

namespace HRMS.Application.Validators;

public class ApplyLeaveRequestValidator : AbstractValidator<ApplyLeaveRequestDto>
{
    public ApplyLeaveRequestValidator()
    {
        // LeaveTypeId is no longer required for simplified monthly leave system

        RuleFor(x => x.FromDate)
            .NotEmpty().WithMessage("From date is required")
            .GreaterThanOrEqualTo(DateTime.Today).WithMessage("From date cannot be in the past");

        RuleFor(x => x.ToDate)
            .NotEmpty().WithMessage("To date is required")
            .GreaterThanOrEqualTo(x => x.FromDate).WithMessage("To date must be greater than or equal to from date");

        RuleFor(x => x.DurationType)
            .NotEmpty().WithMessage("Duration type is required")
            .Must(x => x == "full-day" || x == "half-day").WithMessage("Duration type must be 'full-day' or 'half-day'");

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("Reason is required")
            .MaximumLength(1000).WithMessage("Reason must not exceed 1000 characters");

        RuleFor(x => x)
            .Must(HaveValidDateRange).WithMessage("Leave duration cannot exceed 365 days");
    }

    private bool HaveValidDateRange(ApplyLeaveRequestDto request)
    {
        var daysDifference = (request.ToDate - request.FromDate).Days;
        return daysDifference <= 365;
    }
}

public class UpdateLeaveStatusValidator : AbstractValidator<UpdateLeaveStatusDto>
{
    public UpdateLeaveStatusValidator()
    {
        RuleFor(x => x.Status)
            .NotEmpty().WithMessage("Status is required")
            .Must(x => x == "approved" || x == "rejected" || x == "cancelled")
            .WithMessage("Status must be 'approved', 'rejected', or 'cancelled'");

        RuleFor(x => x.Comments)
            .MaximumLength(1000).WithMessage("Comments must not exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Comments));
    }
}
