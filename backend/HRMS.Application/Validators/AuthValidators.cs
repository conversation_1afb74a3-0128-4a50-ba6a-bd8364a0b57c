using FluentValidation;
using HRMS.Application.DTOs;

namespace HRMS.Application.Validators;

public class LoginRequestValidator : AbstractValidator<LoginRequestDto>
{
    public LoginRequestValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(255).WithMessage("Email must not exceed 255 characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(8).WithMessage("Password must be at least 8 characters long");
    }
}

public class OrganizationRegistrationValidator : AbstractValidator<OrganizationRegistrationDto>
{
    public OrganizationRegistrationValidator()
    {
        RuleFor(x => x.Organization)
            .NotNull().WithMessage("Organization information is required")
            .SetValidator(new OrganizationCreateValidator());

        RuleFor(x => x.Admin)
            .NotNull().WithMessage("Admin user information is required")
            .SetValidator(new AdminUserCreateValidator());
    }
}

public class OrganizationCreateValidator : AbstractValidator<OrganizationCreateDto>
{
    public OrganizationCreateValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Organization name is required")
            .MaximumLength(255).WithMessage("Organization name must not exceed 255 characters");

        RuleFor(x => x.Domain)
            .NotEmpty().WithMessage("Domain is required")
            .MaximumLength(255).WithMessage("Domain must not exceed 255 characters")
            .Matches(@"^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$").WithMessage("Invalid domain format");

        RuleFor(x => x.Industry)
            .NotEmpty().WithMessage("Industry is required")
            .MaximumLength(100).WithMessage("Industry must not exceed 100 characters");
    }
}

public class AdminUserCreateValidator : AbstractValidator<AdminUserCreateDto>
{
    public AdminUserCreateValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Name is required")
            .MaximumLength(255).WithMessage("Name must not exceed 255 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(255).WithMessage("Email must not exceed 255 characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(8).WithMessage("Password must be at least 8 characters long")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)").WithMessage("Password must contain at least one lowercase letter, one uppercase letter, and one digit");
    }
}


