using FluentValidation;
using HRMS.Application.DTOs;

namespace HRMS.Application.Validators;

public class CreateTaskRequestValidator : AbstractValidator<CreateTaskRequestDto>
{
    public CreateTaskRequestValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Title is required")
            .MaximumLength(255).WithMessage("Title must not exceed 255 characters");

        RuleFor(x => x.Description)
            .MaximumLength(2000).WithMessage("Description must not exceed 2000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.AssignedTo)
            .NotEmpty().WithMessage("Assigned user is required");

        RuleFor(x => x.Priority)
            .Must(x => x == "low" || x == "medium" || x == "high" || x == "urgent")
            .WithMessage("Priority must be 'low', 'medium', 'high', or 'urgent'")
            .When(x => !string.IsNullOrEmpty(x.Priority));

        RuleFor(x => x.Category)
            .MaximumLength(100).WithMessage("Category must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.Category));

        RuleFor(x => x.EstimatedHours)
            .GreaterThan(0).WithMessage("Estimated hours must be greater than 0")
            .LessThanOrEqualTo(1000).WithMessage("Estimated hours must not exceed 1000")
            .When(x => x.EstimatedHours.HasValue);

        RuleFor(x => x.DueDate)
            .GreaterThanOrEqualTo(DateTime.Today).WithMessage("Due date cannot be in the past")
            .When(x => x.DueDate.HasValue);
    }
}

public class UpdateTaskProgressRequestValidator : AbstractValidator<UpdateTaskProgressRequestDto>
{
    public UpdateTaskProgressRequestValidator()
    {
        RuleFor(x => x.ProgressPercentage)
            .InclusiveBetween(0, 100).WithMessage("Progress percentage must be between 0 and 100");

        RuleFor(x => x.Status)
            .Must(x => x == "pending" || x == "inprogress" || x == "completed" || x == "cancelled")
            .WithMessage("Status must be 'pending', 'inprogress', 'completed', or 'cancelled'")
            .When(x => !string.IsNullOrEmpty(x.Status));

        RuleFor(x => x.HoursSpent)
            .GreaterThan(0).WithMessage("Hours spent must be greater than 0")
            .LessThanOrEqualTo(24).WithMessage("Hours spent cannot exceed 24 hours per update")
            .When(x => x.HoursSpent.HasValue);

        RuleFor(x => x.UpdateNotes)
            .MaximumLength(1000).WithMessage("Update notes must not exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.UpdateNotes));

        RuleFor(x => x.Blockers)
            .MaximumLength(1000).WithMessage("Blockers must not exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Blockers));

        RuleFor(x => x.NextSteps)
            .MaximumLength(1000).WithMessage("Next steps must not exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.NextSteps));
    }
}

public class AddTaskCommentRequestValidator : AbstractValidator<AddTaskCommentRequestDto>
{
    public AddTaskCommentRequestValidator()
    {
        RuleFor(x => x.Comment)
            .NotEmpty().WithMessage("Comment is required")
            .MaximumLength(2000).WithMessage("Comment must not exceed 2000 characters");
    }
}
