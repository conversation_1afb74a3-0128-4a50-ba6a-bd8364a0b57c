using HRMS.Application.Common;

namespace HRMS.Application.Interfaces;

/// <summary>
/// Interface for synchronizing dynamic leave balance calculations with database records
/// </summary>
public interface ILeaveBalanceSyncService
{
    /// <summary>
    /// Sync leave balance for a specific employee
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Sync result</returns>
    Task<ApiResponse> SyncEmployeeLeaveBalanceAsync(Guid employeeId);

    /// <summary>
    /// Sync leave balances for all employees in an organization
    /// </summary>
    /// <param name="organizationId">Organization ID</param>
    /// <returns>Sync result</returns>
    Task<ApiResponse> SyncOrganizationLeaveBalancesAsync(Guid organizationId);

    /// <summary>
    /// Sync leave balances for all employees in the system
    /// </summary>
    /// <returns>Sync result</returns>
    Task<ApiResponse> SyncAllLeaveBalancesAsync();
}
