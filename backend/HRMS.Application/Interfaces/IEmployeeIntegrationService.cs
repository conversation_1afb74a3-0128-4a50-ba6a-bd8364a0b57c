using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IEmployeeIntegrationService
{
    System.Threading.Tasks.Task<ApiResponse> OnEmployeeCreatedAsync(Guid employeeId);
    System.Threading.Tasks.Task<ApiResponse> OnEmployeeUpdatedAsync(Guid employeeId, EmployeeUpdateContext context);
    System.Threading.Tasks.Task<ApiResponse> OnEmployeeDeactivatedAsync(Guid employeeId);
    System.Threading.Tasks.Task<ApiResponse<EmployeeIntegrationSummaryDto>> GetEmployeeIntegrationSummaryAsync(Guid employeeId);
}

public class EmployeeUpdateContext
{
    public bool DepartmentChanged { get; set; }
    public string? NewDepartment { get; set; }
    public bool RoleChanged { get; set; }
    public string? NewRole { get; set; }
    public bool SalaryChanged { get; set; }
    public decimal? NewSalary { get; set; }
    public bool ManagerChanged { get; set; }
    public Guid? NewManagerId { get; set; }
}

public class EmployeeIntegrationSummaryDto
{
    public Guid EmployeeId { get; set; }
    public string EmployeeName { get; set; } = string.Empty;
    public AttendanceIntegrationSummaryDto AttendanceSummary { get; set; } = new();
    public LeaveIntegrationSummaryDto LeaveSummary { get; set; } = new();
    public TaskIntegrationSummaryDto TaskSummary { get; set; } = new();
    public PayrollIntegrationSummaryDto PayrollSummary { get; set; } = new();
}

public class AttendanceIntegrationSummaryDto
{
    public int TotalDaysPresent { get; set; }
    public int TotalDaysAbsent { get; set; }
    public double AverageWorkHours { get; set; }
    public DateTime? LastCheckIn { get; set; }
}

public class LeaveIntegrationSummaryDto
{
    public int TotalLeaveBalance { get; set; }
    public int UsedLeaves { get; set; }
    public int PendingRequests { get; set; }
    public int ApprovedRequests { get; set; }
}

public class TaskIntegrationSummaryDto
{
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int PendingTasks { get; set; }
    public int InProgressTasks { get; set; }
    public int OverdueTasks { get; set; }
}

public class PayrollIntegrationSummaryDto
{
    public decimal BaseSalary { get; set; }
    public decimal AnnualCTC { get; set; }
    public DateTime? LastPayrollDate { get; set; }
    public decimal YtdEarnings { get; set; }
}
