using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IEmployeeManagementService
{
    Task<ApiResponse<PaginatedResponse<EmployeeDto>>> GetEmployeesAsync(GetEmployeesRequestDto request);
    Task<ApiResponse<EmployeeDto>> GetEmployeeByIdAsync(Guid employeeId);
    Task<ApiResponse<EmployeeDto>> CreateEmployeeAsync(CreateEmployeeRequestDto request);
    Task<ApiResponse<EmployeeDto>> UpdateEmployeeAsync(Guid employeeId, UpdateEmployeeRequestDto request);
    Task<ApiResponse> DeleteEmployeeAsync(Guid employeeId);
    Task<ApiResponse<OrganizationHierarchyDto>> GetOrganizationHierarchyAsync();
    Task<ApiResponse<List<EmployeeDto>>> GetEmployeesByManagerAsync(Guid managerId);
    Task<ApiResponse<List<EmployeeDto>>> GetEmployeesByDepartmentAsync(string department);
    Task<ApiResponse<EmployeeStatsDto>> GetEmployeeStatsAsync();
    Task<ApiResponse<bool>> ValidateHierarchyAssignmentAsync(Guid employeeId, Guid managerId);
    Task<ApiResponse<EmployeeDto>> GenerateCredentialsAsync(Guid employeeId);
    Task<ApiResponse<EmployeeDto>> GenerateCredentialsAsync(Guid employeeId, GenerateCredentialsRequestDto request);
    Task<ApiResponse<EmployeeDto>> ChangePasswordAsync(Guid employeeId, ChangePasswordRequestDto request);
}
