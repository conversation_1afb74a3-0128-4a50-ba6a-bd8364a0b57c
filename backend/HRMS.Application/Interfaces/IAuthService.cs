using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IAuthService
{
    Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginRequestDto request);
    Task<ApiResponse<OrganizationDto>> RegisterOrganizationAsync(OrganizationRegistrationDto request);
    Task<ApiResponse<RefreshTokenResponseDto>> RefreshTokenAsync(RefreshTokenRequestDto request);
    Task<ApiResponse> LogoutAsync(string userId);
    Task<ApiResponse<UserDto>> GetCurrentUserAsync(string userId);
    Task<ApiResponse<UserDto>> UpdateUserProfileAsync(string userId, UpdateUserProfileDto request);
    Task<ApiResponse<PasswordResetResponseDto>> ChangePasswordAsync(string userId, ChangePasswordRequestDto request);
    Task<ApiResponse<PasswordResetResponseDto>> ResetUserPasswordAsync(string adminUserId, ResetPasswordRequestDto request);
}



public interface IUserService
{
    Task<ApiResponse<PaginatedResponse<UserDto>>> GetUsersAsync(GetUsersRequestDto request);
    Task<ApiResponse<UserDto>> CreateEmployeeAsync(CreateEmployeeDto request);
    Task<ApiResponse<UserDto>> GetUserByIdAsync(Guid userId);
    Task<ApiResponse<UserDto>> UpdateUserAsync(Guid userId, UpdateUserDto request);
    Task<ApiResponse> DeleteUserAsync(Guid userId);
}

public class UpdateUserProfileDto
{
    public string? Name { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? AvatarUrl { get; set; }
}

public class GetUsersRequestDto
{
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 20;
    public string? Department { get; set; }
    public string? Role { get; set; }
    public string? Search { get; set; }
}

public class CreateEmployeeDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Role { get; set; } = "employee";
    public EmployeeDetailsCreateDto EmployeeDetails { get; set; } = new();
}

public class EmployeeDetailsCreateDto
{
    public string EmployeeId { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public Guid? ManagerId { get; set; }
    public DateTime JoinDate { get; set; }
    public string EmploymentType { get; set; } = "full-time";
    public decimal? BaseSalary { get; set; }
    public decimal? AnnualCTC { get; set; }
}

public class UpdateUserDto
{
    public string? Name { get; set; }
    public string? Role { get; set; }
    public bool? IsActive { get; set; }
    public EmployeeDetailsUpdateDto? EmployeeDetails { get; set; }
}

public class EmployeeDetailsUpdateDto
{
    public string? JobTitle { get; set; }
    public string? Department { get; set; }
    public Guid? ManagerId { get; set; }
    public string? EmploymentType { get; set; }
    public string? EmploymentStatus { get; set; }
    public string? WorkLocation { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public decimal? BaseSalary { get; set; }
    public decimal? AnnualCTC { get; set; }
}
