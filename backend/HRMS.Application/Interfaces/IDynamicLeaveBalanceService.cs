using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

/// <summary>
/// Interface for dynamic leave balance calculation service
/// </summary>
public interface IDynamicLeaveBalanceService
{
    /// <summary>
    /// Calculate dynamic leave balance for an employee based on their join date
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Dynamic leave balance calculation result</returns>
    Task<ApiResponse<DynamicLeaveBalanceDto>> CalculateLeaveBalanceAsync(Guid employeeId);

    /// <summary>
    /// Calculate leave balance for multiple employees
    /// </summary>
    /// <param name="employeeIds">List of employee IDs</param>
    /// <returns>Dictionary of employee ID to leave balance</returns>
    Task<ApiResponse<Dictionary<Guid, DynamicLeaveBalanceDto>>> CalculateLeaveBalancesAsync(IEnumerable<Guid> employeeIds);

    /// <summary>
    /// Get leave balance summary for dashboard display
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Leave balance summary</returns>
    Task<ApiResponse<LeaveBalanceSummaryDto>> GetLeaveBalanceSummaryAsync(Guid employeeId);
}
