using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface ITaskService
{
    Task<ApiResponse<TasksResponseDto>> GetTasksAsync(GetTasksRequestDto request);
    Task<ApiResponse<CreateTaskResponseDto>> CreateTaskAsync(string userId, CreateTaskRequestDto request);
    Task<ApiResponse<UpdateTaskProgressResponseDto>> UpdateTaskProgressAsync(Guid taskId, string userId, UpdateTaskProgressRequestDto request);
    Task<ApiResponse<AddTaskCommentResponseDto>> AddTaskCommentAsync(Guid taskId, string userId, AddTaskCommentRequestDto request);

    // Organization Admin specific methods
    Task<ApiResponse<OrganizationTasksResponseDto>> GetOrganizationTasksAsync(string organizationId, GetOrganizationTasksDto request);
    Task<ApiResponse<TaskStatisticsResponseDto>> GetTaskStatisticsAsync(string organizationId, GetTaskStatisticsRequestDto request);
    Task<ApiResponse<CreateTaskResponseDto>> AssignTaskAsync(string adminUserId, AssignTaskRequestDto request);
    Task<ApiResponse<TaskUpdatesResponseDto>> GetTaskUpdatesAsync(string organizationId, GetTaskUpdatesRequestDto request);
    Task<ApiResponse<TaskDto>> UpdateTaskStatusAsync(Guid taskId, string adminUserId, UpdateTaskStatusDto request);

    // Employee self-task management methods
    Task<ApiResponse<CreateSelfTaskResponseDto>> CreateSelfTaskAsync(string userId, CreateSelfTaskRequestDto request);
    Task<ApiResponse<TasksResponseDto>> GetEmployeeTasksAsync(string userId, GetEmployeeTasksRequestDto request);
    Task<ApiResponse<CreateDailyUpdateResponseDto>> CreateDailyUpdateAsync(string userId, CreateDailyUpdateRequestDto request);
    Task<ApiResponse<TaskUpdatesResponseDto>> GetEmployeeDailyUpdatesAsync(string userId, GetDailyUpdatesRequestDto request);

    // Manager visibility methods
    Task<ApiResponse<TasksResponseDto>> GetManagerVisibleTasksAsync(string managerId, GetManagerTasksRequestDto request);
    Task<ApiResponse<TaskUpdatesResponseDto>> GetManagerVisibleUpdatesAsync(string managerId, GetDailyUpdatesRequestDto request);

    // Role-based access control methods
    Task<bool> CanUserViewTaskAsync(string userId, Guid taskId);
    Task<bool> CanUserEditTaskAsync(string userId, Guid taskId);
}
