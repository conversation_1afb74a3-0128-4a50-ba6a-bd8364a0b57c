using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IEmployeeService
{
    Task<ApiResponse<EmployeeDashboardDto>> GetDashboardAsync(Guid employeeId);
    Task<ApiResponse<EmployeeProfileSummaryDto>> GetProfileSummaryAsync(Guid employeeId);
    Task<ApiResponse<List<EmployeeLeaveBalanceDto>>> GetLeaveBalancesAsync(Guid employeeId);
    Task<ApiResponse<EmployeeTaskSummaryDto>> GetTaskSummaryAsync(Guid employeeId);
    Task<ApiResponse<EmployeeAttendanceSummaryDto>> GetAttendanceSummaryAsync(Guid employeeId);
    Task<ApiResponse<EmployeePerformanceDto>> GetPerformanceDataAsync(Guid employeeId);
    Task<ApiResponse<List<EmployeeActivityDto>>> GetRecentActivitiesAsync(Guid employeeId, int limit = 10);
    Task<ApiResponse<List<UpcomingTaskDto>>> GetUpcomingTasksAsync(Guid employeeId, int limit = 5);
}
