using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface ILeaveService
{
    Task<ApiResponse<LeaveBalanceResponseDto>> GetLeaveBalanceAsync(GetLeaveBalanceRequestDto request);
    Task<ApiResponse<LeaveRequestResponseDto>> ApplyLeaveAsync(string userId, ApplyLeaveRequestDto request);
    Task<ApiResponse<LeaveRequestsResponseDto>> GetLeaveRequestsAsync(GetLeaveRequestsDto request);
    Task<ApiResponse<LeaveRequestDto>> UpdateLeaveStatusAsync(Guid leaveRequestId, string userId, UpdateLeaveStatusDto request);
    Task<ApiResponse<LeaveTypesResponseDto>> GetLeaveTypesAsync(string organizationId);

    // Organization Admin specific methods
    Task<ApiResponse<OrganizationLeaveRequestsResponseDto>> GetOrganizationLeaveRequestsAsync(string organizationId, GetOrganizationLeaveRequestsDto request);
    Task<ApiResponse<LeaveStatisticsResponseDto>> GetLeaveStatisticsAsync(string organizationId, GetLeaveStatisticsRequestDto request);
    Task<ApiResponse<LeaveRequestDto>> ApproveRejectLeaveAsync(Guid leaveRequestId, string adminUserId, ApproveRejectLeaveDto request);
}
