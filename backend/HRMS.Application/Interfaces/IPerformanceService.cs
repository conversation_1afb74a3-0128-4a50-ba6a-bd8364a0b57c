using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IPerformanceService
{
    Task<ApiResponse<PerformanceReviewsResponseDto>> GetPerformanceReviewsAsync(GetPerformanceReviewsRequestDto request);
    Task<ApiResponse<CreatePerformanceReviewResponseDto>> CreatePerformanceReviewAsync(string userId, CreatePerformanceReviewRequestDto request);
}
