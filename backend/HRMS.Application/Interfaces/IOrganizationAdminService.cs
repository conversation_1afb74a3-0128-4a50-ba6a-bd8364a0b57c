using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IOrganizationAdminService
{
    Task<ApiResponse<OrganizationDashboardDto>> GetDashboardAsync();
    Task<ApiResponse<OrganizationDashboardDto>> GetOrganizationDashboardAsync(string userId);
    Task<ApiResponse<BillingInformationDto>> GetBillingInformationAsync(string userId);

    // Employee dashboard access for admins
    Task<ApiResponse<EmployeeDashboardDto>> GetEmployeeDashboardAsync(Guid employeeId, string organizationId);
    Task<ApiResponse<EmployeeProfileSummaryDto>> GetEmployeeProfileAsync(Guid employeeId, string organizationId);
    Task<ApiResponse<EmployeeAttendanceSummaryDto>> GetEmployeeAttendanceAsync(Guid employeeId, string organizationId);
}
