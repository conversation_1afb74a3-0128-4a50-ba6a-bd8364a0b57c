using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IRecruitmentService
{
    Task<ApiResponse<JobPostingsResponseDto>> GetJobPostingsAsync(GetJobPostingsRequestDto request);
    Task<ApiResponse<CreateJobPostingResponseDto>> CreateJobPostingAsync(string userId, CreateJobPostingRequestDto request);
    Task<ApiResponse<JobApplicationsResponseDto>> GetJobApplicationsAsync(Guid jobPostingId, GetJobApplicationsRequestDto request);
}
