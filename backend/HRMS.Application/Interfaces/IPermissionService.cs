using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IPermissionService
{
    System.Threading.Tasks.Task<ApiResponse<List<PermissionDto>>> GetAllPermissionsAsync();
    System.Threading.Tasks.Task<ApiResponse<List<RoleDto>>> GetRolesByOrganizationAsync(Guid organizationId);
    System.Threading.Tasks.Task<ApiResponse<RoleDto>> CreateRoleAsync(CreateRoleRequestDto request);
    System.Threading.Tasks.Task<ApiResponse<RoleDto>> UpdateRoleAsync(Guid roleId, UpdateRoleRequestDto request);
    System.Threading.Tasks.Task<ApiResponse> DeleteRoleAsync(Guid roleId);
    System.Threading.Tasks.Task<ApiResponse<List<EmployeeRoleDto>>> GetEmployeeRolesAsync(Guid employeeDetailId);
    System.Threading.Tasks.Task<ApiResponse> AssignRoleToEmployeeAsync(AssignRoleRequestDto request);
    System.Threading.Tasks.Task<ApiResponse> RevokeRoleFromEmployeeAsync(Guid employeeDetailId, Guid roleId);
    System.Threading.Tasks.Task<ApiResponse<bool>> CheckPermissionAsync(Guid employeeDetailId, string module, string action);
}
