using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface ISuperAdminService
{
    Task<ApiResponse<SuperAdminDashboardDto>> GetDashboardAsync();
    Task<ApiResponse<OrganizationsResponseDto>> GetOrganizationsAsync(GetOrganizationsRequestDto request);
    Task<ApiResponse<OrganizationDetailsDto>> GetOrganizationDetailsAsync(Guid organizationId);
    Task<ApiResponse<CreateOrganizationResponseDto>> CreateOrganizationAsync(CreateOrganizationRequestDto request);
    Task<ApiResponse<OrganizationDetailsDto>> UpdateOrganizationDetailsAsync(Guid organizationId, UpdateOrganizationDetailsRequestDto request);
    Task<ApiResponse<AdminCredentialsDto>> GetOrganizationAdminCredentialsAsync(Guid organizationId);
    Task<ApiResponse<AdminCredentialsDto>> ResetOrganizationAdminPasswordAsync(Guid organizationId);
    Task<ApiResponse> UpdateUserPasswordAsync(Guid userId, string newPassword);
    Task<ApiResponse<UpdateOrganizationStatusResponseDto>> UpdateOrganizationStatusAsync(Guid organizationId, UpdateOrganizationStatusRequestDto request);
    Task<ApiResponse> DeleteOrganizationAsync(Guid organizationId, bool force = false);
    Task<ApiResponse<SystemAnalyticsDto>> GetSystemAnalyticsAsync(GetSystemAnalyticsRequestDto request);
    Task<ApiResponse<SystemSettingsDto>> GetSystemSettingsAsync();
    Task<ApiResponse<SystemSettingsDto>> UpdateSystemSettingsAsync(UpdateSystemSettingsRequestDto request);
}
