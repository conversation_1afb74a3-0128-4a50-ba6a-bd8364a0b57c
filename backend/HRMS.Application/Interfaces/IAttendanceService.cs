using HRMS.Application.Common;
using HRMS.Application.DTOs;

namespace HRMS.Application.Interfaces;

public interface IAttendanceService
{
    Task<ApiResponse<AttendanceResponseDto>> CheckInAsync(string userId, CheckInRequestDto request);
    Task<ApiResponse<AttendanceResponseDto>> CheckOutAsync(string userId, CheckOutRequestDto request);
    Task<ApiResponse<AttendanceRecordsResponseDto>> GetAttendanceRecordsAsync(GetAttendanceRequestDto request);
    Task<ApiResponse<OrganizationAttendanceResponseDto>> GetOrganizationAttendanceAsync(GetOrganizationAttendanceRequestDto request);
}
