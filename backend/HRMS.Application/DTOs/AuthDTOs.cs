using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class LoginRequestDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty;
}

public class LoginResponseDto
{
    public UserDto User { get; set; } = new();
    public string Token { get; set; } = string.Empty;
    public int ExpiresIn { get; set; }
}

public class OrganizationRegistrationDto
{
    [Required]
    public OrganizationCreateDto Organization { get; set; } = new();
    
    [Required]
    public AdminUserCreateDto Admin { get; set; } = new();
}

public class OrganizationCreateDto
{
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string Domain { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Industry { get; set; } = string.Empty;
}

public class AdminUserCreateDto
{
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string Password { get; set; } = string.Empty;
}

public class RefreshTokenRequestDto
{
    [Required]
    public string RefreshToken { get; set; } = string.Empty;
}

public class ChangePasswordRequestDto
{
    [Required]
    public string CurrentPassword { get; set; } = string.Empty;

    [Required]
    [MinLength(8)]
    public string NewPassword { get; set; } = string.Empty;
}

public class ResetPasswordRequestDto
{
    [Required]
    public Guid UserId { get; set; }

    [Required]
    [MinLength(8)]
    public string NewPassword { get; set; } = string.Empty;

    public bool RequirePasswordReset { get; set; } = true;
}

public class PasswordResetResponseDto
{
    public string Message { get; set; } = string.Empty;
    public bool RequirePasswordReset { get; set; }
}

public class RefreshTokenResponseDto
{
    public string Token { get; set; } = string.Empty;
    public int ExpiresIn { get; set; }
}

public class UserDto
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string? AvatarUrl { get; set; }
    public Guid? OrganizationId { get; set; }
    public OrganizationDto? Organization { get; set; }
    public EmployeeDetailsDto? EmployeeDetails { get; set; }
}

public class OrganizationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public int EmployeeCount { get; set; }
}

public class EmployeeDetailsDto
{
    public string EmployeeId { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string? Manager { get; set; }
    public DateTime JoinDate { get; set; }
    public string EmploymentType { get; set; } = string.Empty;
    public string EmploymentStatus { get; set; } = string.Empty;
    public string? WorkLocation { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public decimal? BaseSalary { get; set; }
    public decimal? AnnualCTC { get; set; }
    public DateTime? NextSalaryReview { get; set; }
    public decimal? PerformanceRating { get; set; }
    public string? TotalExperience { get; set; }

    // Additional personal information
    public string? EmergencyContactName { get; set; }
    public string? EmergencyContactPhone { get; set; }
    public string? EmergencyContactRelation { get; set; }
    public string? BloodGroup { get; set; }
    public string? MaritalStatus { get; set; }
    public string? Gender { get; set; }
    public string? Nationality { get; set; }

    // Banking information
    public string? BankAccountNumber { get; set; }
    public string? BankIFSC { get; set; }
    public string? BankName { get; set; }

    // Government IDs
    public string? PAN { get; set; }
    public string? Aadhar { get; set; }

    // Employment dates
    public DateTime? ProbationEndDate { get; set; }
    public DateTime? ConfirmationDate { get; set; }
    public DateTime? ResignationDate { get; set; }
    public DateTime? LastWorkingDate { get; set; }
    public string? ResignationReason { get; set; }

    public List<EducationDto> Education { get; set; } = new();
    public List<SkillDto> Skills { get; set; } = new();

    // Login credential fields for automatic credential generation
    public string? LoginUsername { get; set; } // Generated login username/email
    public string? LoginTemporaryPassword { get; set; } // Generated temporary password (only visible to admins)
    public bool RequirePasswordReset { get; set; } = true; // Flag for first-time login password reset
    public DateTime? PasswordGeneratedAt { get; set; } // When the credentials were generated
}

public class EducationDto
{
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
    public string Year { get; set; } = string.Empty;
    public string? FieldOfStudy { get; set; }
    public string? Grade { get; set; }
}

public class SkillDto
{
    public string Name { get; set; } = string.Empty;
    public int Level { get; set; }
    public string? Category { get; set; }
    public int? YearsOfExperience { get; set; }
}
