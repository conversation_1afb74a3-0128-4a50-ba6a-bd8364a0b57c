using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class JobPostingDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string EmploymentType { get; set; } = string.Empty;
    public string ExperienceLevel { get; set; } = string.Empty;
    public decimal? SalaryRangeMin { get; set; }
    public decimal? SalaryRangeMax { get; set; }
    public string Status { get; set; } = string.Empty;
    public int ApplicationsCount { get; set; }
    public DateTime PostedDate { get; set; }
    public string? Description { get; set; }
    public string? Requirements { get; set; }
}

public class GetJobPostingsRequestDto
{
    public string? Status { get; set; }
    public string? Department { get; set; }
}

public class JobPostingsResponseDto
{
    public List<JobPostingDto> Jobs { get; set; } = new();
}

public class CreateJobPostingRequestDto
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Department { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public string? Requirements { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Location { get; set; } = string.Empty;
    
    [Required]
    public string EmploymentType { get; set; } = string.Empty;
    
    [Required]
    public string ExperienceLevel { get; set; } = string.Empty;
    
    public decimal? SalaryRangeMin { get; set; }
    
    public decimal? SalaryRangeMax { get; set; }
    
    public DateTime? ApplicationDeadline { get; set; }
}

public class CreateJobPostingResponseDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class JobApplicationDto
{
    public Guid Id { get; set; }
    public string CandidateName { get; set; } = string.Empty;
    public string CandidateEmail { get; set; } = string.Empty;
    public string? CandidatePhone { get; set; }
    public int? ExperienceYears { get; set; }
    public decimal? CurrentSalary { get; set; }
    public decimal? ExpectedSalary { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime AppliedDate { get; set; }
    public string? ResumeUrl { get; set; }
}

public class GetJobApplicationsRequestDto
{
    public string? Status { get; set; }
}

public class JobApplicationsResponseDto
{
    public List<JobApplicationDto> Applications { get; set; } = new();
    public PipelineSummaryDto PipelineSummary { get; set; } = new();
}

public class PipelineSummaryDto
{
    public int Applied { get; set; }
    public int Screening { get; set; }
    public int Interview { get; set; }
    public int Selected { get; set; }
    public int Rejected { get; set; }
}
