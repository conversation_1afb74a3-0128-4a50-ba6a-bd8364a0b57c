namespace HRMS.Application.DTOs;

// Request DTOs
public class GetEmployeesRequestDto
{
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 10;
    public string? Department { get; set; }
    public string? Role { get; set; }
    public string? EmploymentType { get; set; }
    public string? EmploymentStatus { get; set; }
    public string? Search { get; set; }
    public Guid? ManagerId { get; set; }
    public string? SortBy { get; set; } = "name";
    public string? SortOrder { get; set; } = "asc";
}

public class CreateEmployeeRequestDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Role { get; set; } = "employee";
    public CreateEmployeeDetailsDto EmployeeDetails { get; set; } = new();
}

public class UpdateEmployeeRequestDto
{
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Role { get; set; }
    public bool? IsActive { get; set; }
    public UpdateEmployeeDetailsDto? EmployeeDetails { get; set; }
}

public class ValidateHierarchyRequestDto
{
    public Guid EmployeeId { get; set; }
    public Guid ManagerId { get; set; }
}

public class CreateEmployeeDetailsDto
{
    public string EmployeeId { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public Guid? ManagerId { get; set; }
    public DateTime JoinDate { get; set; } = DateTime.UtcNow;
    public string EmploymentType { get; set; } = "full-time";
    public string? WorkLocation { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public decimal? BaseSalary { get; set; }
    public decimal? AnnualCTC { get; set; }
    
    // Additional personal information
    public string? EmergencyContactName { get; set; }
    public string? EmergencyContactPhone { get; set; }
    public string? EmergencyContactRelation { get; set; }
    public string? BloodGroup { get; set; }
    public string? MaritalStatus { get; set; }
    public string? Gender { get; set; }
    public string? Nationality { get; set; }
    
    // Banking information
    public string? BankAccountNumber { get; set; }
    public string? BankIFSC { get; set; }
    public string? BankName { get; set; }
    
    // Government IDs
    public string? PAN { get; set; }
    public string? Aadhar { get; set; }
    
    // Employment dates
    public DateTime? ProbationEndDate { get; set; }
    public DateTime? ConfirmationDate { get; set; }
}

public class UpdateEmployeeDetailsDto
{
    public string? JobTitle { get; set; }
    public string? Department { get; set; }
    public Guid? ManagerId { get; set; }
    public string? EmploymentType { get; set; }
    public string? EmploymentStatus { get; set; }
    public string? WorkLocation { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public decimal? BaseSalary { get; set; }
    public decimal? AnnualCTC { get; set; }
    
    // Additional personal information
    public string? EmergencyContactName { get; set; }
    public string? EmergencyContactPhone { get; set; }
    public string? EmergencyContactRelation { get; set; }
    public string? BloodGroup { get; set; }
    public string? MaritalStatus { get; set; }
    public string? Gender { get; set; }
    public string? Nationality { get; set; }
    
    // Banking information
    public string? BankAccountNumber { get; set; }
    public string? BankIFSC { get; set; }
    public string? BankName { get; set; }
    
    // Government IDs
    public string? PAN { get; set; }
    public string? Aadhar { get; set; }
    
    // Employment dates
    public DateTime? ProbationEndDate { get; set; }
    public DateTime? ConfirmationDate { get; set; }
    public DateTime? ResignationDate { get; set; }
    public DateTime? LastWorkingDate { get; set; }
    public string? ResignationReason { get; set; }
}

// Response DTOs
public class EmployeeDto
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string? AvatarUrl { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastLogin { get; set; }
    public DateTime CreatedAt { get; set; }
    public OrganizationDto? Organization { get; set; }
    public EmployeeDetailsDto? EmployeeDetails { get; set; }
    public string? TempPassword { get; set; } // Only for newly created employees

    // Login credential fields for automatic credential generation
    public string? LoginUsername { get; set; } // Generated login username/email
    public string? LoginTemporaryPassword { get; set; } // Generated temporary password (plain text, only for response)
    public bool RequirePasswordReset { get; set; } = true; // Flag for first-time login password reset
    public DateTime? PasswordGeneratedAt { get; set; } // When the credentials were generated
}

public class GenerateCredentialsRequestDto
{
    /// <summary>
    /// Optional custom password provided by admin. If not provided, a secure password will be auto-generated.
    /// </summary>
    public string? CustomPassword { get; set; }
}

// Note: EmployeeDetailsDto, EducationDto, SkillDto, and ChangePasswordRequestDto are defined in AuthDTOs.cs to avoid duplication

// Organization Hierarchy DTOs
public class OrganizationHierarchyDto
{
    public List<DepartmentHierarchyDto> Departments { get; set; } = new();
}

public class DepartmentHierarchyDto
{
    public string Name { get; set; } = string.Empty;
    public int EmployeeCount { get; set; }
    public string Manager { get; set; } = string.Empty;
    public List<EmployeeHierarchyDto> Employees { get; set; } = new();
}

public class EmployeeHierarchyDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string Level { get; set; } = string.Empty;
    public string ReportingTo { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
}

// Statistics DTOs
public class EmployeeStatsDto
{
    public int TotalEmployees { get; set; }
    public int ActiveEmployees { get; set; }
    public int InactiveEmployees { get; set; }
    public int NewHiresThisMonth { get; set; }
    public Dictionary<string, int> DepartmentBreakdown { get; set; } = new();
    public Dictionary<string, int> EmploymentTypeBreakdown { get; set; } = new();
}
