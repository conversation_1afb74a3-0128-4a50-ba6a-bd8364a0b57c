using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class LeaveBalanceDto
{
    public string LeaveType { get; set; } = string.Empty;
    public int TotalAllocated { get; set; }
    public int UsedLeaves { get; set; }
    public int RemainingLeaves { get; set; }
    public int CarriedForward { get; set; }
}

public class GetLeaveBalanceRequestDto
{
    public Guid? UserId { get; set; }
    public int? Year { get; set; }
}

public class LeaveBalanceResponseDto
{
    public List<LeaveBalanceDto> Balances { get; set; } = new();
}

/// <summary>
/// DTO for dynamic leave balance calculation based on join date
/// </summary>
public class DynamicLeaveBalanceDto
{
    public Guid EmployeeId { get; set; }
    public DateTime JoinDate { get; set; }
    public DateTime CalculationDate { get; set; }
    public int InitialAllocation { get; set; } = 1;
    public int MonthlyAccrual { get; set; }
    public int TotalAllocated { get; set; }
    public int UsedLeaves { get; set; }
    public int PendingLeaves { get; set; }
    public int RemainingLeaves { get; set; }
    public int MonthsCompleted { get; set; }
    public DateTime NextAccrualDate { get; set; }
}

/// <summary>
/// DTO for leave balance summary display
/// </summary>
public class LeaveBalanceSummaryDto
{
    public int TotalLeaves { get; set; }
    public int UsedLeaves { get; set; }
    public int RemainingLeaves { get; set; }
    public int PendingLeaves { get; set; }
    public int MonthlyAccrual { get; set; } = 1;
    public DateTime NextAccrualDate { get; set; }
}

public class ApplyLeaveRequestDto
{
    // LeaveTypeId is no longer required for simplified monthly leave system
    public Guid? LeaveTypeId { get; set; }

    [Required]
    public DateTime FromDate { get; set; }

    [Required]
    public DateTime ToDate { get; set; }

    [Required]
    [MaxLength(50)]
    public string DurationType { get; set; } = "full-day";

    [Required]
    public string Reason { get; set; } = string.Empty;
}

public class LeaveRequestDto
{
    public Guid Id { get; set; }
    public UserSummaryDto User { get; set; } = new();
    public string LeaveType { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalDays { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime AppliedDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? Comments { get; set; }
}

public class UserSummaryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? EmployeeId { get; set; }
}

public class LeaveRequestResponseDto
{
    public Guid Id { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalDays { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class GetLeaveRequestsDto
{
    public Guid? UserId { get; set; }
    public string? Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class LeaveRequestsResponseDto
{
    public List<LeaveRequestDto> Requests { get; set; } = new();
}

public class UpdateLeaveStatusDto
{
    [Required]
    public string Status { get; set; } = string.Empty;

    public string? Comments { get; set; }
}

public class LeaveTypeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int MaxDaysPerYear { get; set; }
    public bool IsActive { get; set; }
}

public class LeaveTypesResponseDto
{
    public List<LeaveTypeDto> Types { get; set; } = new();
}

// Organization Admin specific DTOs
public class GetOrganizationLeaveRequestsDto
{
    public string? Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Department { get; set; }
    public Guid? EmployeeId { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class OrganizationLeaveRequestsResponseDto
{
    public List<LeaveRequestDto> Requests { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
}

public class ApproveRejectLeaveDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "approved" or "rejected"

    public string? Comments { get; set; }
}

public class GetLeaveStatisticsRequestDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Department { get; set; }
}

public class LeaveStatisticsDto
{
    public int TotalRequests { get; set; }
    public int PendingRequests { get; set; }
    public int ApprovedRequests { get; set; }
    public int RejectedRequests { get; set; }
    public int TotalLeaveDays { get; set; }
    public double AverageLeavePerEmployee { get; set; }
    public List<LeaveTypeStatisticsDto> LeaveTypeBreakdown { get; set; } = new();
    public List<DepartmentLeaveStatisticsDto> DepartmentBreakdown { get; set; } = new();
}

public class LeaveTypeStatisticsDto
{
    public string LeaveType { get; set; } = string.Empty;
    public int RequestCount { get; set; }
    public int TotalDays { get; set; }
}

public class DepartmentLeaveStatisticsDto
{
    public string Department { get; set; } = string.Empty;
    public int RequestCount { get; set; }
    public int TotalDays { get; set; }
    public double UtilizationRate { get; set; }
}

public class LeaveStatisticsResponseDto
{
    public LeaveStatisticsDto Statistics { get; set; } = new();
}

