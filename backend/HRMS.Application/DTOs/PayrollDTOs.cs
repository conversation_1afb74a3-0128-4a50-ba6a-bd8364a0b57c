namespace HRMS.Application.DTOs;

public class EmployeePayrollDto
{
    public CurrentMonthPayrollDto CurrentMonth { get; set; } = new();
    public YearToDateSummaryDto YtdSummary { get; set; } = new();
    public List<PayStubDto> PayStubs { get; set; } = new();
}

public class CurrentMonthPayrollDto
{
    public decimal GrossSalary { get; set; }
    public decimal NetSalary { get; set; }
    public decimal TotalDeductions { get; set; }
    public decimal TaxDeductions { get; set; }
    public decimal Benefits { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? PayDate { get; set; }
}

public class YearToDateSummaryDto
{
    public decimal YtdGross { get; set; }
    public decimal YtdNet { get; set; }
    public decimal YtdTax { get; set; }
}

public class PayStubDto
{
    public string Period { get; set; } = string.Empty;
    public decimal Gross { get; set; }
    public decimal Net { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? PayDate { get; set; }
}

public class GetEmployeePayrollRequestDto
{
    public int? Year { get; set; }
    public int? Month { get; set; }
}

public class EmployeeBenefitsDto
{
    public List<BenefitDto> Benefits { get; set; } = new();
}

public class BenefitDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal EmployeeContribution { get; set; }
    public decimal EmployerContribution { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime EffectiveFrom { get; set; }
}
