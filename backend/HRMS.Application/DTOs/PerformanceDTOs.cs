using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class PerformanceReviewDto
{
    public Guid Id { get; set; }
    public UserSummaryDto Employee { get; set; } = new();
    public UserSummaryDto Reviewer { get; set; } = new();
    public string ReviewPeriod { get; set; } = string.Empty;
    public decimal? OverallRating { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime ReviewDate { get; set; }
    public string? Feedback { get; set; }
    public List<PerformanceGoalDto> Goals { get; set; } = new();
}

public class PerformanceGoalDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int WeightPercentage { get; set; }
    public decimal? AchievedRating { get; set; }
    public string? Comments { get; set; }
}

public class GetPerformanceReviewsRequestDto
{
    public Guid? EmployeeId { get; set; }
    public Guid? ReviewerId { get; set; }
    public string? Status { get; set; }
    public string? Period { get; set; }
}

public class PerformanceReviewsResponseDto
{
    public List<PerformanceReviewDto> Reviews { get; set; } = new();
}

public class CreatePerformanceReviewRequestDto
{
    [Required]
    public Guid EmployeeId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string ReviewPeriod { get; set; } = string.Empty;
    
    [MaxLength(50)]
    public string ReviewType { get; set; } = "quarterly";
    
    [Required]
    public DateTime ReviewDate { get; set; }
    
    public List<CreatePerformanceGoalDto> Goals { get; set; } = new();
}

public class CreatePerformanceGoalDto
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Range(1, 100)]
    public int WeightPercentage { get; set; }
}

public class CreatePerformanceReviewResponseDto
{
    public Guid Id { get; set; }
    public Guid EmployeeId { get; set; }
    public string ReviewPeriod { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime ReviewDate { get; set; }
    public DateTime CreatedAt { get; set; }
}
