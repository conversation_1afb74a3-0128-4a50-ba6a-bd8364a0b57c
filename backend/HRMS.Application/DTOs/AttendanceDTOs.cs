using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class CheckInRequestDto
{
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    [MaxLength(255)]
    public string? Location { get; set; }
    
    public string? Notes { get; set; }
}

public class CheckOutRequestDto
{
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public string? Notes { get; set; }
}

public class AttendanceRecordDto
{
    public Guid Id { get; set; }
    public DateTime Date { get; set; }
    public DateTime? CheckInTime { get; set; }
    public DateTime? CheckOutTime { get; set; }
    public decimal? TotalHours { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Location { get; set; }
    public string? Notes { get; set; }
}

public class AttendanceResponseDto
{
    public Guid Id { get; set; }
    public DateTime Date { get; set; }
    public DateTime? CheckInTime { get; set; }
    public DateTime? CheckOutTime { get; set; }
    public decimal? TotalHours { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Location { get; set; }
    public string? Notes { get; set; }
    public string Message { get; set; } = string.Empty;
    public string TimeZone { get; set; } = "IST"; // Indian Standard Time
}

public class GetAttendanceRequestDto
{
    public Guid? UserId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Status { get; set; }
}

public class AttendanceRecordsResponseDto
{
    public List<AttendanceRecordDto> Records { get; set; } = new();
    public AttendanceSummaryDto Summary { get; set; } = new();
}

public class AttendanceSummaryDto
{
    public int TotalDays { get; set; }
    public int PresentDays { get; set; }
    public int AbsentDays { get; set; }
    public int LateDays { get; set; }
    public decimal TotalHours { get; set; }
}

// Organization-wide attendance DTOs
public class GetOrganizationAttendanceRequestDto
{
    public DateTime? Date { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Department { get; set; }
    public string? Status { get; set; }
}

public class EmployeeAttendanceDto
{
    public Guid UserId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string EmployeeId { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // present, absent, late, on_leave, weekend
    public DateTime? CheckInTime { get; set; }
    public DateTime? CheckOutTime { get; set; }
    public decimal? TotalHours { get; set; }
    public string? Location { get; set; }
    public string? Notes { get; set; }
    public List<AttendanceRecordDto> AttendanceRecords { get; set; } = new();
}

public class OrganizationAttendanceStatsDto
{
    public int TotalEmployees { get; set; }
    public int PresentToday { get; set; }
    public int AbsentToday { get; set; }
    public int LateToday { get; set; }
    public int OnLeaveToday { get; set; }
    public double AttendanceRate { get; set; }
}

public class OrganizationAttendanceResponseDto
{
    public DateTime Date { get; set; }
    public List<EmployeeAttendanceDto> Employees { get; set; } = new();
    public OrganizationAttendanceStatsDto Statistics { get; set; } = new();
}
