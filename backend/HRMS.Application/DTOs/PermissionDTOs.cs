namespace HRMS.Application.DTOs;

// Permission DTOs
public class PermissionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Module { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

// Role DTOs
public class RoleDto
{
    public Guid Id { get; set; }
    public Guid OrganizationId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsSystemRole { get; set; }
    public bool IsActive { get; set; }
    public List<PermissionDto> Permissions { get; set; } = new();
}

public class CreateRoleRequestDto
{
    public Guid OrganizationId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<Guid>? PermissionIds { get; set; }
}

public class UpdateRoleRequestDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<Guid>? PermissionIds { get; set; }
}

// Employee Role DTOs
public class EmployeeRoleDto
{
    public Guid Id { get; set; }
    public Guid EmployeeDetailId { get; set; }
    public Guid RoleId { get; set; }
    public string RoleName { get; set; } = string.Empty;
    public DateTime AssignedDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool IsActive { get; set; }
}

public class AssignRoleRequestDto
{
    public Guid EmployeeDetailId { get; set; }
    public Guid RoleId { get; set; }
    public DateTime? ExpiryDate { get; set; }
}

// Employee Permission DTOs
public class EmployeePermissionDto
{
    public Guid Id { get; set; }
    public Guid EmployeeDetailId { get; set; }
    public Guid PermissionId { get; set; }
    public string PermissionName { get; set; } = string.Empty;
    public string Module { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public bool IsGranted { get; set; }
    public DateTime AssignedDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? Reason { get; set; }
}

public class AssignPermissionRequestDto
{
    public Guid EmployeeDetailId { get; set; }
    public Guid PermissionId { get; set; }
    public bool IsGranted { get; set; } = true;
    public DateTime? ExpiryDate { get; set; }
    public string? Reason { get; set; }
}

// Permission Check DTOs
public class CheckPermissionRequestDto
{
    public Guid EmployeeDetailId { get; set; }
    public string Module { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
}

public class PermissionCheckResultDto
{
    public bool HasPermission { get; set; }
    public string Source { get; set; } = string.Empty; // "role" or "direct"
    public string? RoleName { get; set; }
    public string? PermissionName { get; set; }
}

// System Permission Constants
public static class SystemPermissions
{
    public static class Modules
    {
        public const string Attendance = "attendance";
        public const string Leave = "leave";
        public const string Tasks = "tasks";
        public const string Payroll = "payroll";
        public const string Performance = "performance";
        public const string Recruitment = "recruitment";
        public const string EmployeeManagement = "employee_management";
        public const string Reports = "reports";
        public const string Settings = "settings";
    }

    public static class Actions
    {
        public const string Create = "create";
        public const string Read = "read";
        public const string Update = "update";
        public const string Delete = "delete";
        public const string Approve = "approve";
        public const string Reject = "reject";
        public const string Export = "export";
        public const string Import = "import";
        public const string Assign = "assign";
        public const string Manage = "manage";
    }

    public static readonly List<PermissionDefinition> DefaultPermissions = new()
    {
        // Attendance Permissions
        new(Modules.Attendance, Actions.Create, "Record Attendance", "Can record attendance for self"),
        new(Modules.Attendance, Actions.Read, "View Attendance", "Can view attendance records"),
        new(Modules.Attendance, Actions.Update, "Edit Attendance", "Can edit attendance records"),
        new(Modules.Attendance, Actions.Manage, "Manage Attendance", "Can manage all attendance records"),

        // Leave Permissions
        new(Modules.Leave, Actions.Create, "Apply Leave", "Can apply for leave"),
        new(Modules.Leave, Actions.Read, "View Leave", "Can view leave records"),
        new(Modules.Leave, Actions.Update, "Edit Leave", "Can edit leave requests"),
        new(Modules.Leave, Actions.Approve, "Approve Leave", "Can approve leave requests"),
        new(Modules.Leave, Actions.Reject, "Reject Leave", "Can reject leave requests"),
        new(Modules.Leave, Actions.Manage, "Manage Leave", "Can manage all leave records"),

        // Task Permissions
        new(Modules.Tasks, Actions.Create, "Create Tasks", "Can create tasks"),
        new(Modules.Tasks, Actions.Read, "View Tasks", "Can view tasks"),
        new(Modules.Tasks, Actions.Update, "Update Tasks", "Can update task progress"),
        new(Modules.Tasks, Actions.Assign, "Assign Tasks", "Can assign tasks to others"),
        new(Modules.Tasks, Actions.Manage, "Manage Tasks", "Can manage all tasks"),

        // Payroll Permissions
        new(Modules.Payroll, Actions.Read, "View Payroll", "Can view payroll information"),
        new(Modules.Payroll, Actions.Manage, "Manage Payroll", "Can manage payroll processing"),
        new(Modules.Payroll, Actions.Export, "Export Payroll", "Can export payroll data"),

        // Performance Permissions
        new(Modules.Performance, Actions.Read, "View Performance", "Can view performance reviews"),
        new(Modules.Performance, Actions.Create, "Create Reviews", "Can create performance reviews"),
        new(Modules.Performance, Actions.Update, "Update Reviews", "Can update performance reviews"),
        new(Modules.Performance, Actions.Manage, "Manage Performance", "Can manage all performance data"),

        // Employee Management Permissions
        new(Modules.EmployeeManagement, Actions.Create, "Add Employees", "Can add new employees"),
        new(Modules.EmployeeManagement, Actions.Read, "View Employees", "Can view employee information"),
        new(Modules.EmployeeManagement, Actions.Update, "Edit Employees", "Can edit employee information"),
        new(Modules.EmployeeManagement, Actions.Delete, "Remove Employees", "Can remove employees"),
        new(Modules.EmployeeManagement, Actions.Manage, "Manage Employees", "Can manage all employee data"),

        // Reports Permissions
        new(Modules.Reports, Actions.Read, "View Reports", "Can view reports"),
        new(Modules.Reports, Actions.Export, "Export Reports", "Can export reports"),
        new(Modules.Reports, Actions.Manage, "Manage Reports", "Can manage all reports"),

        // Settings Permissions
        new(Modules.Settings, Actions.Read, "View Settings", "Can view system settings"),
        new(Modules.Settings, Actions.Update, "Update Settings", "Can update system settings"),
        new(Modules.Settings, Actions.Manage, "Manage Settings", "Can manage all settings")
    };
}

public record PermissionDefinition(string Module, string Action, string Name, string Description)
{
    public string Code => $"{Module}_{Action}".ToUpperInvariant();
}
