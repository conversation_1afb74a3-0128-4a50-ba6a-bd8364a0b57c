using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class TaskDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int ProgressPercentage { get; set; }
    public UserSummaryDto AssignedTo { get; set; } = new();
    public UserSummaryDto AssignedBy { get; set; } = new();
    public DateTime? DueDate { get; set; }
    public decimal? EstimatedHours { get; set; }
    public decimal? ActualHours { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? Category { get; set; }
    public bool IsSelfCreated { get; set; }
    public string TaskType { get; set; } = string.Empty;
    public List<TaskUpdateDto> RecentUpdates { get; set; } = new();
}

public class GetTasksRequestDto
{
    public Guid? AssignedTo { get; set; }
    public string? Status { get; set; }
    public string? Priority { get; set; }
    public DateTime? DueDate { get; set; }
}

public class TasksResponseDto
{
    public List<TaskDto> Tasks { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
}

public class CreateTaskRequestDto
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;

    public string? Description { get; set; }

    [Required]
    public Guid AssignedTo { get; set; }

    public string Priority { get; set; } = "medium";

    public string? Category { get; set; }

    public decimal? EstimatedHours { get; set; }

    public DateTime? DueDate { get; set; }
}

public class CreateTaskResponseDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public UserSummaryDto AssignedTo { get; set; } = new();
    public DateTime? DueDate { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class UpdateTaskProgressRequestDto
{
    public int ProgressPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal? HoursSpent { get; set; }
    public string? UpdateNotes { get; set; }
    public string? Blockers { get; set; }
    public string? NextSteps { get; set; }
}

public class UpdateTaskProgressResponseDto
{
    public Guid Id { get; set; }
    public int ProgressPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal? ActualHours { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class AddTaskCommentRequestDto
{
    [Required]
    public string Comment { get; set; } = string.Empty;
}

public class TaskCommentDto
{
    public Guid Id { get; set; }
    public string Comment { get; set; } = string.Empty;
    public UserSummaryDto Author { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class AddTaskCommentResponseDto
{
    public Guid Id { get; set; }
    public string Comment { get; set; } = string.Empty;
    public UserSummaryDto Author { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

// Organization Admin specific DTOs
public class GetOrganizationTasksDto
{
    public string? Status { get; set; }
    public string? Priority { get; set; }
    public string? Category { get; set; }
    public string? Department { get; set; }
    public Guid? AssignedTo { get; set; }
    public Guid? AssignedBy { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public DateTime? DueDateTo { get; set; }
    public bool? Overdue { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class OrganizationTasksResponseDto
{
    public List<TaskDto> Tasks { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
}

public class AssignTaskRequestDto
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;

    public string? Description { get; set; }

    [Required]
    public Guid AssignedTo { get; set; }

    public string Priority { get; set; } = "medium";

    public string? Category { get; set; }

    public decimal? EstimatedHours { get; set; }

    public DateTime? DueDate { get; set; }
}

public class GetTaskStatisticsRequestDto
{
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    public string? Department { get; set; }
    public Guid? EmployeeId { get; set; }
}
public class TaskStatisticsResponseDto
{
    public TaskStatisticsDto Statistics { get; set; } = new();
}

public class TaskStatisticsDto
{
    public int TotalTasks { get; set; }
    public int PendingTasks { get; set; }
    public int InProgressTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int OverdueTasks { get; set; }
    public double AverageCompletionTime { get; set; }
    public double CompletionRate { get; set; }
    public List<TaskPriorityStatisticsDto> PriorityBreakdown { get; set; } = new();
    public List<TaskCategoryStatisticsDto> CategoryBreakdown { get; set; } = new();
    public List<DepartmentTaskStatisticsDto> DepartmentBreakdown { get; set; } = new();
    public List<EmployeeTaskStatisticsDto> EmployeeProductivity { get; set; } = new();
}

public class TaskPriorityStatisticsDto
{
    public string Priority { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Percentage { get; set; }
}

public class TaskCategoryStatisticsDto
{
    public string Category { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Percentage { get; set; }
}

public class DepartmentTaskStatisticsDto
{
    public string Department { get; set; } = string.Empty;
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public double CompletionRate { get; set; }
}

public class EmployeeTaskStatisticsDto
{
    public Guid EmployeeId { get; set; }
    public string EmployeeName { get; set; } = string.Empty;
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public double CompletionRate { get; set; }
    public double AverageCompletionTime { get; set; }
}

public class GetTaskUpdatesRequestDto
{
    public Guid? TaskId { get; set; }
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class TaskUpdateDto
{
    public Guid Id { get; set; }
    public Guid TaskId { get; set; }
    public string TaskTitle { get; set; } = string.Empty;
    public UserSummaryDto Employee { get; set; } = new();
    public int ProgressPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal? HoursSpent { get; set; }
    public string UpdateNotes { get; set; } = string.Empty;
    public string? Blockers { get; set; }
    public string? NextSteps { get; set; }
    public DateTime UpdateDate { get; set; }
    public string UpdateType { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class TaskUpdatesResponseDto
{
    public List<TaskUpdateDto> Updates { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
}

public class UpdateTaskStatusDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "pending", "in-progress", "completed", "cancelled"

    public string? Comments { get; set; }

    public int? ProgressPercentage { get; set; }
}

// New DTOs for employee self-task creation and daily updates

public class CreateSelfTaskRequestDto
{
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;

    public string? Description { get; set; }

    [Required]
    public string Priority { get; set; } = "medium"; // "low", "medium", "high", "urgent"

    public string? Category { get; set; }

    public decimal? EstimatedHours { get; set; }

    public DateTime? DueDate { get; set; }

    public string TaskType { get; set; } = "SelfCreated"; // "SelfCreated", "Personal", "Project"
}

public class CreateSelfTaskResponseDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string TaskType { get; set; } = string.Empty;
    public DateTime? DueDate { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class GetEmployeeTasksRequestDto
{
    public string? Status { get; set; }
    public string? Priority { get; set; }
    public string? TaskType { get; set; }
    public bool? IsSelfCreated { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public DateTime? DueDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class CreateDailyUpdateRequestDto
{
    [Required]
    public string TaskId { get; set; } = string.Empty;

    [Required]
    public string UpdateNotes { get; set; } = string.Empty;

    public int? ProgressPercentage { get; set; }

    public decimal? HoursSpent { get; set; }

    public string? Blockers { get; set; }

    public string? NextSteps { get; set; }

    public DateTime? UpdateDate { get; set; }

    public string UpdateType { get; set; } = "Daily";
}

public class CreateDailyUpdateResponseDto
{
    public Guid Id { get; set; }
    public Guid TaskId { get; set; }
    public string TaskTitle { get; set; } = string.Empty;
    public int ProgressPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal? HoursSpent { get; set; }
    public string UpdateNotes { get; set; } = string.Empty;
    public DateTime UpdateDate { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class GetManagerTasksRequestDto
{
    public Guid? EmployeeId { get; set; } // Specific employee or all direct reports
    public string? Status { get; set; }
    public string? Priority { get; set; }
    public string? TaskType { get; set; }
    public bool? IsSelfCreated { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public DateTime? DueDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class GetDailyUpdatesRequestDto
{
    public Guid? TaskId { get; set; }
    public Guid? EmployeeId { get; set; }
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    public string? UpdateType { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}