using System.ComponentModel.DataAnnotations;

namespace HRMS.Application.DTOs;

public class SuperAdminDashboardDto
{
    public SuperAdminMetricsDto Metrics { get; set; } = new();
    public List<SystemActivityDto> RecentActivities { get; set; } = new();
    public List<SystemHealthMetricDto> SystemHealth { get; set; } = new();
    public List<OrganizationSummaryDto> TopOrganizations { get; set; } = new();
}

public class SuperAdminMetricsDto
{
    public int TotalOrganizations { get; set; }
    public int ActiveOrganizations { get; set; }
    public int TotalUsers { get; set; }
    public decimal MonthlyRevenue { get; set; }
    public decimal SystemUptime { get; set; }
    public int PendingApprovals { get; set; }
    public int SystemAlerts { get; set; }
    public decimal RevenueGrowth { get; set; }
    public decimal UserGrowth { get; set; }
}

public class SystemActivityDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
}

public class SystemHealthMetricDto
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty;
}

public class OrganizationSummaryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public int EmployeeCount { get; set; }
    public string Status { get; set; } = string.Empty;
    public string SubscriptionPlan { get; set; } = string.Empty;
    public decimal MonthlyRevenue { get; set; }
    public DateTime JoinDate { get; set; }
    public string AdminEmail { get; set; } = string.Empty;
}

public class GetOrganizationsRequestDto
{
    public string? Status { get; set; }
    public string? Industry { get; set; }
    public string? Search { get; set; }
}

public class OrganizationsResponseDto
{
    public List<OrganizationSummaryDto> Organizations { get; set; } = new();
    public OrganizationsSummaryDto Summary { get; set; } = new();
}

public class OrganizationsSummaryDto
{
    public int TotalOrganizations { get; set; }
    public int ActiveOrganizations { get; set; }
    public int PendingOrganizations { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TotalEmployees { get; set; }
}

public class UpdateOrganizationStatusRequestDto
{
    public string Status { get; set; } = string.Empty;
    public string? Reason { get; set; }
}

public class UpdateOrganizationStatusResponseDto
{
    public Guid Id { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
}

public class OrganizationDetailsDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public int EmployeeCount { get; set; }
    public string Status { get; set; } = string.Empty;
    public string SubscriptionPlan { get; set; } = string.Empty;
    public decimal MonthlyRevenue { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string AdminEmail { get; set; } = string.Empty;
    public string AdminName { get; set; } = string.Empty;
    public List<OrganizationUserDto> Users { get; set; } = new();
    public OrganizationStatsDto Stats { get; set; } = new();
}

public class CreateOrganizationRequestDto
{
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [MaxLength(255)]
    public string Domain { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string Industry { get; set; } = string.Empty;

    [Required]
    [MaxLength(255)]
    public string AdminName { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string AdminEmail { get; set; } = string.Empty;
}

public class CreateOrganizationResponseDto
{
    public Guid OrganizationId { get; set; }
    public string OrganizationName { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public AdminCredentialsDto AdminCredentials { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class AdminCredentialsDto
{
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string TemporaryPassword { get; set; } = string.Empty;
    public bool RequirePasswordReset { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class UpdateOrganizationDetailsRequestDto
{
    public string? Name { get; set; }
    public string? Domain { get; set; }
    public string? Industry { get; set; }
    public string? SubscriptionPlan { get; set; }
    public decimal? MonthlyRevenue { get; set; }
}

public class UpdateUserPasswordRequest
{
    public Guid UserId { get; set; }
    public string NewPassword { get; set; } = string.Empty;
}

public class OrganizationUserDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class OrganizationStatsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int AdminUsers { get; set; }
    public int EmployeeUsers { get; set; }
    public DateTime LastActivity { get; set; }
}

public class SystemAnalyticsDto
{
    public OverviewMetricsDto Overview { get; set; } = new();
    public List<RevenueByPlanDto> RevenueByPlan { get; set; } = new();
    public List<IndustryBreakdownDto> IndustryBreakdown { get; set; } = new();
    public List<FeatureUsageDto> FeatureUsage { get; set; } = new();
}

public class OverviewMetricsDto
{
    public decimal TotalRevenue { get; set; }
    public decimal RevenueGrowth { get; set; }
    public int TotalUsers { get; set; }
    public decimal UserGrowth { get; set; }
    public int TotalOrganizations { get; set; }
    public decimal SystemUptime { get; set; }
}

public class RevenueByPlanDto
{
    public string Plan { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public decimal Percentage { get; set; }
    public int Organizations { get; set; }
}

public class IndustryBreakdownDto
{
    public string Industry { get; set; } = string.Empty;
    public int Organizations { get; set; }
    public int Users { get; set; }
    public decimal Revenue { get; set; }
}

public class FeatureUsageDto
{
    public string Feature { get; set; } = string.Empty;
    public decimal UsagePercentage { get; set; }
    public int OrganizationsUsing { get; set; }
}

public class GetSystemAnalyticsRequestDto
{
    public string Period { get; set; } = "30d"; // 7d, 30d, 90d, 1y
}

public class SystemSettingsDto
{
    public SystemSettingsDataDto Settings { get; set; } = new();
}

public class SystemSettingsDataDto
{
    public string PlatformName { get; set; } = string.Empty;
    public bool MaintenanceMode { get; set; }
    public bool RegistrationEnabled { get; set; }
    public int MaxOrganizations { get; set; }
    public SecuritySettingsDto Security { get; set; } = new();
    public NotificationSettingsDto Notifications { get; set; } = new();
}

public class SecuritySettingsDto
{
    public int PasswordMinLength { get; set; }
    public int SessionTimeout { get; set; }
    public bool TwoFactorRequired { get; set; }
}

public class NotificationSettingsDto
{
    public bool EmailNotifications { get; set; }
    public bool SystemAlerts { get; set; }
}

public class UpdateSystemSettingsRequestDto
{
    public UpdateSystemSettingsDataDto Settings { get; set; } = new();
}

public class UpdateSystemSettingsDataDto
{
    public bool? MaintenanceMode { get; set; }
    public SecuritySettingsDto? Security { get; set; }
}

public class OrganizationDashboardDto
{
    public OrganizationMetricsDto Metrics { get; set; } = new();
    public List<RecentActivityDto> RecentActivities { get; set; } = new();
    public List<UpcomingReviewDto> UpcomingReviews { get; set; } = new();
}

public class OrganizationMetricsDto
{
    public int TotalEmployees { get; set; }
    public int ActiveEmployees { get; set; }
    public int NewHiresThisMonth { get; set; }
    public int PendingLeaveRequests { get; set; }
    public decimal AttendanceRate { get; set; }
    public decimal AveragePerformanceRating { get; set; }
}

public class RecentActivityDto
{
    public string Type { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public class UpcomingReviewDto
{
    public string EmployeeName { get; set; } = string.Empty;
    public DateTime ReviewDate { get; set; }
    public string ReviewType { get; set; } = string.Empty;
}

public class BillingInformationDto
{
    public SubscriptionDto Subscription { get; set; } = new();
    public List<InvoiceDto> Invoices { get; set; } = new();
    public UsageDto Usage { get; set; } = new();
}

public class SubscriptionDto
{
    public string Plan { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string BillingCycle { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int EmployeeLimit { get; set; }
    public int CurrentEmployees { get; set; }
    public DateTime NextBillingDate { get; set; }
}

public class InvoiceDto
{
    public Guid Id { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime IssueDate { get; set; }
    public DateTime DueDate { get; set; }
}

public class UsageDto
{
    public List<string> FeaturesUsed { get; set; } = new();
}
