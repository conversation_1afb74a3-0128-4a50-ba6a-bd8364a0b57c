using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;

namespace HRMS.Application.Services;

public class OrganizationAdminService : IOrganizationAdminService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMasterDatabaseService _masterDatabaseService;
    private readonly ILogger<OrganizationAdminService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public OrganizationAdminService(
        IUnitOfWork unitOfWork,
        IMasterDatabaseService masterDatabaseService,
        ILogger<OrganizationAdminService> logger,
        IServiceProvider serviceProvider)
    {
        _unitOfWork = unitOfWork;
        _masterDatabaseService = masterDatabaseService;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task<ApiResponse<OrganizationDashboardDto>> GetDashboardAsync()
    {
        try
        {
            // For now, we'll use a mock organization ID since we don't have user context here
            // In a real implementation, this would get the organization from the current user's context
            var organizations = await _masterDatabaseService.GetAllOrganizationsAsync();
            var firstOrg = organizations.FirstOrDefault();

            if (firstOrg == null)
            {
                return ApiResponse<OrganizationDashboardDto>.ErrorResult("NO_ORGANIZATION", "No organization found");
            }

            return await GetOrganizationDashboardAsync(firstOrg.Id.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting organization dashboard");
            return ApiResponse<OrganizationDashboardDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting dashboard data");
        }
    }

    public async Task<ApiResponse<OrganizationDashboardDto>> GetOrganizationDashboardAsync(string userId)
    {
        try
        {
            var userGuid = Guid.Parse(userId);
            var user = await _masterDatabaseService.GetUserByIdAsync(userGuid);

            if (user == null || user.OrganizationId == null)
            {
                return ApiResponse<OrganizationDashboardDto>.ErrorResult("USER_NOT_FOUND", "User or organization not found");
            }

            var organizationId = user.OrganizationId.Value;

            // Get organization users (already filtered for active, non-deleted users by GetByOrganizationAsync)
            var organizationUsers = await _masterDatabaseService.GetUsersByOrganizationAsync(organizationId);
            // Include both OrgAdmin and Employee roles (exclude SuperAdmin) - users are already active and not deleted
            var employees = organizationUsers.Where(u => u.Role == UserRole.Employee || u.Role == UserRole.OrgAdmin).ToList();

            // Calculate metrics - all employees are already active and not deleted
            var totalEmployees = employees.Count;
            var activeEmployees = employees.Count(e => e.IsActive && e.EmployeeDetail?.EmploymentStatus == EmploymentStatus.Active);
            var newHiresThisMonth = employees.Count(e =>
                e.EmployeeDetail != null &&
                e.EmployeeDetail.JoinDate.Month == DateTime.UtcNow.Month &&
                e.EmployeeDetail.JoinDate.Year == DateTime.UtcNow.Year);

            // Calculate real metrics from database using UnitOfWork
            var pendingLeaveRequests = await CalculatePendingLeaveRequestsAsync(organizationId);
            var attendanceRate = await CalculateAttendanceRateAsync(organizationId);
            var averagePerformanceRating = await CalculateAveragePerformanceRatingAsync(organizationId);

            var metrics = new OrganizationMetricsDto
            {
                TotalEmployees = totalEmployees,
                ActiveEmployees = activeEmployees,
                NewHiresThisMonth = newHiresThisMonth,
                PendingLeaveRequests = pendingLeaveRequests,
                AttendanceRate = attendanceRate,
                AveragePerformanceRating = averagePerformanceRating
            };

            // Get real recent activities
            var recentActivities = await GetRecentActivitiesAsync(organizationId);

            // Get real upcoming reviews
            var upcomingReviews = await GetUpcomingReviewsAsync(organizationId);

            var dashboard = new OrganizationDashboardDto
            {
                Metrics = metrics,
                RecentActivities = recentActivities,
                UpcomingReviews = upcomingReviews
            };

            return ApiResponse<OrganizationDashboardDto>.SuccessResult(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting organization dashboard for user {UserId}", userId);
            return ApiResponse<OrganizationDashboardDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting organization dashboard");
        }
    }

    public async Task<ApiResponse<BillingInformationDto>> GetBillingInformationAsync(string userId)
    {
        try
        {
            var userGuid = Guid.Parse(userId);
            var user = await _masterDatabaseService.GetUserByIdAsync(userGuid);

            if (user == null || user.Organization == null)
            {
                return ApiResponse<BillingInformationDto>.ErrorResult("USER_NOT_FOUND", "User or organization not found");
            }

            var organization = user.Organization;

            // Mock subscription data
            var subscription = new SubscriptionDto
            {
                Plan = organization.SubscriptionPlan,
                Status = "active",
                BillingCycle = "monthly",
                Price = organization.SubscriptionPlan switch
                {
                    "standard" => 29.99m,
                    "premium" => 49.99m,
                    "enterprise" => 99.99m,
                    _ => 29.99m
                },
                EmployeeLimit = organization.SubscriptionPlan switch
                {
                    "standard" => 50,
                    "premium" => 200,
                    "enterprise" => 1000,
                    _ => 50
                },
                CurrentEmployees = organization.EmployeeCount,
                NextBillingDate = DateTime.UtcNow.AddDays(15) // Mock next billing date
            };

            // Mock invoice data
            var invoices = new List<InvoiceDto>
            {
                new InvoiceDto
                {
                    Id = Guid.NewGuid(),
                    InvoiceNumber = "INV-2024-001",
                    Amount = subscription.Price,
                    Status = "paid",
                    IssueDate = DateTime.UtcNow.AddDays(-30),
                    DueDate = DateTime.UtcNow.AddDays(-15)
                },
                new InvoiceDto
                {
                    Id = Guid.NewGuid(),
                    InvoiceNumber = "INV-2024-002",
                    Amount = subscription.Price,
                    Status = "paid",
                    IssueDate = DateTime.UtcNow.AddDays(-60),
                    DueDate = DateTime.UtcNow.AddDays(-45)
                },
                new InvoiceDto
                {
                    Id = Guid.NewGuid(),
                    InvoiceNumber = "INV-2024-003",
                    Amount = subscription.Price,
                    Status = "pending",
                    IssueDate = DateTime.UtcNow,
                    DueDate = DateTime.UtcNow.AddDays(15)
                }
            };

            // Mock usage data
            var usage = new UsageDto
            {
                FeaturesUsed = new List<string>
                {
                    "Attendance Management",
                    "Leave Management",
                    "Task Management",
                    "Performance Reviews"
                }
            };

            var billingInfo = new BillingInformationDto
            {
                Subscription = subscription,
                Invoices = invoices,
                Usage = usage
            };

            return ApiResponse<BillingInformationDto>.SuccessResult(billingInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting billing information for user {UserId}", userId);
            return ApiResponse<BillingInformationDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting billing information");
        }
    }

    private async Task<int> CalculatePendingLeaveRequestsAsync(Guid organizationId)
    {
        try
        {
            // Get organization users first to filter by organization
            var organizationUsers = await _masterDatabaseService.GetUsersByOrganizationAsync(organizationId);
            var userIds = organizationUsers.Select(u => u.Id).ToList();

            var leaveRepo = _unitOfWork.Leaves;
            var pendingRequests = await leaveRepo.GetAllAsync();

            // Filter by organization users and pending status
            return pendingRequests.Count(lr =>
                userIds.Contains(lr.UserId) &&
                lr.Status == HRMS.Core.Enums.LeaveStatus.Pending);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating pending leave requests for organization {OrganizationId}", organizationId);
            return 0;
        }
    }

    private async Task<decimal> CalculateAttendanceRateAsync(Guid organizationId)
    {
        try
        {
            // Get organization users first to filter by organization
            var organizationUsers = await _masterDatabaseService.GetUsersByOrganizationAsync(organizationId);
            var userIds = organizationUsers.Select(u => u.Id).ToList();

            var attendanceRepo = _unitOfWork.Attendance;
            var currentMonth = DateTime.UtcNow;
            var startOfMonth = new DateTime(currentMonth.Year, currentMonth.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            var attendanceRecords = await attendanceRepo.GetAllAsync();

            // Filter by organization users and current month
            var monthlyRecords = attendanceRecords.Where(ar =>
                userIds.Contains(ar.UserId) &&
                ar.Date >= startOfMonth && ar.Date <= endOfMonth).ToList();

            if (!monthlyRecords.Any())
                return 0m;

            var presentRecords = monthlyRecords.Count(ar => ar.Status == HRMS.Core.Enums.AttendanceStatus.Present);
            return Math.Round((decimal)presentRecords / monthlyRecords.Count() * 100, 1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating attendance rate for organization {OrganizationId}", organizationId);
            return 0m;
        }
    }

    private async Task<decimal> CalculateAveragePerformanceRatingAsync(Guid organizationId)
    {
        try
        {
            // Get organization users first to filter by organization
            var organizationUsers = await _masterDatabaseService.GetUsersByOrganizationAsync(organizationId);
            var userIds = organizationUsers.Select(u => u.Id).ToList();

            var performanceRepo = _unitOfWork.Performance;
            var reviews = await performanceRepo.GetAllAsync();

            // Filter by organization users and completed status
            var completedReviews = reviews.Where(pr =>
                userIds.Contains(pr.EmployeeId) &&
                pr.Status == HRMS.Core.Enums.ReviewStatus.Completed).ToList();

            if (!completedReviews.Any())
                return 0m;

            return Math.Round(completedReviews.Average(pr => pr.OverallRating ?? 0m), 1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating average performance rating for organization {OrganizationId}", organizationId);
            return 0m;
        }
    }

    private async Task<List<UpcomingReviewDto>> GetUpcomingReviewsAsync(Guid organizationId)
    {
        try
        {
            // Get organization users first to filter by organization
            var organizationUsers = await _masterDatabaseService.GetUsersByOrganizationAsync(organizationId);
            var userIds = organizationUsers.Select(u => u.Id).ToList();

            var performanceRepo = _unitOfWork.Performance;
            var reviews = await performanceRepo.GetAllAsync();

            // Filter by organization users, scheduled status, and future dates
            var upcomingReviews = reviews
                .Where(pr =>
                    userIds.Contains(pr.EmployeeId) &&
                    pr.Status == HRMS.Core.Enums.ReviewStatus.Scheduled &&
                    pr.ReviewDate > DateTime.UtcNow)
                .OrderBy(pr => pr.ReviewDate)
                .Take(5)
                .Select(pr => new UpcomingReviewDto
                {
                    EmployeeName = pr.Employee?.Name ?? "Unknown Employee",
                    ReviewDate = pr.ReviewDate,
                    ReviewType = pr.ReviewPeriod
                })
                .ToList();

            return upcomingReviews ?? new List<UpcomingReviewDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting upcoming reviews for organization {OrganizationId}", organizationId);
            return new List<UpcomingReviewDto>();
        }
    }

    private async Task<List<RecentActivityDto>> GetRecentActivitiesAsync(Guid organizationId)
    {
        try
        {
            // Get organization users first to filter by organization
            var organizationUsers = await _masterDatabaseService.GetUsersByOrganizationAsync(organizationId);
            var userIds = organizationUsers.Select(u => u.Id).ToList();

            var activities = new List<RecentActivityDto>();

            // Get recent leave requests for organization users
            var leaveRepo = _unitOfWork.Leaves;
            var recentLeaves = await leaveRepo.GetAllAsync();
            var latestLeaves = recentLeaves
                .Where(lr =>
                    userIds.Contains(lr.UserId) &&
                    lr.CreatedAt >= DateTime.UtcNow.AddDays(-7))
                .OrderByDescending(lr => lr.CreatedAt)
                .Take(2);

            foreach (var leave in latestLeaves)
            {
                activities.Add(new RecentActivityDto
                {
                    Type = "leave_request",
                    Message = $"Leave request submitted by {leave.User?.Name ?? "Employee"}",
                    Timestamp = leave.CreatedAt
                });
            }

            // Get recent task completions for organization users
            var taskRepo = _unitOfWork.Tasks;
            var recentTasks = await taskRepo.GetAllAsync();
            var completedTasks = recentTasks
                .Where(t =>
                    userIds.Contains(t.AssignedToId) &&
                    t.Status == HRMS.Core.Enums.TaskStatus.Completed &&
                    t.UpdatedAt >= DateTime.UtcNow.AddDays(-7))
                .OrderByDescending(t => t.UpdatedAt)
                .Take(2);

            foreach (var task in completedTasks)
            {
                activities.Add(new RecentActivityDto
                {
                    Type = "task_completed",
                    Message = $"Task '{task.Title}' completed by {task.AssignedTo?.Name ?? "Employee"}",
                    Timestamp = task.UpdatedAt
                });
            }

            // Get recent new employee additions
            var newEmployees = organizationUsers
                .Where(u => u.CreatedAt >= DateTime.UtcNow.AddDays(-7))
                .OrderByDescending(u => u.CreatedAt)
                .Take(2);

            foreach (var employee in newEmployees)
            {
                activities.Add(new RecentActivityDto
                {
                    Type = "employee_added",
                    Message = $"New employee '{employee.Name}' joined the organization",
                    Timestamp = employee.CreatedAt
                });
            }

            // If no real activities, add a default one
            if (!activities.Any())
            {
                activities.Add(new RecentActivityDto
                {
                    Type = "system",
                    Message = "Organization dashboard accessed",
                    Timestamp = DateTime.UtcNow
                });
            }

            return activities.OrderByDescending(a => a.Timestamp).Take(5).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent activities for organization {OrganizationId}", organizationId);
            return new List<RecentActivityDto>
            {
                new RecentActivityDto
                {
                    Type = "system",
                    Message = "Organization dashboard accessed",
                    Timestamp = DateTime.UtcNow
                }
            };
        }
    }

    public async Task<ApiResponse<EmployeeDashboardDto>> GetEmployeeDashboardAsync(Guid employeeId, string organizationId)
    {
        try
        {
            // Verify employee belongs to the organization
            var user = await _masterDatabaseService.GetUserByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeDashboardDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            if (user.OrganizationId?.ToString() != organizationId)
            {
                return ApiResponse<EmployeeDashboardDto>.ErrorResult("ACCESS_DENIED", "Employee does not belong to your organization");
            }

            // Use the employee service to get dashboard data
            var employeeService = _serviceProvider.GetService<IEmployeeService>();
            if (employeeService == null)
            {
                return ApiResponse<EmployeeDashboardDto>.ErrorResult("SERVICE_UNAVAILABLE", "Employee service not available");
            }

            return await employeeService.GetDashboardAsync(employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee dashboard for admin access. EmployeeId: {EmployeeId}, OrganizationId: {OrganizationId}",
                employeeId, organizationId);
            return ApiResponse<EmployeeDashboardDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting employee dashboard");
        }
    }

    public async Task<ApiResponse<EmployeeProfileSummaryDto>> GetEmployeeProfileAsync(Guid employeeId, string organizationId)
    {
        try
        {
            // Verify employee belongs to the organization
            var user = await _masterDatabaseService.GetUserByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            if (user.OrganizationId?.ToString() != organizationId)
            {
                return ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("ACCESS_DENIED", "Employee does not belong to your organization");
            }

            // Use the employee service to get profile data
            var employeeService = _serviceProvider.GetService<IEmployeeService>();
            if (employeeService == null)
            {
                return ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("SERVICE_UNAVAILABLE", "Employee service not available");
            }

            return await employeeService.GetProfileSummaryAsync(employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee profile for admin access. EmployeeId: {EmployeeId}, OrganizationId: {OrganizationId}",
                employeeId, organizationId);
            return ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting employee profile");
        }
    }

    public async Task<ApiResponse<EmployeeAttendanceSummaryDto>> GetEmployeeAttendanceAsync(Guid employeeId, string organizationId)
    {
        try
        {
            // Verify employee belongs to the organization
            var user = await _masterDatabaseService.GetUserByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeAttendanceSummaryDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            if (user.OrganizationId?.ToString() != organizationId)
            {
                return ApiResponse<EmployeeAttendanceSummaryDto>.ErrorResult("ACCESS_DENIED", "Employee does not belong to your organization");
            }

            // Use the employee service to get attendance data
            var employeeService = _serviceProvider.GetService<IEmployeeService>();
            if (employeeService == null)
            {
                return ApiResponse<EmployeeAttendanceSummaryDto>.ErrorResult("SERVICE_UNAVAILABLE", "Employee service not available");
            }

            return await employeeService.GetAttendanceSummaryAsync(employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee attendance for admin access. EmployeeId: {EmployeeId}, OrganizationId: {OrganizationId}",
                employeeId, organizationId);
            return ApiResponse<EmployeeAttendanceSummaryDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting employee attendance");
        }
    }
}
