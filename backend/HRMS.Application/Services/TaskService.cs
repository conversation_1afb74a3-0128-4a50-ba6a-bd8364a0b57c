using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;

namespace HRMS.Application.Services;

public class TaskService : ITaskService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<TaskService> _logger;

    public TaskService(IUnitOfWork unitOfWork, ILogger<TaskService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ApiResponse<TasksResponseDto>> GetTasksAsync(GetTasksRequestDto request)
    {
        try
        {
            IEnumerable<HRMS.Core.Entities.Task> tasks;

            if (request.AssignedTo.HasValue)
            {
                tasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(request.AssignedTo.Value);
            }
            else
            {
                tasks = await _unitOfWork.Tasks.GetAllAsync();
            }

            // Apply filters
            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<HRMS.Core.Enums.TaskStatus>(request.Status, true, out var statusEnum))
                {
                    tasks = tasks.Where(t => t.Status == statusEnum);
                }
            }

            if (!string.IsNullOrEmpty(request.Priority))
            {
                if (Enum.TryParse<TaskPriority>(request.Priority, true, out var priorityEnum))
                {
                    tasks = tasks.Where(t => t.Priority == priorityEnum);
                }
            }

            if (request.DueDate.HasValue)
            {
                tasks = tasks.Where(t => t.DueDate.HasValue && t.DueDate.Value.Date <= request.DueDate.Value.Date);
            }

            var taskDtos = tasks.Select(t => new TaskDto
            {
                Id = t.Id,
                Title = t.Title,
                Description = t.Description,
                Priority = t.Priority.ToString().ToLowerInvariant(),
                Status = t.Status.ToString().ToLowerInvariant(),
                ProgressPercentage = t.ProgressPercentage,
                AssignedTo = new UserSummaryDto
                {
                    Id = t.AssignedTo.Id,
                    Name = t.AssignedTo.Name,
                    EmployeeId = t.AssignedTo.EmployeeDetail?.EmployeeId
                },
                AssignedBy = new UserSummaryDto
                {
                    Id = t.AssignedBy.Id,
                    Name = t.AssignedBy.Name,
                    EmployeeId = t.AssignedBy.EmployeeDetail?.EmployeeId
                },
                DueDate = t.DueDate,
                EstimatedHours = t.EstimatedHours,
                ActualHours = t.ActualHours,
                CreatedAt = t.CreatedAt,
                Category = t.Category
            }).ToList();

            var response = new TasksResponseDto
            {
                Tasks = taskDtos
            };

            return ApiResponse<TasksResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tasks");
            return ApiResponse<TasksResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting tasks");
        }
    }

    public async Task<ApiResponse<CreateTaskResponseDto>> CreateTaskAsync(string userId, CreateTaskRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);

            // Validate assigned user exists
            var assignedUser = await _unitOfWork.Users.GetByIdAsync(request.AssignedTo);
            if (assignedUser == null)
            {
                return ApiResponse<CreateTaskResponseDto>.ErrorResult("USER_NOT_FOUND", "Assigned user not found");
            }

            // Create task
            var task = new HRMS.Core.Entities.Task
            {
                Title = request.Title,
                Description = request.Description,
                AssignedToId = request.AssignedTo,
                AssignedById = userGuid,
                Priority = Enum.Parse<TaskPriority>(request.Priority, true),
                Status = HRMS.Core.Enums.TaskStatus.Pending,
                ProgressPercentage = 0,
                Category = request.Category,
                EstimatedHours = request.EstimatedHours,
                DueDate = request.DueDate
            };

            await _unitOfWork.Tasks.AddAsync(task);
            await _unitOfWork.SaveChangesAsync();

            var response = new CreateTaskResponseDto
            {
                Id = task.Id,
                Title = task.Title,
                Status = task.Status.ToString().ToLowerInvariant(),
                AssignedTo = new UserSummaryDto
                {
                    Id = assignedUser.Id,
                    Name = assignedUser.Name,
                    EmployeeId = assignedUser.EmployeeDetail?.EmployeeId
                },
                DueDate = task.DueDate,
                CreatedAt = task.CreatedAt
            };

            return ApiResponse<CreateTaskResponseDto>.SuccessResult(response, "Task created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating task for user {UserId}", userId);
            return ApiResponse<CreateTaskResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while creating task");
        }
    }

    public async Task<ApiResponse<UpdateTaskProgressResponseDto>> UpdateTaskProgressAsync(Guid taskId, string userId, UpdateTaskProgressRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);
            var task = await _unitOfWork.Tasks.GetByIdAsync(taskId);

            if (task == null)
            {
                return ApiResponse<UpdateTaskProgressResponseDto>.ErrorResult("TASK_NOT_FOUND", "Task not found");
            }

            // Check if user is assigned to this task or is the creator
            if (task.AssignedToId != userGuid && task.AssignedById != userGuid)
            {
                return ApiResponse<UpdateTaskProgressResponseDto>.ErrorResult("UNAUTHORIZED", "You are not authorized to update this task");
            }

            // Update task progress
            task.ProgressPercentage = Math.Max(0, Math.Min(100, request.ProgressPercentage));
            
            if (Enum.TryParse<HRMS.Core.Enums.TaskStatus>(request.Status, true, out var statusEnum))
            {
                task.Status = statusEnum;
            }

            if (request.HoursSpent.HasValue)
            {
                task.ActualHours = (task.ActualHours ?? 0) + request.HoursSpent.Value;
            }

            await _unitOfWork.Tasks.UpdateAsync(task);

            // Create task update record
            var taskUpdate = new TaskUpdate
            {
                TaskId = taskId,
                ProgressPercentage = request.ProgressPercentage,
                Status = task.Status,
                HoursSpent = request.HoursSpent,
                UpdateNotes = request.UpdateNotes,
                Blockers = request.Blockers,
                NextSteps = request.NextSteps
            };

            // Note: TaskUpdate would need its own repository or be added through DbContext directly
            await _unitOfWork.SaveChangesAsync();

            var response = new UpdateTaskProgressResponseDto
            {
                Id = task.Id,
                ProgressPercentage = task.ProgressPercentage,
                Status = task.Status.ToString().ToLowerInvariant(),
                ActualHours = task.ActualHours,
                UpdatedAt = task.UpdatedAt
            };

            return ApiResponse<UpdateTaskProgressResponseDto>.SuccessResult(response, "Task progress updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating task progress for task {TaskId}", taskId);
            return ApiResponse<UpdateTaskProgressResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while updating task progress");
        }
    }

    public async Task<ApiResponse<AddTaskCommentResponseDto>> AddTaskCommentAsync(Guid taskId, string userId, AddTaskCommentRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);
            var task = await _unitOfWork.Tasks.GetByIdAsync(taskId);

            if (task == null)
            {
                return ApiResponse<AddTaskCommentResponseDto>.ErrorResult("TASK_NOT_FOUND", "Task not found");
            }

            // Get user details
            var user = await _unitOfWork.Users.GetByIdAsync(userGuid);
            if (user == null)
            {
                return ApiResponse<AddTaskCommentResponseDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            // Create comment
            var comment = new TaskComment
            {
                TaskId = taskId,
                AuthorId = userGuid,
                Comment = request.Comment
            };

            // Note: TaskComment would need its own repository or be added through DbContext directly
            await _unitOfWork.SaveChangesAsync();

            var response = new AddTaskCommentResponseDto
            {
                Id = comment.Id,
                Comment = comment.Comment,
                Author = new UserSummaryDto
                {
                    Id = user.Id,
                    Name = user.Name,
                    EmployeeId = user.EmployeeDetail?.EmployeeId
                },
                CreatedAt = comment.CreatedAt
            };

            return ApiResponse<AddTaskCommentResponseDto>.SuccessResult(response, "Comment added successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding comment to task {TaskId}", taskId);
            return ApiResponse<AddTaskCommentResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while adding comment");
        }
    }

    public async Task<ApiResponse<OrganizationTasksResponseDto>> GetOrganizationTasksAsync(string organizationId, GetOrganizationTasksDto request)
    {
        try
        {
            if (!Guid.TryParse(organizationId, out var orgGuid))
            {
                return ApiResponse<OrganizationTasksResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
            }

            var tasks = await _unitOfWork.Tasks.GetByOrganizationAsync(orgGuid);

            // Apply filters
            var filteredTasks = tasks.AsQueryable();

            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<HRMS.Core.Enums.TaskStatus>(request.Status, true, out var status))
                {
                    filteredTasks = filteredTasks.Where(t => t.Status == status);
                }
            }

            if (!string.IsNullOrEmpty(request.Priority))
            {
                if (Enum.TryParse<TaskPriority>(request.Priority, true, out var priority))
                {
                    filteredTasks = filteredTasks.Where(t => t.Priority == priority);
                }
            }

            if (!string.IsNullOrEmpty(request.Category))
            {
                filteredTasks = filteredTasks.Where(t =>
                    t.Category != null && t.Category.ToLower().Contains(request.Category.ToLower()));
            }

            if (!string.IsNullOrEmpty(request.Department))
            {
                filteredTasks = filteredTasks.Where(t =>
                    t.AssignedTo.EmployeeDetail != null &&
                    t.AssignedTo.EmployeeDetail.Department.ToLower().Contains(request.Department.ToLower()));
            }

            if (request.AssignedTo.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.AssignedToId == request.AssignedTo.Value);
            }

            if (request.AssignedBy.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.AssignedById == request.AssignedBy.Value);
            }

            if (request.DueDateFrom.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.DueDate >= request.DueDateFrom.Value);
            }

            if (request.DueDateTo.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.DueDate <= request.DueDateTo.Value);
            }

            if (request.Overdue.HasValue && request.Overdue.Value)
            {
                var now = DateTime.UtcNow;
                filteredTasks = filteredTasks.Where(t =>
                    t.DueDate.HasValue && t.DueDate.Value < now &&
                    t.Status != HRMS.Core.Enums.TaskStatus.Completed);
            }

            var totalCount = filteredTasks.Count();
            var pagedTasks = filteredTasks
                .OrderByDescending(t => t.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var taskDtos = pagedTasks.Select(t => new TaskDto
            {
                Id = t.Id,
                Title = t.Title,
                Description = t.Description,
                Priority = t.Priority.ToString().ToLowerInvariant(),
                Status = t.Status.ToString().ToLowerInvariant(),
                ProgressPercentage = t.ProgressPercentage,
                AssignedTo = new UserSummaryDto
                {
                    Id = t.AssignedTo.Id,
                    Name = t.AssignedTo.Name,
                    EmployeeId = t.AssignedTo.EmployeeDetail?.EmployeeId
                },
                AssignedBy = new UserSummaryDto
                {
                    Id = t.AssignedBy.Id,
                    Name = t.AssignedBy.Name,
                    EmployeeId = t.AssignedBy.EmployeeDetail?.EmployeeId
                },
                DueDate = t.DueDate,
                EstimatedHours = t.EstimatedHours,
                ActualHours = t.ActualHours,
                CreatedAt = t.CreatedAt,
                Category = t.Category
            }).ToList();

            var response = new OrganizationTasksResponseDto
            {
                Tasks = taskDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<OrganizationTasksResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting organization tasks for organization {OrganizationId}", organizationId);
            return ApiResponse<OrganizationTasksResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting organization tasks");
        }
    }

    public async Task<ApiResponse<TaskStatisticsResponseDto>> GetTaskStatisticsAsync(string organizationId, GetTaskStatisticsRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(organizationId, out var orgGuid))
            {
                return ApiResponse<TaskStatisticsResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
            }

            var tasks = await _unitOfWork.Tasks.GetByOrganizationAsync(orgGuid);

            // Apply date filters
            var filteredTasks = tasks.AsQueryable();
            if (request.DateFrom.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.CreatedAt >= request.DateFrom.Value);
            }
            if (request.DateTo.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.CreatedAt <= request.DateTo.Value);
            }
            if (!string.IsNullOrEmpty(request.Department))
            {
                filteredTasks = filteredTasks.Where(t =>
                    t.AssignedTo.EmployeeDetail != null &&
                    t.AssignedTo.EmployeeDetail.Department.ToLower().Contains(request.Department.ToLower()));
            }


            var tasksList = filteredTasks.ToList();
            var now = DateTime.UtcNow;

            var completedTasks = tasksList.Where(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed).ToList();
            var overdueTasks = tasksList.Where(t =>
                t.DueDate.HasValue && t.DueDate.Value < now &&
                t.Status != HRMS.Core.Enums.TaskStatus.Completed).ToList();

            // Calculate average completion time for completed tasks
            var avgCompletionTime = completedTasks.Any() ?
                completedTasks.Where(t => t.DueDate.HasValue)
                    .Average(t => (t.UpdatedAt - t.CreatedAt).TotalDays) : 0;

            var statistics = new TaskStatisticsDto
            {
                TotalTasks = tasksList.Count,
                PendingTasks = tasksList.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Pending),
                InProgressTasks = tasksList.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.InProgress),
                CompletedTasks = completedTasks.Count,
                OverdueTasks = overdueTasks.Count,
                AverageCompletionTime = avgCompletionTime,
                CompletionRate = tasksList.Count > 0 ? (double)completedTasks.Count / tasksList.Count * 100 : 0,

                PriorityBreakdown = tasksList
                    .GroupBy(t => t.Priority)
                    .Select(g => new TaskPriorityStatisticsDto
                    {
                        Priority = g.Key.ToString(),
                        Count = g.Count(),
                        Percentage = g.Count() > 0 ? (double)g.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed) / g.Count() * 100 : 0
                    }).ToList(),

                CategoryBreakdown = tasksList
                    .Where(t => !string.IsNullOrEmpty(t.Category))
                    .GroupBy(t => t.Category!)
                    .Select(g => new TaskCategoryStatisticsDto
                    {
                        Category = g.Key,
                        Count = g.Count(),
                        Percentage = g.Count() > 0 ? (double)g.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed) / g.Count() * 100 : 0
                    }).ToList(),

                DepartmentBreakdown = tasksList
                    .Where(t => t.AssignedTo.EmployeeDetail != null)
                    .GroupBy(t => t.AssignedTo.EmployeeDetail!.Department)
                    .Select(g => new DepartmentTaskStatisticsDto
                    {
                        Department = g.Key,
                        TotalTasks = g.Count(),
                        CompletedTasks = g.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed),
                        CompletionRate = g.Count() > 0 ? (double)g.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed) / g.Count() * 100 : 0
                    }).ToList(),

                EmployeeProductivity = tasksList
                    .GroupBy(t => t.AssignedTo)
                    .Select(g => new EmployeeTaskStatisticsDto
                    {
                        EmployeeId = g.Key.Id,
                        EmployeeName = g.Key.Name,
                        TotalTasks = g.Count(),
                        CompletedTasks = g.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed),
                        CompletionRate = g.Count() > 0 ? (double)g.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed) / g.Count() * 100 : 0,
                        AverageCompletionTime = g.Where(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed && t.DueDate.HasValue)
                            .Any() ? g.Where(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed && t.DueDate.HasValue)
                            .Average(t => (t.UpdatedAt - t.CreatedAt).TotalDays) : 0
                    })
                    .OrderByDescending(e => e.CompletionRate)
                    .ToList()
            };

            var response = new TaskStatisticsResponseDto { Statistics = statistics };
            return ApiResponse<TaskStatisticsResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting task statistics for organization {OrganizationId}", organizationId);
            return ApiResponse<TaskStatisticsResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting task statistics");
        }
    }

    public async Task<ApiResponse<CreateTaskResponseDto>> AssignTaskAsync(string adminUserId, AssignTaskRequestDto request)
    {
        try
        {
            var adminGuid = Guid.Parse(adminUserId);

            // Validate assigned user exists
            var assignedUser = await _unitOfWork.Users.GetByIdAsync(request.AssignedTo);
            if (assignedUser == null)
            {
                return ApiResponse<CreateTaskResponseDto>.ErrorResult("USER_NOT_FOUND", "Assigned user not found");
            }

            // Create task
            var task = new HRMS.Core.Entities.Task
            {
                Title = request.Title,
                Description = request.Description,
                AssignedToId = request.AssignedTo,
                AssignedById = adminGuid,
                Priority = Enum.Parse<TaskPriority>(request.Priority, true),
                Status = HRMS.Core.Enums.TaskStatus.Pending,
                ProgressPercentage = 0,
                Category = request.Category,
                EstimatedHours = request.EstimatedHours,
                DueDate = request.DueDate
            };

            await _unitOfWork.Tasks.AddAsync(task);
            await _unitOfWork.SaveChangesAsync();

            var response = new CreateTaskResponseDto
            {
                Id = task.Id,
                Title = task.Title,
                Status = task.Status.ToString().ToLowerInvariant(),
                AssignedTo = new UserSummaryDto
                {
                    Id = assignedUser.Id,
                    Name = assignedUser.Name,
                    EmployeeId = assignedUser.EmployeeDetail?.EmployeeId
                },
                DueDate = task.DueDate,
                CreatedAt = task.CreatedAt
            };

            return ApiResponse<CreateTaskResponseDto>.SuccessResult(response, "Task assigned successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning task by admin {AdminUserId}", adminUserId);
            return ApiResponse<CreateTaskResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while assigning task");
        }
    }

    public async Task<ApiResponse<TaskUpdatesResponseDto>> GetTaskUpdatesAsync(string organizationId, GetTaskUpdatesRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(organizationId, out var orgGuid))
            {
                return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
            }

            var taskUpdates = await _unitOfWork.TaskUpdates.GetByOrganizationAsync(orgGuid);

            // Apply filters
            var filteredUpdates = taskUpdates.AsQueryable();

            if (request.DateFrom.HasValue)
            {
                filteredUpdates = filteredUpdates.Where(tu => tu.CreatedAt >= request.DateFrom.Value);
            }

            if (request.DateTo.HasValue)
            {
                filteredUpdates = filteredUpdates.Where(tu => tu.CreatedAt <= request.DateTo.Value);
            }

            var totalCount = filteredUpdates.Count();
            var pagedUpdates = filteredUpdates
                .OrderByDescending(tu => tu.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var updateDtos = pagedUpdates.Select(tu => new TaskUpdateDto
            {
                Id = tu.Id,
                TaskId = tu.TaskId,
                TaskTitle = tu.Task.Title,
                Employee = new UserSummaryDto
                {
                    Id = tu.UpdatedBy.Id,
                    Name = tu.UpdatedBy.Name,
                    EmployeeId = tu.UpdatedBy.EmployeeDetail?.EmployeeId
                },
                ProgressPercentage = tu.ProgressPercentage,
                Status = tu.Status.ToString().ToLowerInvariant(),
                HoursSpent = tu.HoursSpent,
                UpdateNotes = tu.UpdateNotes,
                Blockers = tu.Blockers,
                NextSteps = tu.NextSteps,
                UpdateDate = tu.UpdateDate,
                UpdateType = tu.UpdateType.ToString(),
                CreatedAt = tu.CreatedAt
            }).ToList();

            var response = new TaskUpdatesResponseDto
            {
                Updates = updateDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<TaskUpdatesResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting task updates for organization {OrganizationId}", organizationId);
            return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting task updates");
        }
    }

    public async Task<ApiResponse<TaskDto>> UpdateTaskStatusAsync(Guid taskId, string adminUserId, UpdateTaskStatusDto request)
    {
        try
        {
            var task = await _unitOfWork.Tasks.GetByIdAsync(taskId);
            if (task == null)
            {
                return ApiResponse<TaskDto>.ErrorResult("TASK_NOT_FOUND", "Task not found");
            }

            // Parse and validate status
            if (!Enum.TryParse<HRMS.Core.Enums.TaskStatus>(request.Status, true, out var newStatus))
            {
                return ApiResponse<TaskDto>.ErrorResult("INVALID_STATUS", "Invalid task status");
            }

            // Update task
            task.Status = newStatus;
            if (request.ProgressPercentage.HasValue)
            {
                task.ProgressPercentage = Math.Max(0, Math.Min(100, request.ProgressPercentage.Value));
            }

            await _unitOfWork.Tasks.UpdateAsync(task);

            // Create task update record if there are comments
            if (!string.IsNullOrEmpty(request.Comments))
            {
                var taskUpdate = new TaskUpdate
                {
                    TaskId = taskId,
                    ProgressPercentage = task.ProgressPercentage,
                    Status = task.Status,
                    UpdateNotes = request.Comments
                };

                await _unitOfWork.TaskUpdates.AddAsync(taskUpdate);
            }

            await _unitOfWork.SaveChangesAsync();

            // Reload with related data
            var updatedTask = await _unitOfWork.Tasks.GetByIdAsync(taskId);
            var responseDto = new TaskDto
            {
                Id = updatedTask!.Id,
                Title = updatedTask.Title,
                Description = updatedTask.Description,
                Priority = updatedTask.Priority.ToString().ToLowerInvariant(),
                Status = updatedTask.Status.ToString().ToLowerInvariant(),
                ProgressPercentage = updatedTask.ProgressPercentage,
                AssignedTo = new UserSummaryDto
                {
                    Id = updatedTask.AssignedTo.Id,
                    Name = updatedTask.AssignedTo.Name,
                    EmployeeId = updatedTask.AssignedTo.EmployeeDetail?.EmployeeId
                },
                AssignedBy = new UserSummaryDto
                {
                    Id = updatedTask.AssignedBy.Id,
                    Name = updatedTask.AssignedBy.Name,
                    EmployeeId = updatedTask.AssignedBy.EmployeeDetail?.EmployeeId
                },
                DueDate = updatedTask.DueDate,
                EstimatedHours = updatedTask.EstimatedHours,
                ActualHours = updatedTask.ActualHours,
                CreatedAt = updatedTask.CreatedAt,
                Category = updatedTask.Category
            };

            return ApiResponse<TaskDto>.SuccessResult(responseDto, $"Task status updated to {newStatus.ToString().ToLowerInvariant()} successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating task status for task {TaskId}", taskId);
            return ApiResponse<TaskDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while updating task status");
        }
    }

    // Employee self-task management methods
    public async Task<ApiResponse<CreateSelfTaskResponseDto>> CreateSelfTaskAsync(string userId, CreateSelfTaskRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid user ID format");
            }

            // Verify user exists and is an employee
            var user = await _unitOfWork.Users.GetByIdAsync(userGuid);
            if (user == null)
            {
                return ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            if (user.Role != UserRole.Employee && user.Role != UserRole.OrgAdmin)
            {
                return ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "Only employees and org admins can create self tasks");
            }

            // Parse and validate priority
            if (!Enum.TryParse<TaskPriority>(request.Priority, true, out var priority))
            {
                return ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("INVALID_PRIORITY", "Invalid priority value");
            }

            // Parse and validate task type
            if (!Enum.TryParse<TaskType>(request.TaskType, true, out var taskType))
            {
                return ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("INVALID_TASK_TYPE", "Invalid task type value");
            }

            // Create self-task (employee assigns task to themselves)
            var task = new HRMS.Core.Entities.Task
            {
                Title = request.Title,
                Description = request.Description,
                AssignedToId = userGuid,
                AssignedById = userGuid, // Self-assigned
                Priority = priority,
                Status = HRMS.Core.Enums.TaskStatus.Pending,
                ProgressPercentage = 0,
                Category = request.Category,
                EstimatedHours = request.EstimatedHours,
                DueDate = request.DueDate,
                IsSelfCreated = true,
                TaskType = taskType
            };

            await _unitOfWork.Tasks.AddAsync(task);
            await _unitOfWork.SaveChangesAsync();

            var response = new CreateSelfTaskResponseDto
            {
                Id = task.Id,
                Title = task.Title,
                Status = task.Status.ToString().ToLowerInvariant(),
                TaskType = task.TaskType.ToString(),
                DueDate = task.DueDate,
                CreatedAt = task.CreatedAt
            };

            return ApiResponse<CreateSelfTaskResponseDto>.SuccessResult(response, "Self task created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating self task for user {UserId}", userId);
            return ApiResponse<CreateSelfTaskResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while creating self task");
        }
    }

    public async Task<ApiResponse<TasksResponseDto>> GetEmployeeTasksAsync(string userId, GetEmployeeTasksRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return ApiResponse<TasksResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid user ID format");
            }

            // Get tasks assigned to the employee (both self-created and admin-assigned)
            var tasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(userGuid);

            // Apply filters
            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<HRMS.Core.Enums.TaskStatus>(request.Status, true, out var statusEnum))
                {
                    tasks = tasks.Where(t => t.Status == statusEnum);
                }
            }

            if (!string.IsNullOrEmpty(request.Priority))
            {
                if (Enum.TryParse<TaskPriority>(request.Priority, true, out var priorityEnum))
                {
                    tasks = tasks.Where(t => t.Priority == priorityEnum);
                }
            }

            if (!string.IsNullOrEmpty(request.TaskType))
            {
                if (Enum.TryParse<TaskType>(request.TaskType, true, out var taskTypeEnum))
                {
                    tasks = tasks.Where(t => t.TaskType == taskTypeEnum);
                }
            }

            if (request.IsSelfCreated.HasValue)
            {
                tasks = tasks.Where(t => t.IsSelfCreated == request.IsSelfCreated.Value);
            }

            if (request.DueDateFrom.HasValue)
            {
                tasks = tasks.Where(t => t.DueDate >= request.DueDateFrom.Value);
            }

            if (request.DueDateTo.HasValue)
            {
                tasks = tasks.Where(t => t.DueDate <= request.DueDateTo.Value);
            }

            // Apply pagination
            var totalCount = tasks.Count();
            var paginatedTasks = tasks
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var taskDtos = paginatedTasks.Select(t => new TaskDto
            {
                Id = t.Id,
                Title = t.Title,
                Description = t.Description,
                Priority = t.Priority.ToString().ToLowerInvariant(),
                Status = t.Status.ToString().ToLowerInvariant(),
                ProgressPercentage = t.ProgressPercentage,
                AssignedTo = new UserSummaryDto
                {
                    Id = t.AssignedTo.Id,
                    Name = t.AssignedTo.Name,
                    EmployeeId = t.AssignedTo.EmployeeDetail?.EmployeeId
                },
                AssignedBy = new UserSummaryDto
                {
                    Id = t.AssignedBy.Id,
                    Name = t.AssignedBy.Name,
                    EmployeeId = t.AssignedBy.EmployeeDetail?.EmployeeId
                },
                DueDate = t.DueDate,
                EstimatedHours = t.EstimatedHours,
                ActualHours = t.ActualHours,
                CreatedAt = t.CreatedAt,
                Category = t.Category,
                IsSelfCreated = t.IsSelfCreated,
                TaskType = t.TaskType.ToString(),
                RecentUpdates = t.TaskUpdates
                    .OrderByDescending(u => u.CreatedAt)
                    .Take(3)
                    .Select(u => new TaskUpdateDto
                    {
                        Id = u.Id,
                        TaskId = u.TaskId,
                        TaskTitle = t.Title,
                        Employee = new UserSummaryDto
                        {
                            Id = u.UpdatedBy.Id,
                            Name = u.UpdatedBy.Name,
                            EmployeeId = u.UpdatedBy.EmployeeDetail?.EmployeeId
                        },
                        ProgressPercentage = u.ProgressPercentage,
                        Status = u.Status.ToString().ToLowerInvariant(),
                        HoursSpent = u.HoursSpent,
                        UpdateNotes = u.UpdateNotes,
                        Blockers = u.Blockers,
                        NextSteps = u.NextSteps,
                        UpdateDate = u.UpdateDate,
                        UpdateType = u.UpdateType.ToString(),
                        CreatedAt = u.CreatedAt
                    }).ToList()
            }).ToList();

            var response = new TasksResponseDto
            {
                Tasks = taskDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<TasksResponseDto>.SuccessResult(response, "Employee tasks retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving employee tasks for user {UserId}", userId);
            return ApiResponse<TasksResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while retrieving employee tasks");
        }
    }

    public async Task<ApiResponse<CreateDailyUpdateResponseDto>> CreateDailyUpdateAsync(string userId, CreateDailyUpdateRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid user ID format");
            }

            // Verify task exists and user has permission to update it
            if (!Guid.TryParse(request.TaskId, out var taskId))
            {
                return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("INVALID_TASK_ID", "Invalid task ID format");
            }

            var task = await _unitOfWork.Tasks.GetByIdAsync(taskId);
            if (task == null)
            {
                return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("TASK_NOT_FOUND", "Task not found");
            }

            // Check if user can update this task (assigned to them or they created it)
            if (task.AssignedToId != userGuid && task.AssignedById != userGuid)
            {
                return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "You don't have permission to update this task");
            }

            // Parse and validate update type
            if (!Enum.TryParse<TaskUpdateType>(request.UpdateType, true, out var updateType))
            {
                return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("INVALID_UPDATE_TYPE", "Invalid update type value");
            }

            // Validate progress percentage
            var progressPercentage = request.ProgressPercentage ?? task.ProgressPercentage;
            if (progressPercentage < 0 || progressPercentage > 100)
            {
                return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("INVALID_PROGRESS", "Progress percentage must be between 0 and 100");
            }

            // Create task update
            var taskUpdate = new TaskUpdate
            {
                TaskId = taskId,
                UpdatedById = userGuid,
                ProgressPercentage = progressPercentage,
                Status = task.Status, // Keep current status unless it's a status change update
                HoursSpent = request.HoursSpent,
                UpdateNotes = request.UpdateNotes,
                Blockers = request.Blockers,
                NextSteps = request.NextSteps,
                UpdateDate = request.UpdateDate ?? DateTime.UtcNow.Date,
                UpdateType = updateType
            };

            // Update task progress if provided
            if (request.ProgressPercentage.HasValue)
            {
                task.ProgressPercentage = progressPercentage;

                // Auto-update status based on progress
                if (progressPercentage == 100 && task.Status != HRMS.Core.Enums.TaskStatus.Completed)
                {
                    task.Status = HRMS.Core.Enums.TaskStatus.Completed;
                    taskUpdate.Status = HRMS.Core.Enums.TaskStatus.Completed;
                    taskUpdate.UpdateType = TaskUpdateType.Completion;
                }
                else if (progressPercentage > 0 && task.Status == HRMS.Core.Enums.TaskStatus.Pending)
                {
                    task.Status = HRMS.Core.Enums.TaskStatus.InProgress;
                    taskUpdate.Status = HRMS.Core.Enums.TaskStatus.InProgress;
                }
            }

            // Update actual hours if provided
            if (request.HoursSpent.HasValue)
            {
                task.ActualHours = (task.ActualHours ?? 0) + request.HoursSpent.Value;
            }

            await _unitOfWork.TaskUpdates.AddAsync(taskUpdate);
            await _unitOfWork.Tasks.UpdateAsync(task);
            await _unitOfWork.SaveChangesAsync();

            var response = new CreateDailyUpdateResponseDto
            {
                Id = taskUpdate.Id,
                TaskId = taskUpdate.TaskId,
                TaskTitle = task.Title,
                UpdateNotes = taskUpdate.UpdateNotes,
                ProgressPercentage = taskUpdate.ProgressPercentage,
                Status = taskUpdate.Status.ToString().ToLowerInvariant(),
                HoursSpent = taskUpdate.HoursSpent,
                UpdateDate = taskUpdate.UpdateDate,
                CreatedAt = taskUpdate.CreatedAt
            };

            return ApiResponse<CreateDailyUpdateResponseDto>.SuccessResult(response, "Daily update created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating daily update for user {UserId} and task {TaskId}", userId, request.TaskId);
            return ApiResponse<CreateDailyUpdateResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while creating daily update");
        }
    }

    public async Task<ApiResponse<TaskUpdatesResponseDto>> GetEmployeeDailyUpdatesAsync(string userId, GetDailyUpdatesRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid user ID format");
            }

            // Get all task updates for tasks assigned to the user
            var userTasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(userGuid);
            var taskIds = userTasks.Select(t => t.Id).ToList();

            var updates = await _unitOfWork.TaskUpdates.GetByTaskIdsAsync(taskIds);

            // Apply filters
            if (request.TaskId.HasValue)
            {
                updates = updates.Where(u => u.TaskId == request.TaskId.Value);
            }

            if (request.DateFrom.HasValue)
            {
                updates = updates.Where(u => u.UpdateDate >= request.DateFrom.Value);
            }

            if (request.DateTo.HasValue)
            {
                updates = updates.Where(u => u.UpdateDate <= request.DateTo.Value);
            }

            if (!string.IsNullOrEmpty(request.UpdateType))
            {
                if (Enum.TryParse<TaskUpdateType>(request.UpdateType, true, out var updateTypeEnum))
                {
                    updates = updates.Where(u => u.UpdateType == updateTypeEnum);
                }
            }

            // Apply pagination
            var totalCount = updates.Count();
            var paginatedUpdates = updates
                .OrderByDescending(u => u.UpdateDate)
                .ThenByDescending(u => u.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var updateDtos = paginatedUpdates.Select(u => new TaskUpdateDto
            {
                Id = u.Id,
                TaskId = u.TaskId,
                TaskTitle = u.Task.Title,
                Employee = new UserSummaryDto
                {
                    Id = u.UpdatedBy.Id,
                    Name = u.UpdatedBy.Name,
                    EmployeeId = u.UpdatedBy.EmployeeDetail?.EmployeeId
                },
                ProgressPercentage = u.ProgressPercentage,
                Status = u.Status.ToString().ToLowerInvariant(),
                HoursSpent = u.HoursSpent,
                UpdateNotes = u.UpdateNotes,
                Blockers = u.Blockers,
                NextSteps = u.NextSteps,
                UpdateDate = u.UpdateDate,
                UpdateType = u.UpdateType.ToString(),
                CreatedAt = u.CreatedAt
            }).ToList();

            var response = new TaskUpdatesResponseDto
            {
                Updates = updateDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<TaskUpdatesResponseDto>.SuccessResult(response, "Employee daily updates retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving employee daily updates for user {UserId}", userId);
            return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while retrieving employee daily updates");
        }
    }

    // Manager visibility methods
    public async Task<ApiResponse<TasksResponseDto>> GetManagerVisibleTasksAsync(string managerId, GetManagerTasksRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(managerId, out var managerGuid))
            {
                return ApiResponse<TasksResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid manager ID format");
            }

            // Verify manager exists and has appropriate role
            var manager = await _unitOfWork.Users.GetByIdAsync(managerGuid);
            if (manager == null)
            {
                return ApiResponse<TasksResponseDto>.ErrorResult("USER_NOT_FOUND", "Manager not found");
            }

            if (manager.Role != UserRole.OrgAdmin && manager.Role != UserRole.Employee)
            {
                return ApiResponse<TasksResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "User is not authorized to view team tasks");
            }

            // Get direct reports (employees who have this user as their manager)
            var directReports = await _unitOfWork.EmployeeDetails.GetByManagerIdAsync(managerGuid);
            var directReportIds = directReports.Select(ed => ed.UserId).ToList();

            // If specific employee requested, verify they are a direct report
            if (request.EmployeeId.HasValue)
            {
                if (!directReportIds.Contains(request.EmployeeId.Value))
                {
                    return ApiResponse<TasksResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "You can only view tasks of your direct reports");
                }
                directReportIds = new List<Guid> { request.EmployeeId.Value };
            }

            // Get all tasks for direct reports
            var tasks = new List<HRMS.Core.Entities.Task>();
            foreach (var reportId in directReportIds)
            {
                var reportTasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(reportId);
                tasks.AddRange(reportTasks);
            }

            // Apply filters
            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<HRMS.Core.Enums.TaskStatus>(request.Status, true, out var statusEnum))
                {
                    tasks = tasks.Where(t => t.Status == statusEnum).ToList();
                }
            }

            if (!string.IsNullOrEmpty(request.Priority))
            {
                if (Enum.TryParse<TaskPriority>(request.Priority, true, out var priorityEnum))
                {
                    tasks = tasks.Where(t => t.Priority == priorityEnum).ToList();
                }
            }

            if (!string.IsNullOrEmpty(request.TaskType))
            {
                if (Enum.TryParse<TaskType>(request.TaskType, true, out var taskTypeEnum))
                {
                    tasks = tasks.Where(t => t.TaskType == taskTypeEnum).ToList();
                }
            }

            if (request.IsSelfCreated.HasValue)
            {
                tasks = tasks.Where(t => t.IsSelfCreated == request.IsSelfCreated.Value).ToList();
            }

            if (request.DueDateFrom.HasValue)
            {
                tasks = tasks.Where(t => t.DueDate >= request.DueDateFrom.Value).ToList();
            }

            if (request.DueDateTo.HasValue)
            {
                tasks = tasks.Where(t => t.DueDate <= request.DueDateTo.Value).ToList();
            }

            // Apply pagination
            var totalCount = tasks.Count;
            var paginatedTasks = tasks
                .OrderByDescending(t => t.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var taskDtos = paginatedTasks.Select(t => new TaskDto
            {
                Id = t.Id,
                Title = t.Title,
                Description = t.Description,
                Priority = t.Priority.ToString().ToLowerInvariant(),
                Status = t.Status.ToString().ToLowerInvariant(),
                ProgressPercentage = t.ProgressPercentage,
                AssignedTo = new UserSummaryDto
                {
                    Id = t.AssignedTo.Id,
                    Name = t.AssignedTo.Name,
                    EmployeeId = t.AssignedTo.EmployeeDetail?.EmployeeId
                },
                AssignedBy = new UserSummaryDto
                {
                    Id = t.AssignedBy.Id,
                    Name = t.AssignedBy.Name,
                    EmployeeId = t.AssignedBy.EmployeeDetail?.EmployeeId
                },
                DueDate = t.DueDate,
                EstimatedHours = t.EstimatedHours,
                ActualHours = t.ActualHours,
                CreatedAt = t.CreatedAt,
                Category = t.Category,
                IsSelfCreated = t.IsSelfCreated,
                TaskType = t.TaskType.ToString(),
                RecentUpdates = t.TaskUpdates
                    .OrderByDescending(u => u.CreatedAt)
                    .Take(3)
                    .Select(u => new TaskUpdateDto
                    {
                        Id = u.Id,
                        TaskId = u.TaskId,
                        TaskTitle = t.Title,
                        Employee = new UserSummaryDto
                        {
                            Id = u.UpdatedBy.Id,
                            Name = u.UpdatedBy.Name,
                            EmployeeId = u.UpdatedBy.EmployeeDetail?.EmployeeId
                        },
                        ProgressPercentage = u.ProgressPercentage,
                        Status = u.Status.ToString().ToLowerInvariant(),
                        HoursSpent = u.HoursSpent,
                        UpdateNotes = u.UpdateNotes,
                        Blockers = u.Blockers,
                        NextSteps = u.NextSteps,
                        UpdateDate = u.UpdateDate,
                        UpdateType = u.UpdateType.ToString(),
                        CreatedAt = u.CreatedAt
                    }).ToList()
            }).ToList();

            var response = new TasksResponseDto
            {
                Tasks = taskDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<TasksResponseDto>.SuccessResult(response, "Manager visible tasks retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving manager visible tasks for manager {ManagerId}", managerId);
            return ApiResponse<TasksResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while retrieving manager visible tasks");
        }
    }

    public async Task<ApiResponse<TaskUpdatesResponseDto>> GetManagerVisibleUpdatesAsync(string managerId, GetDailyUpdatesRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(managerId, out var managerGuid))
            {
                return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid manager ID format");
            }

            // Get direct reports
            var directReports = await _unitOfWork.EmployeeDetails.GetByManagerIdAsync(managerGuid);
            var directReportIds = directReports.Select(ed => ed.UserId).ToList();

            if (request.EmployeeId.HasValue)
            {
                if (!directReportIds.Contains(request.EmployeeId.Value))
                {
                    return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "You can only view updates of your direct reports");
                }
                directReportIds = new List<Guid> { request.EmployeeId.Value };
            }

            // Get all task updates for direct reports
            var updates = new List<TaskUpdate>();
            foreach (var reportId in directReportIds)
            {
                var reportTasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(reportId);
                var taskIds = reportTasks.Select(t => t.Id).ToList();
                var reportUpdates = await _unitOfWork.TaskUpdates.GetByTaskIdsAsync(taskIds);
                updates.AddRange(reportUpdates);
            }

            // Apply filters
            if (request.TaskId.HasValue)
            {
                updates = updates.Where(u => u.TaskId == request.TaskId.Value).ToList();
            }

            if (request.DateFrom.HasValue)
            {
                updates = updates.Where(u => u.UpdateDate >= request.DateFrom.Value).ToList();
            }

            if (request.DateTo.HasValue)
            {
                updates = updates.Where(u => u.UpdateDate <= request.DateTo.Value).ToList();
            }

            if (!string.IsNullOrEmpty(request.UpdateType))
            {
                if (Enum.TryParse<TaskUpdateType>(request.UpdateType, true, out var updateTypeEnum))
                {
                    updates = updates.Where(u => u.UpdateType == updateTypeEnum).ToList();
                }
            }

            // Apply pagination
            var totalCount = updates.Count;
            var paginatedUpdates = updates
                .OrderByDescending(u => u.UpdateDate)
                .ThenByDescending(u => u.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var updateDtos = paginatedUpdates.Select(u => new TaskUpdateDto
            {
                Id = u.Id,
                TaskId = u.TaskId,
                TaskTitle = u.Task.Title,
                Employee = new UserSummaryDto
                {
                    Id = u.UpdatedBy.Id,
                    Name = u.UpdatedBy.Name,
                    EmployeeId = u.UpdatedBy.EmployeeDetail?.EmployeeId
                },
                ProgressPercentage = u.ProgressPercentage,
                Status = u.Status.ToString().ToLowerInvariant(),
                HoursSpent = u.HoursSpent,
                UpdateNotes = u.UpdateNotes,
                Blockers = u.Blockers,
                NextSteps = u.NextSteps,
                UpdateDate = u.UpdateDate,
                UpdateType = u.UpdateType.ToString(),
                CreatedAt = u.CreatedAt
            }).ToList();

            var response = new TaskUpdatesResponseDto
            {
                Updates = updateDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<TaskUpdatesResponseDto>.SuccessResult(response, "Manager visible updates retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving manager visible updates for manager {ManagerId}", managerId);
            return ApiResponse<TaskUpdatesResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while retrieving manager visible updates");
        }
    }

    // Role-based access control methods
    public async Task<bool> CanUserViewTaskAsync(string userId, Guid taskId)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return false;
            }

            var user = await _unitOfWork.Users.GetByIdAsync(userGuid);
            if (user == null)
            {
                return false;
            }

            var task = await _unitOfWork.Tasks.GetByIdAsync(taskId);
            if (task == null)
            {
                return false;
            }

            // Super admin and org admin can see all tasks in their organization
            if (user.Role == UserRole.SuperAdmin || user.Role == UserRole.OrgAdmin)
            {
                return true;
            }

            // User can see their own tasks (assigned to them)
            if (task.AssignedToId == userGuid)
            {
                return true;
            }

            // User can see tasks they created/assigned
            if (task.AssignedById == userGuid)
            {
                return true;
            }

            // Manager can see tasks of their direct reports
            var assignedUserEmployeeDetail = await _unitOfWork.EmployeeDetails.GetByUserIdAsync(task.AssignedToId);
            if (assignedUserEmployeeDetail?.ManagerId == userGuid)
            {
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking task view permission for user {UserId} and task {TaskId}", userId, taskId);
            return false;
        }
    }

    public async Task<bool> CanUserEditTaskAsync(string userId, Guid taskId)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return false;
            }

            var user = await _unitOfWork.Users.GetByIdAsync(userGuid);
            if (user == null)
            {
                return false;
            }

            var task = await _unitOfWork.Tasks.GetByIdAsync(taskId);
            if (task == null)
            {
                return false;
            }

            // Super admin and org admin can edit all tasks in their organization
            if (user.Role == UserRole.SuperAdmin || user.Role == UserRole.OrgAdmin)
            {
                return true;
            }

            // User can edit their own tasks (assigned to them) - for progress updates
            if (task.AssignedToId == userGuid)
            {
                return true;
            }

            // User can edit tasks they created/assigned
            if (task.AssignedById == userGuid)
            {
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking task edit permission for user {UserId} and task {TaskId}", userId, taskId);
            return false;
        }
    }
}
