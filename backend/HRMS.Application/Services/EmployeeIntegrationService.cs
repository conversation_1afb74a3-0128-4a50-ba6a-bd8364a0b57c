using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using TaskEntity = HRMS.Core.Entities.Task;

namespace HRMS.Application.Services;

public class EmployeeIntegrationService : IEmployeeIntegrationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<EmployeeIntegrationService> _logger;

    public EmployeeIntegrationService(
        IUnitOfWork unitOfWork,
        ILogger<EmployeeIntegrationService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async System.Threading.Tasks.Task<ApiResponse> OnEmployeeCreatedAsync(Guid employeeId)
    {
        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Create default leave balances
            await CreateDefaultLeaveBalancesAsync(employeeId);

            // Initialize attendance record
            await InitializeAttendanceRecordAsync(employeeId);

            // Set up default permissions based on role
            await SetupDefaultPermissionsAsync(employeeId);

            // Create payroll structure
            await CreateDefaultPayrollStructureAsync(employeeId);

            await _unitOfWork.CommitTransactionAsync();

            return ApiResponse.SuccessResult("Employee integration setup completed successfully");
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Error setting up employee integration for {EmployeeId}", employeeId);
            return ApiResponse.ErrorResult("INTEGRATION_ERROR", "Failed to set up employee integration");
        }
    }

    public async System.Threading.Tasks.Task<ApiResponse> OnEmployeeUpdatedAsync(Guid employeeId, EmployeeUpdateContext context)
    {
        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Update department assignments if department changed
            if (context.DepartmentChanged)
            {
                await UpdateDepartmentAssignmentsAsync(employeeId, context.NewDepartment);
            }

            // Update role permissions if role changed
            if (context.RoleChanged)
            {
                await UpdateRolePermissionsAsync(employeeId, context.NewRole);
            }

            // Update payroll structure if salary changed
            if (context.SalaryChanged && context.NewSalary.HasValue)
            {
                await UpdatePayrollStructureAsync(employeeId, context.NewSalary.Value);
            }

            // Update task assignments if manager changed
            if (context.ManagerChanged)
            {
                await UpdateTaskAssignmentsAsync(employeeId, context.NewManagerId);
            }

            await _unitOfWork.CommitTransactionAsync();

            return ApiResponse.SuccessResult("Employee integration updates completed successfully");
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Error updating employee integration for {EmployeeId}", employeeId);
            return ApiResponse.ErrorResult("INTEGRATION_ERROR", "Failed to update employee integration");
        }
    }

    public async System.Threading.Tasks.Task<ApiResponse> OnEmployeeDeactivatedAsync(Guid employeeId)
    {
        try
        {
            _logger.LogInformation("Starting employee deactivation integration for {EmployeeId}", employeeId);

            // Cancel pending leave requests
            await CancelPendingLeaveRequestsAsync(employeeId);

            // Reassign active tasks
            await ReassignActiveTasksAsync(employeeId);

            // Handle attendance records
            await FinalizeAttendanceRecordsAsync(employeeId);

            // Update leave balances
            await FinalizeLeaveBalancesAsync(employeeId);

            // Process final payroll
            await ProcessFinalPayrollAsync(employeeId);

            // Revoke permissions
            await RevokeAllPermissionsAsync(employeeId);

            // Update performance reviews
            await FinalizePerformanceReviewsAsync(employeeId);

            // Remove from active manager assignments
            await RemoveFromManagerAssignmentsAsync(employeeId);

            _logger.LogInformation("Employee deactivation integration completed successfully for {EmployeeId}", employeeId);
            return ApiResponse.SuccessResult("Employee deactivation integration completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing employee deactivation for {EmployeeId}", employeeId);
            return ApiResponse.ErrorResult("INTEGRATION_ERROR", "Failed to process employee deactivation");
        }
    }

    public async System.Threading.Tasks.Task<ApiResponse<EmployeeIntegrationSummaryDto>> GetEmployeeIntegrationSummaryAsync(Guid employeeId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeIntegrationSummaryDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            // Get attendance summary
            var attendanceRecords = await _unitOfWork.Attendance.GetByUserAndDateRangeAsync(
                employeeId, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);
            
            // Get leave summary
            var leaveBalances = await _unitOfWork.Leaves.GetLeaveBalancesByUserAsync(employeeId, DateTime.UtcNow.Year);
            var leaveRequests = await _unitOfWork.Leaves.GetByUserAsync(employeeId);
            
            // Get task summary
            var tasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(employeeId);
            
            // Get payroll summary
            var employeeDetail = user.EmployeeDetail;

            var summary = new EmployeeIntegrationSummaryDto
            {
                EmployeeId = employeeId,
                EmployeeName = user.Name,
                AttendanceSummary = new AttendanceIntegrationSummaryDto
                {
                    TotalDaysPresent = attendanceRecords.Count(a => a.Status == AttendanceStatus.Present),
                    TotalDaysAbsent = attendanceRecords.Count(a => a.Status == AttendanceStatus.Absent),
                    AverageWorkHours = attendanceRecords.Where(a => a.TotalHours.HasValue).Any()
                        ? attendanceRecords.Where(a => a.TotalHours.HasValue).Average(a => (double)a.TotalHours.Value)
                        : 0.0,
                    LastCheckIn = attendanceRecords.OrderByDescending(a => a.Date).FirstOrDefault()?.CheckInTime
                },
                LeaveSummary = new LeaveIntegrationSummaryDto
                {
                    TotalLeaveBalance = leaveBalances.Sum(lb => lb.TotalAllocated - lb.UsedLeaves),
                    UsedLeaves = leaveBalances.Sum(lb => lb.UsedLeaves),
                    PendingRequests = leaveRequests.Count(lr => lr.Status == LeaveStatus.Pending),
                    ApprovedRequests = leaveRequests.Count(lr => lr.Status == LeaveStatus.Approved)
                },
                TaskSummary = new TaskIntegrationSummaryDto
                {
                    TotalTasks = tasks.Count(),
                    CompletedTasks = tasks.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed),
                    PendingTasks = tasks.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Pending),
                    InProgressTasks = tasks.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.InProgress),
                    OverdueTasks = tasks.Count(t => t.DueDate < DateTime.UtcNow && t.Status != HRMS.Core.Enums.TaskStatus.Completed)
                },
                PayrollSummary = new PayrollIntegrationSummaryDto
                {
                    BaseSalary = employeeDetail?.BaseSalary ?? 0,
                    AnnualCTC = employeeDetail?.AnnualCTC ?? 0,
                    LastPayrollDate = DateTime.UtcNow.AddMonths(-1), // Mock data
                    YtdEarnings = (employeeDetail?.AnnualCTC ?? 0) * DateTime.UtcNow.Month / 12
                }
            };

            return ApiResponse<EmployeeIntegrationSummaryDto>.SuccessResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee integration summary for {EmployeeId}", employeeId);
            return ApiResponse<EmployeeIntegrationSummaryDto>.ErrorResult("INTEGRATION_ERROR", 
                "Failed to get employee integration summary");
        }
    }

    private async System.Threading.Tasks.Task CreateDefaultLeaveBalancesAsync(Guid employeeId)
    {
        // With dynamic leave balance calculation, we no longer need to create static LeaveBalance records
        // The dynamic system calculates leave balance based on join date automatically
        // This method is kept for backward compatibility but now performs minimal setup

        var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
        if (user?.EmployeeDetail == null)
        {
            _logger.LogWarning("Employee {EmployeeId} has no employee details, cannot set up leave balance", employeeId);
            return;
        }

        var joinDate = user.EmployeeDetail.JoinDate;
        _logger.LogInformation("Employee {EmployeeId} joined on {JoinDate}, dynamic leave balance will be calculated automatically",
            employeeId, joinDate.ToString("yyyy-MM-dd"));

        // Note: Dynamic leave balance calculation will automatically provide:
        // - 1 initial leave upon joining
        // - 1 additional leave per complete month since joining
        // - No need to create static LeaveBalance records

        // The calculation is performed in real-time by DynamicLeaveBalanceService
        // based on the employee's join date stored in EmployeeDetail.JoinDate
    }

    private async System.Threading.Tasks.Task InitializeAttendanceRecordAsync(Guid employeeId)
    {
        // Create initial attendance record for today if not exists
        var today = DateTime.UtcNow.Date;
        var existingRecord = await _unitOfWork.Attendance.GetByUserAndDateAsync(employeeId, today);
        
        if (existingRecord == null)
        {
            var attendanceRecord = new AttendanceRecord
            {
                UserId = employeeId,
                Date = today,
                Status = AttendanceStatus.Absent, // Default to absent until check-in
                TotalHours = 0
            };

            await _unitOfWork.Attendance.AddAsync(attendanceRecord);
        }
    }

    private async System.Threading.Tasks.Task SetupDefaultPermissionsAsync(Guid employeeId)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
        if (user == null) return;

        // Get default role for the user's role type
        var defaultRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r =>
            r.OrganizationId == user.OrganizationId &&
            r.Name == user.Role.ToString() &&
            r.IsSystemRole);

        if (defaultRole != null)
        {
            var employeeRole = new EmployeeRole
            {
                EmployeeDetailId = user.EmployeeDetail?.Id ?? Guid.Empty,
                RoleId = defaultRole.Id,
                AssignedDate = DateTime.UtcNow,
                IsActive = true
            };

            await _unitOfWork.EmployeeRoles.AddAsync(employeeRole);
        }
    }

    private async System.Threading.Tasks.Task CreateDefaultPayrollStructureAsync(Guid employeeId)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
        if (user?.EmployeeDetail?.BaseSalary == null || user.OrganizationId == null) return;

        // Get default salary components for the organization
        var salaryComponents = await _unitOfWork.SalaryComponents.GetByOrganizationAsync(user.OrganizationId.Value);

        foreach (var component in salaryComponents)
        {
            var employeeSalaryStructure = new EmployeeSalaryStructure
            {
                EmployeeId = employeeId,
                ComponentId = component.Id,
                Amount = component.DefaultValue ?? 0,
                EffectiveFrom = DateTime.UtcNow,
                IsActive = true
            };

            await _unitOfWork.EmployeeSalaryStructures.AddAsync(employeeSalaryStructure);
        }
    }

    private async System.Threading.Tasks.Task UpdateDepartmentAssignmentsAsync(Guid employeeId, string newDepartment)
    {
        // Implementation for updating department assignments
        // This would involve updating DepartmentEmployee records
        _logger.LogInformation("Updating department assignments for employee {EmployeeId} to {Department}",
            employeeId, newDepartment);
    }

    private async System.Threading.Tasks.Task UpdateRolePermissionsAsync(Guid employeeId, string newRole)
    {
        // Implementation for updating role permissions
        _logger.LogInformation("Updating role permissions for employee {EmployeeId} to {Role}",
            employeeId, newRole);
    }

    private async System.Threading.Tasks.Task UpdatePayrollStructureAsync(Guid employeeId, decimal newSalary)
    {
        // Implementation for updating payroll structure
        _logger.LogInformation("Updating payroll structure for employee {EmployeeId} with new salary {Salary}",
            employeeId, newSalary);
    }

    private async System.Threading.Tasks.Task UpdateTaskAssignmentsAsync(Guid employeeId, Guid? newManagerId)
    {
        // Implementation for updating task assignments
        _logger.LogInformation("Updating task assignments for employee {EmployeeId} with new manager {ManagerId}",
            employeeId, newManagerId);
    }

    private async System.Threading.Tasks.Task CancelPendingLeaveRequestsAsync(Guid employeeId)
    {
        var pendingRequests = await _unitOfWork.Leaves.GetByUserAsync(employeeId);
        var pendingLeaves = pendingRequests.Where(lr => lr.Status == LeaveStatus.Pending);

        foreach (var leave in pendingLeaves)
        {
            leave.Status = LeaveStatus.Cancelled;
            await _unitOfWork.Leaves.UpdateAsync(leave);
        }
    }

    private async System.Threading.Tasks.Task ReassignActiveTasksAsync(Guid employeeId)
    {
        var activeTasks = await _unitOfWork.Tasks.GetByAssignedUserAsync(employeeId);
        var incompleteTasks = activeTasks.Where(t => t.Status != HRMS.Core.Enums.TaskStatus.Completed);

        foreach (var task in incompleteTasks)
        {
            task.Status = HRMS.Core.Enums.TaskStatus.Cancelled;
            await _unitOfWork.Tasks.UpdateAsync(task);
        }
    }

    private async System.Threading.Tasks.Task ProcessFinalPayrollAsync(Guid employeeId)
    {
        // Implementation for processing final payroll
        _logger.LogInformation("Processing final payroll for employee {EmployeeId}", employeeId);
    }

    private async System.Threading.Tasks.Task RevokeAllPermissionsAsync(Guid employeeId)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
        if (user?.EmployeeDetail == null) return;

        var employeeRoles = await _unitOfWork.EmployeeRoles.FindAsync(er => 
            er.EmployeeDetailId == user.EmployeeDetail.Id && er.IsActive);

        foreach (var role in employeeRoles)
        {
            role.IsActive = false;
            await _unitOfWork.EmployeeRoles.UpdateAsync(role);
        }
    }

    private async System.Threading.Tasks.Task FinalizeAttendanceRecordsAsync(Guid employeeId)
    {
        try
        {
            // Get any incomplete attendance records (checked in but not checked out)
            var incompleteRecords = await _unitOfWork.Attendance.GetByUserAndDateRangeAsync(
                employeeId, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);

            var incompleteToday = incompleteRecords.Where(ar =>
                ar.CheckInTime.HasValue &&
                !ar.CheckOutTime.HasValue &&
                ar.Date.Date == DateTime.UtcNow.Date).ToList();

            foreach (var record in incompleteToday)
            {
                // Auto check-out incomplete records
                record.CheckOutTime = DateTime.UtcNow;
                record.TotalHours = (decimal)(record.CheckOutTime.Value - record.CheckInTime!.Value).TotalHours;
                record.Status = AttendanceStatus.Present;
                await _unitOfWork.Attendance.UpdateAsync(record);
            }

            _logger.LogInformation("Finalized {Count} incomplete attendance records for employee {EmployeeId}",
                incompleteToday.Count, employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finalizing attendance records for employee {EmployeeId}", employeeId);
        }
    }

    private async System.Threading.Tasks.Task FinalizeLeaveBalancesAsync(Guid employeeId)
    {
        try
        {
            var currentYear = DateTime.UtcNow.Year;
            var leaveBalances = await _unitOfWork.LeaveBalances.GetByUserAsync(employeeId, currentYear);

            foreach (var balance in leaveBalances)
            {
                // Mark leave balances as finalized - set remaining leaves to 0
                balance.UsedLeaves = balance.TotalAllocated + balance.CarriedForward;
                balance.UpdatedAt = DateTime.UtcNow;
                await _unitOfWork.LeaveBalances.UpdateAsync(balance);
            }

            _logger.LogInformation("Finalized {Count} leave balances for employee {EmployeeId}",
                leaveBalances.Count(), employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finalizing leave balances for employee {EmployeeId}", employeeId);
        }
    }

    private async System.Threading.Tasks.Task FinalizePerformanceReviewsAsync(Guid employeeId)
    {
        try
        {
            var activeReviews = await _unitOfWork.Performance.GetByEmployeeAsync(employeeId);
            var pendingReviews = activeReviews.Where(pr => pr.Status == ReviewStatus.InProgress);

            foreach (var review in pendingReviews)
            {
                review.Status = ReviewStatus.Cancelled;
                review.Feedback = (review.Feedback ?? "") + "\n[System] Review cancelled due to employee deactivation.";
                review.UpdatedAt = DateTime.UtcNow;
                await _unitOfWork.Performance.UpdateAsync(review);
            }

            _logger.LogInformation("Finalized {Count} performance reviews for employee {EmployeeId}",
                pendingReviews.Count(), employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finalizing performance reviews for employee {EmployeeId}", employeeId);
        }
    }

    private async System.Threading.Tasks.Task RemoveFromManagerAssignmentsAsync(Guid employeeId)
    {
        try
        {
            // Find all employees who report to this manager
            var subordinates = await _unitOfWork.EmployeeDetails.GetByManagerIdAsync(employeeId);

            foreach (var subordinate in subordinates)
            {
                subordinate.ManagerId = null;
                subordinate.UpdatedAt = DateTime.UtcNow;
                await _unitOfWork.EmployeeDetails.UpdateAsync(subordinate);
            }

            _logger.LogInformation("Removed manager assignment from {Count} subordinates for employee {EmployeeId}",
                subordinates.Count(), employeeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing manager assignments for employee {EmployeeId}", employeeId);
        }
    }

}
