using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using HRMS.Core.Services;


namespace HRMS.Application.Services;

public class AuthService : IAuthService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IJwtService _jwtService;
    private readonly IPasswordService _passwordService;
    private readonly IPasswordValidationService _passwordValidationService;
    private readonly IDatabaseProvisioningService _provisioningService;
    private readonly IMasterDatabaseService _masterDatabaseService;
    private readonly ILogger<AuthService> _logger;

    public AuthService(
        IUnitOfWork unitOfWork,
        IJwtService jwtService,
        IPasswordService passwordService,
        IPasswordValidationService passwordValidationService,
        IDatabaseProvisioningService provisioningService,
        IMasterDatabaseService masterDatabaseService,
        ILogger<AuthService> logger)
    {
        _unitOfWork = unitOfWork;
        _jwtService = jwtService;
        _passwordService = passwordService;
        _passwordValidationService = passwordValidationService;
        _provisioningService = provisioningService;
        _masterDatabaseService = masterDatabaseService;
        _logger = logger;
    }

    public async Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginRequestDto request)
    {
        try
        {
            _logger.LogInformation("Starting login process for email: {Email}", request.Email);

            // Validate input
            if (string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.Password))
            {
                _logger.LogWarning("Login attempt with empty email or password");
                return ApiResponse<LoginResponseDto>.ErrorResult("INVALID_CREDENTIALS", "Email and password are required");
            }

            // Use master database service for authentication since all users are stored in master DB
            var user = await _masterDatabaseService.GetUserByEmailAsync(request.Email);
            if (user == null)
            {
                _logger.LogWarning("Login attempt for non-existent user: {Email}", request.Email);
                return ApiResponse<LoginResponseDto>.ErrorResult("INVALID_CREDENTIALS", "Invalid email or password");
            }

            _logger.LogInformation("User found in database: {UserId}, Role: {Role}, IsActive: {IsActive}",
                user.Id, user.Role, user.IsActive);

            // Verify password
            if (!_passwordService.VerifyPassword(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("Invalid password attempt for user: {Email}", request.Email);
                return ApiResponse<LoginResponseDto>.ErrorResult("INVALID_CREDENTIALS", "Invalid email or password");
            }

            // Check if account is active
            if (!user.IsActive)
            {
                _logger.LogWarning("Login attempt for disabled account: {Email}", request.Email);
                return ApiResponse<LoginResponseDto>.ErrorResult("ACCOUNT_DISABLED", "Account is disabled");
            }

            // Verify organization exists and is active
            if (user.OrganizationId.HasValue)
            {
                var organization = await _masterDatabaseService.GetOrganizationByIdAsync(user.OrganizationId.Value);
                if (organization == null || organization.Status != OrganizationStatus.Active)
                {
                    _logger.LogWarning("Login attempt for user with inactive organization: {Email}, OrgId: {OrganizationId}",
                        request.Email, user.OrganizationId);
                    return ApiResponse<LoginResponseDto>.ErrorResult("ORGANIZATION_INACTIVE", "Organization is not active");
                }
            }

            // Update last login timestamp
            user.LastLogin = DateTime.UtcNow;
            await _masterDatabaseService.UpdateUserAsync(user);

            // Generate JWT token
            var token = _jwtService.GenerateToken(user);
            var userDto = MapToUserDto(user);

            var response = new LoginResponseDto
            {
                User = userDto,
                Token = token,
                ExpiresIn = 86400 // 24 hours in seconds
            };

            _logger.LogInformation("Login successful for user: {Email}, UserId: {UserId}", request.Email, user.Id);
            return ApiResponse<LoginResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for email {Email}", request.Email);
            return ApiResponse<LoginResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during login");
        }
    }

    public async Task<ApiResponse<OrganizationDto>> RegisterOrganizationAsync(OrganizationRegistrationDto request)
    {
        try
        {
            // Check if domain already exists
            if (await _masterDatabaseService.DomainExistsAsync(request.Organization.Domain))
            {
                return ApiResponse<OrganizationDto>.ErrorResult("DOMAIN_EXISTS", "Organization domain already exists");
            }

            // Check if admin email already exists
            if (await _masterDatabaseService.EmailExistsAsync(request.Admin.Email))
            {
                return ApiResponse<OrganizationDto>.ErrorResult("EMAIL_EXISTS", "Admin email already exists");
            }

            await _masterDatabaseService.BeginTransactionAsync();

            try
            {
                // Create organization
                var organization = new Organization
                {
                    Name = request.Organization.Name,
                    Domain = request.Organization.Domain,
                    Industry = request.Organization.Industry,
                    Status = OrganizationStatus.Pending,
                    EmployeeCount = 1
                };

                organization = await _masterDatabaseService.CreateOrganizationAsync(organization);

                // Create admin user
                var adminUser = new User
                {
                    Email = request.Admin.Email,
                    Name = request.Admin.Name,
                    PasswordHash = _passwordService.HashPassword(request.Admin.Password),
                    Role = UserRole.OrgAdmin,
                    OrganizationId = organization.Id,
                    IsActive = true,
                    EmailVerified = true
                };

                await _masterDatabaseService.CreateUserAsync(adminUser);

                // Create comprehensive employee details for the admin
                var adminEmployeeDetail = new EmployeeDetail
                {
                    UserId = adminUser.Id,
                    EmployeeId = $"ADM{DateTime.UtcNow:yyyyMMdd}001",
                    JobTitle = "Organization Administrator",
                    Department = "Administration",
                    JoinDate = DateTime.UtcNow,
                    EmploymentType = EmploymentType.FullTime,
                    EmploymentStatus = EmploymentStatus.Active,
                    WorkLocation = "Head Office"
                };

                await _masterDatabaseService.CreateEmployeeDetailAsync(adminEmployeeDetail);

                // TODO: Provision database schema (temporarily disabled for testing)
                // var provisioningResult = await _provisioningService.ProvisionOrganizationSchemaAsync(organization.Id.ToString());
                // if (!provisioningResult)
                // {
                //     throw new InvalidOperationException("Failed to provision organization database schema");
                // }
                var provisioningResult = true; // Temporarily set to true for testing

                // Update organization status to active
                organization.Status = OrganizationStatus.Active;
                await _masterDatabaseService.UpdateOrganizationAsync(organization);

                await _masterDatabaseService.CommitTransactionAsync();

                var organizationDto = new OrganizationDto
                {
                    Id = organization.Id,
                    Name = organization.Name,
                    Domain = organization.Domain,
                    Industry = organization.Industry,
                    EmployeeCount = organization.EmployeeCount
                };

                return ApiResponse<OrganizationDto>.SuccessResult(organizationDto, "Organization registered successfully");
            }
            catch
            {
                await _masterDatabaseService.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during organization registration for domain {Domain}", request.Organization.Domain);
            return ApiResponse<OrganizationDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during registration");
        }
    }

    public async Task<ApiResponse<RefreshTokenResponseDto>> RefreshTokenAsync(RefreshTokenRequestDto request)
    {
        try
        {
            // For now, we'll implement a simple token refresh
            // In a production system, you'd want to store and validate refresh tokens
            if (!_jwtService.ValidateToken(request.RefreshToken))
            {
                return ApiResponse<RefreshTokenResponseDto>.ErrorResult("INVALID_TOKEN", "Invalid refresh token");
            }

            var userId = _jwtService.GetUserIdFromToken(request.RefreshToken);
            if (string.IsNullOrEmpty(userId))
            {
                return ApiResponse<RefreshTokenResponseDto>.ErrorResult("INVALID_TOKEN", "Invalid refresh token");
            }

            var user = await _unitOfWork.Users.GetByIdAsync(Guid.Parse(userId));
            if (user == null || !user.IsActive)
            {
                return ApiResponse<RefreshTokenResponseDto>.ErrorResult("USER_NOT_FOUND", "User not found or inactive");
            }

            var newToken = _jwtService.GenerateToken(user);

            var response = new RefreshTokenResponseDto
            {
                Token = newToken,
                ExpiresIn = 86400 // 24 hours in seconds
            };

            return ApiResponse<RefreshTokenResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return ApiResponse<RefreshTokenResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during token refresh");
        }
    }

    public async Task<ApiResponse> LogoutAsync(string userId)
    {
        try
        {
            _logger.LogInformation("Logout request for user: {UserId}", userId);

            // Invalidate all tokens for the user
            var invalidated = await _jwtService.InvalidateAllUserTokensAsync(userId, "logout");

            if (invalidated)
            {
                _logger.LogInformation("User logged out successfully and all tokens invalidated: {UserId}", userId);
                return ApiResponse.SuccessResult("Logged out successfully");
            }
            else
            {
                _logger.LogWarning("User logout completed but token invalidation failed: {UserId}", userId);
                return ApiResponse.SuccessResult("Logged out successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout for user {UserId}", userId);
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during logout");
        }
    }

    public async Task<ApiResponse> LogoutWithTokenAsync(string token, string userId)
    {
        try
        {
            _logger.LogInformation("Logout with token request for user: {UserId}", userId);

            // Invalidate the specific token
            var invalidated = await _jwtService.InvalidateTokenAsync(token, "logout");

            if (invalidated)
            {
                _logger.LogInformation("User logged out successfully and token invalidated: {UserId}", userId);
                return ApiResponse.SuccessResult("Logged out successfully");
            }
            else
            {
                _logger.LogWarning("User logout completed but token invalidation failed: {UserId}", userId);
                return ApiResponse.SuccessResult("Logged out successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout with token for user {UserId}", userId);
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during logout");
        }
    }

    public async Task<ApiResponse<UserDto>> GetCurrentUserAsync(string userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(Guid.Parse(userId));
            if (user == null)
            {
                return ApiResponse<UserDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            var userDto = MapToUserDto(user);
            return ApiResponse<UserDto>.SuccessResult(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user {UserId}", userId);
            return ApiResponse<UserDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting user information");
        }
    }

    public async Task<ApiResponse<UserDto>> UpdateUserProfileAsync(string userId, UpdateUserProfileDto request)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(Guid.Parse(userId));
            if (user == null)
            {
                return ApiResponse<UserDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            // Update user fields
            if (!string.IsNullOrEmpty(request.Name))
                user.Name = request.Name;
            
            if (!string.IsNullOrEmpty(request.AvatarUrl))
                user.AvatarUrl = request.AvatarUrl;

            await _unitOfWork.Users.UpdateAsync(user);

            // Update employee details if they exist
            if (user.EmployeeDetail != null)
            {
                if (!string.IsNullOrEmpty(request.Phone))
                    user.EmployeeDetail.Phone = request.Phone;
                
                if (!string.IsNullOrEmpty(request.Address))
                    user.EmployeeDetail.Address = request.Address;

                await _unitOfWork.EmployeeDetails.UpdateAsync(user.EmployeeDetail);
            }

            await _unitOfWork.SaveChangesAsync();

            var userDto = MapToUserDto(user);
            return ApiResponse<UserDto>.SuccessResult(userDto, "Profile updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user profile for user {UserId}", userId);
            return ApiResponse<UserDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while updating profile");
        }
    }

    public async Task<ApiResponse<PasswordResetResponseDto>> ChangePasswordAsync(string userId, ChangePasswordRequestDto request)
    {
        try
        {
            _logger.LogInformation("Password change attempt for user: {UserId}", userId);

            if (!Guid.TryParse(userId, out var userGuid))
            {
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("INVALID_USER_ID", "Invalid user ID format");
            }

            // Get the user from the database
            var user = await _masterDatabaseService.GetUserByIdAsync(userGuid);
            if (user == null)
            {
                _logger.LogWarning("Password change attempt for non-existent user: {UserId}", userId);
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            // Verify current password
            if (!_passwordService.VerifyPassword(request.CurrentPassword, user.PasswordHash))
            {
                _logger.LogWarning("Invalid current password for user: {UserId}", userId);
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("INVALID_CURRENT_PASSWORD", "Current password is incorrect");
            }

            // Validate new password
            var validationResult = _passwordValidationService.ValidatePassword(request.NewPassword);
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("; ", validationResult.Errors);
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("INVALID_PASSWORD", $"Password validation failed: {errorMessage}");
            }

            // Update password
            user.PasswordHash = _passwordService.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _masterDatabaseService.UpdateUserAsync(user);

            _logger.LogInformation("Password changed successfully for user: {UserId}", userId);

            var response = new PasswordResetResponseDto
            {
                Message = "Password changed successfully",
                RequirePasswordReset = false
            };

            return ApiResponse<PasswordResetResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user: {UserId}", userId);
            return ApiResponse<PasswordResetResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while changing password");
        }
    }

    public async Task<ApiResponse<PasswordResetResponseDto>> ResetUserPasswordAsync(string adminUserId, ResetPasswordRequestDto request)
    {
        try
        {
            _logger.LogInformation("Password reset attempt by admin: {AdminUserId} for user: {UserId}", adminUserId, request.UserId);

            if (!Guid.TryParse(adminUserId, out var adminUserGuid))
            {
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("INVALID_ADMIN_ID", "Invalid admin user ID format");
            }

            // Get the admin user and verify permissions
            var adminUser = await _masterDatabaseService.GetUserByIdAsync(adminUserGuid);
            if (adminUser == null)
            {
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("ADMIN_NOT_FOUND", "Admin user not found");
            }

            // Check if admin has permission to reset passwords
            if (adminUser.Role != UserRole.SuperAdmin && adminUser.Role != UserRole.OrgAdmin)
            {
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "Insufficient permissions to reset passwords");
            }

            // Get the target user
            var targetUser = await _masterDatabaseService.GetUserByIdAsync(request.UserId);
            if (targetUser == null)
            {
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("USER_NOT_FOUND", "Target user not found");
            }

            // For organization admins, ensure they can only reset passwords within their organization
            if (adminUser.Role == UserRole.OrgAdmin)
            {
                if (targetUser.OrganizationId != adminUser.OrganizationId)
                {
                    return ApiResponse<PasswordResetResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "Cannot reset password for users outside your organization");
                }

                // Organization admins cannot reset super admin passwords
                if (targetUser.Role == UserRole.SuperAdmin)
                {
                    return ApiResponse<PasswordResetResponseDto>.ErrorResult("INSUFFICIENT_PERMISSIONS", "Cannot reset super admin password");
                }
            }

            // Validate new password
            var validationResult = _passwordValidationService.ValidatePassword(request.NewPassword);
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("; ", validationResult.Errors);
                return ApiResponse<PasswordResetResponseDto>.ErrorResult("INVALID_PASSWORD", $"Password validation failed: {errorMessage}");
            }

            // Update password
            targetUser.PasswordHash = _passwordService.HashPassword(request.NewPassword);
            targetUser.UpdatedAt = DateTime.UtcNow;

            await _masterDatabaseService.UpdateUserAsync(targetUser);

            _logger.LogInformation("Password reset successfully by admin: {AdminUserId} for user: {UserId}", adminUserId, request.UserId);

            var response = new PasswordResetResponseDto
            {
                Message = "Password reset successfully",
                RequirePasswordReset = request.RequirePasswordReset
            };

            return ApiResponse<PasswordResetResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password by admin: {AdminUserId} for user: {UserId}", adminUserId, request.UserId);
            return ApiResponse<PasswordResetResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while resetting password");
        }
    }

    private UserDto MapToUserDto(User user)
    {
        var userDto = new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Name = user.Name,
            Role = user.Role.ToString().ToLowerInvariant(),
            AvatarUrl = user.AvatarUrl,
            OrganizationId = user.OrganizationId
        };

        if (user.Organization != null)
        {
            userDto.Organization = new OrganizationDto
            {
                Id = user.Organization.Id,
                Name = user.Organization.Name,
                Domain = user.Organization.Domain,
                Industry = user.Organization.Industry,
                EmployeeCount = user.Organization.EmployeeCount
            };
        }

        if (user.EmployeeDetail != null)
        {
            userDto.EmployeeDetails = new EmployeeDetailsDto
            {
                EmployeeId = user.EmployeeDetail.EmployeeId,
                JobTitle = user.EmployeeDetail.JobTitle,
                Department = user.EmployeeDetail.Department,
                Manager = user.EmployeeDetail.Manager?.Name,
                JoinDate = user.EmployeeDetail.JoinDate,
                EmploymentType = user.EmployeeDetail.EmploymentType.ToString().ToLowerInvariant(),
                EmploymentStatus = user.EmployeeDetail.EmploymentStatus.ToString().ToLowerInvariant(),
                WorkLocation = user.EmployeeDetail.WorkLocation,
                Phone = user.EmployeeDetail.Phone,
                Address = user.EmployeeDetail.Address,
                DateOfBirth = user.EmployeeDetail.DateOfBirth,
                BaseSalary = user.EmployeeDetail.BaseSalary,
                AnnualCTC = user.EmployeeDetail.AnnualCTC,
                NextSalaryReview = user.EmployeeDetail.NextSalaryReview,
                PerformanceRating = user.EmployeeDetail.PerformanceRating,
                TotalExperience = user.EmployeeDetail.TotalExperience,
                Education = user.EmployeeDetail.Education.Select(e => new EducationDto
                {
                    Degree = e.Degree,
                    Institution = e.Institution,
                    Year = e.Year
                }).ToList(),
                Skills = user.EmployeeDetail.Skills.Select(s => new SkillDto
                {
                    Name = s.Name,
                    Level = s.Level
                }).ToList()
            };
        }

        return userDto;
    }
}
