using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using TaskEntity = HRMS.Core.Entities.Task;

namespace HRMS.Application.Services;

public class PermissionService : IPermissionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PermissionService> _logger;

    public PermissionService(
        IUnitOfWork unitOfWork,
        ILogger<PermissionService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async System.Threading.Tasks.Task<ApiResponse<List<PermissionDto>>> GetAllPermissionsAsync()
    {
        try
        {
            var permissions = await _unitOfWork.Permissions.GetAllAsync();
            var permissionDtos = permissions.Select(MapToPermissionDto).ToList();
            return ApiResponse<List<PermissionDto>>.SuccessResult(permissionDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all permissions");
            return ApiResponse<List<PermissionDto>>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting permissions");
        }
    }

    public async System.Threading.Tasks.Task<ApiResponse<List<RoleDto>>> GetRolesByOrganizationAsync(Guid organizationId)
    {
        try
        {
            var roles = await _unitOfWork.Roles.GetByOrganizationAsync(organizationId);
            var roleDtos = roles.Select(MapToRoleDto).ToList();
            return ApiResponse<List<RoleDto>>.SuccessResult(roleDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for organization {OrganizationId}", organizationId);
            return ApiResponse<List<RoleDto>>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting roles");
        }
    }

    public async Task<ApiResponse<RoleDto>> CreateRoleAsync(CreateRoleRequestDto request)
    {
        try
        {
            // Check if role name already exists in organization
            var existingRole = await _unitOfWork.Roles.GetByNameAsync(request.OrganizationId, request.Name);
            if (existingRole != null)
            {
                return ApiResponse<RoleDto>.ErrorResult("ROLE_EXISTS", "Role with this name already exists");
            }

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                var role = new Role
                {
                    OrganizationId = request.OrganizationId,
                    Name = request.Name,
                    Description = request.Description,
                    IsSystemRole = false,
                    IsActive = true
                };

                await _unitOfWork.Roles.AddAsync(role);
                await _unitOfWork.SaveChangesAsync();

                // Assign permissions to role
                if (request.PermissionIds?.Any() == true)
                {
                    foreach (var permissionId in request.PermissionIds)
                    {
                        var rolePermission = new RolePermission
                        {
                            RoleId = role.Id,
                            PermissionId = permissionId
                        };
                        await _unitOfWork.RolePermissions.AddAsync(rolePermission);
                    }
                    await _unitOfWork.SaveChangesAsync();
                }

                await _unitOfWork.CommitTransactionAsync();

                var roleDto = MapToRoleDto(role);
                return ApiResponse<RoleDto>.SuccessResult(roleDto, "Role created successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role {RoleName}", request.Name);
            return ApiResponse<RoleDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while creating role");
        }
    }

    public async Task<ApiResponse<RoleDto>> UpdateRoleAsync(Guid roleId, UpdateRoleRequestDto request)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId);
            if (role == null)
            {
                return ApiResponse<RoleDto>.ErrorResult("ROLE_NOT_FOUND", "Role not found");
            }

            if (role.IsSystemRole)
            {
                return ApiResponse<RoleDto>.ErrorResult("SYSTEM_ROLE", "Cannot modify system roles");
            }

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Update role details
                if (!string.IsNullOrEmpty(request.Name))
                    role.Name = request.Name;
                if (!string.IsNullOrEmpty(request.Description))
                    role.Description = request.Description;
                if (request.IsActive.HasValue)
                    role.IsActive = request.IsActive.Value;

                await _unitOfWork.Roles.UpdateAsync(role);

                // Update permissions if provided
                if (request.PermissionIds != null)
                {
                    // Remove existing permissions
                    var existingPermissions = await _unitOfWork.RolePermissions.FindAsync(rp => rp.RoleId == roleId);
                    await _unitOfWork.RolePermissions.DeleteRangeAsync(existingPermissions);

                    // Add new permissions
                    foreach (var permissionId in request.PermissionIds)
                    {
                        var rolePermission = new RolePermission
                        {
                            RoleId = roleId,
                            PermissionId = permissionId
                        };
                        await _unitOfWork.RolePermissions.AddAsync(rolePermission);
                    }
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                var roleDto = MapToRoleDto(role);
                return ApiResponse<RoleDto>.SuccessResult(roleDto, "Role updated successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role {RoleId}", roleId);
            return ApiResponse<RoleDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while updating role");
        }
    }

    public async Task<ApiResponse> DeleteRoleAsync(Guid roleId)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId);
            if (role == null)
            {
                return ApiResponse.ErrorResult("ROLE_NOT_FOUND", "Role not found");
            }

            if (role.IsSystemRole)
            {
                return ApiResponse.ErrorResult("SYSTEM_ROLE", "Cannot delete system roles");
            }

            // Check if role is assigned to any employees
            var employeeRoles = await _unitOfWork.EmployeeRoles.FindAsync(er => er.RoleId == roleId && er.IsActive);
            if (employeeRoles.Any())
            {
                return ApiResponse.ErrorResult("ROLE_IN_USE", "Cannot delete role that is assigned to employees");
            }

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Remove role permissions
                var rolePermissions = await _unitOfWork.RolePermissions.FindAsync(rp => rp.RoleId == roleId);
                await _unitOfWork.RolePermissions.DeleteRangeAsync(rolePermissions);

                // Delete role
                await _unitOfWork.Roles.DeleteAsync(role);
                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return ApiResponse.SuccessResult("Role deleted successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role {RoleId}", roleId);
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while deleting role");
        }
    }

    public async Task<ApiResponse<List<EmployeeRoleDto>>> GetEmployeeRolesAsync(Guid employeeDetailId)
    {
        try
        {
            var employeeRoles = await _unitOfWork.EmployeeRoles.GetByEmployeeAsync(employeeDetailId);
            var employeeRoleDtos = employeeRoles.Select(MapToEmployeeRoleDto).ToList();
            return ApiResponse<List<EmployeeRoleDto>>.SuccessResult(employeeRoleDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee roles for {EmployeeDetailId}", employeeDetailId);
            return ApiResponse<List<EmployeeRoleDto>>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting employee roles");
        }
    }

    public async Task<ApiResponse> AssignRoleToEmployeeAsync(AssignRoleRequestDto request)
    {
        try
        {
            // Check if role is already assigned
            var existingAssignment = await _unitOfWork.EmployeeRoles.FirstOrDefaultAsync(er => 
                er.EmployeeDetailId == request.EmployeeDetailId && 
                er.RoleId == request.RoleId && 
                er.IsActive);

            if (existingAssignment != null)
            {
                return ApiResponse.ErrorResult("ROLE_ALREADY_ASSIGNED", "Role is already assigned to this employee");
            }

            var employeeRole = new EmployeeRole
            {
                EmployeeDetailId = request.EmployeeDetailId,
                RoleId = request.RoleId,
                AssignedDate = DateTime.UtcNow,
                ExpiryDate = request.ExpiryDate,
                IsActive = true
            };

            await _unitOfWork.EmployeeRoles.AddAsync(employeeRole);
            await _unitOfWork.SaveChangesAsync();

            return ApiResponse.SuccessResult("Role assigned successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role to employee");
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while assigning role");
        }
    }

    public async Task<ApiResponse> RevokeRoleFromEmployeeAsync(Guid employeeDetailId, Guid roleId)
    {
        try
        {
            var employeeRole = await _unitOfWork.EmployeeRoles.FirstOrDefaultAsync(er => 
                er.EmployeeDetailId == employeeDetailId && 
                er.RoleId == roleId && 
                er.IsActive);

            if (employeeRole == null)
            {
                return ApiResponse.ErrorResult("ROLE_NOT_ASSIGNED", "Role is not assigned to this employee");
            }

            employeeRole.IsActive = false;
            await _unitOfWork.EmployeeRoles.UpdateAsync(employeeRole);
            await _unitOfWork.SaveChangesAsync();

            return ApiResponse.SuccessResult("Role revoked successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking role from employee");
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while revoking role");
        }
    }

    public async Task<ApiResponse<bool>> CheckPermissionAsync(Guid employeeDetailId, string module, string action)
    {
        try
        {
            // Get employee roles
            var employeeRoles = await _unitOfWork.EmployeeRoles.GetByEmployeeAsync(employeeDetailId);
            var activeRoles = employeeRoles.Where(er => er.IsActive && 
                (er.ExpiryDate == null || er.ExpiryDate > DateTime.UtcNow));

            // Check role permissions
            foreach (var employeeRole in activeRoles)
            {
                var rolePermissions = await _unitOfWork.RolePermissions.FindAsync(rp => rp.RoleId == employeeRole.RoleId);
                foreach (var rolePermission in rolePermissions)
                {
                    var permission = await _unitOfWork.Permissions.GetByIdAsync(rolePermission.PermissionId);
                    if (permission != null && permission.Module == module && permission.Action == action && permission.IsActive)
                    {
                        return ApiResponse<bool>.SuccessResult(true);
                    }
                }
            }

            // Check direct employee permissions
            var employeePermissions = await _unitOfWork.EmployeePermissions.GetByEmployeeAsync(employeeDetailId);
            var activePermissions = employeePermissions.Where(ep => 
                ep.ExpiryDate == null || ep.ExpiryDate > DateTime.UtcNow);

            foreach (var employeePermission in activePermissions)
            {
                var permission = await _unitOfWork.Permissions.GetByIdAsync(employeePermission.PermissionId);
                if (permission != null && permission.Module == module && permission.Action == action && permission.IsActive)
                {
                    return ApiResponse<bool>.SuccessResult(employeePermission.IsGranted);
                }
            }

            return ApiResponse<bool>.SuccessResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for employee {EmployeeDetailId}", employeeDetailId);
            return ApiResponse<bool>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while checking permission");
        }
    }

    private PermissionDto MapToPermissionDto(Permission permission)
    {
        return new PermissionDto
        {
            Id = permission.Id,
            Name = permission.Name,
            Code = permission.Code,
            Description = permission.Description,
            Module = permission.Module,
            Action = permission.Action,
            IsActive = permission.IsActive
        };
    }

    private RoleDto MapToRoleDto(Role role)
    {
        return new RoleDto
        {
            Id = role.Id,
            OrganizationId = role.OrganizationId,
            Name = role.Name,
            Description = role.Description,
            IsSystemRole = role.IsSystemRole,
            IsActive = role.IsActive
        };
    }

    private EmployeeRoleDto MapToEmployeeRoleDto(EmployeeRole employeeRole)
    {
        return new EmployeeRoleDto
        {
            Id = employeeRole.Id,
            EmployeeDetailId = employeeRole.EmployeeDetailId,
            RoleId = employeeRole.RoleId,
            RoleName = employeeRole.Role?.Name ?? "Unknown",
            AssignedDate = employeeRole.AssignedDate,
            ExpiryDate = employeeRole.ExpiryDate,
            IsActive = employeeRole.IsActive
        };
    }
}
