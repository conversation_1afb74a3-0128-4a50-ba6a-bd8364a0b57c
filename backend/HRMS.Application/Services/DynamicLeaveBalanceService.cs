using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Interfaces;
using HRMS.Core.Enums;
using Microsoft.Extensions.Logging;

namespace HRMS.Application.Services;

/// <summary>
/// Service for calculating dynamic leave balances based on employee join date
/// Implements the policy: 1 initial leave + 1 leave per complete month since joining
/// </summary>
public class DynamicLeaveBalanceService : IDynamicLeaveBalanceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITimeZoneService _timeZoneService;
    private readonly ILogger<DynamicLeaveBalanceService> _logger;

    public DynamicLeaveBalanceService(
        IUnitOfWork unitOfWork,
        ITimeZoneService timeZoneService,
        ILogger<DynamicLeaveBalanceService> logger)
    {
        _unitOfWork = unitOfWork;
        _timeZoneService = timeZoneService;
        _logger = logger;
    }

    /// <summary>
    /// Calculate dynamic leave balance for an employee based on their join date
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Dynamic leave balance calculation result</returns>
    public async Task<ApiResponse<DynamicLeaveBalanceDto>> CalculateLeaveBalanceAsync(Guid employeeId)
    {
        try
        {
            // Get employee details to find join date
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user?.EmployeeDetail == null)
            {
                return ApiResponse<DynamicLeaveBalanceDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            var joinDate = user.EmployeeDetail.JoinDate;
            var currentDate = _timeZoneService.GetCurrentIstTime().Date;

            // Calculate dynamic leave balance
            var dynamicBalance = CalculateLeaveBalance(joinDate, currentDate);

            // Get used leaves from approved leave requests
            var leaveRequests = await _unitOfWork.Leaves.GetAllAsync();
            var userLeaveRequests = leaveRequests
                .Where(lr => lr.UserId == employeeId && lr.Status == LeaveStatus.Approved)
                .ToList();

            var usedLeaves = userLeaveRequests.Sum(lr => lr.TotalDays);

            // Get pending leaves
            var pendingLeaves = leaveRequests
                .Where(lr => lr.UserId == employeeId && lr.Status == LeaveStatus.Pending)
                .Sum(lr => lr.TotalDays);

            var result = new DynamicLeaveBalanceDto
            {
                EmployeeId = employeeId,
                JoinDate = joinDate,
                CalculationDate = currentDate,
                InitialAllocation = 1, // Always 1 initial leave
                MonthlyAccrual = dynamicBalance.MonthsCompleted,
                TotalAllocated = dynamicBalance.TotalLeaves,
                UsedLeaves = usedLeaves,
                PendingLeaves = pendingLeaves,
                RemainingLeaves = Math.Max(0, dynamicBalance.TotalLeaves - usedLeaves),
                MonthsCompleted = dynamicBalance.MonthsCompleted,
                NextAccrualDate = dynamicBalance.NextAccrualDate
            };

            _logger.LogInformation("Calculated dynamic leave balance for employee {EmployeeId}: Total={Total}, Used={Used}, Remaining={Remaining}",
                employeeId, result.TotalAllocated, result.UsedLeaves, result.RemainingLeaves);

            return ApiResponse<DynamicLeaveBalanceDto>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating dynamic leave balance for employee {EmployeeId}", employeeId);
            return ApiResponse<DynamicLeaveBalanceDto>.ErrorResult("CALCULATION_ERROR", "Error calculating leave balance");
        }
    }

    /// <summary>
    /// Calculate leave balance for multiple employees
    /// </summary>
    /// <param name="employeeIds">List of employee IDs</param>
    /// <returns>Dictionary of employee ID to leave balance</returns>
    public async Task<ApiResponse<Dictionary<Guid, DynamicLeaveBalanceDto>>> CalculateLeaveBalancesAsync(IEnumerable<Guid> employeeIds)
    {
        try
        {
            var results = new Dictionary<Guid, DynamicLeaveBalanceDto>();

            foreach (var employeeId in employeeIds)
            {
                var balanceResult = await CalculateLeaveBalanceAsync(employeeId);
                if (balanceResult.Success && balanceResult.Data != null)
                {
                    results[employeeId] = balanceResult.Data;
                }
            }

            return ApiResponse<Dictionary<Guid, DynamicLeaveBalanceDto>>.SuccessResult(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating dynamic leave balances for multiple employees");
            return ApiResponse<Dictionary<Guid, DynamicLeaveBalanceDto>>.ErrorResult("CALCULATION_ERROR", "Error calculating leave balances");
        }
    }

    /// <summary>
    /// Core calculation logic for leave balance based on join date
    /// </summary>
    /// <param name="joinDate">Employee join date</param>
    /// <param name="currentDate">Current date for calculation</param>
    /// <returns>Leave balance calculation details</returns>
    private LeaveBalanceCalculation CalculateLeaveBalance(DateTime joinDate, DateTime currentDate)
    {
        // Ensure we're working with dates only (no time component)
        joinDate = joinDate.Date;
        currentDate = currentDate.Date;

        // If current date is before join date, no leaves allocated
        if (currentDate < joinDate)
        {
            return new LeaveBalanceCalculation
            {
                TotalLeaves = 0,
                MonthsCompleted = 0,
                NextAccrualDate = joinDate.AddMonths(1)
            };
        }

        // Calculate complete months since joining
        var monthsCompleted = CalculateCompleteMonths(joinDate, currentDate);

        // Total leaves = 1 initial + 1 per complete month
        var totalLeaves = 1 + monthsCompleted;

        // Calculate next accrual date (first day of next month after the last completed month)
        var nextAccrualDate = joinDate.AddMonths(monthsCompleted + 1);

        return new LeaveBalanceCalculation
        {
            TotalLeaves = totalLeaves,
            MonthsCompleted = monthsCompleted,
            NextAccrualDate = nextAccrualDate
        };
    }

    /// <summary>
    /// Calculate the number of complete months between two dates
    /// </summary>
    /// <param name="startDate">Start date (join date)</param>
    /// <param name="endDate">End date (current date)</param>
    /// <returns>Number of complete months</returns>
    private int CalculateCompleteMonths(DateTime startDate, DateTime endDate)
    {
        if (endDate < startDate)
            return 0;

        var months = 0;
        var currentMonth = startDate.AddMonths(1);

        // Count complete months from the month after joining
        while (currentMonth <= endDate)
        {
            months++;
            currentMonth = currentMonth.AddMonths(1);
        }

        return months;
    }

    /// <summary>
    /// Get leave balance summary for dashboard display
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Leave balance summary</returns>
    public async Task<ApiResponse<LeaveBalanceSummaryDto>> GetLeaveBalanceSummaryAsync(Guid employeeId)
    {
        try
        {
            var balanceResult = await CalculateLeaveBalanceAsync(employeeId);
            if (!balanceResult.Success || balanceResult.Data == null)
            {
                return ApiResponse<LeaveBalanceSummaryDto>.ErrorResult(balanceResult.Error?.Code ?? "ERROR", 
                    balanceResult.Error?.Message ?? "Failed to calculate leave balance");
            }

            var balance = balanceResult.Data;
            var summary = new LeaveBalanceSummaryDto
            {
                TotalLeaves = balance.TotalAllocated,
                UsedLeaves = balance.UsedLeaves,
                RemainingLeaves = balance.RemainingLeaves,
                PendingLeaves = balance.PendingLeaves,
                MonthlyAccrual = 1, // Always 1 leave per month
                NextAccrualDate = balance.NextAccrualDate
            };

            return ApiResponse<LeaveBalanceSummaryDto>.SuccessResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave balance summary for employee {EmployeeId}", employeeId);
            return ApiResponse<LeaveBalanceSummaryDto>.ErrorResult("SUMMARY_ERROR", "Error getting leave balance summary");
        }
    }

    /// <summary>
    /// Internal class for leave balance calculation results
    /// </summary>
    private class LeaveBalanceCalculation
    {
        public int TotalLeaves { get; set; }
        public int MonthsCompleted { get; set; }
        public DateTime NextAccrualDate { get; set; }
    }
}
