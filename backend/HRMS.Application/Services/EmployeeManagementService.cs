using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using HRMS.Core.Services;

namespace HRMS.Application.Services;

public class EmployeeManagementService : IEmployeeManagementService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordService _passwordService;
    private readonly IEmployeeIntegrationService _integrationService;
    private readonly ILogger<EmployeeManagementService> _logger;
    private readonly ICredentialGenerationService _credentialService;
    private readonly IDatabaseUpdateService _databaseUpdateService;
    private readonly ITenantService _tenantService;
    private readonly IPasswordValidationService _passwordValidationService;

    public EmployeeManagementService(
        IUnitOfWork unitOfWork,
        IPasswordService passwordService,
        IEmployeeIntegrationService integrationService,
        ILogger<EmployeeManagementService> logger,
        ICredentialGenerationService credentialService,
        IDatabaseUpdateService databaseUpdateService,
        ITenantService tenantService,
        IPasswordValidationService passwordValidationService)
    {
        _unitOfWork = unitOfWork;
        _passwordService = passwordService;
        _integrationService = integrationService;
        _logger = logger;
        _credentialService = credentialService;
        _databaseUpdateService = databaseUpdateService;
        _tenantService = tenantService;
        _passwordValidationService = passwordValidationService;
    }

    public async Task<ApiResponse<PaginatedResponse<EmployeeDto>>> GetEmployeesAsync(GetEmployeesRequestDto request)
    {
        try
        {
            // Get current tenant context to ensure organization isolation
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse<PaginatedResponse<EmployeeDto>>.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            var organizationGuid = Guid.Parse(tenant.OrganizationId);
            var users = await _unitOfWork.Users.GetAllAsync();

            // Filter only active employees from the current organization (include OrgAdmin and Employee roles, exclude SuperAdmin, not deleted)
            var employees = users.Where(u =>
                (u.Role == UserRole.OrgAdmin || u.Role == UserRole.Employee) &&
                u.IsActive &&
                !u.IsDeleted &&
                u.OrganizationId == organizationGuid).AsEnumerable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.Department))
            {
                employees = employees.Where(u => 
                    u.EmployeeDetail?.Department?.Contains(request.Department, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (!string.IsNullOrEmpty(request.Role))
            {
                employees = employees.Where(u => 
                    u.Role.ToString().Equals(request.Role, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(request.EmploymentType))
            {
                employees = employees.Where(u => 
                    u.EmployeeDetail?.EmploymentType.ToString().Equals(request.EmploymentType, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (!string.IsNullOrEmpty(request.EmploymentStatus))
            {
                employees = employees.Where(u => 
                    u.EmployeeDetail?.EmploymentStatus.ToString().Equals(request.EmploymentStatus, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (!string.IsNullOrEmpty(request.Search))
            {
                employees = employees.Where(u => 
                    u.Name.Contains(request.Search, StringComparison.OrdinalIgnoreCase) ||
                    u.Email.Contains(request.Search, StringComparison.OrdinalIgnoreCase) ||
                    (u.EmployeeDetail?.EmployeeId?.Contains(request.Search, StringComparison.OrdinalIgnoreCase) == true) ||
                    (u.EmployeeDetail?.JobTitle?.Contains(request.Search, StringComparison.OrdinalIgnoreCase) == true));
            }

            if (request.ManagerId.HasValue)
            {
                employees = employees.Where(u => u.EmployeeDetail?.ManagerId == request.ManagerId);
            }

            // Sorting - Always prioritize OrgAdmin users first, then apply requested sorting
            employees = request.SortBy?.ToLower() switch
            {
                "name" => request.SortOrder?.ToLower() == "desc"
                    ? employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenByDescending(u => u.Name)
                    : employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenBy(u => u.Name),
                "joiningdate" => request.SortOrder?.ToLower() == "desc"
                    ? employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenByDescending(u => u.EmployeeDetail?.JoinDate)
                    : employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenBy(u => u.EmployeeDetail?.JoinDate),
                "department" => request.SortOrder?.ToLower() == "desc"
                    ? employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenByDescending(u => u.EmployeeDetail?.Department)
                    : employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenBy(u => u.EmployeeDetail?.Department),
                _ => employees.OrderBy(u => u.Role == UserRole.OrgAdmin ? 0 : 1).ThenBy(u => u.Name)
            };

            // Pagination
            var totalItems = employees.Count();
            var totalPages = (int)Math.Ceiling((double)totalItems / request.Limit);
            var skip = (request.Page - 1) * request.Limit;
            var pagedEmployees = employees.Skip(skip).Take(request.Limit);

            var employeeDtos = pagedEmployees.Select(MapToEmployeeDto).ToList();

            var response = new PaginatedResponse<EmployeeDto>
            {
                Items = employeeDtos,
                Pagination = new PaginationMetadata
                {
                    CurrentPage = request.Page,
                    TotalPages = totalPages,
                    TotalItems = totalItems,
                    ItemsPerPage = request.Limit
                }
            };

            return ApiResponse<PaginatedResponse<EmployeeDto>>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employees");
            return ApiResponse<PaginatedResponse<EmployeeDto>>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting employees");
        }
    }

    public async Task<ApiResponse<EmployeeDto>> GetEmployeeByIdAsync(Guid employeeId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            var employeeDto = MapToEmployeeDto(user);
            return ApiResponse<EmployeeDto>.SuccessResult(employeeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee by ID {EmployeeId}", employeeId);
            return ApiResponse<EmployeeDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting employee");
        }
    }

    public async Task<ApiResponse<EmployeeDto>> CreateEmployeeAsync(CreateEmployeeRequestDto request)
    {
        try
        {
            // Check if email already exists
            if (await _unitOfWork.Users.EmailExistsAsync(request.Email))
            {
                return ApiResponse<EmployeeDto>.ErrorResult("EMAIL_EXISTS", "Email already exists");
            }

            // Check if employee ID already exists
            var existingEmployee = await _unitOfWork.EmployeeDetails.GetByEmployeeIdAsync(request.EmployeeDetails.EmployeeId);
            if (existingEmployee != null)
            {
                return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_ID_EXISTS", "Employee ID already exists");
            }

            // Ensure database schema is up to date with login credential columns
            var tenant = _tenantService.GetCurrentTenant();
            var organizationId = tenant.OrganizationId ?? "1";
            await _databaseUpdateService.AddLoginCredentialColumnsAsync(organizationId);

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Generate automatic login credentials using existing email
                var credentials = await _credentialService.GenerateCredentialsWithEmailAsync(request.Email);

                // Use the existing tenant context to get organization ID
                if (string.IsNullOrEmpty(organizationId) || !Guid.TryParse(organizationId, out var organizationGuid))
                {
                    return ApiResponse<EmployeeDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
                }

                // Create user with generated credentials and organization ID
                var user = new User
                {
                    Email = request.Email,
                    Name = request.Name,
                    PasswordHash = credentials.HashedPassword, // Use generated password hash
                    Role = Enum.Parse<UserRole>(request.Role, true),
                    OrganizationId = organizationGuid, // Set the organization ID
                    IsActive = true,
                    EmailVerified = false
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Create comprehensive employee details with login credentials
                var employeeDetail = CreateEmployeeDetailFromRequest(user.Id, request.EmployeeDetails);

                // Add the generated login credentials (for reference and admin display)
                employeeDetail.LoginUsername = credentials.Username; // This will be the same as user.Email
                employeeDetail.TemporaryPassword = credentials.TemporaryPassword; // Store plain text for admin display
                employeeDetail.RequirePasswordReset = credentials.RequirePasswordReset;
                employeeDetail.PasswordGeneratedAt = credentials.GeneratedAt;

                await _unitOfWork.EmployeeDetails.AddAsync(employeeDetail);
                await _unitOfWork.SaveChangesAsync();

                await _unitOfWork.CommitTransactionAsync();

                // Trigger cross-module integration
                await _integrationService.OnEmployeeCreatedAsync(user.Id);

                // Load the complete user with employee details
                var createdUser = await _unitOfWork.Users.GetByIdAsync(user.Id);
                var employeeDto = MapToEmployeeDto(createdUser!);

                // Add login credentials to response
                employeeDto.LoginUsername = credentials.Username;
                employeeDto.LoginTemporaryPassword = credentials.TemporaryPassword;

                _logger.LogInformation("Successfully created employee with login credentials. Email: {Email}, LoginUsername: {LoginUsername}",
                    request.Email, credentials.Username);

                return ApiResponse<EmployeeDto>.SuccessResult(employeeDto, "Employee created successfully with login credentials");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating employee with email {Email}", request.Email);
            return ApiResponse<EmployeeDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while creating employee");
        }
    }

    public async Task<ApiResponse<EmployeeDto>> UpdateEmployeeAsync(Guid employeeId, UpdateEmployeeRequestDto request)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Update user fields
                if (!string.IsNullOrEmpty(request.Name))
                    user.Name = request.Name;

                if (!string.IsNullOrEmpty(request.Email))
                    user.Email = request.Email;

                if (!string.IsNullOrEmpty(request.Role))
                    user.Role = Enum.Parse<UserRole>(request.Role, true);

                if (request.IsActive.HasValue)
                    user.IsActive = request.IsActive.Value;

                await _unitOfWork.Users.UpdateAsync(user);

                // Update employee details
                if (request.EmployeeDetails != null && user.EmployeeDetail != null)
                {
                    UpdateEmployeeDetailFromRequest(user.EmployeeDetail, request.EmployeeDetails);
                    await _unitOfWork.EmployeeDetails.UpdateAsync(user.EmployeeDetail);
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                // Trigger cross-module integration for updates
                var updateContext = new EmployeeUpdateContext
                {
                    DepartmentChanged = !string.IsNullOrEmpty(request.EmployeeDetails?.Department),
                    NewDepartment = request.EmployeeDetails?.Department,
                    RoleChanged = !string.IsNullOrEmpty(request.Role),
                    NewRole = request.Role,
                    SalaryChanged = request.EmployeeDetails?.BaseSalary.HasValue == true,
                    NewSalary = request.EmployeeDetails?.BaseSalary,
                    ManagerChanged = request.EmployeeDetails?.ManagerId.HasValue == true,
                    NewManagerId = request.EmployeeDetails?.ManagerId
                };

                await _integrationService.OnEmployeeUpdatedAsync(employeeId, updateContext);

                var employeeDto = MapToEmployeeDto(user);
                return ApiResponse<EmployeeDto>.SuccessResult(employeeDto, "Employee updated successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating employee {EmployeeId}", employeeId);
            return ApiResponse<EmployeeDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while updating employee");
        }
    }

    private EmployeeDetail CreateEmployeeDetailFromRequest(Guid userId, CreateEmployeeDetailsDto details)
    {
        return new EmployeeDetail
        {
            UserId = userId,
            EmployeeId = details.EmployeeId,
            JobTitle = details.JobTitle,
            Department = details.Department,
            ManagerId = details.ManagerId,
            JoinDate = details.JoinDate,
            EmploymentType = Enum.Parse<EmploymentType>(details.EmploymentType.Replace("-", ""), true),
            EmploymentStatus = EmploymentStatus.Active,
            WorkLocation = details.WorkLocation,
            Phone = details.Phone,
            Address = details.Address,
            DateOfBirth = details.DateOfBirth,
            BaseSalary = details.BaseSalary,
            AnnualCTC = details.AnnualCTC,
            EmergencyContactName = details.EmergencyContactName,
            EmergencyContactPhone = details.EmergencyContactPhone,
            EmergencyContactRelation = details.EmergencyContactRelation,
            BloodGroup = details.BloodGroup,
            MaritalStatus = details.MaritalStatus,
            Gender = details.Gender,
            Nationality = details.Nationality,
            BankAccountNumber = details.BankAccountNumber,
            BankIFSC = details.BankIFSC,
            BankName = details.BankName,
            PAN = details.PAN,
            Aadhar = details.Aadhar,
            ProbationEndDate = details.ProbationEndDate,
            ConfirmationDate = details.ConfirmationDate
        };
    }

    private void UpdateEmployeeDetailFromRequest(EmployeeDetail employeeDetail, UpdateEmployeeDetailsDto details)
    {
        if (!string.IsNullOrEmpty(details.JobTitle))
            employeeDetail.JobTitle = details.JobTitle;

        if (!string.IsNullOrEmpty(details.Department))
            employeeDetail.Department = details.Department;

        if (details.ManagerId.HasValue)
            employeeDetail.ManagerId = details.ManagerId;

        if (!string.IsNullOrEmpty(details.EmploymentType))
            employeeDetail.EmploymentType = Enum.Parse<EmploymentType>(details.EmploymentType.Replace("-", ""), true);

        if (!string.IsNullOrEmpty(details.EmploymentStatus))
            employeeDetail.EmploymentStatus = Enum.Parse<EmploymentStatus>(details.EmploymentStatus, true);

        if (!string.IsNullOrEmpty(details.WorkLocation))
            employeeDetail.WorkLocation = details.WorkLocation;

        if (!string.IsNullOrEmpty(details.Phone))
            employeeDetail.Phone = details.Phone;

        if (!string.IsNullOrEmpty(details.Address))
            employeeDetail.Address = details.Address;

        if (details.DateOfBirth.HasValue)
            employeeDetail.DateOfBirth = details.DateOfBirth;

        if (details.BaseSalary.HasValue)
            employeeDetail.BaseSalary = details.BaseSalary;

        if (details.AnnualCTC.HasValue)
            employeeDetail.AnnualCTC = details.AnnualCTC;

        // Update additional fields
        if (!string.IsNullOrEmpty(details.EmergencyContactName))
            employeeDetail.EmergencyContactName = details.EmergencyContactName;

        if (!string.IsNullOrEmpty(details.EmergencyContactPhone))
            employeeDetail.EmergencyContactPhone = details.EmergencyContactPhone;

        if (!string.IsNullOrEmpty(details.EmergencyContactRelation))
            employeeDetail.EmergencyContactRelation = details.EmergencyContactRelation;

        if (!string.IsNullOrEmpty(details.BloodGroup))
            employeeDetail.BloodGroup = details.BloodGroup;

        if (!string.IsNullOrEmpty(details.MaritalStatus))
            employeeDetail.MaritalStatus = details.MaritalStatus;

        if (!string.IsNullOrEmpty(details.Gender))
            employeeDetail.Gender = details.Gender;

        if (!string.IsNullOrEmpty(details.Nationality))
            employeeDetail.Nationality = details.Nationality;

        if (!string.IsNullOrEmpty(details.BankAccountNumber))
            employeeDetail.BankAccountNumber = details.BankAccountNumber;

        if (!string.IsNullOrEmpty(details.BankIFSC))
            employeeDetail.BankIFSC = details.BankIFSC;

        if (!string.IsNullOrEmpty(details.BankName))
            employeeDetail.BankName = details.BankName;

        if (!string.IsNullOrEmpty(details.PAN))
            employeeDetail.PAN = details.PAN;

        if (!string.IsNullOrEmpty(details.Aadhar))
            employeeDetail.Aadhar = details.Aadhar;

        if (details.ProbationEndDate.HasValue)
            employeeDetail.ProbationEndDate = details.ProbationEndDate;

        if (details.ConfirmationDate.HasValue)
            employeeDetail.ConfirmationDate = details.ConfirmationDate;

        if (details.ResignationDate.HasValue)
            employeeDetail.ResignationDate = details.ResignationDate;

        if (details.LastWorkingDate.HasValue)
            employeeDetail.LastWorkingDate = details.LastWorkingDate;

        if (!string.IsNullOrEmpty(details.ResignationReason))
            employeeDetail.ResignationReason = details.ResignationReason;
    }

    private EmployeeDto MapToEmployeeDto(User user)
    {
        var employeeDto = new EmployeeDto
        {
            Id = user.Id,
            Email = user.Email,
            Name = user.Name,
            Role = user.Role.ToString().ToLowerInvariant(),
            AvatarUrl = user.AvatarUrl,
            IsActive = user.IsActive,
            LastLogin = user.LastLogin,
            CreatedAt = user.CreatedAt
        };

        if (user.Organization != null)
        {
            employeeDto.Organization = new OrganizationDto
            {
                Id = user.Organization.Id,
                Name = user.Organization.Name,
                Domain = user.Organization.Domain,
                Industry = user.Organization.Industry,
                EmployeeCount = user.Organization.EmployeeCount
            };
        }

        if (user.EmployeeDetail != null)
        {
            employeeDto.EmployeeDetails = MapToEmployeeDetailsDto(user.EmployeeDetail);

            // Also set root level login credential fields for easier frontend access
            employeeDto.LoginUsername = user.EmployeeDetail.LoginUsername;
            employeeDto.LoginTemporaryPassword = user.EmployeeDetail.TemporaryPassword;
            employeeDto.RequirePasswordReset = user.EmployeeDetail.RequirePasswordReset;
            employeeDto.PasswordGeneratedAt = user.EmployeeDetail.PasswordGeneratedAt;
        }

        return employeeDto;
    }

    private EmployeeDetailsDto MapToEmployeeDetailsDto(EmployeeDetail employeeDetail)
    {
        return new EmployeeDetailsDto
        {
            EmployeeId = employeeDetail.EmployeeId,
            JobTitle = employeeDetail.JobTitle,
            Department = employeeDetail.Department,
            Manager = employeeDetail.Manager?.Name,
            JoinDate = employeeDetail.JoinDate,
            EmploymentType = employeeDetail.EmploymentType.ToString().ToLowerInvariant(),
            EmploymentStatus = employeeDetail.EmploymentStatus.ToString().ToLowerInvariant(),
            WorkLocation = employeeDetail.WorkLocation,
            Phone = employeeDetail.Phone,
            Address = employeeDetail.Address,
            DateOfBirth = employeeDetail.DateOfBirth,
            BaseSalary = employeeDetail.BaseSalary,
            AnnualCTC = employeeDetail.AnnualCTC,
            NextSalaryReview = employeeDetail.NextSalaryReview,
            PerformanceRating = employeeDetail.PerformanceRating,
            TotalExperience = employeeDetail.TotalExperience,
            EmergencyContactName = employeeDetail.EmergencyContactName,
            EmergencyContactPhone = employeeDetail.EmergencyContactPhone,
            EmergencyContactRelation = employeeDetail.EmergencyContactRelation,
            BloodGroup = employeeDetail.BloodGroup,
            MaritalStatus = employeeDetail.MaritalStatus,
            Gender = employeeDetail.Gender,
            Nationality = employeeDetail.Nationality,
            BankAccountNumber = employeeDetail.BankAccountNumber,
            BankIFSC = employeeDetail.BankIFSC,
            BankName = employeeDetail.BankName,
            PAN = employeeDetail.PAN,
            Aadhar = employeeDetail.Aadhar,
            ProbationEndDate = employeeDetail.ProbationEndDate,
            ConfirmationDate = employeeDetail.ConfirmationDate,
            ResignationDate = employeeDetail.ResignationDate,
            LastWorkingDate = employeeDetail.LastWorkingDate,
            ResignationReason = employeeDetail.ResignationReason,
            Education = employeeDetail.Education.Select(e => new EducationDto
            {
                Degree = e.Degree,
                Institution = e.Institution,
                Year = e.Year,
                FieldOfStudy = e.FieldOfStudy,
                Grade = e.Grade
            }).ToList(),
            Skills = employeeDetail.Skills.Select(s => new SkillDto
            {
                Name = s.Name,
                Level = s.Level,
                Category = s.Category,
                YearsOfExperience = s.YearsOfExperience
            }).ToList(),

            // Login credential fields
            LoginUsername = employeeDetail.LoginUsername,
            LoginTemporaryPassword = employeeDetail.TemporaryPassword, // Include temporary password for admin view
            RequirePasswordReset = employeeDetail.RequirePasswordReset,
            PasswordGeneratedAt = employeeDetail.PasswordGeneratedAt
        };
    }

    public async Task<ApiResponse> DeleteEmployeeAsync(Guid employeeId)
    {
        try
        {
            // Use the method that includes deleted records for deletion operations
            var user = await _unitOfWork.Users.GetByIdIncludingDeletedAsync(employeeId);
            if (user == null)
            {
                return ApiResponse.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            // Check if already deleted
            if (user.IsDeleted)
            {
                return ApiResponse.ErrorResult("EMPLOYEE_ALREADY_DELETED", "Employee is already deleted");
            }

            // Validate multi-tenant isolation - ensure user can only delete employees from their organization
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            var organizationGuid = Guid.Parse(tenant.OrganizationId);
            if (user.OrganizationId != organizationGuid)
            {
                return ApiResponse.ErrorResult("UNAUTHORIZED_ACCESS", "Cannot delete employee from different organization");
            }

            // Validate deletion constraints
            var validationResult = await ValidateEmployeeDeletionAsync(employeeId);
            if (!validationResult.Success)
            {
                return validationResult;
            }

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                // Soft delete
                user.IsActive = false;
                user.IsDeleted = true;
                user.DeletedAt = DateTime.UtcNow;

                if (user.EmployeeDetail != null)
                {
                    user.EmployeeDetail.EmploymentStatus = EmploymentStatus.Terminated;
                    user.EmployeeDetail.LastWorkingDate = DateTime.UtcNow;
                    user.EmployeeDetail.ResignationReason = "Account deactivated by administrator";
                }

                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Trigger cross-module integration for deactivation
                await _integrationService.OnEmployeeDeactivatedAsync(employeeId);

                await _unitOfWork.CommitTransactionAsync();

                return ApiResponse.SuccessResult("Employee deleted successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting employee {EmployeeId}", employeeId);
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while deleting employee");
        }
    }

    public async Task<ApiResponse<OrganizationHierarchyDto>> GetOrganizationHierarchyAsync()
    {
        try
        {
            // Get current tenant context to ensure organization isolation
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse<OrganizationHierarchyDto>.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            var organizationGuid = Guid.Parse(tenant.OrganizationId);
            var users = await _unitOfWork.Users.GetAllAsync();
            var employees = users.Where(u =>
                (u.Role == UserRole.OrgAdmin || u.Role == UserRole.Employee) &&
                u.EmployeeDetail != null &&
                u.IsActive &&
                !u.IsDeleted &&
                u.OrganizationId == organizationGuid).ToList();

            // Group by department
            var departmentGroups = employees
                .GroupBy(u => u.EmployeeDetail!.Department)
                .ToDictionary(g => g.Key, g => g.ToList());

            var hierarchy = new OrganizationHierarchyDto
            {
                Departments = departmentGroups.Select(dept => new DepartmentHierarchyDto
                {
                    Name = dept.Key,
                    EmployeeCount = dept.Value.Count,
                    Manager = dept.Value.FirstOrDefault(e => e.Role == UserRole.OrgAdmin)?.Name ?? "N/A",
                    Employees = dept.Value.Select(emp => new EmployeeHierarchyDto
                    {
                        Id = emp.Id,
                        Name = emp.Name,
                        Role = emp.EmployeeDetail!.JobTitle,
                        Level = emp.EmployeeDetail.EmploymentType.ToString(),
                        ReportingTo = emp.EmployeeDetail.Manager?.Name ?? "N/A",
                        Email = emp.Email,
                        Department = emp.EmployeeDetail.Department
                    }).ToList()
                }).ToList()
            };

            return ApiResponse<OrganizationHierarchyDto>.SuccessResult(hierarchy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting organization hierarchy");
            return ApiResponse<OrganizationHierarchyDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting organization hierarchy");
        }
    }

    private async Task<ApiResponse> ValidateEmployeeDeletionAsync(Guid employeeId)
    {
        try
        {
            // Check if employee is a manager with active subordinates
            var subordinates = await GetEmployeesByManagerAsync(employeeId);
            if (subordinates.Success && subordinates.Data != null && subordinates.Data.Any())
            {
                return ApiResponse.ErrorResult("EMPLOYEE_HAS_SUBORDINATES",
                    $"Cannot delete employee who manages {subordinates.Data.Count} subordinate(s). Please reassign or remove subordinates first.");
            }

            // Check if employee has pending leave requests as approver
            var tenant = _tenantService.GetCurrentTenant();
            if (!string.IsNullOrEmpty(tenant.OrganizationId))
            {
                var organizationGuid = Guid.Parse(tenant.OrganizationId);
                var allLeaves = await _unitOfWork.Leaves.GetByOrganizationAsync(organizationGuid);
                var pendingLeavesAsApprover = allLeaves.Where(lr =>
                    lr.ApprovedBy == employeeId &&
                    lr.Status == LeaveStatus.Pending).ToList();

                if (pendingLeavesAsApprover.Any())
                {
                    return ApiResponse.ErrorResult("EMPLOYEE_HAS_PENDING_APPROVALS",
                        $"Cannot delete employee who has {pendingLeavesAsApprover.Count} pending leave approval(s). Please reassign or process these requests first.");
                }
            }

            return ApiResponse.SuccessResult("Validation passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating employee deletion for {EmployeeId}", employeeId);
            return ApiResponse.ErrorResult("VALIDATION_ERROR", "Error occurred during deletion validation");
        }
    }

    public async Task<ApiResponse<List<EmployeeDto>>> GetEmployeesByManagerAsync(Guid managerId)
    {
        try
        {
            // Get current tenant context to ensure organization isolation
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse<List<EmployeeDto>>.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            var organizationGuid = Guid.Parse(tenant.OrganizationId);
            var users = await _unitOfWork.Users.GetAllAsync();
            var subordinates = users.Where(u =>
                u.EmployeeDetail?.ManagerId == managerId &&
                u.OrganizationId == organizationGuid &&
                u.IsActive &&
                !u.IsDeleted).ToList();

            var employeeDtos = subordinates.Select(MapToEmployeeDto).ToList();
            return ApiResponse<List<EmployeeDto>>.SuccessResult(employeeDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employees by manager {ManagerId}", managerId);
            return ApiResponse<List<EmployeeDto>>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting employees by manager");
        }
    }

    public async Task<ApiResponse<bool>> ValidateHierarchyAssignmentAsync(Guid employeeId, Guid managerId)
    {
        try
        {
            // Basic validation
            if (employeeId == managerId)
            {
                return ApiResponse<bool>.ErrorResult("INVALID_ASSIGNMENT", "An employee cannot be their own manager");
            }

            var users = await _unitOfWork.Users.GetAllAsync();
            var employee = users.FirstOrDefault(u => u.Id == employeeId);
            var manager = users.FirstOrDefault(u => u.Id == managerId);

            if (employee == null)
            {
                return ApiResponse<bool>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            if (manager == null)
            {
                return ApiResponse<bool>.ErrorResult("MANAGER_NOT_FOUND", "Manager not found");
            }

            if (!manager.IsActive)
            {
                return ApiResponse<bool>.ErrorResult("INACTIVE_MANAGER", "Cannot assign inactive employee as manager");
            }

            // Check for circular reporting relationships
            var currentManagerId = manager.EmployeeDetail?.ManagerId;
            var visited = new HashSet<Guid>();

            while (currentManagerId.HasValue && !visited.Contains(currentManagerId.Value))
            {
                if (currentManagerId.Value == employeeId)
                {
                    return ApiResponse<bool>.ErrorResult("CIRCULAR_RELATIONSHIP",
                        "This assignment would create a circular reporting relationship");
                }

                visited.Add(currentManagerId.Value);
                var currentManager = users.FirstOrDefault(u => u.Id == currentManagerId.Value);
                currentManagerId = currentManager?.EmployeeDetail?.ManagerId;
            }

            return ApiResponse<bool>.SuccessResult(true, "Hierarchy assignment is valid");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating hierarchy assignment for employee {EmployeeId} to manager {ManagerId}",
                employeeId, managerId);
            return ApiResponse<bool>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while validating hierarchy assignment");
        }
    }

    public async Task<ApiResponse<List<EmployeeDto>>> GetEmployeesByDepartmentAsync(string department)
    {
        try
        {
            // Get current tenant context to ensure organization isolation
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse<List<EmployeeDto>>.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            var organizationGuid = Guid.Parse(tenant.OrganizationId);
            var users = await _unitOfWork.Users.GetAllAsync();
            var departmentEmployees = users.Where(u =>
                u.EmployeeDetail?.Department?.Equals(department, StringComparison.OrdinalIgnoreCase) == true &&
                u.OrganizationId == organizationGuid).ToList();

            var employeeDtos = departmentEmployees.Select(MapToEmployeeDto).ToList();
            return ApiResponse<List<EmployeeDto>>.SuccessResult(employeeDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employees by department {Department}", department);
            return ApiResponse<List<EmployeeDto>>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting employees by department");
        }
    }

    public async Task<ApiResponse<EmployeeStatsDto>> GetEmployeeStatsAsync()
    {
        try
        {
            // Get current tenant context to ensure organization isolation
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse<EmployeeStatsDto>.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            var organizationGuid = Guid.Parse(tenant.OrganizationId);
            var users = await _unitOfWork.Users.GetAllAsync();
            var employees = users.Where(u =>
                u.Role != UserRole.SuperAdmin &&
                u.IsActive &&
                !u.IsDeleted &&
                u.OrganizationId == organizationGuid).ToList();

            var stats = new EmployeeStatsDto
            {
                TotalEmployees = employees.Count,
                ActiveEmployees = employees.Count(e => e.IsActive && e.EmployeeDetail?.EmploymentStatus == EmploymentStatus.Active),
                InactiveEmployees = employees.Count(e => !e.IsActive || e.EmployeeDetail?.EmploymentStatus != EmploymentStatus.Active),
                NewHiresThisMonth = employees.Count(e => e.EmployeeDetail?.JoinDate.Month == DateTime.UtcNow.Month &&
                                                        e.EmployeeDetail.JoinDate.Year == DateTime.UtcNow.Year),
                DepartmentBreakdown = employees
                    .Where(e => e.EmployeeDetail != null)
                    .GroupBy(e => e.EmployeeDetail!.Department)
                    .ToDictionary(g => g.Key, g => g.Count()),
                EmploymentTypeBreakdown = employees
                    .Where(e => e.EmployeeDetail != null)
                    .GroupBy(e => e.EmployeeDetail!.EmploymentType)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count())
            };

            return ApiResponse<EmployeeStatsDto>.SuccessResult(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee stats");
            return ApiResponse<EmployeeStatsDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting employee statistics");
        }
    }

    public async Task<ApiResponse<EmployeeDto>> GenerateCredentialsAsync(Guid employeeId)
    {
        // Call the new method with empty request for backward compatibility
        return await GenerateCredentialsAsync(employeeId, new GenerateCredentialsRequestDto());
    }

    public async Task<ApiResponse<EmployeeDto>> GenerateCredentialsAsync(Guid employeeId, GenerateCredentialsRequestDto request)
    {
        try
        {
            // Get the employee
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            // Check if employee already has credentials (only block if no custom password provided)
            if (user.EmployeeDetail != null && !string.IsNullOrEmpty(user.EmployeeDetail.LoginUsername) &&
                string.IsNullOrWhiteSpace(request.CustomPassword))
            {
                return ApiResponse<EmployeeDto>.ErrorResult("CREDENTIALS_ALREADY_EXIST",
                    "Employee already has login credentials. Use custom password option to regenerate.");
            }

            // Ensure database schema is up to date with login credential columns
            var tenant = _tenantService.GetCurrentTenant();
            var organizationId = tenant.OrganizationId ?? "1";
            await _databaseUpdateService.AddLoginCredentialColumnsAsync(organizationId);

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                EmployeeCredentialsDto credentials;

                // Check if custom password is provided
                if (!string.IsNullOrWhiteSpace(request.CustomPassword))
                {
                    // Validate the custom password
                    var validationResult = _passwordValidationService.ValidatePassword(request.CustomPassword);
                    if (!validationResult.IsValid)
                    {
                        var errorMessage = string.Join("; ", validationResult.Errors);
                        return ApiResponse<EmployeeDto>.ErrorResult("INVALID_PASSWORD",
                            $"Password validation failed: {errorMessage}");
                    }

                    // Generate credentials with custom password
                    credentials = await _credentialService.GenerateCredentialsWithCustomPasswordAsync(user.Email, request.CustomPassword);
                    _logger.LogInformation("Generated credentials with custom password for employee: {Email}", user.Email);
                }
                else
                {
                    // Generate credentials with auto-generated password
                    credentials = await _credentialService.GenerateCredentialsWithEmailAsync(user.Email);
                    _logger.LogInformation("Generated credentials with auto-generated password for employee: {Email}", user.Email);
                }

                // Update employee details with login credentials
                if (user.EmployeeDetail == null)
                {
                    return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_DETAILS_NOT_FOUND",
                        "Employee details not found");
                }

                // Update User table with new password hash (this is what's used for authentication)
                user.PasswordHash = credentials.HashedPassword;

                // Update employee details with login credentials (for reference and admin display)
                user.EmployeeDetail.LoginUsername = credentials.Username; // This will be the same as user.Email
                user.EmployeeDetail.TemporaryPassword = credentials.TemporaryPassword; // Store plain text for admin display
                user.EmployeeDetail.RequirePasswordReset = credentials.RequirePasswordReset;
                user.EmployeeDetail.PasswordGeneratedAt = credentials.GeneratedAt;

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                // Load the updated user with employee details
                var updatedUser = await _unitOfWork.Users.GetByIdAsync(user.Id);
                var employeeDto = MapToEmployeeDto(updatedUser!);

                // Add the plain-text temporary password to response for admin display
                employeeDto.LoginUsername = credentials.Username;
                employeeDto.LoginTemporaryPassword = credentials.TemporaryPassword;

                var passwordType = !string.IsNullOrWhiteSpace(request.CustomPassword) ? "custom" : "auto-generated";
                _logger.LogInformation("Successfully generated {PasswordType} credentials for existing employee. Email: {Email}, LoginUsername: {LoginUsername}",
                    passwordType, user.Email, credentials.Username);

                return ApiResponse<EmployeeDto>.SuccessResult(employeeDto, $"Login credentials generated successfully with {passwordType} password");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating credentials for employee {EmployeeId}", employeeId);
            return ApiResponse<EmployeeDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while generating credentials");
        }
    }

    public async Task<ApiResponse<EmployeeDto>> ChangePasswordAsync(Guid employeeId, ChangePasswordRequestDto request)
    {
        try
        {
            // Get the employee
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            // Validate the new password
            var validationResult = _passwordValidationService.ValidatePassword(request.NewPassword);
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("; ", validationResult.Errors);
                return ApiResponse<EmployeeDto>.ErrorResult("INVALID_PASSWORD",
                    $"Password validation failed: {errorMessage}");
            }

            // Ensure database schema is up to date with login credential columns
            var tenant = _tenantService.GetCurrentTenant();
            var organizationId = tenant.OrganizationId ?? "1";
            await _databaseUpdateService.AddLoginCredentialColumnsAsync(organizationId);

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Generate credentials with the new password
                var credentials = await _credentialService.GenerateCredentialsWithCustomPasswordAsync(user.Email, request.NewPassword);

                // Update employee details with login credentials
                if (user.EmployeeDetail == null)
                {
                    return ApiResponse<EmployeeDto>.ErrorResult("EMPLOYEE_DETAILS_NOT_FOUND",
                        "Employee details not found");
                }

                // Update User table with new password hash (this is what's used for authentication)
                user.PasswordHash = credentials.HashedPassword;

                // Update employee details with login credentials (for reference and admin display)
                user.EmployeeDetail.LoginUsername = credentials.Username; // This will be the same as user.Email
                user.EmployeeDetail.TemporaryPassword = credentials.TemporaryPassword; // Store plain text for admin display
                user.EmployeeDetail.RequirePasswordReset = true; // Always require password reset after admin change
                user.EmployeeDetail.PasswordGeneratedAt = credentials.GeneratedAt;

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                // Load the updated user with employee details
                var updatedUser = await _unitOfWork.Users.GetByIdAsync(user.Id);
                var employeeDto = MapToEmployeeDto(updatedUser!);

                // Add the plain-text temporary password to response for admin display
                employeeDto.LoginUsername = credentials.Username;
                employeeDto.LoginTemporaryPassword = credentials.TemporaryPassword;

                _logger.LogInformation("Successfully changed password for employee. Email: {Email}, LoginUsername: {LoginUsername}",
                    user.Email, credentials.Username);

                return ApiResponse<EmployeeDto>.SuccessResult(employeeDto, "Employee password changed successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for employee {EmployeeId}", employeeId);
            return ApiResponse<EmployeeDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while changing password");
        }
    }

    private static string ExtractDomainFromEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email) || !email.Contains('@'))
            return "company.com"; // Default domain

        var parts = email.Split('@');
        return parts.Length > 1 ? parts[1] : "company.com";
    }
}
