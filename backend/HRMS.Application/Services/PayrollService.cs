using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Interfaces;

namespace HRMS.Application.Services;

public class PayrollService : IPayrollService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PayrollService> _logger;

    public PayrollService(IUnitOfWork unitOfWork, ILogger<PayrollService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ApiResponse<EmployeePayrollDto>> GetEmployeePayrollAsync(string userId, GetEmployeePayrollRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);
            var user = await _unitOfWork.Users.GetByIdAsync(userGuid);

            if (user == null || user.EmployeeDetail == null)
            {
                return ApiResponse<EmployeePayrollDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            var currentYear = request.Year ?? DateTime.UtcNow.Year;
            var currentMonth = request.Month ?? DateTime.UtcNow.Month;

            // Calculate payroll data (this would typically come from a payroll system)
            var baseSalary = user.EmployeeDetail.BaseSalary ?? 0;
            var monthlySalary = user.EmployeeDetail.AnnualCTC.HasValue 
                ? user.EmployeeDetail.AnnualCTC.Value / 12 
                : baseSalary;

            // Mock calculations for demonstration
            var taxDeductions = monthlySalary * 0.20m; // 20% tax
            var benefits = monthlySalary * 0.05m; // 5% benefits
            var totalDeductions = taxDeductions;
            var netSalary = monthlySalary - totalDeductions + benefits;

            var currentMonthPayroll = new CurrentMonthPayrollDto
            {
                GrossSalary = monthlySalary,
                NetSalary = netSalary,
                TotalDeductions = totalDeductions,
                TaxDeductions = taxDeductions,
                Benefits = benefits,
                Status = "processed",
                PayDate = new DateTime(currentYear, currentMonth, 25) // Assume 25th of each month
            };

            // YTD calculations
            var monthsElapsed = currentMonth;
            var ytdSummary = new YearToDateSummaryDto
            {
                YtdGross = monthlySalary * monthsElapsed,
                YtdNet = netSalary * monthsElapsed,
                YtdTax = taxDeductions * monthsElapsed
            };

            // Generate pay stubs for the year
            var payStubs = new List<PayStubDto>();
            for (int month = 1; month <= Math.Min(currentMonth, 12); month++)
            {
                payStubs.Add(new PayStubDto
                {
                    Period = $"{currentYear}-{month:D2}",
                    Gross = monthlySalary,
                    Net = netSalary,
                    Status = month <= currentMonth ? "paid" : "pending",
                    PayDate = new DateTime(currentYear, month, 25)
                });
            }

            var response = new EmployeePayrollDto
            {
                CurrentMonth = currentMonthPayroll,
                YtdSummary = ytdSummary,
                PayStubs = payStubs
            };

            return ApiResponse<EmployeePayrollDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee payroll for user {UserId}", userId);
            return ApiResponse<EmployeePayrollDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting payroll information");
        }
    }

    public async Task<ApiResponse<EmployeeBenefitsDto>> GetEmployeeBenefitsAsync(string userId)
    {
        try
        {
            var userGuid = Guid.Parse(userId);
            var user = await _unitOfWork.Users.GetByIdAsync(userGuid);

            if (user == null || user.EmployeeDetail == null)
            {
                return ApiResponse<EmployeeBenefitsDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            // Mock benefits data (this would typically come from a benefits management system)
            var benefits = new List<BenefitDto>
            {
                new BenefitDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Health Insurance",
                    Type = "Medical",
                    EmployeeContribution = 150.00m,
                    EmployerContribution = 300.00m,
                    Status = "active",
                    EffectiveFrom = user.EmployeeDetail.JoinDate
                },
                new BenefitDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Retirement Plan (401k)",
                    Type = "Retirement",
                    EmployeeContribution = 500.00m,
                    EmployerContribution = 250.00m,
                    Status = "active",
                    EffectiveFrom = user.EmployeeDetail.JoinDate.AddMonths(3) // Eligible after 3 months
                },
                new BenefitDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Life Insurance",
                    Type = "Insurance",
                    EmployeeContribution = 25.00m,
                    EmployerContribution = 75.00m,
                    Status = "active",
                    EffectiveFrom = user.EmployeeDetail.JoinDate
                },
                new BenefitDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Dental Insurance",
                    Type = "Medical",
                    EmployeeContribution = 50.00m,
                    EmployerContribution = 100.00m,
                    Status = "active",
                    EffectiveFrom = user.EmployeeDetail.JoinDate
                }
            };

            var response = new EmployeeBenefitsDto
            {
                Benefits = benefits
            };

            return ApiResponse<EmployeeBenefitsDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee benefits for user {UserId}", userId);
            return ApiResponse<EmployeeBenefitsDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting benefits information");
        }
    }
}
