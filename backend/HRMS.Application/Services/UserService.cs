using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;

namespace HRMS.Application.Services;

public class UserService : IUserService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordService _passwordService;
    private readonly ITenantService _tenantService;
    private readonly ILogger<UserService> _logger;

    public UserService(
        IUnitOfWork unitOfWork,
        IPasswordService passwordService,
        ITenantService tenantService,
        ILogger<UserService> logger)
    {
        _unitOfWork = unitOfWork;
        _passwordService = passwordService;
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<ApiResponse<PaginatedResponse<UserDto>>> GetUsersAsync(GetUsersRequestDto request)
    {
        try
        {
            // For now, we'll implement basic pagination without complex filtering
            // In a real implementation, you'd want to use proper pagination with Entity Framework
            var users = await _unitOfWork.Users.GetAllAsync();

            // Apply filters
            var filteredUsers = users.AsEnumerable();

            if (!string.IsNullOrEmpty(request.Department))
            {
                filteredUsers = filteredUsers.Where(u => 
                    u.EmployeeDetail?.Department?.Contains(request.Department, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (!string.IsNullOrEmpty(request.Role))
            {
                filteredUsers = filteredUsers.Where(u => 
                    u.Role.ToString().Equals(request.Role, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(request.Search))
            {
                filteredUsers = filteredUsers.Where(u => 
                    u.Name.Contains(request.Search, StringComparison.OrdinalIgnoreCase) ||
                    u.Email.Contains(request.Search, StringComparison.OrdinalIgnoreCase));
            }

            // Pagination
            var totalItems = filteredUsers.Count();
            var totalPages = (int)Math.Ceiling((double)totalItems / request.Limit);
            var skip = (request.Page - 1) * request.Limit;
            var pagedUsers = filteredUsers.Skip(skip).Take(request.Limit);

            var userDtos = pagedUsers.Select(MapToUserDto).ToList();

            var response = new PaginatedResponse<UserDto>
            {
                Items = userDtos,
                Pagination = new PaginationMetadata
                {
                    CurrentPage = request.Page,
                    TotalPages = totalPages,
                    TotalItems = totalItems,
                    ItemsPerPage = request.Limit
                }
            };

            return ApiResponse<PaginatedResponse<UserDto>>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users");
            return ApiResponse<PaginatedResponse<UserDto>>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting users");
        }
    }

    public async Task<ApiResponse<UserDto>> CreateEmployeeAsync(CreateEmployeeDto request)
    {
        try
        {
            // Check if email already exists
            if (await _unitOfWork.Users.EmailExistsAsync(request.Email))
            {
                return ApiResponse<UserDto>.ErrorResult("EMAIL_EXISTS", "Email already exists");
            }

            // Check if employee ID already exists
            var existingEmployee = await _unitOfWork.EmployeeDetails.GetByEmployeeIdAsync(request.EmployeeDetails.EmployeeId);
            if (existingEmployee != null)
            {
                return ApiResponse<UserDto>.ErrorResult("EMPLOYEE_ID_EXISTS", "Employee ID already exists");
            }

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Generate temporary password
                var tempPassword = _passwordService.GenerateTemporaryPassword();

                // Get current organization ID from tenant context
                var tenant = _tenantService.GetCurrentTenant();
                var currentOrgId = tenant.OrganizationId;

                if (string.IsNullOrEmpty(currentOrgId) || !Guid.TryParse(currentOrgId, out var organizationGuid))
                {
                    return ApiResponse<UserDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
                }

                // Create user with organization ID
                var user = new User
                {
                    Email = request.Email,
                    Name = request.Name,
                    PasswordHash = _passwordService.HashPassword(tempPassword),
                    Role = Enum.Parse<UserRole>(request.Role, true),
                    OrganizationId = organizationGuid, // Set the organization ID
                    IsActive = true,
                    EmailVerified = false
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Create employee details
                var employeeDetail = new EmployeeDetail
                {
                    UserId = user.Id,
                    EmployeeId = request.EmployeeDetails.EmployeeId,
                    JobTitle = request.EmployeeDetails.JobTitle,
                    Department = request.EmployeeDetails.Department,
                    ManagerId = request.EmployeeDetails.ManagerId,
                    JoinDate = request.EmployeeDetails.JoinDate,
                    EmploymentType = Enum.Parse<EmploymentType>(request.EmployeeDetails.EmploymentType.Replace("-", ""), true),
                    BaseSalary = request.EmployeeDetails.BaseSalary,
                    AnnualCTC = request.EmployeeDetails.AnnualCTC
                };

                await _unitOfWork.EmployeeDetails.AddAsync(employeeDetail);
                await _unitOfWork.SaveChangesAsync();

                await _unitOfWork.CommitTransactionAsync();

                // Load the complete user with employee details
                var createdUser = await _unitOfWork.Users.GetByIdAsync(user.Id);
                var userDto = MapToUserDto(createdUser!);

                // Add temporary password to response (in real app, send via email)
                userDto.EmployeeDetails!.TotalExperience = $"Temporary Password: {tempPassword}";

                return ApiResponse<UserDto>.SuccessResult(userDto, "Employee created successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating employee with email {Email}", request.Email);
            return ApiResponse<UserDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while creating employee");
        }
    }

    public async Task<ApiResponse<UserDto>> GetUserByIdAsync(Guid userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<UserDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            var userDto = MapToUserDto(user);
            return ApiResponse<UserDto>.SuccessResult(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID {UserId}", userId);
            return ApiResponse<UserDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting user");
        }
    }

    public async Task<ApiResponse<UserDto>> UpdateUserAsync(Guid userId, UpdateUserDto request)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<UserDto>.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            // Update user fields
            if (!string.IsNullOrEmpty(request.Name))
                user.Name = request.Name;

            if (!string.IsNullOrEmpty(request.Role))
                user.Role = Enum.Parse<UserRole>(request.Role, true);

            if (request.IsActive.HasValue)
                user.IsActive = request.IsActive.Value;

            await _unitOfWork.Users.UpdateAsync(user);

            // Update employee details if provided
            if (request.EmployeeDetails != null && user.EmployeeDetail != null)
            {
                var employeeDetail = user.EmployeeDetail;

                if (!string.IsNullOrEmpty(request.EmployeeDetails.JobTitle))
                    employeeDetail.JobTitle = request.EmployeeDetails.JobTitle;

                if (!string.IsNullOrEmpty(request.EmployeeDetails.Department))
                    employeeDetail.Department = request.EmployeeDetails.Department;

                if (request.EmployeeDetails.ManagerId.HasValue)
                    employeeDetail.ManagerId = request.EmployeeDetails.ManagerId;

                if (!string.IsNullOrEmpty(request.EmployeeDetails.EmploymentType))
                    employeeDetail.EmploymentType = Enum.Parse<EmploymentType>(request.EmployeeDetails.EmploymentType.Replace("-", ""), true);

                if (!string.IsNullOrEmpty(request.EmployeeDetails.EmploymentStatus))
                    employeeDetail.EmploymentStatus = Enum.Parse<EmploymentStatus>(request.EmployeeDetails.EmploymentStatus, true);

                if (!string.IsNullOrEmpty(request.EmployeeDetails.WorkLocation))
                    employeeDetail.WorkLocation = request.EmployeeDetails.WorkLocation;

                if (!string.IsNullOrEmpty(request.EmployeeDetails.Phone))
                    employeeDetail.Phone = request.EmployeeDetails.Phone;

                if (!string.IsNullOrEmpty(request.EmployeeDetails.Address))
                    employeeDetail.Address = request.EmployeeDetails.Address;

                if (request.EmployeeDetails.BaseSalary.HasValue)
                    employeeDetail.BaseSalary = request.EmployeeDetails.BaseSalary;

                if (request.EmployeeDetails.AnnualCTC.HasValue)
                    employeeDetail.AnnualCTC = request.EmployeeDetails.AnnualCTC;

                await _unitOfWork.EmployeeDetails.UpdateAsync(employeeDetail);
            }

            await _unitOfWork.SaveChangesAsync();

            var userDto = MapToUserDto(user);
            return ApiResponse<UserDto>.SuccessResult(userDto, "User updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", userId);
            return ApiResponse<UserDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while updating user");
        }
    }

    public async Task<ApiResponse> DeleteUserAsync(Guid userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse.ErrorResult("USER_NOT_FOUND", "User not found");
            }

            // Soft delete
            user.IsActive = false;
            user.IsDeleted = true;
            user.DeletedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return ApiResponse.SuccessResult("User deleted successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", userId);
            return ApiResponse.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while deleting user");
        }
    }

    private UserDto MapToUserDto(User user)
    {
        var userDto = new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Name = user.Name,
            Role = user.Role.ToString().ToLowerInvariant(),
            AvatarUrl = user.AvatarUrl,
            OrganizationId = user.OrganizationId
        };

        if (user.Organization != null)
        {
            userDto.Organization = new OrganizationDto
            {
                Id = user.Organization.Id,
                Name = user.Organization.Name,
                Domain = user.Organization.Domain,
                Industry = user.Organization.Industry,
                EmployeeCount = user.Organization.EmployeeCount
            };
        }

        if (user.EmployeeDetail != null)
        {
            userDto.EmployeeDetails = new EmployeeDetailsDto
            {
                EmployeeId = user.EmployeeDetail.EmployeeId,
                JobTitle = user.EmployeeDetail.JobTitle,
                Department = user.EmployeeDetail.Department,
                Manager = user.EmployeeDetail.Manager?.Name,
                JoinDate = user.EmployeeDetail.JoinDate,
                EmploymentType = user.EmployeeDetail.EmploymentType.ToString().ToLowerInvariant(),
                EmploymentStatus = user.EmployeeDetail.EmploymentStatus.ToString().ToLowerInvariant(),
                WorkLocation = user.EmployeeDetail.WorkLocation,
                Phone = user.EmployeeDetail.Phone,
                Address = user.EmployeeDetail.Address,
                DateOfBirth = user.EmployeeDetail.DateOfBirth,
                BaseSalary = user.EmployeeDetail.BaseSalary,
                AnnualCTC = user.EmployeeDetail.AnnualCTC,
                NextSalaryReview = user.EmployeeDetail.NextSalaryReview,
                PerformanceRating = user.EmployeeDetail.PerformanceRating,
                TotalExperience = user.EmployeeDetail.TotalExperience,
                Education = user.EmployeeDetail.Education.Select(e => new EducationDto
                {
                    Degree = e.Degree,
                    Institution = e.Institution,
                    Year = e.Year
                }).ToList(),
                Skills = user.EmployeeDetail.Skills.Select(s => new SkillDto
                {
                    Name = s.Name,
                    Level = s.Level
                }).ToList()
            };
        }

        return userDto;
    }
}
