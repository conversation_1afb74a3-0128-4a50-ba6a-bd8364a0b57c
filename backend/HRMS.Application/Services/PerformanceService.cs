using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;

namespace HRMS.Application.Services;

public class PerformanceService : IPerformanceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PerformanceService> _logger;

    public PerformanceService(IUnitOfWork unitOfWork, ILogger<PerformanceService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ApiResponse<PerformanceReviewsResponseDto>> GetPerformanceReviewsAsync(GetPerformanceReviewsRequestDto request)
    {
        try
        {
            IEnumerable<PerformanceReview> reviews;

            if (request.EmployeeId.HasValue)
            {
                reviews = await _unitOfWork.Performance.GetByEmployeeAsync(request.EmployeeId.Value);
            }
            else if (request.ReviewerId.HasValue)
            {
                reviews = await _unitOfWork.Performance.GetByReviewerAsync(request.ReviewerId.Value);
            }
            else
            {
                reviews = await _unitOfWork.Performance.GetAllAsync();
            }

            // Apply filters
            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<ReviewStatus>(request.Status, true, out var statusEnum))
                {
                    reviews = reviews.Where(r => r.Status == statusEnum);
                }
            }

            if (!string.IsNullOrEmpty(request.Period))
            {
                reviews = reviews.Where(r => r.ReviewPeriod.Contains(request.Period, StringComparison.OrdinalIgnoreCase));
            }

            var reviewDtos = reviews.Select(r => new PerformanceReviewDto
            {
                Id = r.Id,
                Employee = new UserSummaryDto
                {
                    Id = r.Employee.Id,
                    Name = r.Employee.Name,
                    EmployeeId = r.Employee.EmployeeDetail?.EmployeeId
                },
                Reviewer = new UserSummaryDto
                {
                    Id = r.Reviewer.Id,
                    Name = r.Reviewer.Name,
                    EmployeeId = r.Reviewer.EmployeeDetail?.EmployeeId
                },
                ReviewPeriod = r.ReviewPeriod,
                OverallRating = r.OverallRating,
                Status = r.Status.ToString().ToLowerInvariant(),
                ReviewDate = r.ReviewDate,
                Feedback = r.Feedback,
                Goals = r.Goals.Select(g => new PerformanceGoalDto
                {
                    Id = g.Id,
                    Title = g.Title,
                    Description = g.Description,
                    WeightPercentage = g.WeightPercentage,
                    AchievedRating = g.AchievedRating,
                    Comments = g.Comments
                }).ToList()
            }).ToList();

            var response = new PerformanceReviewsResponseDto
            {
                Reviews = reviewDtos
            };

            return ApiResponse<PerformanceReviewsResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance reviews");
            return ApiResponse<PerformanceReviewsResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting performance reviews");
        }
    }

    public async Task<ApiResponse<CreatePerformanceReviewResponseDto>> CreatePerformanceReviewAsync(string userId, CreatePerformanceReviewRequestDto request)
    {
        try
        {
            var reviewerGuid = Guid.Parse(userId);

            // Validate employee exists
            var employee = await _unitOfWork.Users.GetByIdAsync(request.EmployeeId);
            if (employee == null)
            {
                return ApiResponse<CreatePerformanceReviewResponseDto>.ErrorResult("EMPLOYEE_NOT_FOUND", "Employee not found");
            }

            // Validate goals weight percentages sum to 100
            var totalWeight = request.Goals.Sum(g => g.WeightPercentage);
            if (totalWeight != 100)
            {
                return ApiResponse<CreatePerformanceReviewResponseDto>.ErrorResult("INVALID_GOAL_WEIGHTS", "Goal weight percentages must sum to 100");
            }

            await _unitOfWork.BeginTransactionAsync();

            try
            {
                // Create performance review
                var review = new PerformanceReview
                {
                    EmployeeId = request.EmployeeId,
                    ReviewerId = reviewerGuid,
                    ReviewPeriod = request.ReviewPeriod,
                    ReviewType = request.ReviewType,
                    ReviewDate = request.ReviewDate,
                    Status = ReviewStatus.Scheduled
                };

                await _unitOfWork.Performance.AddAsync(review);
                await _unitOfWork.SaveChangesAsync();

                // Create performance goals
                foreach (var goalDto in request.Goals)
                {
                    var goal = new PerformanceGoal
                    {
                        ReviewId = review.Id,
                        Title = goalDto.Title,
                        Description = goalDto.Description,
                        WeightPercentage = goalDto.WeightPercentage
                    };

                    // Note: PerformanceGoal would need its own repository or be added through DbContext directly
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                var response = new CreatePerformanceReviewResponseDto
                {
                    Id = review.Id,
                    EmployeeId = review.EmployeeId,
                    ReviewPeriod = review.ReviewPeriod,
                    Status = review.Status.ToString().ToLowerInvariant(),
                    ReviewDate = review.ReviewDate,
                    CreatedAt = review.CreatedAt
                };

                return ApiResponse<CreatePerformanceReviewResponseDto>.SuccessResult(response, "Performance review created successfully");
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating performance review for employee {EmployeeId}", request.EmployeeId);
            return ApiResponse<CreatePerformanceReviewResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while creating performance review");
        }
    }
}
