using HRMS.Application.Common;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace HRMS.Application.Services;

/// <summary>
/// Service for synchronizing dynamic leave balance calculations with database records
/// Ensures the LeaveBalance table reflects the current dynamic calculations
/// </summary>
public class LeaveBalanceSyncService : ILeaveBalanceSyncService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IDynamicLeaveBalanceService _dynamicLeaveBalanceService;
    private readonly ITimeZoneService _timeZoneService;
    private readonly ILogger<LeaveBalanceSyncService> _logger;

    public LeaveBalanceSyncService(
        IUnitOfWork unitOfWork,
        IDynamicLeaveBalanceService dynamicLeaveBalanceService,
        ITimeZoneService timeZoneService,
        ILogger<LeaveBalanceSyncService> logger)
    {
        _unitOfWork = unitOfWork;
        _dynamicLeaveBalanceService = dynamicLeaveBalanceService;
        _timeZoneService = timeZoneService;
        _logger = logger;
    }

    /// <summary>
    /// Sync leave balance for a specific employee
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Sync result</returns>
    public async Task<ApiResponse> SyncEmployeeLeaveBalanceAsync(Guid employeeId)
    {
        try
        {
            // Get dynamic leave balance calculation
            var dynamicBalanceResult = await _dynamicLeaveBalanceService.CalculateLeaveBalanceAsync(employeeId);
            
            if (!dynamicBalanceResult.Success || dynamicBalanceResult.Data == null)
            {
                return ApiResponse.ErrorResult("CALCULATION_ERROR", 
                    $"Failed to calculate dynamic leave balance for employee {employeeId}");
            }

            var dynamicBalance = dynamicBalanceResult.Data;
            var currentYear = _timeZoneService.GetCurrentIstTime().Year;

            // Get or create a generic leave type for dynamic balance
            var leaveType = await GetOrCreateDynamicLeaveTypeAsync(employeeId);
            if (leaveType == null)
            {
                return ApiResponse.ErrorResult("LEAVE_TYPE_ERROR", "Failed to get or create dynamic leave type");
            }

            // Get existing leave balance record
            var existingBalances = await _unitOfWork.LeaveBalances.GetAllAsync();
            var existingBalance = existingBalances.FirstOrDefault(lb => 
                lb.UserId == employeeId && 
                lb.LeaveTypeId == leaveType.Id && 
                lb.Year == currentYear);

            if (existingBalance != null)
            {
                // Update existing record
                existingBalance.TotalAllocated = dynamicBalance.TotalAllocated;
                existingBalance.UsedLeaves = dynamicBalance.UsedLeaves;
                existingBalance.CarriedForward = 0; // Dynamic system doesn't use traditional carryforward
                existingBalance.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.LeaveBalances.UpdateAsync(existingBalance);
                _logger.LogInformation("Updated leave balance for employee {EmployeeId}: Total={Total}, Used={Used}", 
                    employeeId, dynamicBalance.TotalAllocated, dynamicBalance.UsedLeaves);
            }
            else
            {
                // Create new record
                var newBalance = new LeaveBalance
                {
                    UserId = employeeId,
                    LeaveTypeId = leaveType.Id,
                    Year = currentYear,
                    TotalAllocated = dynamicBalance.TotalAllocated,
                    UsedLeaves = dynamicBalance.UsedLeaves,
                    CarriedForward = 0
                };

                await _unitOfWork.LeaveBalances.AddAsync(newBalance);
                _logger.LogInformation("Created new leave balance for employee {EmployeeId}: Total={Total}, Used={Used}", 
                    employeeId, dynamicBalance.TotalAllocated, dynamicBalance.UsedLeaves);
            }

            await _unitOfWork.SaveChangesAsync();
            return ApiResponse.SuccessResult("Leave balance synchronized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing leave balance for employee {EmployeeId}", employeeId);
            return ApiResponse.ErrorResult("SYNC_ERROR", "Failed to sync leave balance");
        }
    }

    /// <summary>
    /// Sync leave balances for all employees in an organization
    /// </summary>
    /// <param name="organizationId">Organization ID</param>
    /// <returns>Sync result</returns>
    public async Task<ApiResponse> SyncOrganizationLeaveBalancesAsync(Guid organizationId)
    {
        try
        {
            // Get all active employees in the organization
            var users = await _unitOfWork.Users.GetAllAsync();
            var organizationEmployees = users.Where(u => 
                u.OrganizationId == organizationId && 
                u.IsActive && 
                u.EmployeeDetail != null).ToList();

            var syncedCount = 0;
            var failedCount = 0;

            foreach (var employee in organizationEmployees)
            {
                var syncResult = await SyncEmployeeLeaveBalanceAsync(employee.Id);
                if (syncResult.Success)
                {
                    syncedCount++;
                }
                else
                {
                    failedCount++;
                    _logger.LogWarning("Failed to sync leave balance for employee {EmployeeId}: {Error}", 
                        employee.Id, syncResult.Error?.Message);
                }
            }

            _logger.LogInformation("Synced leave balances for organization {OrganizationId}: {Synced} successful, {Failed} failed", 
                organizationId, syncedCount, failedCount);

            return ApiResponse.SuccessResult($"Synced {syncedCount} employees, {failedCount} failed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing leave balances for organization {OrganizationId}", organizationId);
            return ApiResponse.ErrorResult("SYNC_ERROR", "Failed to sync organization leave balances");
        }
    }

    /// <summary>
    /// Sync leave balances for all employees in the system
    /// </summary>
    /// <returns>Sync result</returns>
    public async Task<ApiResponse> SyncAllLeaveBalancesAsync()
    {
        try
        {
            // Get all organizations
            var organizations = await _unitOfWork.Organizations.GetAllAsync();
            var totalSynced = 0;
            var totalFailed = 0;

            foreach (var organization in organizations)
            {
                var syncResult = await SyncOrganizationLeaveBalancesAsync(organization.Id);
                if (syncResult.Success && syncResult.Message != null)
                {
                    // Parse the result message to get counts
                    var parts = syncResult.Message.Split(',');
                    if (parts.Length >= 2)
                    {
                        var syncedPart = parts[0].Trim().Split(' ')[1];
                        var failedPart = parts[1].Trim().Split(' ')[0];
                        
                        if (int.TryParse(syncedPart, out var synced))
                            totalSynced += synced;
                        if (int.TryParse(failedPart, out var failed))
                            totalFailed += failed;
                    }
                }
            }

            _logger.LogInformation("Completed system-wide leave balance sync: {TotalSynced} successful, {TotalFailed} failed", 
                totalSynced, totalFailed);

            return ApiResponse.SuccessResult($"System-wide sync completed: {totalSynced} successful, {totalFailed} failed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing all leave balances");
            return ApiResponse.ErrorResult("SYNC_ERROR", "Failed to sync all leave balances");
        }
    }

    /// <summary>
    /// Get or create a dynamic leave type for the organization
    /// </summary>
    /// <param name="employeeId">Employee ID to get organization from</param>
    /// <returns>Leave type</returns>
    private async Task<LeaveType?> GetOrCreateDynamicLeaveTypeAsync(Guid employeeId)
    {
        try
        {
            // Get employee's organization
            var user = await _unitOfWork.Users.GetByIdAsync(employeeId);
            if (user?.OrganizationId == null)
            {
                _logger.LogWarning("Employee {EmployeeId} has no organization", employeeId);
                return null;
            }

            var organizationId = user.OrganizationId.Value;

            // Look for existing dynamic leave type
            var leaveTypes = await _unitOfWork.LeaveTypes.GetByOrganizationAsync(organizationId);
            var dynamicLeaveType = leaveTypes.FirstOrDefault(lt => 
                lt.Name == "Dynamic Leave Balance" || 
                lt.Name.Contains("Dynamic"));

            if (dynamicLeaveType != null)
            {
                return dynamicLeaveType;
            }

            // Create new dynamic leave type
            var newLeaveType = new LeaveType
            {
                OrganizationId = organizationId,
                Name = "Dynamic Leave Balance",
                Description = "Dynamically calculated leave balance based on join date (1 initial + 1 per month)",
                MaxDaysPerYear = 365, // High limit since it's calculated dynamically
                IsCarryForward = false,
                MaxCarryForwardDays = 0,
                IsActive = true
            };

            await _unitOfWork.LeaveTypes.AddAsync(newLeaveType);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Created dynamic leave type for organization {OrganizationId}", organizationId);
            return newLeaveType;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating dynamic leave type for employee {EmployeeId}", employeeId);
            return null;
        }
    }
}
