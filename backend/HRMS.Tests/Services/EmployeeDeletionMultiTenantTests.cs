using Microsoft.Extensions.Logging;
using Moq;
using HRMS.Application.Services;
using HRMS.Application.DTOs;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using HRMS.Application.Interfaces;
using Xunit;

namespace HRMS.Tests.Services;

public class EmployeeDeletionMultiTenantTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<ILogger<EmployeeManagementService>> _mockLogger;
    private readonly Mock<ITenantService> _mockTenantService;
    private readonly Mock<IEmployeeIntegrationService> _mockIntegrationService;
    private readonly Mock<IPasswordService> _mockPasswordService;
    private readonly EmployeeManagementService _service;

    public EmployeeDeletionMultiTenantTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockLogger = new Mock<ILogger<EmployeeManagementService>>();
        _mockTenantService = new Mock<ITenantService>();
        _mockIntegrationService = new Mock<IEmployeeIntegrationService>();
        _mockPasswordService = new Mock<IPasswordService>();

        _service = new EmployeeManagementService(
            _mockUnitOfWork.Object,
            _mockLogger.Object,
            _mockTenantService.Object,
            _mockIntegrationService.Object,
            _mockPasswordService.Object
        );
    }

    [Fact]
    public async Task DeleteEmployeeAsync_ShouldPreventCrossTenantDeletion()
    {
        // Arrange
        var organizationA = Guid.NewGuid();
        var organizationB = Guid.NewGuid();
        var employeeId = Guid.NewGuid();

        // Current tenant is Organization A
        _mockTenantService.Setup(x => x.GetCurrentTenant())
            .Returns(new TenantContext { OrganizationId = organizationA.ToString() });

        // Employee belongs to Organization B
        var employee = new User
        {
            Id = employeeId,
            OrganizationId = organizationB,
            IsActive = true,
            IsDeleted = false,
            Role = UserRole.Employee
        };

        _mockUnitOfWork.Setup(x => x.Users.GetByIdIncludingDeletedAsync(employeeId))
            .ReturnsAsync(employee);

        // Act
        var result = await _service.DeleteEmployeeAsync(employeeId);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("UNAUTHORIZED_ACCESS", result.Error?.Code);
        Assert.Equal("Cannot delete employee from different organization", result.Error?.Message);

        // Verify that no deletion operations were performed
        _mockUnitOfWork.Verify(x => x.Users.UpdateAsync(It.IsAny<User>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
        _mockIntegrationService.Verify(x => x.OnEmployeeDeactivatedAsync(It.IsAny<Guid>()), Times.Never);
    }

    [Fact]
    public async Task DeleteEmployeeAsync_ShouldAllowSameTenantDeletion()
    {
        // Arrange
        var organizationId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();

        // Current tenant and employee belong to same organization
        _mockTenantService.Setup(x => x.GetCurrentTenant())
            .Returns(new TenantContext { OrganizationId = organizationId.ToString() });

        var employee = new User
        {
            Id = employeeId,
            OrganizationId = organizationId,
            IsActive = true,
            IsDeleted = false,
            Role = UserRole.Employee,
            EmployeeDetail = new EmployeeDetail
            {
                UserId = employeeId,
                EmploymentStatus = EmploymentStatus.Active
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.GetByIdIncludingDeletedAsync(employeeId))
            .ReturnsAsync(employee);

        // Mock validation methods to return success
        _mockUnitOfWork.Setup(x => x.Users.GetAllAsync())
            .ReturnsAsync(new List<User>());

        _mockIntegrationService.Setup(x => x.OnEmployeeDeactivatedAsync(employeeId))
            .ReturnsAsync(ApiResponse.SuccessResult("Integration completed"));

        // Act
        var result = await _service.DeleteEmployeeAsync(employeeId);

        // Assert
        Assert.True(result.Success);

        // Verify that deletion operations were performed
        _mockUnitOfWork.Verify(x => x.Users.UpdateAsync(It.Is<User>(u => 
            u.Id == employeeId && 
            u.IsDeleted == true && 
            u.IsActive == false)), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockIntegrationService.Verify(x => x.OnEmployeeDeactivatedAsync(employeeId), Times.Once);
    }

    [Fact]
    public async Task DeleteEmployeeAsync_ShouldFailWhenNoTenantContext()
    {
        // Arrange
        var employeeId = Guid.NewGuid();

        // No tenant context
        _mockTenantService.Setup(x => x.GetCurrentTenant())
            .Returns(new TenantContext { OrganizationId = null });

        var employee = new User
        {
            Id = employeeId,
            OrganizationId = Guid.NewGuid(),
            IsActive = true,
            IsDeleted = false,
            Role = UserRole.Employee
        };

        _mockUnitOfWork.Setup(x => x.Users.GetByIdIncludingDeletedAsync(employeeId))
            .ReturnsAsync(employee);

        // Act
        var result = await _service.DeleteEmployeeAsync(employeeId);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("INVALID_TENANT", result.Error?.Code);
        Assert.Equal("No organization context found", result.Error?.Message);

        // Verify that no deletion operations were performed
        _mockUnitOfWork.Verify(x => x.Users.UpdateAsync(It.IsAny<User>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
    }
}

// Helper class for tenant context
public class TenantContext
{
    public string? OrganizationId { get; set; }
}
