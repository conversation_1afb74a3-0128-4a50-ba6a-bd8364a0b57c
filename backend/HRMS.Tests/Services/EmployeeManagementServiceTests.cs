using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using HRMS.Application.Services;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;

namespace HRMS.Tests.Services;

public class EmployeeManagementServiceTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IPasswordService> _mockPasswordService;
    private readonly Mock<IEmployeeIntegrationService> _mockIntegrationService;
    private readonly Mock<ILogger<EmployeeManagementService>> _mockLogger;
    private readonly EmployeeManagementService _service;

    public EmployeeManagementServiceTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockPasswordService = new Mock<IPasswordService>();
        _mockIntegrationService = new Mock<IEmployeeIntegrationService>();
        _mockLogger = new Mock<ILogger<EmployeeManagementService>>();
        
        _service = new EmployeeManagementService(
            _mockUnitOfWork.Object,
            _mockPasswordService.Object,
            _mockIntegrationService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetEmployeesAsync_ShouldReturnPaginatedEmployees_WhenValidRequest()
    {
        // Arrange
        var request = new GetEmployeesRequestDto
        {
            Page = 1,
            Limit = 10,
            Department = "Engineering"
        };

        var mockUsers = new List<User>
        {
            new User
            {
                Id = Guid.NewGuid(),
                Name = "John Doe",
                Email = "<EMAIL>",
                Role = UserRole.Employee,
                IsActive = true,
                EmployeeDetail = new EmployeeDetail
                {
                    EmployeeId = "EMP001",
                    JobTitle = "Software Engineer",
                    Department = "Engineering",
                    EmploymentType = EmploymentType.FullTime,
                    EmploymentStatus = EmploymentStatus.Active
                }
            },
            new User
            {
                Id = Guid.NewGuid(),
                Name = "Jane Smith",
                Email = "<EMAIL>",
                Role = UserRole.Employee,
                IsActive = true,
                EmployeeDetail = new EmployeeDetail
                {
                    EmployeeId = "EMP002",
                    JobTitle = "Senior Engineer",
                    Department = "Engineering",
                    EmploymentType = EmploymentType.FullTime,
                    EmploymentStatus = EmploymentStatus.Active
                }
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.GetAllAsync())
            .ReturnsAsync(mockUsers);

        // Act
        var result = await _service.GetEmployeesAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(2, result.Data.Items.Count);
        Assert.All(result.Data.Items, emp => 
            Assert.Equal("Engineering", emp.EmployeeDetails?.Department));
    }

    [Fact]
    public async Task CreateEmployeeAsync_ShouldCreateEmployee_WhenValidRequest()
    {
        // Arrange
        var request = new CreateEmployeeRequestDto
        {
            Name = "New Employee",
            Email = "<EMAIL>",
            Role = "employee",
            EmployeeDetails = new CreateEmployeeDetailsDto
            {
                EmployeeId = "EMP003",
                JobTitle = "Developer",
                Department = "Engineering",
                JoinDate = DateTime.UtcNow,
                EmploymentType = "full-time",
                BaseSalary = 50000
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.EmailExistsAsync(request.Email))
            .ReturnsAsync(false);
        
        _mockUnitOfWork.Setup(x => x.EmployeeDetails.GetByEmployeeIdAsync(request.EmployeeDetails.EmployeeId))
            .ReturnsAsync((EmployeeDetail?)null);

        _mockPasswordService.Setup(x => x.GenerateTemporaryPassword())
            .Returns("TempPass123!");

        _mockPasswordService.Setup(x => x.HashPassword(It.IsAny<string>()))
            .Returns("hashedpassword");

        var createdUser = new User
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            Email = request.Email,
            Role = UserRole.Employee,
            IsActive = true
        };

        _mockUnitOfWork.Setup(x => x.Users.AddAsync(It.IsAny<User>()))
            .Returns(Task.CompletedTask);

        _mockUnitOfWork.Setup(x => x.EmployeeDetails.AddAsync(It.IsAny<EmployeeDetail>()))
            .Returns(Task.CompletedTask);

        _mockUnitOfWork.Setup(x => x.Users.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(createdUser);

        _mockIntegrationService.Setup(x => x.OnEmployeeCreatedAsync(It.IsAny<Guid>()))
            .ReturnsAsync(new ApiResponse { Success = true });

        // Act
        var result = await _service.CreateEmployeeAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(request.Name, result.Data.Name);
        Assert.Equal(request.Email, result.Data.Email);
        Assert.NotNull(result.Data.TempPassword);

        _mockUnitOfWork.Verify(x => x.Users.AddAsync(It.IsAny<User>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.EmployeeDetails.AddAsync(It.IsAny<EmployeeDetail>()), Times.Once);
        _mockIntegrationService.Verify(x => x.OnEmployeeCreatedAsync(It.IsAny<Guid>()), Times.Once);
    }

    [Fact]
    public async Task CreateEmployeeAsync_ShouldReturnError_WhenEmailExists()
    {
        // Arrange
        var request = new CreateEmployeeRequestDto
        {
            Name = "New Employee",
            Email = "<EMAIL>",
            Role = "employee",
            EmployeeDetails = new CreateEmployeeDetailsDto
            {
                EmployeeId = "EMP003",
                JobTitle = "Developer",
                Department = "Engineering"
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.EmailExistsAsync(request.Email))
            .ReturnsAsync(true);

        // Act
        var result = await _service.CreateEmployeeAsync(request);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("EMAIL_EXISTS", result.Error?.Code);
        Assert.Equal("Email already exists", result.Error?.Message);
    }

    [Fact]
    public async Task UpdateEmployeeAsync_ShouldUpdateEmployee_WhenValidRequest()
    {
        // Arrange
        var employeeId = Guid.NewGuid();
        var request = new UpdateEmployeeRequestDto
        {
            Name = "Updated Name",
            Role = "employee",
            EmployeeDetails = new UpdateEmployeeDetailsDto
            {
                JobTitle = "Senior Developer",
                Department = "Engineering",
                BaseSalary = 60000
            }
        };

        var existingUser = new User
        {
            Id = employeeId,
            Name = "Original Name",
            Email = "<EMAIL>",
            Role = UserRole.Employee,
            IsActive = true,
            EmployeeDetail = new EmployeeDetail
            {
                Id = Guid.NewGuid(),
                UserId = employeeId,
                EmployeeId = "EMP001",
                JobTitle = "Developer",
                Department = "Engineering",
                BaseSalary = 50000
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.GetByIdAsync(employeeId))
            .ReturnsAsync(existingUser);

        _mockIntegrationService.Setup(x => x.OnEmployeeUpdatedAsync(It.IsAny<Guid>(), It.IsAny<EmployeeUpdateContext>()))
            .ReturnsAsync(new ApiResponse { Success = true });

        // Act
        var result = await _service.UpdateEmployeeAsync(employeeId, request);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(request.Name, result.Data.Name);

        _mockUnitOfWork.Verify(x => x.Users.UpdateAsync(It.IsAny<User>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.EmployeeDetails.UpdateAsync(It.IsAny<EmployeeDetail>()), Times.Once);
        _mockIntegrationService.Verify(x => x.OnEmployeeUpdatedAsync(It.IsAny<Guid>(), It.IsAny<EmployeeUpdateContext>()), Times.Once);
    }

    [Fact]
    public async Task DeleteEmployeeAsync_ShouldSoftDeleteEmployee_WhenValidId()
    {
        // Arrange
        var employeeId = Guid.NewGuid();
        var existingUser = new User
        {
            Id = employeeId,
            Name = "Test User",
            Email = "<EMAIL>",
            Role = UserRole.Employee,
            IsActive = true,
            EmployeeDetail = new EmployeeDetail
            {
                Id = Guid.NewGuid(),
                UserId = employeeId,
                EmployeeId = "EMP001",
                EmploymentStatus = EmploymentStatus.Active
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.GetByIdAsync(employeeId))
            .ReturnsAsync(existingUser);

        _mockIntegrationService.Setup(x => x.OnEmployeeDeactivatedAsync(employeeId))
            .ReturnsAsync(new ApiResponse { Success = true });

        // Act
        var result = await _service.DeleteEmployeeAsync(employeeId);

        // Assert
        Assert.True(result.Success);
        Assert.False(existingUser.IsActive);
        Assert.True(existingUser.IsDeleted);
        Assert.Equal(EmploymentStatus.Terminated, existingUser.EmployeeDetail.EmploymentStatus);

        _mockUnitOfWork.Verify(x => x.Users.UpdateAsync(existingUser), Times.Once);
        _mockIntegrationService.Verify(x => x.OnEmployeeDeactivatedAsync(employeeId), Times.Once);
    }

    [Fact]
    public async Task GetEmployeeStatsAsync_ShouldReturnCorrectStats()
    {
        // Arrange
        var mockUsers = new List<User>
        {
            new User
            {
                Id = Guid.NewGuid(),
                Role = UserRole.Employee,
                IsActive = true,
                EmployeeDetail = new EmployeeDetail
                {
                    Department = "Engineering",
                    EmploymentType = EmploymentType.FullTime,
                    EmploymentStatus = EmploymentStatus.Active,
                    JoinDate = DateTime.UtcNow.AddDays(-15) // This month
                }
            },
            new User
            {
                Id = Guid.NewGuid(),
                Role = UserRole.Employee,
                IsActive = true,
                EmployeeDetail = new EmployeeDetail
                {
                    Department = "HR",
                    EmploymentType = EmploymentType.PartTime,
                    EmploymentStatus = EmploymentStatus.Active,
                    JoinDate = DateTime.UtcNow.AddMonths(-2) // Not this month
                }
            },
            new User
            {
                Id = Guid.NewGuid(),
                Role = UserRole.Employee,
                IsActive = false,
                EmployeeDetail = new EmployeeDetail
                {
                    Department = "Engineering",
                    EmploymentType = EmploymentType.FullTime,
                    EmploymentStatus = EmploymentStatus.Terminated
                }
            }
        };

        _mockUnitOfWork.Setup(x => x.Users.GetAllAsync())
            .ReturnsAsync(mockUsers);

        // Act
        var result = await _service.GetEmployeeStatsAsync();

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(3, result.Data.TotalEmployees);
        Assert.Equal(2, result.Data.ActiveEmployees);
        Assert.Equal(1, result.Data.InactiveEmployees);
        Assert.Equal(1, result.Data.NewHiresThisMonth);
        Assert.Equal(2, result.Data.DepartmentBreakdown["Engineering"]);
        Assert.Equal(1, result.Data.DepartmentBreakdown["HR"]);
    }
}
