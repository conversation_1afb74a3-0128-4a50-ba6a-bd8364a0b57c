using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using HRMS.Infrastructure.Data;
using HRMS.Core.Entities;

namespace HRMS.Tests.Validation;

public class DatabaseSchemaValidationTests
{
    [Fact]
    public void DbContext_ShouldHaveAllRequiredDbSets()
    {
        // Arrange
        var options = new DbContextOptionsBuilder<HRMSDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        using var context = new HRMSDbContext(options);

        // Act & Assert - Verify all DbSets exist
        Assert.NotNull(context.Organizations);
        Assert.NotNull(context.Users);
        Assert.NotNull(context.EmployeeDetails);
        Assert.NotNull(context.EmployeeEducation);
        Assert.NotNull(context.EmployeeSkills);
        Assert.NotNull(context.AttendanceRecords);
        Assert.NotNull(context.LeaveTypes);
        Assert.NotNull(context.LeaveBalances);
        Assert.NotNull(context.LeaveRequests);
        Assert.NotNull(context.Tasks);
        Assert.NotNull(context.TaskUpdates);
        Assert.NotNull(context.TaskComments);
        Assert.NotNull(context.PerformanceReviews);
        Assert.NotNull(context.PerformanceGoals);
        Assert.NotNull(context.OrganizationSettings);
        Assert.NotNull(context.JobPostings);
        Assert.NotNull(context.JobApplications);
        
        // New entities for comprehensive employee management
        Assert.NotNull(context.Permissions);
        Assert.NotNull(context.Roles);
        Assert.NotNull(context.RolePermissions);
        Assert.NotNull(context.EmployeeRoles);
        Assert.NotNull(context.EmployeePermissions);
        Assert.NotNull(context.Departments);
        Assert.NotNull(context.DepartmentEmployees);
        Assert.NotNull(context.Positions);
        Assert.NotNull(context.EmployeePositions);
        Assert.NotNull(context.PayrollCycles);
        Assert.NotNull(context.EmployeePayrolls);
        Assert.NotNull(context.SalaryComponents);
        Assert.NotNull(context.EmployeeSalaryStructures);
        Assert.NotNull(context.PayrollComponents);
    }

    [Fact]
    public void EmployeeDetail_ShouldHaveAllRequiredProperties()
    {
        // Arrange
        var employeeDetail = new EmployeeDetail();

        // Act & Assert - Verify all new properties exist
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.EmergencyContactName)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.EmergencyContactPhone)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.EmergencyContactRelation)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.BloodGroup)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.MaritalStatus)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.Gender)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.Nationality)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.BankAccountNumber)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.BankIFSC)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.BankName)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.PAN)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.Aadhar)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.ProbationEndDate)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.ConfirmationDate)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.ResignationDate)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.LastWorkingDate)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.ResignationReason)));
        Assert.NotNull(employeeDetail.GetType().GetProperty(nameof(EmployeeDetail.EmployeeRoles)));
    }

    [Fact]
    public void Permission_ShouldHaveCorrectStructure()
    {
        // Arrange
        var permission = new Permission
        {
            Name = "Test Permission",
            Code = "TEST_PERMISSION",
            Description = "Test description",
            Module = "test",
            Action = "read",
            IsActive = true
        };

        // Act & Assert
        Assert.Equal("Test Permission", permission.Name);
        Assert.Equal("TEST_PERMISSION", permission.Code);
        Assert.Equal("Test description", permission.Description);
        Assert.Equal("test", permission.Module);
        Assert.Equal("read", permission.Action);
        Assert.True(permission.IsActive);
        Assert.NotNull(permission.RolePermissions);
        Assert.NotNull(permission.EmployeePermissions);
    }

    [Fact]
    public void Role_ShouldHaveCorrectStructure()
    {
        // Arrange
        var organizationId = Guid.NewGuid();
        var role = new Role
        {
            OrganizationId = organizationId,
            Name = "Test Role",
            Description = "Test role description",
            IsSystemRole = false,
            IsActive = true
        };

        // Act & Assert
        Assert.Equal(organizationId, role.OrganizationId);
        Assert.Equal("Test Role", role.Name);
        Assert.Equal("Test role description", role.Description);
        Assert.False(role.IsSystemRole);
        Assert.True(role.IsActive);
        Assert.NotNull(role.RolePermissions);
        Assert.NotNull(role.EmployeeRoles);
    }

    [Fact]
    public void Department_ShouldHaveCorrectStructure()
    {
        // Arrange
        var organizationId = Guid.NewGuid();
        var headId = Guid.NewGuid();
        var department = new Department
        {
            OrganizationId = organizationId,
            Name = "Engineering",
            Code = "ENG",
            Description = "Engineering Department",
            HeadOfDepartmentId = headId,
            Location = "Building A",
            Budget = 1000000,
            EmployeeCount = 25,
            IsActive = true,
            Level = 1,
            HierarchyPath = "Engineering"
        };

        // Act & Assert
        Assert.Equal(organizationId, department.OrganizationId);
        Assert.Equal("Engineering", department.Name);
        Assert.Equal("ENG", department.Code);
        Assert.Equal("Engineering Department", department.Description);
        Assert.Equal(headId, department.HeadOfDepartmentId);
        Assert.Equal("Building A", department.Location);
        Assert.Equal(1000000, department.Budget);
        Assert.Equal(25, department.EmployeeCount);
        Assert.True(department.IsActive);
        Assert.Equal(1, department.Level);
        Assert.Equal("Engineering", department.HierarchyPath);
        Assert.NotNull(department.SubDepartments);
        Assert.NotNull(department.DepartmentEmployees);
    }

    [Fact]
    public void PayrollCycle_ShouldHaveCorrectStructure()
    {
        // Arrange
        var organizationId = Guid.NewGuid();
        var processedBy = Guid.NewGuid();
        var payrollCycle = new PayrollCycle
        {
            OrganizationId = organizationId,
            CycleName = "January 2024",
            PayPeriodStart = new DateTime(2024, 1, 1),
            PayPeriodEnd = new DateTime(2024, 1, 31),
            PayDate = new DateTime(2024, 2, 1),
            Status = Core.Enums.PayrollStatus.Draft,
            TotalGrossAmount = 500000,
            TotalNetAmount = 400000,
            TotalDeductions = 100000,
            ProcessedBy = processedBy,
            ProcessedAt = DateTime.UtcNow
        };

        // Act & Assert
        Assert.Equal(organizationId, payrollCycle.OrganizationId);
        Assert.Equal("January 2024", payrollCycle.CycleName);
        Assert.Equal(new DateTime(2024, 1, 1), payrollCycle.PayPeriodStart);
        Assert.Equal(new DateTime(2024, 1, 31), payrollCycle.PayPeriodEnd);
        Assert.Equal(new DateTime(2024, 2, 1), payrollCycle.PayDate);
        Assert.Equal(Core.Enums.PayrollStatus.Draft, payrollCycle.Status);
        Assert.Equal(500000, payrollCycle.TotalGrossAmount);
        Assert.Equal(400000, payrollCycle.TotalNetAmount);
        Assert.Equal(100000, payrollCycle.TotalDeductions);
        Assert.Equal(processedBy, payrollCycle.ProcessedBy);
        Assert.NotNull(payrollCycle.EmployeePayrolls);
    }

    [Fact]
    public void EntityRelationships_ShouldBeConfiguredCorrectly()
    {
        // Arrange
        var options = new DbContextOptionsBuilder<HRMSDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        using var context = new HRMSDbContext(options);

        // Act
        var model = context.Model;

        // Assert - Check key relationships exist
        var userEntity = model.FindEntityType(typeof(User));
        var employeeDetailEntity = model.FindEntityType(typeof(EmployeeDetail));
        var roleEntity = model.FindEntityType(typeof(Role));
        var permissionEntity = model.FindEntityType(typeof(Permission));
        var departmentEntity = model.FindEntityType(typeof(Department));

        Assert.NotNull(userEntity);
        Assert.NotNull(employeeDetailEntity);
        Assert.NotNull(roleEntity);
        Assert.NotNull(permissionEntity);
        Assert.NotNull(departmentEntity);

        // Check that navigation properties are configured
        var userEmployeeDetailNavigation = userEntity?.FindNavigation(nameof(User.EmployeeDetail));
        var employeeDetailUserNavigation = employeeDetailEntity?.FindNavigation(nameof(EmployeeDetail.User));
        
        Assert.NotNull(userEmployeeDetailNavigation);
        Assert.NotNull(employeeDetailUserNavigation);
    }

    [Fact]
    public void DatabaseSchema_ShouldSupportMultiTenancy()
    {
        // Arrange
        var options = new DbContextOptionsBuilder<HRMSDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        using var context = new HRMSDbContext(options);

        // Act & Assert - Verify entities have OrganizationId where needed
        var roleEntity = context.Model.FindEntityType(typeof(Role));
        var departmentEntity = context.Model.FindEntityType(typeof(Department));
        var salaryComponentEntity = context.Model.FindEntityType(typeof(SalaryComponent));
        var payrollCycleEntity = context.Model.FindEntityType(typeof(PayrollCycle));

        Assert.NotNull(roleEntity?.FindProperty(nameof(Role.OrganizationId)));
        Assert.NotNull(departmentEntity?.FindProperty(nameof(Department.OrganizationId)));
        Assert.NotNull(salaryComponentEntity?.FindProperty(nameof(SalaryComponent.OrganizationId)));
        Assert.NotNull(payrollCycleEntity?.FindProperty(nameof(PayrollCycle.OrganizationId)));
    }
}
