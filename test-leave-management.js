// Test script for Leave Management functionality
// This script tests the complete workflow: Employee applies for leave → <PERSON><PERSON> sees the application

const API_BASE = 'http://localhost:5020/api/v1';

// Test credentials
const EMPLOYEE_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'ChangedPassword123!' // From employee data
};

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'PlanSquare@123' // From the script output
};

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    console.error('Request failed:', error);
    return { status: 500, error: error.message };
  }
}

async function login(credentials) {
  console.log(`\n🔐 Logging in as ${credentials.email}...`);
  
  const result = await makeRequest(`${API_BASE}/auth/login`, {
    method: 'POST',
    body: JSON.stringify(credentials)
  });
  
  if (result.status === 200 && result.data.success) {
    console.log(`✅ Login successful for ${credentials.email}`);
    return result.data.data.token;
  } else {
    console.log(`❌ Login failed for ${credentials.email}:`, result.data);
    return null;
  }
}

async function getLeaveTypes(token) {
  console.log('\n📋 Fetching leave types...');
  
  const result = await makeRequest(`${API_BASE}/leave/types`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Leave types fetched successfully:');
    result.data.data.types.forEach(type => {
      console.log(`   - ${type.name} (${type.maxDaysPerYear} days/year)`);
    });
    return result.data.data.types; // Return all leave types
  } else {
    console.log('❌ Failed to fetch leave types:', result.data);
    return [];
  }
}

async function applyLeave(token, leaveTypeId) {
  console.log('\n📝 Applying for leave...');
  
  // Use dates further in the future to avoid overlapping with existing requests
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + 10); // 10 days from now

  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 11); // 11 days from now
  
  const leaveRequest = {
    LeaveTypeId: leaveTypeId,
    FromDate: futureDate.toISOString(),
    ToDate: endDate.toISOString(),
    DurationType: 'full-day',
    Reason: 'Test leave application from automated script'
  };

  console.log(`🔍 Leave request payload:`, JSON.stringify(leaveRequest, null, 2));
  
  const result = await makeRequest(`${API_BASE}/leave/apply`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(leaveRequest)
  });
  
  if ((result.status === 200 || result.status === 201) && result.data.success) {
    console.log('✅ Leave application submitted successfully');
    console.log(`   Request ID: ${result.data.data.id}`);
    return result.data.data;
  } else {
    console.log('❌ Leave application failed:', result.data);
    return null;
  }
}

async function getAdminLeaveRequests(token) {
  console.log('\n👨‍💼 Fetching leave requests as admin...');
  
  const result = await makeRequest(`${API_BASE}/organization-admin/leave/requests`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Admin leave requests fetched successfully:');
    result.data.data.requests.forEach(request => {
      console.log(`   - ${request.employeeName}: ${request.leaveType} (${request.status})`);
    });
    return result.data.data.requests;
  } else {
    console.log('❌ Failed to fetch admin leave requests:', result.data);
    return [];
  }
}

async function testLeaveManagementWorkflow() {
  console.log('🚀 Starting Leave Management Test Workflow');
  console.log('=' .repeat(50));
  
  // Step 1: Login as employee
  const employeeToken = await login(EMPLOYEE_CREDENTIALS);
  if (!employeeToken) {
    console.log('❌ Test failed: Could not login as employee');
    return;
  }
  
  // Step 2: Get leave types
  const leaveTypes = await getLeaveTypes(employeeToken);
  if (leaveTypes.length === 0) {
    console.log('❌ Test failed: No leave types available');
    return;
  }
  
  // Step 3: Apply for leave using the first available leave type
  const firstLeaveType = leaveTypes[0];
  console.log(`🔍 Using leave type: ${firstLeaveType.name} (ID: ${firstLeaveType.id})`);
  const leaveRequest = await applyLeave(employeeToken, firstLeaveType.id);
  if (!leaveRequest) {
    console.log('❌ Test failed: Could not apply for leave');
    return;
  }
  
  // Step 4: Login as admin
  const adminToken = await login(ADMIN_CREDENTIALS);
  if (!adminToken) {
    console.log('❌ Test failed: Could not login as admin');
    return;
  }
  
  // Step 5: Check if admin can see the leave request
  const adminRequests = await getAdminLeaveRequests(adminToken);
  const foundRequest = adminRequests.find(req => req.id === leaveRequest.id);
  
  if (foundRequest) {
    console.log('\n🎉 SUCCESS: Complete workflow test passed!');
    console.log('✅ Employee successfully applied for leave');
    console.log('✅ Admin can see the leave request');
    console.log('✅ Leave Management module is working correctly');
  } else {
    console.log('\n❌ PARTIAL SUCCESS: Leave was applied but admin cannot see it');
    console.log('   This might indicate an issue with admin-employee association');
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Test completed');
}

// Run the test
testLeaveManagementWorkflow().catch(console.error);
