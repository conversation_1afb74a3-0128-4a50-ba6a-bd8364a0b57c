# HRMS Application - Database Schema Design

## Overview
This document provides a comprehensive database schema design for the HRMS (Human Resource Management System) application based on the analysis of the frontend codebase. The schema supports multi-tenant architecture with role-based access control for super admins, organization admins, and employees.

## Database Architecture
- **Database Type**: SQL Server (recommended for ACID compliance and advanced features)
- **Architecture**: Multi-tenant with organization-based data isolation
- **Indexing Strategy**: Optimized for common queries and reporting
- **Data Types**: Using appropriate SQL Server data types with constraints

---

## Core Tables

### 1. Organizations Table
Stores information about registered organizations in the system.

```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    industry VARCHAR(100) NOT NULL,
    employee_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'pending', 'suspended')),
    subscription_plan VARCHAR(50) DEFAULT 'standard' CHECK (subscription_plan IN ('standard', 'professional', 'enterprise')),
    monthly_revenue DECIMAL(12,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_organizations_domain (domain),
    INDEX idx_organizations_status (status),
    INDEX idx_organizations_industry (industry)
);
```

**Sample Data:**
```sql
INSERT INTO organizations VALUES 
('org-1', 'Tech Innovators Inc', 'techinnovators.com', 'Technology', 150, 'active', 'professional', 1250000.00),
('org-2', 'Healthcare Solutions', 'healthcaresol.com', 'Healthcare', 80, 'active', 'standard', 666000.00);
```

### 2. Users Table
Central user authentication and basic profile information.

```sql
CREATE TABLE users (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    email NVARCHAR(255) UNIQUE NOT NULL,
    password_hash NVARCHAR(255) NOT NULL,
    name NVARCHAR(255) NOT NULL,
    role NVARCHAR(20) NOT NULL CHECK (role IN ('super_admin', 'org_admin', 'employee')),
    organization_id UNIQUEIDENTIFIER REFERENCES organizations(id) ON DELETE CASCADE,
    avatar_url NVARCHAR(500),
    is_active BIT DEFAULT 1,
    email_verified BIT DEFAULT 0,
    last_login DATETIMEOFFSET,
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    updated_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT chk_org_required CHECK (
        (role = 'super_admin' AND organization_id IS NULL) OR 
        (role IN ('org_admin', 'employee') AND organization_id IS NOT NULL)
    ),
    
    -- Indexes
    INDEX idx_users_email (email),
    INDEX idx_users_organization (organization_id),
    INDEX idx_users_role (role)
);
```

### 3. Employee Details Table
Comprehensive employee information and HR data.

```sql
CREATE TABLE employee_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) NOT NULL,
    job_title VARCHAR(255) NOT NULL,
    department VARCHAR(100) NOT NULL,
    manager_id UUID REFERENCES users(id),
    join_date DATE NOT NULL,
    employment_type VARCHAR(50) DEFAULT 'full-time' CHECK (employment_type IN ('full-time', 'part-time', 'contract', 'intern')),
    employment_status VARCHAR(50) DEFAULT 'active' CHECK (employment_status IN ('active', 'inactive', 'terminated', 'on-leave')),
    work_location VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    base_salary DECIMAL(12,2),
    annual_ctc DECIMAL(12,2),
    next_salary_review DATE,
    performance_rating DECIMAL(3,2) CHECK (performance_rating >= 0 AND performance_rating <= 5),
    total_experience VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for employee_id within organization
    UNIQUE(employee_id, user_id),
    
    -- Indexes
    INDEX idx_employee_details_user (user_id),
    INDEX idx_employee_details_employee_id (employee_id),
    INDEX idx_employee_details_department (department),
    INDEX idx_employee_details_manager (manager_id),
    INDEX idx_employee_details_join_date (join_date)
);
```

### 4. Employee Education Table
Educational qualifications and certifications.

```sql
CREATE TABLE employee_education (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employee_details(id) ON DELETE CASCADE,
    degree VARCHAR(255) NOT NULL,
    institution VARCHAR(255) NOT NULL,
    year VARCHAR(4) NOT NULL,
    grade_or_percentage VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_employee_education_employee (employee_id)
);
```

### 5. Employee Skills Table
Technical and soft skills with proficiency levels.

```sql
CREATE TABLE employee_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employee_details(id) ON DELETE CASCADE,
    skill_name VARCHAR(255) NOT NULL,
    proficiency_level INTEGER CHECK (proficiency_level >= 0 AND proficiency_level <= 100),
    skill_category VARCHAR(100) DEFAULT 'technical' CHECK (skill_category IN ('technical', 'soft', 'language', 'certification')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for skill per employee
    UNIQUE(employee_id, skill_name),
    
    -- Indexes
    INDEX idx_employee_skills_employee (employee_id),
    INDEX idx_employee_skills_name (skill_name)
);
```

---

## Attendance Management Tables

### 6. Attendance Records Table
Daily attendance tracking with check-in/check-out times.

```sql
CREATE TABLE attendance_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL(4,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'half-day', 'work-from-home')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for one record per user per date
    UNIQUE(user_id, date),
    
    -- Indexes
    INDEX idx_attendance_user_date (user_id, date),
    INDEX idx_attendance_organization_date (organization_id, date),
    INDEX idx_attendance_status (status)
);
```

---

## Leave Management Tables

### 7. Leave Types Table
Configurable leave types per organization.

```sql
CREATE TABLE leave_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL,
    annual_allocation INTEGER DEFAULT 0,
    carry_forward_allowed BOOLEAN DEFAULT false,
    max_carry_forward INTEGER DEFAULT 0,
    requires_approval BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for leave type code per organization
    UNIQUE(organization_id, code),
    
    -- Indexes
    INDEX idx_leave_types_organization (organization_id),
    INDEX idx_leave_types_active (is_active)
);
```

### 8. ~~Leave Balances Table~~ (REMOVED)
~~Employee leave balances by type and year.~~

**⚠️ TABLE REMOVED**: This table has been removed as part of the leave balance functionality removal. The system now operates without balance restrictions.

```sql
-- TABLE REMOVED: leave_balances
-- Reason: Leave balance functionality disabled
-- Migration: RemoveLeaveBalanceTables.sql
-- Date: 2024-01-XX
```

### 9. Leave Requests Table
Leave applications and their approval workflow.

```sql
CREATE TABLE leave_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    leave_type_id UUID REFERENCES leave_types(id),
    from_date DATE NOT NULL,
    to_date DATE NOT NULL,
    total_days DECIMAL(4,1) NOT NULL,
    duration_type VARCHAR(20) DEFAULT 'full-day' CHECK (duration_type IN ('half-day', 'full-day', 'multiple-days')),
    reason TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_leave_requests_user (user_id),
    INDEX idx_leave_requests_status (status),
    INDEX idx_leave_requests_dates (from_date, to_date),
    INDEX idx_leave_requests_approver (approved_by)
);
```

---

## Task Management Tables

### 10. Tasks Table
Task assignments and tracking system.

```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    assigned_to UUID REFERENCES users(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'cancelled')),
    category VARCHAR(100),
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2) DEFAULT 0,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    due_date DATE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_tasks_assigned_to (assigned_to),
    INDEX idx_tasks_assigned_by (assigned_by),
    INDEX idx_tasks_organization (organization_id),
    INDEX idx_tasks_status (status),
    INDEX idx_tasks_due_date (due_date),
    INDEX idx_tasks_priority (priority)
);
```

### 11. Task Updates Table
Daily progress updates and comments on tasks.

```sql
CREATE TABLE task_updates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    update_date DATE NOT NULL,
    progress_update TEXT NOT NULL,
    progress_percentage INTEGER CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    hours_spent DECIMAL(4,2) DEFAULT 0,
    blockers TEXT,
    next_steps TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_task_updates_task (task_id),
    INDEX idx_task_updates_user (user_id),
    INDEX idx_task_updates_date (update_date)
);
```

### 12. Task Comments Table
Comments and feedback on tasks.

```sql
CREATE TABLE task_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_task_comments_task (task_id),
    INDEX idx_task_comments_user (user_id)
);
```

---

## Performance Management Tables

### 13. Performance Reviews Table
Employee performance review cycles and ratings.

```sql
CREATE TABLE performance_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES users(id),
    review_period VARCHAR(50) NOT NULL,
    review_type VARCHAR(50) DEFAULT 'quarterly' CHECK (review_type IN ('quarterly', 'annual', 'probation', 'project-based')),
    overall_rating DECIMAL(3,2) CHECK (overall_rating >= 0 AND overall_rating <= 5),
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in-progress', 'completed', 'cancelled')),
    feedback TEXT,
    goals_achieved TEXT,
    areas_for_improvement TEXT,
    development_plan TEXT,
    review_date DATE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_performance_reviews_employee (employee_id),
    INDEX idx_performance_reviews_reviewer (reviewer_id),
    INDEX idx_performance_reviews_period (review_period),
    INDEX idx_performance_reviews_status (status)
);
```

### 14. Performance Goals Table
Individual and team performance goals.

```sql
CREATE TABLE performance_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES users(id) ON DELETE CASCADE,
    review_id UUID REFERENCES performance_reviews(id) ON DELETE CASCADE,
    goal_title VARCHAR(255) NOT NULL,
    goal_description TEXT,
    target_date DATE,
    weight_percentage INTEGER DEFAULT 0 CHECK (weight_percentage >= 0 AND weight_percentage <= 100),
    achievement_percentage INTEGER DEFAULT 0 CHECK (achievement_percentage >= 0 AND achievement_percentage <= 100),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'deferred')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_performance_goals_employee (employee_id),
    INDEX idx_performance_goals_review (review_id),
    INDEX idx_performance_goals_status (status)
);
```

---

## Payroll Management Tables

### 15. Payroll Cycles Table
Monthly/periodic payroll processing cycles.

```sql
CREATE TABLE payroll_cycles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    cycle_name VARCHAR(100) NOT NULL,
    pay_period_start DATE NOT NULL,
    pay_period_end DATE NOT NULL,
    pay_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed', 'cancelled')),
    total_gross_amount DECIMAL(15,2) DEFAULT 0,
    total_net_amount DECIMAL(15,2) DEFAULT 0,
    total_deductions DECIMAL(15,2) DEFAULT 0,
    processed_by UUID REFERENCES users(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_payroll_cycles_organization (organization_id),
    INDEX idx_payroll_cycles_period (pay_period_start, pay_period_end),
    INDEX idx_payroll_cycles_status (status)
);
```

### 16. Employee Payroll Table
Individual employee payroll records.

```sql
CREATE TABLE employee_payroll (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_cycle_id UUID REFERENCES payroll_cycles(id) ON DELETE CASCADE,
    employee_id UUID REFERENCES users(id) ON DELETE CASCADE,
    gross_salary DECIMAL(12,2) NOT NULL,
    basic_salary DECIMAL(12,2) NOT NULL,
    allowances DECIMAL(12,2) DEFAULT 0,
    overtime_amount DECIMAL(12,2) DEFAULT 0,
    bonus_amount DECIMAL(12,2) DEFAULT 0,
    total_deductions DECIMAL(12,2) DEFAULT 0,
    tax_deductions DECIMAL(12,2) DEFAULT 0,
    insurance_deductions DECIMAL(12,2) DEFAULT 0,
    provident_fund DECIMAL(12,2) DEFAULT 0,
    other_deductions DECIMAL(12,2) DEFAULT 0,
    net_salary DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'paid', 'cancelled')),
    pay_slip_generated BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Unique constraint for employee per payroll cycle
    UNIQUE(payroll_cycle_id, employee_id),

    -- Indexes
    INDEX idx_employee_payroll_cycle (payroll_cycle_id),
    INDEX idx_employee_payroll_employee (employee_id),
    INDEX idx_employee_payroll_status (status)
);
```

### 17. Employee Benefits Table
Employee benefits and their contributions.

```sql
CREATE TABLE employee_benefits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES users(id) ON DELETE CASCADE,
    benefit_name VARCHAR(255) NOT NULL,
    benefit_type VARCHAR(100) CHECK (benefit_type IN ('health-insurance', 'dental-insurance', 'vision-insurance', 'life-insurance', 'provident-fund', 'other')),
    employee_contribution DECIMAL(10,2) DEFAULT 0,
    employer_contribution DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_employee_benefits_employee (employee_id),
    INDEX idx_employee_benefits_type (benefit_type),
    INDEX idx_employee_benefits_status (status)
);
```

---

## Recruitment Management Tables

### 18. Job Postings Table
Job openings and their details.

```sql
CREATE TABLE job_postings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    job_title VARCHAR(255) NOT NULL,
    department VARCHAR(100) NOT NULL,
    job_description TEXT NOT NULL,
    requirements TEXT,
    location VARCHAR(255),
    employment_type VARCHAR(50) DEFAULT 'full-time' CHECK (employment_type IN ('full-time', 'part-time', 'contract', 'intern')),
    experience_level VARCHAR(50) CHECK (experience_level IN ('entry', 'mid', 'senior', 'executive')),
    salary_range_min DECIMAL(12,2),
    salary_range_max DECIMAL(12,2),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'closed', 'cancelled')),
    posted_by UUID REFERENCES users(id),
    posted_date DATE,
    application_deadline DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_job_postings_organization (organization_id),
    INDEX idx_job_postings_department (department),
    INDEX idx_job_postings_status (status),
    INDEX idx_job_postings_posted_by (posted_by)
);
```

### 19. Job Applications Table
Candidate applications for job postings.

```sql
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_posting_id UUID REFERENCES job_postings(id) ON DELETE CASCADE,
    candidate_name VARCHAR(255) NOT NULL,
    candidate_email VARCHAR(255) NOT NULL,
    candidate_phone VARCHAR(20),
    resume_url VARCHAR(500),
    cover_letter TEXT,
    experience_years INTEGER DEFAULT 0,
    current_salary DECIMAL(12,2),
    expected_salary DECIMAL(12,2),
    notice_period VARCHAR(50),
    status VARCHAR(20) DEFAULT 'applied' CHECK (status IN ('applied', 'screening', 'interview', 'selected', 'rejected', 'withdrawn')),
    applied_date DATE DEFAULT CURRENT_DATE,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_job_applications_posting (job_posting_id),
    INDEX idx_job_applications_email (candidate_email),
    INDEX idx_job_applications_status (status),
    INDEX idx_job_applications_applied_date (applied_date)
);
```

---

## System Configuration Tables

### 20. System Settings Table
Platform-wide configuration settings.

```sql
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_system_settings_key (setting_key),
    INDEX idx_system_settings_public (is_public)
);
```

### 21. Organization Settings Table
Organization-specific configuration settings.

```sql
CREATE TABLE organization_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Unique constraint for setting per organization
    UNIQUE(organization_id, setting_key),

    -- Indexes
    INDEX idx_organization_settings_org (organization_id),
    INDEX idx_organization_settings_key (setting_key)
);
```

### 22. Audit Logs Table
System-wide audit trail for security and compliance.

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_audit_logs_user (user_id),
    INDEX idx_audit_logs_organization (organization_id),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_resource (resource_type, resource_id),
    INDEX idx_audit_logs_created_at (created_at)
);
```

---

## Billing and Subscription Tables

### 23. Subscription Plans Table
Available subscription plans and their features.

```sql
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    employee_limit INTEGER,
    features JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_subscription_plans_active (is_active),
    INDEX idx_subscription_plans_name (name)
);
```

### 24. Organization Subscriptions Table
Organization subscription details and billing information.

```sql
CREATE TABLE organization_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID UNIQUE REFERENCES organizations(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'suspended', 'expired')),
    billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
    current_period_start DATE NOT NULL,
    current_period_end DATE NOT NULL,
    next_billing_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_org_subscriptions_organization (organization_id),
    INDEX idx_org_subscriptions_plan (plan_id),
    INDEX idx_org_subscriptions_status (status),
    INDEX idx_org_subscriptions_billing_date (next_billing_date)
);
```

### 25. Billing Invoices Table
Invoice generation and payment tracking.

```sql
CREATE TABLE billing_invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES organization_subscriptions(id),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    paid_date DATE,
    payment_method VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_billing_invoices_organization (organization_id),
    INDEX idx_billing_invoices_subscription (subscription_id),
    INDEX idx_billing_invoices_status (status),
    INDEX idx_billing_invoices_due_date (due_date)
);
```

---

## Sample Data Inserts

### Organizations Sample Data
```sql
INSERT INTO organizations (id, name, domain, industry, employee_count, status, subscription_plan, monthly_revenue) VALUES
('org-1', 'Tech Innovators Inc', 'techinnovators.com', 'Technology', 150, 'active', 'professional', 1250000.00),
('org-2', 'Healthcare Solutions', 'healthcaresol.com', 'Healthcare', 80, 'active', 'standard', 666000.00),
('org-3', 'Finance Corp', 'financecorp.com', 'Finance', 200, 'pending', 'enterprise', 1666000.00);
```

### Leave Types Sample Data
```sql
INSERT INTO leave_types (organization_id, name, code, annual_allocation, carry_forward_allowed, max_carry_forward) VALUES
('org-1', 'Annual Leave', 'AL', 25, true, 5),
('org-1', 'Sick Leave', 'SL', 12, false, 0),
('org-1', 'Personal Leave', 'PL', 5, false, 0),
('org-1', 'Maternity/Paternity Leave', 'ML', 90, false, 0);
```

### System Settings Sample Data
```sql
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('platform_name', 'HRMS Portal', 'string', 'Platform display name', true),
('max_organizations', '100', 'number', 'Maximum allowed organizations', false),
('maintenance_mode', 'false', 'boolean', 'System maintenance mode', true),
('registration_enabled', 'true', 'boolean', 'Allow new organization registration', true);
```

---

## Database Relationships Summary

### Primary Relationships
1. **Organizations** → **Users** (1:N) - One organization has many users
2. **Users** → **Employee Details** (1:1) - One user has one employee detail record
3. **Employee Details** → **Education/Skills** (1:N) - One employee has multiple education/skill records
4. **Users** → **Attendance Records** (1:N) - One user has many attendance records
5. **Users** → **Leave Requests** (1:N) - One user has many leave requests
6. **Users** → **Tasks** (1:N) - One user can be assigned many tasks
7. **Users** → **Performance Reviews** (1:N) - One user can have many performance reviews

### Key Constraints
- **Multi-tenancy**: All organization-specific data includes organization_id for data isolation
- **Role-based Access**: User roles determine data access permissions
- **Data Integrity**: Foreign key constraints ensure referential integrity
- **Audit Trail**: Comprehensive logging for compliance and security

### Indexing Strategy
- **Primary Keys**: All tables use UUID primary keys for scalability
- **Foreign Keys**: Indexed for efficient joins and lookups
- **Query Optimization**: Indexes on frequently queried columns (dates, status, organization_id)
- **Composite Indexes**: Multi-column indexes for complex queries

This schema provides a robust foundation for the HRMS application with proper normalization, data integrity, and performance optimization.
