-- Migration 001: Create Core Tables
-- Creates the fundamental tables for organizations, users, and employee details
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations table (single record for this schema)
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    industry VARCHAR(100) NOT NULL,
    employee_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'pending', 'suspended')),
    subscription_plan VARCHAR(50) DEFAULT 'standard' CHECK (subscription_plan IN ('standard', 'professional', 'enterprise')),
    monthly_revenue DECIMAL(12,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('super_admin', 'org_admin', 'employee')),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_org_required CHECK (
        (role = 'super_admin' AND organization_id IS NULL) OR 
        (role IN ('org_admin', 'employee') AND organization_id IS NOT NULL)
    )
);

-- Employee details table
CREATE TABLE employee_details (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) NOT NULL,
    job_title VARCHAR(255) NOT NULL,
    department VARCHAR(100) NOT NULL,
    manager_id UUID REFERENCES users(id),
    join_date DATE NOT NULL,
    employment_type VARCHAR(50) DEFAULT 'full-time' CHECK (employment_type IN ('full-time', 'part-time', 'contract', 'intern')),
    employment_status VARCHAR(50) DEFAULT 'active' CHECK (employment_status IN ('active', 'inactive', 'terminated', 'on-leave')),
    work_location VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    base_salary DECIMAL(12,2),
    annual_ctc DECIMAL(12,2),
    next_salary_review DATE,
    performance_rating DECIMAL(3,2) CHECK (performance_rating >= 0 AND performance_rating <= 5),
    total_experience VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for employee_id within organization
    UNIQUE(employee_id, user_id)
);

-- Employee education table
CREATE TABLE employee_education (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employee_details(id) ON DELETE CASCADE,
    degree VARCHAR(255) NOT NULL,
    institution VARCHAR(255) NOT NULL,
    year VARCHAR(4) NOT NULL,
    grade_or_percentage VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee skills table
CREATE TABLE employee_skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employee_details(id) ON DELETE CASCADE,
    skill_name VARCHAR(255) NOT NULL,
    proficiency_level INTEGER CHECK (proficiency_level >= 0 AND proficiency_level <= 100),
    skill_category VARCHAR(100) DEFAULT 'technical' CHECK (skill_category IN ('technical', 'soft', 'language', 'certification')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for skill per employee
    UNIQUE(employee_id, skill_name)
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON organizations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_details_updated_at 
    BEFORE UPDATE ON employee_details 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert the organization record for this schema
INSERT INTO organizations (id, name, domain, industry, status, created_at) 
VALUES ('{ORG_ID}', 'Organization Name', 'organization.domain', 'Industry', 'active', CURRENT_TIMESTAMP);

-- Create comments for documentation
COMMENT ON TABLE organizations IS 'Organization information for this tenant schema';
COMMENT ON TABLE users IS 'User accounts and authentication information';
COMMENT ON TABLE employee_details IS 'Comprehensive employee HR information';
COMMENT ON TABLE employee_education IS 'Employee educational qualifications and certifications';
COMMENT ON TABLE employee_skills IS 'Employee technical and soft skills with proficiency levels';

-- Grant permissions to application user
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA {SCHEMA_NAME} TO hrms_app_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA {SCHEMA_NAME} TO hrms_app_user;

-- Grant read-only access to readonly user
GRANT SELECT ON ALL TABLES IN SCHEMA {SCHEMA_NAME} TO hrms_readonly;

COMMIT;
