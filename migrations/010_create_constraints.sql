-- Migration 010: Create Additional Constraints and Views
-- Creates additional constraints, views, and final schema optimizations
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Additional constraints for data integrity

-- Ensure leave request dates are logical
ALTER TABLE leave_requests 
ADD CONSTRAINT chk_leave_dates CHECK (from_date <= to_date);

-- Ensure payroll cycle dates are logical
ALTER TABLE payroll_cycles 
ADD CONSTRAINT chk_payroll_cycle_dates CHECK (pay_period_start <= pay_period_end);

-- Ensure salary components have valid calculation values
ALTER TABLE salary_components 
ADD CONSTRAINT chk_salary_component_calculation 
CHECK (
    (calculation_type = 'fixed' AND calculation_value >= 0) OR
    (calculation_type = 'percentage' AND calculation_value >= 0 AND calculation_value <= 100) OR
    (calculation_type = 'formula')
);

-- Ensure interview schedules are in the future when created
ALTER TABLE interview_schedules 
ADD CONSTRAINT chk_interview_future_date 
CHECK (scheduled_date > created_at);

-- Ensure task progress percentage matches status
ALTER TABLE tasks 
ADD CONSTRAINT chk_task_progress_status 
CHECK (
    (status = 'completed' AND progress_percentage = 100) OR
    (status != 'completed')
);

-- Create useful views for common queries

-- Employee summary view
CREATE VIEW employee_summary AS
SELECT 
    u.id,
    u.name,
    u.email,
    ed.employee_id,
    ed.job_title,
    ed.department,
    ed.employment_status,
    ed.join_date,
    ed.base_salary,
    ed.annual_ctc,
    manager.name as manager_name,
    o.name as organization_name
FROM users u
JOIN employee_details ed ON u.id = ed.user_id
LEFT JOIN users manager ON ed.manager_id = manager.id
JOIN organizations o ON u.organization_id = o.id
WHERE u.role IN ('employee', 'org_admin');

-- Current leave balances view
CREATE VIEW current_leave_balances AS
SELECT 
    lb.user_id,
    u.name as employee_name,
    lt.name as leave_type,
    lt.code as leave_code,
    lb.year,
    lb.total_allocated,
    lb.used_leaves,
    lb.remaining_leaves,
    lb.carried_forward
FROM leave_balances lb
JOIN users u ON lb.user_id = u.id
JOIN leave_types lt ON lb.leave_type_id = lt.id
WHERE lb.year = EXTRACT(YEAR FROM CURRENT_DATE)
AND lt.is_active = true;

-- Active tasks summary view
CREATE VIEW active_tasks_summary AS
SELECT 
    t.id,
    t.title,
    t.priority,
    t.status,
    t.progress_percentage,
    t.due_date,
    assigned_user.name as assigned_to_name,
    assigned_user.email as assigned_to_email,
    assigner.name as assigned_by_name,
    t.estimated_hours,
    t.actual_hours,
    CASE 
        WHEN t.due_date < CURRENT_DATE AND t.status != 'completed' THEN 'overdue'
        WHEN t.due_date = CURRENT_DATE AND t.status != 'completed' THEN 'due_today'
        ELSE 'on_track'
    END as urgency_status
FROM tasks t
JOIN users assigned_user ON t.assigned_to = assigned_user.id
LEFT JOIN users assigner ON t.assigned_by = assigner.id
WHERE t.status IN ('pending', 'in-progress');

-- Monthly attendance summary view
CREATE VIEW monthly_attendance_summary AS
SELECT 
    u.id as user_id,
    u.name as employee_name,
    ed.employee_id,
    ed.department,
    asm.month,
    asm.year,
    asm.total_working_days,
    asm.present_days,
    asm.absent_days,
    asm.late_days,
    asm.total_hours,
    asm.attendance_percentage,
    CASE 
        WHEN asm.attendance_percentage >= 95 THEN 'excellent'
        WHEN asm.attendance_percentage >= 90 THEN 'good'
        WHEN asm.attendance_percentage >= 80 THEN 'average'
        ELSE 'poor'
    END as attendance_grade
FROM attendance_summary asm
JOIN users u ON asm.user_id = u.id
JOIN employee_details ed ON u.id = ed.user_id;

-- Payroll summary view
CREATE VIEW payroll_summary AS
SELECT 
    pc.id as cycle_id,
    pc.cycle_name,
    pc.pay_period_start,
    pc.pay_period_end,
    pc.pay_date,
    pc.status as cycle_status,
    COUNT(ep.id) as employee_count,
    SUM(ep.gross_salary) as total_gross,
    SUM(ep.total_deductions) as total_deductions,
    SUM(ep.net_salary) as total_net,
    AVG(ep.gross_salary) as avg_gross_salary
FROM payroll_cycles pc
LEFT JOIN employee_payroll ep ON pc.id = ep.payroll_cycle_id
GROUP BY pc.id, pc.cycle_name, pc.pay_period_start, pc.pay_period_end, pc.pay_date, pc.status;

-- Recruitment pipeline view
CREATE VIEW recruitment_pipeline AS
SELECT 
    jp.id as job_id,
    jp.job_title,
    jp.department,
    jp.status as job_status,
    jp.posted_date,
    COUNT(ja.id) as total_applications,
    COUNT(CASE WHEN ja.status = 'applied' THEN 1 END) as applied_count,
    COUNT(CASE WHEN ja.status = 'screening' THEN 1 END) as screening_count,
    COUNT(CASE WHEN ja.status = 'interview' THEN 1 END) as interview_count,
    COUNT(CASE WHEN ja.status = 'selected' THEN 1 END) as selected_count,
    COUNT(CASE WHEN ja.status = 'rejected' THEN 1 END) as rejected_count
FROM job_postings jp
LEFT JOIN job_applications ja ON jp.id = ja.job_posting_id
GROUP BY jp.id, jp.job_title, jp.department, jp.status, jp.posted_date;

-- Performance review summary view
CREATE VIEW performance_review_summary AS
SELECT 
    pr.id as review_id,
    u.name as employee_name,
    ed.employee_id,
    ed.department,
    pr.review_period,
    pr.review_type,
    pr.overall_rating,
    pr.status,
    pr.review_date,
    reviewer.name as reviewer_name,
    COUNT(pg.id) as total_goals,
    COUNT(CASE WHEN pg.status = 'completed' THEN 1 END) as completed_goals,
    AVG(pg.achievement_percentage) as avg_goal_achievement
FROM performance_reviews pr
JOIN users u ON pr.employee_id = u.id
JOIN employee_details ed ON u.id = ed.user_id
LEFT JOIN users reviewer ON pr.reviewer_id = reviewer.id
LEFT JOIN performance_goals pg ON pr.id = pg.review_id
GROUP BY pr.id, u.name, ed.employee_id, ed.department, pr.review_period, 
         pr.review_type, pr.overall_rating, pr.status, pr.review_date, reviewer.name;

-- Create functions for common calculations

-- Function to get employee's current leave balance
CREATE OR REPLACE FUNCTION get_leave_balance(
    p_user_id UUID,
    p_leave_type_code VARCHAR(20),
    p_year INTEGER DEFAULT NULL
)
RETURNS DECIMAL(4,1) AS $$
DECLARE
    balance DECIMAL(4,1);
    target_year INTEGER;
BEGIN
    target_year := COALESCE(p_year, EXTRACT(YEAR FROM CURRENT_DATE));
    
    SELECT remaining_leaves INTO balance
    FROM leave_balances lb
    JOIN leave_types lt ON lb.leave_type_id = lt.id
    WHERE lb.user_id = p_user_id
    AND lt.code = p_leave_type_code
    AND lb.year = target_year;
    
    RETURN COALESCE(balance, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to calculate employee's total compensation
CREATE OR REPLACE FUNCTION calculate_total_compensation(
    p_employee_id UUID,
    p_effective_date DATE DEFAULT CURRENT_DATE
)
RETURNS DECIMAL(12,2) AS $$
DECLARE
    total_compensation DECIMAL(12,2) := 0;
BEGIN
    SELECT COALESCE(SUM(ess.amount), 0) INTO total_compensation
    FROM employee_salary_structure ess
    JOIN salary_components sc ON ess.component_id = sc.id
    WHERE ess.employee_id = p_employee_id
    AND sc.component_type = 'earning'
    AND ess.effective_from <= p_effective_date
    AND (ess.effective_to IS NULL OR ess.effective_to >= p_effective_date);
    
    RETURN total_compensation;
END;
$$ LANGUAGE plpgsql;

-- Function to get employee's attendance percentage for a period
CREATE OR REPLACE FUNCTION get_attendance_percentage(
    p_user_id UUID,
    p_start_date DATE,
    p_end_date DATE
)
RETURNS DECIMAL(5,2) AS $$
DECLARE
    total_days INTEGER;
    present_days INTEGER;
    percentage DECIMAL(5,2);
BEGIN
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status IN ('present', 'work-from-home') THEN 1 END) as present
    INTO total_days, present_days
    FROM attendance_records
    WHERE user_id = p_user_id
    AND date BETWEEN p_start_date AND p_end_date;
    
    IF total_days > 0 THEN
        percentage := (present_days::DECIMAL / total_days * 100);
    ELSE
        percentage := 0;
    END IF;
    
    RETURN percentage;
END;
$$ LANGUAGE plpgsql;

-- Create row-level security policies (if enabled)
-- These will be activated based on configuration

-- Enable RLS on sensitive tables
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE employee_details ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE leave_requests ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE employee_payroll ENABLE ROW LEVEL SECURITY;

-- Create policies for organization isolation
-- CREATE POLICY org_isolation_users ON users
--     FOR ALL TO hrms_app_user
--     USING (organization_id = current_setting('app.current_organization_id')::UUID);

-- Grant permissions on views to application users
GRANT SELECT ON employee_summary TO hrms_app_user, hrms_readonly;
GRANT SELECT ON current_leave_balances TO hrms_app_user, hrms_readonly;
GRANT SELECT ON active_tasks_summary TO hrms_app_user, hrms_readonly;
GRANT SELECT ON monthly_attendance_summary TO hrms_app_user, hrms_readonly;
GRANT SELECT ON payroll_summary TO hrms_app_user, hrms_readonly;
GRANT SELECT ON recruitment_pipeline TO hrms_app_user, hrms_readonly;
GRANT SELECT ON performance_review_summary TO hrms_app_user, hrms_readonly;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_leave_balance(UUID, VARCHAR(20), INTEGER) TO hrms_app_user, hrms_readonly;
GRANT EXECUTE ON FUNCTION calculate_total_compensation(UUID, DATE) TO hrms_app_user, hrms_readonly;
GRANT EXECUTE ON FUNCTION get_attendance_percentage(UUID, DATE, DATE) TO hrms_app_user, hrms_readonly;

-- Create comments for views
COMMENT ON VIEW employee_summary IS 'Comprehensive employee information summary';
COMMENT ON VIEW current_leave_balances IS 'Current year leave balances for all employees';
COMMENT ON VIEW active_tasks_summary IS 'Summary of active tasks with urgency status';
COMMENT ON VIEW monthly_attendance_summary IS 'Monthly attendance summary with grades';
COMMENT ON VIEW payroll_summary IS 'Payroll cycle summary with totals';
COMMENT ON VIEW recruitment_pipeline IS 'Recruitment pipeline status summary';
COMMENT ON VIEW performance_review_summary IS 'Performance review summary with goal achievements';

COMMIT;
