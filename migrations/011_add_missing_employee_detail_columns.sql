-- Migration: Add missing columns to EmployeeDetails table
-- This migration adds the missing columns that are causing the 500 error during employee creation

-- Add missing columns to EmployeeDetails table
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'EmergencyContactName')
BEGIN
    ALTER TABLE EmployeeDetails ADD EmergencyContactName NVARCHAR(100) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'EmergencyContactPhone')
BEGIN
    ALTER TABLE EmployeeDetails ADD EmergencyContactPhone NVARCHAR(20) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'EmergencyContactRelation')
BEGIN
    ALTER TABLE EmployeeDetails ADD EmergencyContactRelation NVARCHAR(100) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'BloodGroup')
BEGIN
    ALTER TABLE EmployeeDetails ADD BloodGroup NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'MaritalStatus')
BEGIN
    ALTER TABLE EmployeeDetails ADD MaritalStatus NVARCHAR(20) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'Gender')
BEGIN
    ALTER TABLE EmployeeDetails ADD Gender NVARCHAR(10) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'Nationality')
BEGIN
    ALTER TABLE EmployeeDetails ADD Nationality NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'BankAccountNumber')
BEGIN
    ALTER TABLE EmployeeDetails ADD BankAccountNumber NVARCHAR(100) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'BankIFSC')
BEGIN
    ALTER TABLE EmployeeDetails ADD BankIFSC NVARCHAR(20) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'BankName')
BEGIN
    ALTER TABLE EmployeeDetails ADD BankName NVARCHAR(100) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'PAN')
BEGIN
    ALTER TABLE EmployeeDetails ADD PAN NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'Aadhar')
BEGIN
    ALTER TABLE EmployeeDetails ADD Aadhar NVARCHAR(20) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'ProbationEndDate')
BEGIN
    ALTER TABLE EmployeeDetails ADD ProbationEndDate DATETIME2 NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'ConfirmationDate')
BEGIN
    ALTER TABLE EmployeeDetails ADD ConfirmationDate DATETIME2 NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'ResignationDate')
BEGIN
    ALTER TABLE EmployeeDetails ADD ResignationDate DATETIME2 NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'LastWorkingDate')
BEGIN
    ALTER TABLE EmployeeDetails ADD LastWorkingDate DATETIME2 NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'ResignationReason')
BEGIN
    ALTER TABLE EmployeeDetails ADD ResignationReason NVARCHAR(MAX) NULL;
END

PRINT 'Migration 011: Added missing EmployeeDetails columns successfully';
