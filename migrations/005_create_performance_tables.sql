-- Migration 005: Create Performance Management Tables
-- Creates tables for performance reviews, goals, and ratings
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Performance reviews table
CREATE TABLE performance_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES users(id),
    review_period VARCHAR(50) NOT NULL,
    review_type VARCHAR(50) DEFAULT 'quarterly' CHECK (review_type IN ('quarterly', 'annual', 'probation', 'project-based')),
    overall_rating DECIMAL(3,2) CHECK (overall_rating >= 0 AND overall_rating <= 5),
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in-progress', 'completed', 'cancelled')),
    feedback TEXT,
    goals_achieved TEXT,
    areas_for_improvement TEXT,
    development_plan TEXT,
    review_date DATE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance goals table
CREATE TABLE performance_goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES users(id) ON DELETE CASCADE,
    review_id UUID REFERENCES performance_reviews(id) ON DELETE CASCADE,
    goal_title VARCHAR(255) NOT NULL,
    goal_description TEXT,
    target_date DATE,
    weight_percentage INTEGER DEFAULT 0 CHECK (weight_percentage >= 0 AND weight_percentage <= 100),
    achievement_percentage INTEGER DEFAULT 0 CHECK (achievement_percentage >= 0 AND achievement_percentage <= 100),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'deferred')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance metrics table
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    metric_description TEXT,
    metric_type VARCHAR(50) CHECK (metric_type IN ('quantitative', 'qualitative', 'behavioral')),
    measurement_scale VARCHAR(50) DEFAULT '1-5' CHECK (measurement_scale IN ('1-5', '1-10', 'percentage', 'binary')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance ratings table
CREATE TABLE performance_ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    review_id UUID REFERENCES performance_reviews(id) ON DELETE CASCADE,
    metric_id UUID REFERENCES performance_metrics(id),
    rating_value DECIMAL(5,2) NOT NULL,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers
CREATE TRIGGER update_performance_reviews_updated_at 
    BEFORE UPDATE ON performance_reviews 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_performance_goals_updated_at 
    BEFORE UPDATE ON performance_goals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;
