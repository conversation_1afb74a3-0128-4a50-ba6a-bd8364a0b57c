-- Migration: 013_add_missing_task_columns.sql
-- Description: Add missing columns to TaskUpdates table
-- Date: 2025-01-21
-- Author: HRMS System

-- Add missing columns to TaskUpdates table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdatedById')
BEGIN
    ALTER TABLE TaskUpdates ADD UpdatedById UNIQUEIDENTIFIER;
    ALTER TABLE TaskUpdates ADD CONSTRAINT FK_TaskUpdates_UpdatedBy FOREIGN KEY (UpdatedById) REFERENCES Users(Id);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdateDate')
BEGIN
    ALTER TABLE TaskUpdates ADD UpdateDate DATE DEFAULT CAST(GETDATE() AS DATE);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdateType')
BEGIN
    ALTER TABLE TaskUpdates ADD UpdateType NVARCHAR(20) DEFAULT 'Daily' CHECK (UpdateType IN ('Daily', 'Milestone', 'Completion', 'Blocker', 'StatusChange'));
END

-- Update existing TaskUpdates records to have UpdatedById populated from CreatedBy
UPDATE TaskUpdates 
SET UpdatedById = CreatedBy 
WHERE UpdatedById IS NULL AND CreatedBy IS NOT NULL;

-- Update existing TaskUpdates records to have UpdateDate populated
UPDATE TaskUpdates 
SET UpdateDate = COALESCE(CAST(CreatedAt AS DATE), CAST(GETDATE() AS DATE))
WHERE UpdateDate IS NULL;

-- Rename the snake_case columns to PascalCase for consistency
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Tasks') AND name = 'is_self_created')
BEGIN
    EXEC sp_rename 'Tasks.is_self_created', 'IsSelfCreated', 'COLUMN';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Tasks') AND name = 'task_type')
BEGIN
    EXEC sp_rename 'Tasks.task_type', 'TaskType', 'COLUMN';
END

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Tasks') AND name = 'idx_tasks_self_created')
BEGIN
    CREATE INDEX idx_tasks_self_created ON Tasks(IsSelfCreated);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Tasks') AND name = 'idx_tasks_task_type')
BEGIN
    CREATE INDEX idx_tasks_task_type ON Tasks(TaskType);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'idx_task_updates_update_date')
BEGIN
    CREATE INDEX idx_task_updates_update_date ON TaskUpdates(UpdateDate);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'idx_task_updates_updated_by')
BEGIN
    CREATE INDEX idx_task_updates_updated_by ON TaskUpdates(UpdatedById);
END

PRINT 'Missing task columns migration completed successfully';
