-- Migration: 012_enhance_task_management.sql
-- Description: Enhance task management system to support employee self-created tasks and daily updates
-- Date: 2025-01-21
-- Author: HRMS System

BEGIN;

-- Add new columns to tasks table for enhanced functionality
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS is_self_created BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS task_type VARCHAR(20) DEFAULT 'Regular' CHECK (task_type IN ('Regular', 'SelfCreated', 'Personal', 'Project'));

-- Add new columns to task_updates table for better daily update tracking
ALTER TABLE task_updates 
ADD COLUMN IF NOT EXISTS updated_by_id UUID REFERENCES users(id) ON DELETE RESTRICT,
ADD COLUMN IF NOT EXISTS update_date DATE DEFAULT CURRENT_DATE,
ADD COLUMN IF NOT EXISTS update_type VARCHAR(20) DEFAULT 'Daily' CHECK (update_type IN ('Daily', 'Milestone', 'Completion', 'Blocker', 'StatusChange'));

-- Make update_notes required (not null) for better data integrity
ALTER TABLE task_updates 
ALTER COLUMN update_notes SET NOT NULL,
ALTER COLUMN update_notes SET DEFAULT '';

-- Update existing task_updates records to have updated_by_id populated
-- This assumes that the user_id column exists and should be mapped to updated_by_id
UPDATE task_updates 
SET updated_by_id = user_id 
WHERE updated_by_id IS NULL AND user_id IS NOT NULL;

-- Update existing task_updates records to have update_date populated
UPDATE task_updates 
SET update_date = COALESCE(DATE(created_at), CURRENT_DATE)
WHERE update_date IS NULL;

-- Create index for better performance on task queries by type and self-created status
CREATE INDEX IF NOT EXISTS idx_tasks_self_created ON tasks(is_self_created);
CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to_self_created ON tasks(assigned_to, is_self_created);

-- Create index for better performance on task_updates queries
CREATE INDEX IF NOT EXISTS idx_task_updates_update_date ON task_updates(update_date);
CREATE INDEX IF NOT EXISTS idx_task_updates_updated_by ON task_updates(updated_by_id);
CREATE INDEX IF NOT EXISTS idx_task_updates_task_date ON task_updates(task_id, update_date);
CREATE INDEX IF NOT EXISTS idx_task_updates_type ON task_updates(update_type);

-- Create a view for manager visibility - tasks visible to managers based on employee hierarchy
CREATE OR REPLACE VIEW manager_visible_tasks AS
SELECT DISTINCT
    t.*,
    ed_assigned.manager_id as manager_id
FROM tasks t
JOIN users u_assigned ON t.assigned_to = u_assigned.id
LEFT JOIN employee_details ed_assigned ON u_assigned.id = ed_assigned.user_id
WHERE 
    -- Include all admin-assigned tasks (visible to org admins)
    t.is_self_created = FALSE
    OR 
    -- Include self-created tasks that should be visible to managers
    (t.is_self_created = TRUE AND ed_assigned.manager_id IS NOT NULL);

-- Create a view for daily updates with user information
CREATE OR REPLACE VIEW daily_task_updates AS
SELECT 
    tu.*,
    t.title as task_title,
    t.assigned_to as task_assigned_to,
    u.name as updated_by_name,
    u.email as updated_by_email,
    ed.employee_id as updated_by_employee_id,
    ed.department as updated_by_department,
    ed.manager_id as updated_by_manager_id
FROM task_updates tu
JOIN tasks t ON tu.task_id = t.id
JOIN users u ON tu.updated_by_id = u.id
LEFT JOIN employee_details ed ON u.id = ed.user_id
WHERE tu.update_type = 'Daily'
ORDER BY tu.update_date DESC, tu.created_at DESC;

-- Function to check if a user can view a specific task (for role-based access control)
CREATE OR REPLACE FUNCTION can_user_view_task(user_id UUID, task_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR(20);
    user_org_id UUID;
    task_assigned_to UUID;
    task_assigned_by UUID;
    task_is_self_created BOOLEAN;
    user_manager_id UUID;
    assigned_user_manager_id UUID;
BEGIN
    -- Get user information
    SELECT u.role, u.organization_id, ed.manager_id
    INTO user_role, user_org_id, user_manager_id
    FROM users u
    LEFT JOIN employee_details ed ON u.id = ed.user_id
    WHERE u.id = user_id;
    
    -- Get task information
    SELECT t.assigned_to, t.assigned_by, t.is_self_created
    INTO task_assigned_to, task_assigned_by, task_is_self_created
    FROM tasks t
    WHERE t.id = task_id;
    
    -- Get assigned user's manager
    SELECT ed.manager_id
    INTO assigned_user_manager_id
    FROM users u
    JOIN employee_details ed ON u.id = ed.user_id
    WHERE u.id = task_assigned_to;
    
    -- Super admin and org admin can see all tasks in their organization
    IF user_role IN ('super_admin', 'org_admin') THEN
        RETURN TRUE;
    END IF;
    
    -- User can see their own tasks (assigned to them)
    IF task_assigned_to = user_id THEN
        RETURN TRUE;
    END IF;
    
    -- User can see tasks they created/assigned
    IF task_assigned_by = user_id THEN
        RETURN TRUE;
    END IF;
    
    -- Manager can see tasks of their direct reports
    IF assigned_user_manager_id = user_id THEN
        RETURN TRUE;
    END IF;
    
    -- Default: no access
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to get tasks visible to a specific user with role-based filtering
CREATE OR REPLACE FUNCTION get_user_visible_tasks(user_id UUID)
RETURNS TABLE (
    task_id UUID,
    title VARCHAR(255),
    description TEXT,
    priority VARCHAR(20),
    status VARCHAR(20),
    task_type VARCHAR(20),
    is_self_created BOOLEAN,
    progress_percentage INTEGER,
    assigned_to UUID,
    assigned_by UUID,
    due_date DATE,
    estimated_hours DECIMAL(6,2),
    actual_hours DECIMAL(6,2),
    category VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    user_role VARCHAR(20);
    user_org_id UUID;
    user_manager_id UUID;
BEGIN
    -- Get user information
    SELECT u.role, u.organization_id, ed.manager_id
    INTO user_role, user_org_id, user_manager_id
    FROM users u
    LEFT JOIN employee_details ed ON u.id = ed.user_id
    WHERE u.id = user_id;
    
    -- Return tasks based on user role and relationships
    RETURN QUERY
    SELECT DISTINCT
        t.id, t.title, t.description, t.priority::VARCHAR, t.status::VARCHAR, 
        t.task_type::VARCHAR, t.is_self_created, t.progress_percentage,
        t.assigned_to, t.assigned_by, t.due_date, t.estimated_hours, 
        t.actual_hours, t.category, t.created_at, t.updated_at
    FROM tasks t
    JOIN users u_assigned ON t.assigned_to = u_assigned.id
    LEFT JOIN employee_details ed_assigned ON u_assigned.id = ed_assigned.user_id
    WHERE 
        -- Super admin and org admin can see all tasks in organization
        (user_role IN ('super_admin', 'org_admin') AND u_assigned.organization_id = user_org_id)
        OR
        -- User can see their own tasks
        t.assigned_to = user_id
        OR
        -- User can see tasks they created
        t.assigned_by = user_id
        OR
        -- Manager can see tasks of direct reports
        (ed_assigned.manager_id = user_id)
    ORDER BY t.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON COLUMN tasks.is_self_created IS 'Indicates if the task was created by the employee themselves';
COMMENT ON COLUMN tasks.task_type IS 'Type of task: Regular (admin-assigned), SelfCreated, Personal, Project';
COMMENT ON COLUMN task_updates.updated_by_id IS 'User who created this update';
COMMENT ON COLUMN task_updates.update_date IS 'Date of the update for daily tracking';
COMMENT ON COLUMN task_updates.update_type IS 'Type of update: Daily, Milestone, Completion, Blocker, StatusChange';

COMMENT ON VIEW manager_visible_tasks IS 'Tasks visible to managers based on employee hierarchy';
COMMENT ON VIEW daily_task_updates IS 'Daily task updates with user information for reporting';
COMMENT ON FUNCTION can_user_view_task(UUID, UUID) IS 'Check if a user has permission to view a specific task';
COMMENT ON FUNCTION get_user_visible_tasks(UUID) IS 'Get all tasks visible to a user based on role and hierarchy';

COMMIT;
