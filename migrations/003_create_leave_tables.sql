-- Migration 003: Create Leave Management Tables
-- Creates tables for leave types, balances, requests, and approval workflow
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Leave types table (configurable leave types per organization)
CREATE TABLE leave_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL,
    annual_allocation INTEGER DEFAULT 0,
    carry_forward_allowed BOOLEAN DEFAULT false,
    max_carry_forward INTEGER DEFAULT 0,
    requires_approval BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for leave type code per organization
    UNIQUE(organization_id, code)
);

-- Leave balances table (REMOVED - balance functionality disabled)
-- This table was removed in migration: RemoveLeaveBalanceTables.sql
-- Reason: Leave balance functionality disabled to allow unrestricted leave applications

-- Leave requests table (leave applications and their approval workflow)
CREATE TABLE leave_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    leave_type_id UUID REFERENCES leave_types(id),
    from_date DATE NOT NULL,
    to_date DATE NOT NULL,
    total_days DECIMAL(4,1) NOT NULL,
    duration_type VARCHAR(20) DEFAULT 'full-day' CHECK (duration_type IN ('half-day', 'full-day', 'multiple-days')),
    reason TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Leave approval workflow table (multi-level approval process)
CREATE TABLE leave_approval_workflow (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    leave_request_id UUID REFERENCES leave_requests(id) ON DELETE CASCADE,
    approver_id UUID REFERENCES users(id),
    approval_level INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'skipped')),
    comments TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Leave policies table (organization-specific leave policies)
CREATE TABLE leave_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    leave_type_id UUID REFERENCES leave_types(id) ON DELETE CASCADE,
    policy_name VARCHAR(100) NOT NULL,
    policy_rules JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Leave calendar table (company holidays and blackout dates)
CREATE TABLE leave_calendar (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    event_type VARCHAR(50) CHECK (event_type IN ('holiday', 'blackout', 'optional_holiday', 'company_event')),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_working_day BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for date per organization
    UNIQUE(organization_id, date)
);

-- Leave encashment table (for leave encashment requests)
CREATE TABLE leave_encashment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    leave_type_id UUID REFERENCES leave_types(id),
    year INTEGER NOT NULL,
    days_to_encash DECIMAL(4,1) NOT NULL,
    rate_per_day DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'processed')),
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at columns
CREATE TRIGGER update_leave_balances_updated_at 
    BEFORE UPDATE ON leave_balances 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leave_requests_updated_at 
    BEFORE UPDATE ON leave_requests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leave_policies_updated_at 
    BEFORE UPDATE ON leave_policies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leave_encashment_updated_at 
    BEFORE UPDATE ON leave_encashment 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate total leave days
CREATE OR REPLACE FUNCTION calculate_leave_days()
RETURNS TRIGGER AS $$
DECLARE
    total_days DECIMAL(4,1);
    holiday_count INTEGER;
BEGIN
    -- Calculate total days excluding weekends and holidays
    IF NEW.duration_type = 'half-day' THEN
        NEW.total_days = 0.5;
    ELSE
        -- Count working days between from_date and to_date
        SELECT COUNT(*)
        INTO total_days
        FROM generate_series(NEW.from_date, NEW.to_date, '1 day'::interval) AS date_series
        WHERE EXTRACT(DOW FROM date_series) NOT IN (0, 6); -- Exclude weekends
        
        -- Subtract holidays
        SELECT COUNT(*)
        INTO holiday_count
        FROM leave_calendar lc
        JOIN organizations o ON lc.organization_id = o.id
        JOIN users u ON u.organization_id = o.id
        WHERE u.id = NEW.user_id
        AND lc.date BETWEEN NEW.from_date AND NEW.to_date
        AND lc.event_type = 'holiday'
        AND lc.is_working_day = false;
        
        NEW.total_days = total_days - holiday_count;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically calculate leave days
CREATE TRIGGER calculate_leave_days_trigger 
    BEFORE INSERT OR UPDATE ON leave_requests 
    FOR EACH ROW EXECUTE FUNCTION calculate_leave_days();

-- Function to update leave balance when leave is approved
CREATE OR REPLACE FUNCTION update_leave_balance_on_approval()
RETURNS TRIGGER AS $$
DECLARE
    balance_record leave_balances%ROWTYPE;
    current_year INTEGER;
BEGIN
    -- Only process when status changes to approved
    IF NEW.status = 'approved' AND (OLD.status IS NULL OR OLD.status != 'approved') THEN
        current_year := EXTRACT(YEAR FROM NEW.from_date);
        
        -- Get or create leave balance record
        SELECT * INTO balance_record
        FROM leave_balances
        WHERE user_id = NEW.user_id
        AND leave_type_id = NEW.leave_type_id
        AND year = current_year;
        
        IF NOT FOUND THEN
            -- Create new balance record if not exists
            INSERT INTO leave_balances (user_id, leave_type_id, year, total_allocated, used_leaves, remaining_leaves)
            SELECT NEW.user_id, NEW.leave_type_id, current_year, 
                   lt.annual_allocation, NEW.total_days, 
                   lt.annual_allocation - NEW.total_days
            FROM leave_types lt
            WHERE lt.id = NEW.leave_type_id;
        ELSE
            -- Update existing balance record
            UPDATE leave_balances
            SET used_leaves = used_leaves + NEW.total_days,
                remaining_leaves = remaining_leaves - NEW.total_days,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = NEW.user_id
            AND leave_type_id = NEW.leave_type_id
            AND year = current_year;
        END IF;
    END IF;
    
    -- If leave is rejected or cancelled, restore balance
    IF NEW.status IN ('rejected', 'cancelled') AND OLD.status = 'approved' THEN
        current_year := EXTRACT(YEAR FROM NEW.from_date);
        
        UPDATE leave_balances
        SET used_leaves = used_leaves - NEW.total_days,
            remaining_leaves = remaining_leaves + NEW.total_days,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = NEW.user_id
        AND leave_type_id = NEW.leave_type_id
        AND year = current_year;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update leave balance on approval
CREATE TRIGGER update_leave_balance_on_approval_trigger
    AFTER UPDATE ON leave_requests
    FOR EACH ROW EXECUTE FUNCTION update_leave_balance_on_approval();

-- Function to initialize leave balances for new employees
CREATE OR REPLACE FUNCTION initialize_leave_balances_for_user(
    p_user_id UUID,
    p_year INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    leave_type_record leave_types%ROWTYPE;
    target_year INTEGER;
    org_id UUID;
BEGIN
    target_year := COALESCE(p_year, EXTRACT(YEAR FROM CURRENT_DATE));
    
    -- Get organization ID for the user
    SELECT organization_id INTO org_id
    FROM users
    WHERE id = p_user_id;
    
    -- Initialize balances for all active leave types
    FOR leave_type_record IN 
        SELECT * FROM leave_types 
        WHERE organization_id = org_id AND is_active = true
    LOOP
        INSERT INTO leave_balances (
            user_id, leave_type_id, year, total_allocated, 
            used_leaves, remaining_leaves, carried_forward
        ) VALUES (
            p_user_id, leave_type_record.id, target_year, 
            leave_type_record.annual_allocation, 0, 
            leave_type_record.annual_allocation, 0
        )
        ON CONFLICT (user_id, leave_type_id, year) DO NOTHING;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to carry forward leave balances to next year
CREATE OR REPLACE FUNCTION carry_forward_leave_balances(
    p_from_year INTEGER,
    p_to_year INTEGER
)
RETURNS VOID AS $$
DECLARE
    balance_record RECORD;
    carry_forward_amount DECIMAL(4,1);
BEGIN
    FOR balance_record IN 
        SELECT lb.*, lt.carry_forward_allowed, lt.max_carry_forward
        FROM leave_balances lb
        JOIN leave_types lt ON lb.leave_type_id = lt.id
        WHERE lb.year = p_from_year
        AND lt.carry_forward_allowed = true
        AND lb.remaining_leaves > 0
    LOOP
        -- Calculate carry forward amount
        carry_forward_amount := LEAST(
            balance_record.remaining_leaves, 
            balance_record.max_carry_forward
        );
        
        -- Create next year balance with carry forward
        INSERT INTO leave_balances (
            user_id, leave_type_id, year, total_allocated, 
            used_leaves, remaining_leaves, carried_forward
        ) VALUES (
            balance_record.user_id, balance_record.leave_type_id, p_to_year,
            balance_record.total_allocated, 0,
            balance_record.total_allocated + carry_forward_amount,
            carry_forward_amount
        )
        ON CONFLICT (user_id, leave_type_id, year) 
        DO UPDATE SET
            carried_forward = carry_forward_amount,
            remaining_leaves = total_allocated + carry_forward_amount,
            updated_at = CURRENT_TIMESTAMP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create comments for documentation
COMMENT ON TABLE leave_types IS 'Configurable leave types per organization';
COMMENT ON TABLE leave_balances IS 'Employee leave balances by type and year';
COMMENT ON TABLE leave_requests IS 'Leave applications and their approval workflow';
COMMENT ON TABLE leave_approval_workflow IS 'Multi-level approval process for leave requests';
COMMENT ON TABLE leave_policies IS 'Organization-specific leave policies and rules';
COMMENT ON TABLE leave_calendar IS 'Company holidays and blackout dates';
COMMENT ON TABLE leave_encashment IS 'Leave encashment requests and processing';

-- Create indexes for better performance (will be added in migration 009)
-- These are documented here for reference

-- Indexes for leave_requests
-- CREATE INDEX idx_leave_requests_user ON leave_requests(user_id);
-- CREATE INDEX idx_leave_requests_status ON leave_requests(status);
-- CREATE INDEX idx_leave_requests_dates ON leave_requests(from_date, to_date);
-- CREATE INDEX idx_leave_requests_approver ON leave_requests(approved_by);

-- Indexes for leave_balances
-- CREATE INDEX idx_leave_balances_user_year ON leave_balances(user_id, year);
-- CREATE INDEX idx_leave_balances_leave_type ON leave_balances(leave_type_id);

-- Indexes for leave_types
-- CREATE INDEX idx_leave_types_organization ON leave_types(organization_id);
-- CREATE INDEX idx_leave_types_active ON leave_types(is_active);

COMMIT;
