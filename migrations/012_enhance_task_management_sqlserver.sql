-- Migration: 012_enhance_task_management_sqlserver.sql
-- Description: Enhance task management system for SQL Server
-- Date: 2025-01-21
-- Author: HRMS System

-- Add new columns to Tasks table for enhanced functionality
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Tasks') AND name = 'IsSelfCreated')
BEGIN
    ALTER TABLE Tasks ADD IsSelfCreated BIT DEFAULT 0;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Tasks') AND name = 'TaskType')
BEGIN
    ALTER TABLE Tasks ADD TaskType NVARCHAR(20) DEFAULT 'Regular' CHECK (TaskType IN ('Regular', 'SelfCreated', 'Personal', 'Project'));
END

-- Add new columns to TaskUpdates table for better daily update tracking
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdatedById')
BEGIN
    ALTER TABLE TaskUpdates ADD UpdatedById UNIQUEIDENTIFIER;
    ALTER TABLE TaskUpdates ADD CONSTRAINT FK_TaskUpdates_UpdatedBy FOREIGN KEY (UpdatedById) REFERENCES Users(Id);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdateDate')
BEGIN
    ALTER TABLE TaskUpdates ADD UpdateDate DATE DEFAULT CAST(GETDATE() AS DATE);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdateType')
BEGIN
    ALTER TABLE TaskUpdates ADD UpdateType NVARCHAR(20) DEFAULT 'Daily' CHECK (UpdateType IN ('Daily', 'Milestone', 'Completion', 'Blocker', 'StatusChange'));
END

-- Make UpdateNotes required (not null) for better data integrity
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UpdateNotes' AND is_nullable = 1)
BEGIN
    -- First update any NULL values
    UPDATE TaskUpdates SET UpdateNotes = '' WHERE UpdateNotes IS NULL;
    -- Then make it NOT NULL
    ALTER TABLE TaskUpdates ALTER COLUMN UpdateNotes NVARCHAR(MAX) NOT NULL;
END

-- Update existing TaskUpdates records to have UpdatedById populated
-- This assumes that the UserId column exists and should be mapped to UpdatedById
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'UserId')
BEGIN
    UPDATE TaskUpdates
    SET UpdatedById = UserId
    WHERE UpdatedById IS NULL AND UserId IS NOT NULL;
END

-- Update existing TaskUpdates records to have UpdateDate populated
UPDATE TaskUpdates
SET UpdateDate = COALESCE(CAST(CreatedAt AS DATE), CAST(GETDATE() AS DATE))
WHERE UpdateDate IS NULL;

-- Create indexes for better performance on task queries by type and self-created status
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Tasks') AND name = 'idx_tasks_self_created')
BEGIN
    CREATE INDEX idx_tasks_self_created ON Tasks(IsSelfCreated);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Tasks') AND name = 'idx_tasks_task_type')
BEGIN
    CREATE INDEX idx_tasks_task_type ON Tasks(TaskType);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Tasks') AND name = 'idx_tasks_assigned_to_self_created')
BEGIN
    CREATE INDEX idx_tasks_assigned_to_self_created ON Tasks(AssignedToId, IsSelfCreated);
END

-- Create indexes for better performance on TaskUpdates queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'idx_task_updates_update_date')
BEGIN
    CREATE INDEX idx_task_updates_update_date ON TaskUpdates(UpdateDate);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'idx_task_updates_updated_by')
BEGIN
    CREATE INDEX idx_task_updates_updated_by ON TaskUpdates(UpdatedById);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'idx_task_updates_task_date')
BEGIN
    CREATE INDEX idx_task_updates_task_date ON TaskUpdates(TaskId, UpdateDate);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('TaskUpdates') AND name = 'idx_task_updates_type')
BEGIN
    CREATE INDEX idx_task_updates_type ON TaskUpdates(UpdateType);
END

-- Create a view for manager visibility - tasks visible to managers based on employee hierarchy
IF OBJECT_ID('manager_visible_tasks', 'V') IS NOT NULL
    DROP VIEW manager_visible_tasks;
GO

CREATE VIEW manager_visible_tasks AS
SELECT DISTINCT
    t.*,
    ed_assigned.ManagerId as manager_id
FROM Tasks t
JOIN Users u_assigned ON t.AssignedToId = u_assigned.Id
LEFT JOIN EmployeeDetails ed_assigned ON u_assigned.Id = ed_assigned.UserId
WHERE
    -- Include all admin-assigned tasks (visible to org admins)
    t.IsSelfCreated = 0
    OR
    -- Include self-created tasks that should be visible to managers
    (t.IsSelfCreated = 1 AND ed_assigned.ManagerId IS NOT NULL);
GO

-- Create a view for daily updates with user information
IF OBJECT_ID('daily_task_updates', 'V') IS NOT NULL
    DROP VIEW daily_task_updates;
GO

CREATE VIEW daily_task_updates AS
SELECT
    tu.*,
    t.Title as task_title,
    t.AssignedToId as task_assigned_to,
    u.Name as updated_by_name,
    u.Email as updated_by_email,
    ed.EmployeeId as updated_by_employee_id,
    ed.Department as updated_by_department,
    ed.ManagerId as updated_by_manager_id
FROM TaskUpdates tu
JOIN Tasks t ON tu.TaskId = t.Id
JOIN Users u ON tu.UpdatedById = u.Id
LEFT JOIN EmployeeDetails ed ON u.Id = ed.UserId
WHERE tu.UpdateType = 'Daily';
GO

PRINT 'Task Management enhancement migration completed successfully';
