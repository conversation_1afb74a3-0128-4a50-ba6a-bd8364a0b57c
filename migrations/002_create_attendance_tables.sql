-- Migration 002: Create Attendance Management Tables
-- Creates tables for attendance tracking and time management
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Attendance records table
CREATE TABLE attendance_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL(4,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'half-day', 'work-from-home')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for one record per user per date
    UNIQUE(user_id, date)
);

-- Attendance policies table (organization-specific policies)
CREATE TABLE attendance_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    policy_name VARCHAR(100) NOT NULL,
    policy_type VARCHAR(50) CHECK (policy_type IN ('working_hours', 'overtime', 'break_time', 'late_policy')),
    policy_value JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Work schedules table (flexible working hours)
CREATE TABLE work_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    schedule_name VARCHAR(100) NOT NULL,
    monday_start TIME,
    monday_end TIME,
    tuesday_start TIME,
    tuesday_end TIME,
    wednesday_start TIME,
    wednesday_end TIME,
    thursday_start TIME,
    thursday_end TIME,
    friday_start TIME,
    friday_end TIME,
    saturday_start TIME,
    saturday_end TIME,
    sunday_start TIME,
    sunday_end TIME,
    is_active BOOLEAN DEFAULT true,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Overtime records table
CREATE TABLE overtime_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    regular_hours DECIMAL(4,2) DEFAULT 0,
    overtime_hours DECIMAL(4,2) DEFAULT 0,
    overtime_type VARCHAR(50) CHECK (overtime_type IN ('weekday', 'weekend', 'holiday')),
    overtime_rate DECIMAL(4,2) DEFAULT 1.5,
    approved_by UUID REFERENCES users(id),
    approval_status VARCHAR(20) DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Break records table (for detailed time tracking)
CREATE TABLE break_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    attendance_record_id UUID REFERENCES attendance_records(id) ON DELETE CASCADE,
    break_start TIMESTAMP WITH TIME ZONE NOT NULL,
    break_end TIMESTAMP WITH TIME ZONE,
    break_type VARCHAR(50) DEFAULT 'regular' CHECK (break_type IN ('regular', 'lunch', 'meeting', 'personal')),
    duration_minutes INTEGER,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Attendance summary table (for reporting and analytics)
CREATE TABLE attendance_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    year INTEGER NOT NULL,
    total_working_days INTEGER DEFAULT 0,
    present_days INTEGER DEFAULT 0,
    absent_days INTEGER DEFAULT 0,
    late_days INTEGER DEFAULT 0,
    half_days INTEGER DEFAULT 0,
    total_hours DECIMAL(6,2) DEFAULT 0,
    overtime_hours DECIMAL(6,2) DEFAULT 0,
    attendance_percentage DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for user per month/year
    UNIQUE(user_id, month, year)
);

-- Create triggers for updated_at columns
CREATE TRIGGER update_attendance_records_updated_at 
    BEFORE UPDATE ON attendance_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_policies_updated_at 
    BEFORE UPDATE ON attendance_policies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_work_schedules_updated_at 
    BEFORE UPDATE ON work_schedules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_overtime_records_updated_at 
    BEFORE UPDATE ON overtime_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_summary_updated_at 
    BEFORE UPDATE ON attendance_summary 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate total hours from check-in/check-out
CREATE OR REPLACE FUNCTION calculate_total_hours()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.check_in_time IS NOT NULL AND NEW.check_out_time IS NOT NULL THEN
        NEW.total_hours = EXTRACT(EPOCH FROM (NEW.check_out_time - NEW.check_in_time)) / 3600.0;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically calculate total hours
CREATE TRIGGER calculate_attendance_hours 
    BEFORE INSERT OR UPDATE ON attendance_records 
    FOR EACH ROW EXECUTE FUNCTION calculate_total_hours();

-- Function to update attendance summary when attendance records change
CREATE OR REPLACE FUNCTION update_attendance_summary_trigger()
RETURNS TRIGGER AS $$
DECLARE
    summary_record attendance_summary%ROWTYPE;
    working_days INTEGER;
    present_count INTEGER;
    absent_count INTEGER;
    late_count INTEGER;
    half_day_count INTEGER;
    total_hrs DECIMAL(6,2);
BEGIN
    -- Get the month and year from the attendance record
    IF TG_OP = 'DELETE' THEN
        -- Use OLD record for DELETE operations
        SELECT * INTO summary_record 
        FROM attendance_summary 
        WHERE user_id = OLD.user_id 
        AND month = EXTRACT(MONTH FROM OLD.date)
        AND year = EXTRACT(YEAR FROM OLD.date);
    ELSE
        -- Use NEW record for INSERT/UPDATE operations
        SELECT * INTO summary_record 
        FROM attendance_summary 
        WHERE user_id = NEW.user_id 
        AND month = EXTRACT(MONTH FROM NEW.date)
        AND year = EXTRACT(YEAR FROM NEW.date);
    END IF;

    -- Calculate summary statistics for the month
    IF TG_OP = 'DELETE' THEN
        SELECT 
            COUNT(*) as working_days,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN status = 'half-day' THEN 1 END) as half_day_count,
            COALESCE(SUM(total_hours), 0) as total_hrs
        INTO working_days, present_count, absent_count, late_count, half_day_count, total_hrs
        FROM attendance_records 
        WHERE user_id = OLD.user_id 
        AND EXTRACT(MONTH FROM date) = EXTRACT(MONTH FROM OLD.date)
        AND EXTRACT(YEAR FROM date) = EXTRACT(YEAR FROM OLD.date);
    ELSE
        SELECT 
            COUNT(*) as working_days,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN status = 'half-day' THEN 1 END) as half_day_count,
            COALESCE(SUM(total_hours), 0) as total_hrs
        INTO working_days, present_count, absent_count, late_count, half_day_count, total_hrs
        FROM attendance_records 
        WHERE user_id = NEW.user_id 
        AND EXTRACT(MONTH FROM date) = EXTRACT(MONTH FROM NEW.date)
        AND EXTRACT(YEAR FROM date) = EXTRACT(YEAR FROM NEW.date);
    END IF;

    -- Insert or update summary record
    IF TG_OP = 'DELETE' THEN
        INSERT INTO attendance_summary (
            user_id, month, year, total_working_days, present_days, absent_days, 
            late_days, half_days, total_hours, attendance_percentage
        ) VALUES (
            OLD.user_id, 
            EXTRACT(MONTH FROM OLD.date), 
            EXTRACT(YEAR FROM OLD.date),
            working_days, present_count, absent_count, late_count, half_day_count, total_hrs,
            CASE WHEN working_days > 0 THEN (present_count::DECIMAL / working_days * 100) ELSE 0 END
        )
        ON CONFLICT (user_id, month, year) 
        DO UPDATE SET
            total_working_days = working_days,
            present_days = present_count,
            absent_days = absent_count,
            late_days = late_count,
            half_days = half_day_count,
            total_hours = total_hrs,
            attendance_percentage = CASE WHEN working_days > 0 THEN (present_count::DECIMAL / working_days * 100) ELSE 0 END,
            updated_at = CURRENT_TIMESTAMP;
    ELSE
        INSERT INTO attendance_summary (
            user_id, month, year, total_working_days, present_days, absent_days, 
            late_days, half_days, total_hours, attendance_percentage
        ) VALUES (
            NEW.user_id, 
            EXTRACT(MONTH FROM NEW.date), 
            EXTRACT(YEAR FROM NEW.date),
            working_days, present_count, absent_count, late_count, half_day_count, total_hrs,
            CASE WHEN working_days > 0 THEN (present_count::DECIMAL / working_days * 100) ELSE 0 END
        )
        ON CONFLICT (user_id, month, year) 
        DO UPDATE SET
            total_working_days = working_days,
            present_days = present_count,
            absent_days = absent_count,
            late_days = late_count,
            half_days = half_day_count,
            total_hours = total_hrs,
            attendance_percentage = CASE WHEN working_days > 0 THEN (present_count::DECIMAL / working_days * 100) ELSE 0 END,
            updated_at = CURRENT_TIMESTAMP;
    END IF;

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update attendance summary
CREATE TRIGGER update_attendance_summary_on_change
    AFTER INSERT OR UPDATE OR DELETE ON attendance_records
    FOR EACH ROW EXECUTE FUNCTION update_attendance_summary_trigger();

-- Create comments for documentation
COMMENT ON TABLE attendance_records IS 'Daily attendance records with check-in/check-out times';
COMMENT ON TABLE attendance_policies IS 'Organization-specific attendance policies and rules';
COMMENT ON TABLE work_schedules IS 'Employee work schedules and flexible working hours';
COMMENT ON TABLE overtime_records IS 'Overtime tracking and approval records';
COMMENT ON TABLE break_records IS 'Detailed break time tracking within work hours';
COMMENT ON TABLE attendance_summary IS 'Monthly attendance summary for reporting and analytics';

COMMIT;
