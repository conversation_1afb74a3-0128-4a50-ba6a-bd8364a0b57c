-- Migration 004: Create Task Management Tables
-- Creates tables for task assignments, progress tracking, and collaboration
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Tasks table (task assignments and tracking system)
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    assigned_to UUID REFERENCES users(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'cancelled')),
    category VARCHAR(100),
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2) DEFAULT 0,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    due_date DATE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Task updates table (daily progress updates and comments on tasks)
CREATE TABLE task_updates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    update_date DATE NOT NULL,
    progress_update TEXT NOT NULL,
    progress_percentage INTEGER CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    hours_spent DECIMAL(4,2) DEFAULT 0,
    blockers TEXT,
    next_steps TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Task comments table (comments and feedback on tasks)
CREATE TABLE task_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Task attachments table (file attachments for tasks)
CREATE TABLE task_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    uploaded_by UUID REFERENCES users(id),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Task dependencies table (task dependencies and relationships)
CREATE TABLE task_dependencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    depends_on_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    dependency_type VARCHAR(50) DEFAULT 'finish_to_start' CHECK (dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Prevent self-dependency
    CHECK (task_id != depends_on_task_id),
    
    -- Unique constraint for task dependency pair
    UNIQUE(task_id, depends_on_task_id)
);

-- Task categories table (predefined task categories)
CREATE TABLE task_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color_code VARCHAR(7), -- Hex color code
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for category name per organization
    UNIQUE(organization_id, name)
);

-- Task time tracking table (detailed time tracking for tasks)
CREATE TABLE task_time_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Task templates table (reusable task templates)
CREATE TABLE task_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    estimated_hours DECIMAL(5,2),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    template_data JSONB, -- Store template structure
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at columns
CREATE TRIGGER update_tasks_updated_at 
    BEFORE UPDATE ON tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_templates_updated_at 
    BEFORE UPDATE ON task_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update task progress and actual hours
CREATE OR REPLACE FUNCTION update_task_progress()
RETURNS TRIGGER AS $$
BEGIN
    -- Update task progress and actual hours when task update is added
    UPDATE tasks 
    SET 
        progress_percentage = NEW.progress_percentage,
        actual_hours = actual_hours + NEW.hours_spent,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.task_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update task progress
CREATE TRIGGER update_task_progress_trigger
    AFTER INSERT ON task_updates
    FOR EACH ROW EXECUTE FUNCTION update_task_progress();

-- Function to automatically set completed_at when task is completed
CREATE OR REPLACE FUNCTION set_task_completion_time()
RETURNS TRIGGER AS $$
BEGIN
    -- Set completed_at when status changes to completed
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        NEW.completed_at = CURRENT_TIMESTAMP;
        NEW.progress_percentage = 100;
    END IF;
    
    -- Clear completed_at if status changes from completed to something else
    IF OLD.status = 'completed' AND NEW.status != 'completed' THEN
        NEW.completed_at = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to set completion time
CREATE TRIGGER set_task_completion_time_trigger
    BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION set_task_completion_time();

-- Function to calculate time tracking duration
CREATE OR REPLACE FUNCTION calculate_time_tracking_duration()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.end_time IS NOT NULL AND NEW.start_time IS NOT NULL THEN
        NEW.duration_minutes = EXTRACT(EPOCH FROM (NEW.end_time - NEW.start_time)) / 60;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to calculate duration
CREATE TRIGGER calculate_time_tracking_duration_trigger
    BEFORE INSERT OR UPDATE ON task_time_tracking
    FOR EACH ROW EXECUTE FUNCTION calculate_time_tracking_duration();

-- Function to validate task dependencies (prevent circular dependencies)
CREATE OR REPLACE FUNCTION validate_task_dependency()
RETURNS TRIGGER AS $$
DECLARE
    circular_dependency BOOLEAN := false;
BEGIN
    -- Check for circular dependencies using recursive CTE
    WITH RECURSIVE dependency_chain AS (
        -- Base case: direct dependency
        SELECT task_id, depends_on_task_id, 1 as depth
        FROM task_dependencies
        WHERE task_id = NEW.depends_on_task_id
        
        UNION ALL
        
        -- Recursive case: follow the chain
        SELECT dc.task_id, td.depends_on_task_id, dc.depth + 1
        FROM dependency_chain dc
        JOIN task_dependencies td ON dc.depends_on_task_id = td.task_id
        WHERE dc.depth < 10 -- Prevent infinite recursion
    )
    SELECT EXISTS(
        SELECT 1 FROM dependency_chain 
        WHERE depends_on_task_id = NEW.task_id
    ) INTO circular_dependency;
    
    IF circular_dependency THEN
        RAISE EXCEPTION 'Circular dependency detected. Cannot create this dependency.';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to validate dependencies
CREATE TRIGGER validate_task_dependency_trigger
    BEFORE INSERT ON task_dependencies
    FOR EACH ROW EXECUTE FUNCTION validate_task_dependency();

-- Function to create task from template
CREATE OR REPLACE FUNCTION create_task_from_template(
    p_template_id UUID,
    p_assigned_to UUID,
    p_assigned_by UUID,
    p_due_date DATE DEFAULT NULL,
    p_custom_title VARCHAR(255) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    template_record task_templates%ROWTYPE;
    new_task_id UUID;
BEGIN
    -- Get template data
    SELECT * INTO template_record
    FROM task_templates
    WHERE id = p_template_id AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Template not found or inactive';
    END IF;
    
    -- Create new task from template
    INSERT INTO tasks (
        organization_id, title, description, assigned_to, assigned_by,
        priority, category, estimated_hours, due_date
    ) VALUES (
        template_record.organization_id,
        COALESCE(p_custom_title, template_record.name),
        template_record.description,
        p_assigned_to,
        p_assigned_by,
        template_record.priority,
        template_record.category,
        template_record.estimated_hours,
        p_due_date
    )
    RETURNING id INTO new_task_id;
    
    RETURN new_task_id;
END;
$$ LANGUAGE plpgsql;

-- Create comments for documentation
COMMENT ON TABLE tasks IS 'Task assignments and tracking system';
COMMENT ON TABLE task_updates IS 'Daily progress updates and comments on tasks';
COMMENT ON TABLE task_comments IS 'Comments and feedback on tasks';
COMMENT ON TABLE task_attachments IS 'File attachments for tasks';
COMMENT ON TABLE task_dependencies IS 'Task dependencies and relationships';
COMMENT ON TABLE task_categories IS 'Predefined task categories for organization';
COMMENT ON TABLE task_time_tracking IS 'Detailed time tracking for tasks';
COMMENT ON TABLE task_templates IS 'Reusable task templates';

COMMIT;
