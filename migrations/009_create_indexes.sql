-- Migration 009: Create Database Indexes
-- Creates indexes for performance optimization across all tables
-- Schema: {SCHEMA_NAME}
-- Organization ID: {ORG_ID}
-- Created: {CREATED_DATE}

-- Set search path to the organization schema
SET search_path TO {SCHEMA_NAME}, public;

-- Core Tables Indexes
CREATE INDEX idx_organizations_domain ON organizations(domain);
CREATE INDEX idx_organizations_status ON organizations(status);
CREATE INDEX idx_organizations_industry ON organizations(industry);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

CREATE INDEX idx_employee_details_user ON employee_details(user_id);
CREATE INDEX idx_employee_details_employee_id ON employee_details(employee_id);
CREATE INDEX idx_employee_details_department ON employee_details(department);
CREATE INDEX idx_employee_details_manager ON employee_details(manager_id);
CREATE INDEX idx_employee_details_join_date ON employee_details(join_date);
CREATE INDEX idx_employee_details_status ON employee_details(employment_status);

CREATE INDEX idx_employee_education_employee ON employee_education(employee_id);
CREATE INDEX idx_employee_skills_employee ON employee_skills(employee_id);
CREATE INDEX idx_employee_skills_name ON employee_skills(skill_name);

-- Attendance Tables Indexes
CREATE INDEX idx_attendance_user_date ON attendance_records(user_id, date);
CREATE INDEX idx_attendance_organization_date ON attendance_records(organization_id, date);
CREATE INDEX idx_attendance_status ON attendance_records(status);
CREATE INDEX idx_attendance_check_in ON attendance_records(check_in_time);

CREATE INDEX idx_attendance_policies_org ON attendance_policies(organization_id);
CREATE INDEX idx_attendance_policies_active ON attendance_policies(is_active);

CREATE INDEX idx_work_schedules_user ON work_schedules(user_id);
CREATE INDEX idx_work_schedules_active ON work_schedules(is_active);

CREATE INDEX idx_overtime_records_user_date ON overtime_records(user_id, date);
CREATE INDEX idx_overtime_records_approval ON overtime_records(approval_status);

CREATE INDEX idx_break_records_attendance ON break_records(attendance_record_id);

CREATE INDEX idx_attendance_summary_user_period ON attendance_summary(user_id, year, month);

-- Leave Management Indexes
CREATE INDEX idx_leave_types_organization ON leave_types(organization_id);
CREATE INDEX idx_leave_types_active ON leave_types(is_active);
CREATE INDEX idx_leave_types_code ON leave_types(code);

CREATE INDEX idx_leave_balances_user_year ON leave_balances(user_id, year);
CREATE INDEX idx_leave_balances_leave_type ON leave_balances(leave_type_id);

CREATE INDEX idx_leave_requests_user ON leave_requests(user_id);
CREATE INDEX idx_leave_requests_status ON leave_requests(status);
CREATE INDEX idx_leave_requests_dates ON leave_requests(from_date, to_date);
CREATE INDEX idx_leave_requests_approver ON leave_requests(approved_by);
CREATE INDEX idx_leave_requests_leave_type ON leave_requests(leave_type_id);

CREATE INDEX idx_leave_approval_workflow_request ON leave_approval_workflow(leave_request_id);
CREATE INDEX idx_leave_approval_workflow_approver ON leave_approval_workflow(approver_id);

CREATE INDEX idx_leave_policies_org_type ON leave_policies(organization_id, leave_type_id);
CREATE INDEX idx_leave_policies_active ON leave_policies(is_active);

CREATE INDEX idx_leave_calendar_org_date ON leave_calendar(organization_id, date);
CREATE INDEX idx_leave_calendar_event_type ON leave_calendar(event_type);

CREATE INDEX idx_leave_encashment_user_year ON leave_encashment(user_id, year);
CREATE INDEX idx_leave_encashment_status ON leave_encashment(status);

-- Task Management Indexes
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_assigned_by ON tasks(assigned_by);
CREATE INDEX idx_tasks_organization ON tasks(organization_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_category ON tasks(category);

CREATE INDEX idx_task_updates_task ON task_updates(task_id);
CREATE INDEX idx_task_updates_user ON task_updates(user_id);
CREATE INDEX idx_task_updates_date ON task_updates(update_date);

CREATE INDEX idx_task_comments_task ON task_comments(task_id);
CREATE INDEX idx_task_comments_user ON task_comments(user_id);

CREATE INDEX idx_task_attachments_task ON task_attachments(task_id);
CREATE INDEX idx_task_attachments_uploaded_by ON task_attachments(uploaded_by);

CREATE INDEX idx_task_dependencies_task ON task_dependencies(task_id);
CREATE INDEX idx_task_dependencies_depends_on ON task_dependencies(depends_on_task_id);

CREATE INDEX idx_task_categories_org ON task_categories(organization_id);
CREATE INDEX idx_task_categories_active ON task_categories(is_active);

CREATE INDEX idx_task_time_tracking_task ON task_time_tracking(task_id);
CREATE INDEX idx_task_time_tracking_user ON task_time_tracking(user_id);
CREATE INDEX idx_task_time_tracking_start ON task_time_tracking(start_time);

CREATE INDEX idx_task_templates_org ON task_templates(organization_id);
CREATE INDEX idx_task_templates_active ON task_templates(is_active);

-- Performance Management Indexes
CREATE INDEX idx_performance_reviews_employee ON performance_reviews(employee_id);
CREATE INDEX idx_performance_reviews_reviewer ON performance_reviews(reviewer_id);
CREATE INDEX idx_performance_reviews_period ON performance_reviews(review_period);
CREATE INDEX idx_performance_reviews_status ON performance_reviews(status);
CREATE INDEX idx_performance_reviews_date ON performance_reviews(review_date);

CREATE INDEX idx_performance_goals_employee ON performance_goals(employee_id);
CREATE INDEX idx_performance_goals_review ON performance_goals(review_id);
CREATE INDEX idx_performance_goals_status ON performance_goals(status);

CREATE INDEX idx_performance_metrics_org ON performance_metrics(organization_id);
CREATE INDEX idx_performance_metrics_active ON performance_metrics(is_active);

CREATE INDEX idx_performance_ratings_review ON performance_ratings(review_id);
CREATE INDEX idx_performance_ratings_metric ON performance_ratings(metric_id);

-- Payroll Management Indexes
CREATE INDEX idx_payroll_cycles_organization ON payroll_cycles(organization_id);
CREATE INDEX idx_payroll_cycles_period ON payroll_cycles(pay_period_start, pay_period_end);
CREATE INDEX idx_payroll_cycles_status ON payroll_cycles(status);
CREATE INDEX idx_payroll_cycles_pay_date ON payroll_cycles(pay_date);

CREATE INDEX idx_employee_payroll_cycle ON employee_payroll(payroll_cycle_id);
CREATE INDEX idx_employee_payroll_employee ON employee_payroll(employee_id);
CREATE INDEX idx_employee_payroll_status ON employee_payroll(status);

CREATE INDEX idx_employee_benefits_employee ON employee_benefits(employee_id);
CREATE INDEX idx_employee_benefits_type ON employee_benefits(benefit_type);
CREATE INDEX idx_employee_benefits_status ON employee_benefits(status);

CREATE INDEX idx_salary_components_org ON salary_components(organization_id);
CREATE INDEX idx_salary_components_type ON salary_components(component_type);
CREATE INDEX idx_salary_components_active ON salary_components(is_active);

CREATE INDEX idx_employee_salary_structure_employee ON employee_salary_structure(employee_id);
CREATE INDEX idx_employee_salary_structure_component ON employee_salary_structure(component_id);
CREATE INDEX idx_employee_salary_structure_effective ON employee_salary_structure(effective_from, effective_to);

-- Recruitment Management Indexes
CREATE INDEX idx_job_postings_organization ON job_postings(organization_id);
CREATE INDEX idx_job_postings_department ON job_postings(department);
CREATE INDEX idx_job_postings_status ON job_postings(status);
CREATE INDEX idx_job_postings_posted_by ON job_postings(posted_by);
CREATE INDEX idx_job_postings_posted_date ON job_postings(posted_date);

CREATE INDEX idx_job_applications_posting ON job_applications(job_posting_id);
CREATE INDEX idx_job_applications_email ON job_applications(candidate_email);
CREATE INDEX idx_job_applications_status ON job_applications(status);
CREATE INDEX idx_job_applications_applied_date ON job_applications(applied_date);

CREATE INDEX idx_interview_schedules_application ON interview_schedules(application_id);
CREATE INDEX idx_interview_schedules_interviewer ON interview_schedules(interviewer_id);
CREATE INDEX idx_interview_schedules_date ON interview_schedules(scheduled_date);
CREATE INDEX idx_interview_schedules_status ON interview_schedules(status);

CREATE INDEX idx_candidate_assessments_application ON candidate_assessments(application_id);
CREATE INDEX idx_candidate_assessments_type ON candidate_assessments(assessment_type);
CREATE INDEX idx_candidate_assessments_status ON candidate_assessments(status);

-- System Tables Indexes
CREATE INDEX idx_organization_settings_org ON organization_settings(organization_id);
CREATE INDEX idx_organization_settings_key ON organization_settings(setting_key);

CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_organization ON audit_logs(organization_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

CREATE INDEX idx_subscription_plans_active ON subscription_plans(is_active);
CREATE INDEX idx_subscription_plans_name ON subscription_plans(name);

CREATE INDEX idx_org_subscriptions_organization ON organization_subscriptions(organization_id);
CREATE INDEX idx_org_subscriptions_plan ON organization_subscriptions(plan_id);
CREATE INDEX idx_org_subscriptions_status ON organization_subscriptions(status);
CREATE INDEX idx_org_subscriptions_billing_date ON organization_subscriptions(next_billing_date);

CREATE INDEX idx_billing_invoices_organization ON billing_invoices(organization_id);
CREATE INDEX idx_billing_invoices_subscription ON billing_invoices(subscription_id);
CREATE INDEX idx_billing_invoices_status ON billing_invoices(status);
CREATE INDEX idx_billing_invoices_due_date ON billing_invoices(due_date);
CREATE INDEX idx_billing_invoices_number ON billing_invoices(invoice_number);

CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

CREATE INDEX idx_file_uploads_organization ON file_uploads(organization_id);
CREATE INDEX idx_file_uploads_uploaded_by ON file_uploads(uploaded_by);
CREATE INDEX idx_file_uploads_purpose ON file_uploads(purpose);
CREATE INDEX idx_file_uploads_public ON file_uploads(is_public);

-- Composite indexes for common query patterns
CREATE INDEX idx_users_org_role_active ON users(organization_id, role, is_active);
CREATE INDEX idx_attendance_user_month ON attendance_records(user_id, EXTRACT(YEAR FROM date), EXTRACT(MONTH FROM date));
CREATE INDEX idx_leave_requests_user_status_dates ON leave_requests(user_id, status, from_date, to_date);
CREATE INDEX idx_tasks_assignee_status_priority ON tasks(assigned_to, status, priority);
CREATE INDEX idx_payroll_employee_cycle_status ON employee_payroll(employee_id, payroll_cycle_id, status);

-- Partial indexes for better performance on filtered queries
CREATE INDEX idx_active_users ON users(id) WHERE is_active = true;
CREATE INDEX idx_pending_leave_requests ON leave_requests(id) WHERE status = 'pending';
CREATE INDEX idx_active_tasks ON tasks(id) WHERE status IN ('pending', 'in-progress');
CREATE INDEX idx_unpaid_invoices ON billing_invoices(id) WHERE status IN ('pending', 'overdue');

COMMIT;
