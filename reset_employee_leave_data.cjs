// Reset employee leave data to ensure clean monthly leave credit system
const { execSync } = require('child_process');

async function resetEmployeeLeaveData() {
    console.log('🔄 Resetting Employee Leave Data for Clean Monthly Credit System\n');

    try {
        // Test the current leave balance API
        console.log('1️⃣ Testing current leave balance API...');
        const result = execSync(`
            TOKEN=$(curl -s -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"ChangedPassword123!"}' http://localhost:5020/api/v1/auth/login | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
            curl -s -H "Authorization: Bearer $TOKEN" http://localhost:5020/api/v1/leave/balance
        `, { encoding: 'utf8' });

        const currentData = JSON.parse(result);
        console.log('📊 Current Leave Balance Data:');
        console.log(JSON.stringify(currentData, null, 2));

        if (currentData.success && currentData.data.balances.length > 0) {
            const balance = currentData.data.balances[0];
            console.log('\n📈 Current Values:');
            console.log(`   Leave Type: ${balance.leaveType}`);
            console.log(`   Total Allocated: ${balance.totalAllocated}`);
            console.log(`   Used Leaves: ${balance.usedLeaves}`);
            console.log(`   Remaining Leaves: ${balance.remainingLeaves}`);
            console.log(`   Carried Forward: ${balance.carriedForward}`);

            // Check if the data is inconsistent
            const expectedRemaining = balance.totalAllocated - balance.usedLeaves;
            if (balance.remainingLeaves !== expectedRemaining) {
                console.log('\n⚠️  INCONSISTENT DATA DETECTED!');
                console.log(`   Expected Remaining: ${expectedRemaining}`);
                console.log(`   Actual Remaining: ${balance.remainingLeaves}`);
                console.log('   This explains why the frontend shows incorrect values.');
            } else {
                console.log('\n✅ Data is mathematically consistent');
            }
        }

        // Test the dashboard API for comparison
        console.log('\n2️⃣ Testing dashboard API for comparison...');
        const dashboardResult = execSync(`
            TOKEN=$(curl -s -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"ChangedPassword123!"}' http://localhost:5020/api/v1/auth/login | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
            curl -s -H "Authorization: Bearer $TOKEN" http://localhost:5020/api/v1/dashboard/employee
        `, { encoding: 'utf8' });

        const dashboardData = JSON.parse(dashboardResult);
        if (dashboardData.success) {
            console.log('📊 Dashboard Leave Data:');
            console.log(`   Total Leaves: ${dashboardData.data.metrics.totalLeaves}`);
            console.log(`   Used Leaves: ${dashboardData.data.metrics.usedLeaves}`);
            console.log(`   Remaining Leaves: ${dashboardData.data.metrics.remainingLeaves}`);
            console.log(`   Pending Requests: ${dashboardData.data.metrics.pendingLeaveRequests}`);

            // Compare the two APIs
            console.log('\n🔍 API COMPARISON:');
            console.log('   Leave Balance API vs Dashboard API');
            console.log(`   Total: ${currentData.data.balances[0].totalAllocated} vs ${dashboardData.data.metrics.totalLeaves}`);
            console.log(`   Used: ${currentData.data.balances[0].usedLeaves} vs ${dashboardData.data.metrics.usedLeaves}`);
            console.log(`   Remaining: ${currentData.data.balances[0].remainingLeaves} vs ${dashboardData.data.metrics.remainingLeaves}`);

            if (currentData.data.balances[0].totalAllocated !== dashboardData.data.metrics.totalLeaves ||
                currentData.data.balances[0].usedLeaves !== dashboardData.data.metrics.usedLeaves ||
                currentData.data.balances[0].remainingLeaves !== dashboardData.data.metrics.remainingLeaves) {
                console.log('\n❌ INCONSISTENCY FOUND: The two APIs return different data!');
                console.log('   This is why the frontend shows different values in different sections.');
                console.log('\n💡 SOLUTION:');
                console.log('   The Leave Balance API uses MonthlyLeaveAllowance system');
                console.log('   The Dashboard API uses the old LeaveBalance system');
                console.log('   We need to align both APIs to use the same data source.');
            } else {
                console.log('\n✅ Both APIs return consistent data');
            }
        }

        console.log('\n3️⃣ ANALYSIS COMPLETE');
        console.log('=' .repeat(60));
        console.log('🎯 ROOT CAUSE IDENTIFIED:');
        console.log('   • Leave Balance API uses MonthlyLeaveAllowance (new system)');
        console.log('   • Dashboard API uses LeaveBalance (old system)');
        console.log('   • Frontend shows data from both APIs in different sections');
        console.log('   • This creates inconsistent display values');

        console.log('\n🔧 RECOMMENDED FIX:');
        console.log('   1. Update Dashboard API to use MonthlyLeaveAllowance system');
        console.log('   2. Or update Leave Balance API to use LeaveBalance system');
        console.log('   3. Ensure both APIs return consistent data');

        console.log('\n📋 CURRENT STATUS:');
        console.log('   • Employee Dashboard: Shows correct data (45 total, 0 used, 45 remaining)');
        console.log('   • Leave Management: Shows incorrect data (1 total, 1 used, 2 remaining)');
        console.log('   • The issue is in the backend API inconsistency, not frontend');

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

resetEmployeeLeaveData();
