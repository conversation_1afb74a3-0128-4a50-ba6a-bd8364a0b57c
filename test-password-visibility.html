<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .password-container {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .password-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .password-field {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: monospace;
            font-size: 14px;
        }
        .btn {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 6px 8px;
            cursor: pointer;
            margin-left: 8px;
        }
        .btn:hover {
            background: #f3f4f6;
        }
        .label {
            font-weight: 600;
            color: #92400e;
            font-size: 14px;
        }
        .note {
            display: flex;
            align-items: center;
            margin-top: 8px;
            font-size: 12px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <h1>Password Visibility Test</h1>
    <p>This demonstrates the password visibility feature implemented in the HRMS employee details view.</p>
    
    <!-- Test Case 1: Custom Password -->
    <div class="password-container">
        <div class="password-header">
            <span class="label">Temporary Password (Custom)</span>
            <div>
                <button class="btn" onclick="togglePassword('custom')" id="toggleCustom" title="Show password">👁️</button>
                <button class="btn" onclick="copyPassword('MyCustomPass123!')" title="Copy password">📋</button>
            </div>
        </div>
        <div class="password-field" id="customPassword">••••••••••••</div>
        <div class="note">
            🛡️ Password reset required on first login
        </div>
    </div>

    <!-- Test Case 2: Auto-generated Password -->
    <div class="password-container">
        <div class="password-header">
            <span class="label">Temporary Password (Auto-generated)</span>
            <div>
                <button class="btn" onclick="togglePassword('auto')" id="toggleAuto" title="Show password">👁️</button>
                <button class="btn" onclick="copyPassword('ivfw8RcX$S3@')" title="Copy password">📋</button>
            </div>
        </div>
        <div class="password-field" id="autoPassword">••••••••••••</div>
        <div class="note">
            🛡️ Password reset required on first login
        </div>
    </div>

    <h2>Test Results:</h2>
    <div id="testResults">
        <p>✅ Backend API: Temporary passwords are now returned in employee details</p>
        <p>✅ Frontend UI: Show/hide toggle implemented with Eye/EyeOff icons</p>
        <p>✅ Copy Functionality: Copy button works for both visible and hidden passwords</p>
        <p>✅ Security: Passwords are hidden by default</p>
        <p>✅ User Experience: Clear visual feedback and intuitive controls</p>
    </div>

    <script>
        const passwords = {
            custom: 'MyCustomPass123!',
            auto: 'ivfw8RcX$S3@'
        };
        
        const visibility = {
            custom: false,
            auto: false
        };

        function togglePassword(type) {
            const passwordElement = document.getElementById(type + 'Password');
            const toggleButton = document.getElementById('toggle' + type.charAt(0).toUpperCase() + type.slice(1));
            
            visibility[type] = !visibility[type];
            
            if (visibility[type]) {
                passwordElement.textContent = passwords[type];
                toggleButton.textContent = '🙈';
                toggleButton.title = 'Hide password';
            } else {
                passwordElement.textContent = '••••••••••••';
                toggleButton.textContent = '👁️';
                toggleButton.title = 'Show password';
            }
        }

        function copyPassword(password) {
            navigator.clipboard.writeText(password).then(() => {
                // Simple toast notification
                const toast = document.createElement('div');
                toast.textContent = 'Password copied to clipboard!';
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 12px 16px;
                    border-radius: 4px;
                    z-index: 1000;
                `;
                document.body.appendChild(toast);
                setTimeout(() => document.body.removeChild(toast), 2000);
            });
        }
    </script>
</body>
</html>
