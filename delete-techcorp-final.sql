-- Final script to completely delete TechCorp Solutions organization and all associated data
-- This script uses the correct table names from the database

USE dbHRMS;
GO

-- Start transaction for atomic operation
BEGIN TRANSACTION;

DECLARE @OrganizationId UNIQUEIDENTIFIER = '67DD72CC-5D99-4065-BA4A-0F2EE36443AE';
DECLARE @OrganizationName NVARCHAR(255) = 'TechCorp Solutions';

-- Verify the organization exists
IF NOT EXISTS (SELECT 1 FROM Organizations WHERE Id = @OrganizationId AND Name = @OrganizationName)
BEGIN
    PRINT 'Organization "' + @OrganizationName + '" with specified ID not found.';
    ROLL<PERSON>CK TRANSACTION;
    RETURN;
END

PRINT 'Found organization: ' + @OrganizationName + ' (ID: ' + CAST(@OrganizationId AS NVARCHAR(50)) + ')';

-- Get all users in this organization
DECLARE @UserIds TABLE (UserId UNIQUEIDENTIFIER);
INSERT INTO @UserIds (UserId)
SELECT Id FROM Users WHERE OrganizationId = @OrganizationId;

DECLARE @UserCount INT = (SELECT COUNT(*) FROM @UserIds);
PRINT 'Found ' + CAST(@UserCount AS NVARCHAR(10)) + ' users in this organization';

-- Display users to be deleted
SELECT 'Users to be deleted:' AS Info, Name, Email, Role FROM Users WHERE OrganizationId = @OrganizationId;

PRINT 'Deleting organization-specific data...';

-- Delete attendance records for users in this organization
DECLARE @AttendanceCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'AttendanceRecords')
BEGIN
    SELECT @AttendanceCount = COUNT(*) FROM AttendanceRecords WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM AttendanceRecords WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@AttendanceCount AS NVARCHAR(10)) + ' attendance records';
END

-- Delete leave requests for users in this organization
DECLARE @LeaveRequestCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'LeaveRequests')
BEGIN
    SELECT @LeaveRequestCount = COUNT(*) FROM LeaveRequests WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM LeaveRequests WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@LeaveRequestCount AS NVARCHAR(10)) + ' leave requests';
END

-- Delete leave balances for users in this organization
DECLARE @LeaveBalanceCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'LeaveBalances')
BEGIN
    SELECT @LeaveBalanceCount = COUNT(*) FROM LeaveBalances WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM LeaveBalances WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@LeaveBalanceCount AS NVARCHAR(10)) + ' leave balances';
END

-- Delete monthly leave allowances for users in this organization
DECLARE @MonthlyAllowanceCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'MonthlyLeaveAllowances')
BEGIN
    SELECT @MonthlyAllowanceCount = COUNT(*) FROM MonthlyLeaveAllowances WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM MonthlyLeaveAllowances WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@MonthlyAllowanceCount AS NVARCHAR(10)) + ' monthly leave allowances';
END

-- Delete monthly leave usage for users in this organization (via MonthlyLeaveAllowances)
DECLARE @MonthlyUsageCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'MonthlyLeaveUsages')
BEGIN
    SELECT @MonthlyUsageCount = COUNT(*) 
    FROM MonthlyLeaveUsages mlu
    INNER JOIN MonthlyLeaveAllowances mla ON mlu.MonthlyLeaveAllowanceId = mla.Id
    WHERE mla.UserId IN (SELECT UserId FROM @UserIds);

    DELETE mlu
    FROM MonthlyLeaveUsages mlu
    INNER JOIN MonthlyLeaveAllowances mla ON mlu.MonthlyLeaveAllowanceId = mla.Id
    WHERE mla.UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@MonthlyUsageCount AS NVARCHAR(10)) + ' monthly leave usage records';
END

-- Delete task comments first (foreign key dependency)
DECLARE @TaskCommentCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'TaskComments')
BEGIN
    SELECT @TaskCommentCount = COUNT(*) FROM TaskComments tc
    INNER JOIN Tasks t ON tc.TaskId = t.Id
    WHERE t.AssignedToId IN (SELECT UserId FROM @UserIds) OR t.CreatedBy IN (SELECT UserId FROM @UserIds);
    
    DELETE tc FROM TaskComments tc
    INNER JOIN Tasks t ON tc.TaskId = t.Id
    WHERE t.AssignedToId IN (SELECT UserId FROM @UserIds) OR t.CreatedBy IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@TaskCommentCount AS NVARCHAR(10)) + ' task comments';
END

-- Delete task updates (foreign key dependency)
DECLARE @TaskUpdateCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'TaskUpdates')
BEGIN
    SELECT @TaskUpdateCount = COUNT(*) FROM TaskUpdates WHERE UpdatedBy IN (SELECT UserId FROM @UserIds);
    DELETE FROM TaskUpdates WHERE UpdatedBy IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@TaskUpdateCount AS NVARCHAR(10)) + ' task updates';
END

-- Delete tasks
DECLARE @TaskCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Tasks')
BEGIN
    SELECT @TaskCount = COUNT(*) FROM Tasks WHERE AssignedToId IN (SELECT UserId FROM @UserIds) OR CreatedBy IN (SELECT UserId FROM @UserIds);
    DELETE FROM Tasks WHERE AssignedToId IN (SELECT UserId FROM @UserIds) OR CreatedBy IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@TaskCount AS NVARCHAR(10)) + ' tasks';
END

-- Delete performance goals
DECLARE @PerformanceGoalCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PerformanceGoals')
BEGIN
    SELECT @PerformanceGoalCount = COUNT(*) FROM PerformanceGoals WHERE EmployeeId IN (SELECT UserId FROM @UserIds);
    DELETE FROM PerformanceGoals WHERE EmployeeId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@PerformanceGoalCount AS NVARCHAR(10)) + ' performance goals';
END

-- Delete performance reviews
DECLARE @PerformanceCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PerformanceReviews')
BEGIN
    SELECT @PerformanceCount = COUNT(*) FROM PerformanceReviews WHERE EmployeeId IN (SELECT UserId FROM @UserIds) OR ReviewerId IN (SELECT UserId FROM @UserIds);
    DELETE FROM PerformanceReviews WHERE EmployeeId IN (SELECT UserId FROM @UserIds) OR ReviewerId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@PerformanceCount AS NVARCHAR(10)) + ' performance reviews';
END

-- Delete employee education
DECLARE @EmployeeEducationCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EmployeeEducation')
BEGIN
    SELECT @EmployeeEducationCount = COUNT(*) FROM EmployeeEducation WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM EmployeeEducation WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@EmployeeEducationCount AS NVARCHAR(10)) + ' employee education records';
END

-- Delete employee skills
DECLARE @EmployeeSkillCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EmployeeSkills')
BEGIN
    SELECT @EmployeeSkillCount = COUNT(*) FROM EmployeeSkills WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM EmployeeSkills WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@EmployeeSkillCount AS NVARCHAR(10)) + ' employee skill records';
END

-- Delete employee details
DECLARE @EmployeeDetailCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EmployeeDetails')
BEGIN
    SELECT @EmployeeDetailCount = COUNT(*) FROM EmployeeDetails WHERE UserId IN (SELECT UserId FROM @UserIds);
    DELETE FROM EmployeeDetails WHERE UserId IN (SELECT UserId FROM @UserIds);
    PRINT 'Deleted ' + CAST(@EmployeeDetailCount AS NVARCHAR(10)) + ' employee detail records';
END

-- Delete job applications for this organization
DECLARE @JobApplicationCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'JobApplications')
BEGIN
    SELECT @JobApplicationCount = COUNT(*) FROM JobApplications ja
    INNER JOIN JobPostings jp ON ja.JobPostingId = jp.Id
    WHERE jp.OrganizationId = @OrganizationId;
    
    DELETE ja FROM JobApplications ja
    INNER JOIN JobPostings jp ON ja.JobPostingId = jp.Id
    WHERE jp.OrganizationId = @OrganizationId;
    PRINT 'Deleted ' + CAST(@JobApplicationCount AS NVARCHAR(10)) + ' job applications';
END

-- Delete job postings for this organization
DECLARE @JobPostingCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'JobPostings')
BEGIN
    SELECT @JobPostingCount = COUNT(*) FROM JobPostings WHERE OrganizationId = @OrganizationId;
    DELETE FROM JobPostings WHERE OrganizationId = @OrganizationId;
    PRINT 'Deleted ' + CAST(@JobPostingCount AS NVARCHAR(10)) + ' job postings';
END

-- Delete organization settings
DECLARE @OrgSettingCount INT = 0;
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'OrganizationSettings')
BEGIN
    SELECT @OrgSettingCount = COUNT(*) FROM OrganizationSettings WHERE OrganizationId = @OrganizationId;
    DELETE FROM OrganizationSettings WHERE OrganizationId = @OrganizationId;
    PRINT 'Deleted ' + CAST(@OrgSettingCount AS NVARCHAR(10)) + ' organization settings';
END

-- Delete leave types for this organization
DECLARE @LeaveTypeCount INT = 0;
SELECT @LeaveTypeCount = COUNT(*) FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
DELETE FROM LeaveTypes WHERE OrganizationId = @OrganizationId;
PRINT 'Deleted ' + CAST(@LeaveTypeCount AS NVARCHAR(10)) + ' organization leave types';

-- Delete users from this organization
DELETE FROM Users WHERE OrganizationId = @OrganizationId;
PRINT 'Deleted ' + CAST(@UserCount AS NVARCHAR(10)) + ' users';

-- Delete the organization itself
DELETE FROM Organizations WHERE Id = @OrganizationId;
PRINT 'Deleted organization: ' + @OrganizationName;

-- Commit the transaction
COMMIT TRANSACTION;

PRINT '';
PRINT 'SUCCESS: Organization "' + @OrganizationName + '" completely deleted!';
PRINT '================================================================';

-- Verify deletion
IF NOT EXISTS (SELECT 1 FROM Organizations WHERE Id = @OrganizationId)
BEGIN
    PRINT 'VERIFICATION PASSED: Organization successfully removed from database';
    PRINT 'TechCorp Solutions and all its data has been completely deleted.';
END
ELSE
BEGIN
    PRINT 'ERROR: Organization still exists in database - deletion may have failed';
END

-- Show remaining organizations
PRINT '';
PRINT 'Remaining organizations in database:';
SELECT Name, Domain, EmployeeCount, Status FROM Organizations ORDER BY Name;
