// Test script to verify employee authorization fixes
const API_BASE = 'http://localhost:5020/api/v1';

async function testEmployeeAuth() {
    console.log('🧪 Testing Employee Authorization Fixes...\n');

    try {
        // Step 1: Login as Organization Admin to create an employee
        console.log('1️⃣ Logging in as Organization Admin...');
        const adminLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'Admin123!'
            })
        });

        if (!adminLoginResponse.ok) {
            throw new Error(`Admin login failed: ${adminLoginResponse.status}`);
        }

        const adminLoginData = await adminLoginResponse.json();
        const adminToken = adminLoginData.data.token;
        const organizationId = adminLoginData.data.user.organization?.id;
        
        console.log(`✅ Admin login successful. Organization ID: ${organizationId}`);

        // Step 2: Create a test employee
        console.log('\n2️⃣ Creating test employee...');
        const createEmployeeResponse = await fetch(`${API_BASE}/employee-management/employees`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`,
                'X-Organization-ID': organizationId
            },
            body: JSON.stringify({
                name: 'Test Employee',
                email: '<EMAIL>',
                role: 'Employee',
                employeeDetails: {
                    employeeId: 'EMP001',
                    jobTitle: 'Software Developer',
                    department: 'Engineering',
                    joinDate: new Date().toISOString(),
                    employmentType: 'full-time',
                    baseSalary: 75000,
                    annualCTC: 90000
                }
            })
        });

        let employeeCredentials = null;
        if (createEmployeeResponse.ok) {
            const createEmployeeData = await createEmployeeResponse.json();
            employeeCredentials = {
                email: createEmployeeData.data.email,
                password: createEmployeeData.data.loginTemporaryPassword
            };
            console.log(`✅ Employee created successfully. Email: ${employeeCredentials.email}, Password: ${employeeCredentials.password}`);
        } else {
            // Employee might already exist, try with existing credentials
            console.log('⚠️ Employee creation failed (might already exist), trying with existing credentials...');
            employeeCredentials = {
                email: '<EMAIL>',
                password: 'TempPass123!' // Assuming a known password
            };
        }

        // Step 3: Login as Employee
        console.log('\n3️⃣ Logging in as Employee...');
        const employeeLoginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(employeeCredentials)
        });

        if (!employeeLoginResponse.ok) {
            throw new Error(`Employee login failed: ${employeeLoginResponse.status} - ${await employeeLoginResponse.text()}`);
        }

        const employeeLoginData = await employeeLoginResponse.json();
        const employeeToken = employeeLoginData.data.token;
        const employeeOrgId = employeeLoginData.data.user.organization?.id;
        
        console.log(`✅ Employee login successful. Organization ID: ${employeeOrgId}`);

        // Step 4: Test Employee API Access
        console.log('\n4️⃣ Testing Employee API Access...');
        
        const testEndpoints = [
            { name: 'Leave Balance', url: '/leave/balance' },
            { name: 'Leave Types', url: '/leave/types' },
            { name: 'Leave Requests', url: '/leave/requests' },
            { name: 'Attendance Records', url: '/attendance/records' },
            { name: 'Tasks', url: '/tasks' }
        ];

        for (const endpoint of testEndpoints) {
            console.log(`\n   Testing ${endpoint.name}...`);
            try {
                const response = await fetch(`${API_BASE}${endpoint.url}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${employeeToken}`,
                        'X-Organization-ID': employeeOrgId,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log(`   ✅ ${endpoint.name}: SUCCESS (${response.status})`);
                    console.log(`   📊 Response: ${JSON.stringify(data).substring(0, 100)}...`);
                } else {
                    const errorText = await response.text();
                    console.log(`   ❌ ${endpoint.name}: FAILED (${response.status})`);
                    console.log(`   🚨 Error: ${errorText.substring(0, 200)}...`);
                }
            } catch (error) {
                console.log(`   ❌ ${endpoint.name}: ERROR - ${error.message}`);
            }
        }

        console.log('\n🎉 Employee Authorization Test Completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testEmployeeAuth();
